#!/usr/bin/env python3
"""
📊 即時統計報告器
提供處理過程中的即時統計更新和進度報告功能
"""

import time
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from dataclasses import dataclass, asdict
import json

from ..core.base_inference import Detection, TimingInfo, ConfusionMetrics


@dataclass
class ProcessingSession:
    """處理會話統計"""
    session_id: str
    start_time: float
    total_images: int
    processed_images: int = 0
    successful_images: int = 0
    failed_images: int = 0
    total_detections: int = 0
    total_processing_time: float = 0.0
    average_processing_time: float = 0.0
    current_image: str = ""
    
    @property
    def progress_percent(self) -> float:
        """進度百分比"""
        return (self.processed_images / max(self.total_images, 1)) * 100
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return (self.successful_images / max(self.processed_images, 1)) * 100
    
    @property
    def elapsed_time(self) -> float:
        """已用時間（秒）"""
        return time.time() - self.start_time
    
    @property
    def estimated_remaining_time(self) -> float:
        """預估剩餘時間（秒）"""
        if self.processed_images == 0:
            return 0.0
        
        avg_time_per_image = self.elapsed_time / self.processed_images
        remaining_images = self.total_images - self.processed_images
        return avg_time_per_image * remaining_images


@dataclass
class ClassStatistics:
    """類別統計"""
    class_id: int
    class_name: str
    detection_count: int = 0
    total_confidence: float = 0.0
    min_confidence: float = 1.0
    max_confidence: float = 0.0
    
    @property
    def average_confidence(self) -> float:
        """平均置信度"""
        return self.total_confidence / max(self.detection_count, 1)


class RealTimeReporter:
    """
    即時統計報告器
    
    功能:
    - 處理過程中的即時統計更新
    - 進度報告和ETA計算
    - 類別統計追蹤
    - 性能指標監控
    - 自動報告生成和保存
    """
    
    def __init__(self, output_dir: str, report_interval: int = 10):
        """
        初始化即時報告器
        
        Args:
            output_dir: 輸出目錄
            report_interval: 報告間隔（處理多少張圖像後輸出一次報告）
        """
        self.output_dir = Path(output_dir)
        self.report_interval = report_interval
        self.logger = logging.getLogger(__name__)
        
        # 確保輸出目錄存在
        self.reports_dir = self.output_dir / "reports"
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        
        # 當前處理會話
        self.current_session: Optional[ProcessingSession] = None
        
        # 類別統計
        self.class_stats: Dict[int, ClassStatistics] = {}
        
        # 性能統計
        self.performance_stats = {
            'total_inference_time': 0.0,
            'total_preprocessing_time': 0.0,
            'total_postprocessing_time': 0.0,
            'total_visualization_time': 0.0,
            'min_processing_time': float('inf'),
            'max_processing_time': 0.0,
            'processing_times': []
        }
        
        # 檢測統計
        self.detection_stats = {
            'total_detections': 0,
            'detections_per_image': [],
            'confidence_distribution': {'0.0-0.2': 0, '0.2-0.4': 0, '0.4-0.6': 0, '0.6-0.8': 0, '0.8-1.0': 0}
        }
        
        self.logger.info("📊 即時統計報告器初始化完成")
    
    def start_session(self, session_id: str, total_images: int) -> None:
        """
        開始新的處理會話
        
        Args:
            session_id: 會話ID
            total_images: 總圖像數量
        """
        self.current_session = ProcessingSession(
            session_id=session_id,
            start_time=time.time(),
            total_images=total_images
        )
        
        # 重置統計
        self.class_stats = {}
        self.performance_stats = {
            'total_inference_time': 0.0,
            'total_preprocessing_time': 0.0,
            'total_postprocessing_time': 0.0,
            'total_visualization_time': 0.0,
            'min_processing_time': float('inf'),
            'max_processing_time': 0.0,
            'processing_times': []
        }
        self.detection_stats = {
            'total_detections': 0,
            'detections_per_image': [],
            'confidence_distribution': {'0.0-0.2': 0, '0.2-0.4': 0, '0.4-0.6': 0, '0.6-0.8': 0, '0.8-1.0': 0}
        }
        
        self.logger.info(f"📊 開始處理會話: {session_id}, 總圖像數: {total_images}")
    
    def update_image_start(self, image_path: str) -> None:
        """
        更新當前處理的圖像
        
        Args:
            image_path: 圖像路徑
        """
        if not self.current_session:
            return
        
        self.current_session.current_image = Path(image_path).name
        self.logger.debug(f"📸 開始處理: {self.current_session.current_image}")
    
    def update_image_completed(self, 
                             image_path: str,
                             detections: List[Detection],
                             timing_info: TimingInfo,
                             success: bool = True,
                             error_message: str = "") -> None:
        """
        更新圖像處理完成統計
        
        Args:
            image_path: 圖像路徑
            detections: 檢測結果
            timing_info: 計時信息
            success: 是否成功
            error_message: 錯誤信息
        """
        if not self.current_session:
            return
        
        # 更新會話統計
        self.current_session.processed_images += 1
        
        if success:
            self.current_session.successful_images += 1
            detection_count = len(detections)
            self.current_session.total_detections += detection_count
            
            # 更新檢測統計
            self.detection_stats['total_detections'] += detection_count
            self.detection_stats['detections_per_image'].append(detection_count)
            
            # 更新類別統計
            for detection in detections:
                self._update_class_stats(detection)
            
            # 更新置信度分布
            for detection in detections:
                self._update_confidence_distribution(detection.confidence)
            
        else:
            self.current_session.failed_images += 1
            if error_message:
                self.logger.warning(f"❌ 圖像處理失敗 {Path(image_path).name}: {error_message}")
        
        # 更新性能統計
        if timing_info:
            self._update_performance_stats(timing_info)
        
        # 計算平均處理時間
        if self.current_session.processed_images > 0:
            self.current_session.average_processing_time = (
                self.current_session.total_processing_time / self.current_session.processed_images
            )
        
        # 定期輸出報告
        if self.current_session.processed_images % self.report_interval == 0:
            self._output_progress_report()
        
        self.logger.debug(f"✅ 完成處理: {Path(image_path).name}, 檢測數: {len(detections) if success else 0}")
    
    def _update_class_stats(self, detection: Detection) -> None:
        """更新類別統計"""
        class_id = detection.class_id
        
        if class_id not in self.class_stats:
            self.class_stats[class_id] = ClassStatistics(
                class_id=class_id,
                class_name=detection.class_name
            )
        
        stats = self.class_stats[class_id]
        stats.detection_count += 1
        stats.total_confidence += detection.confidence
        stats.min_confidence = min(stats.min_confidence, detection.confidence)
        stats.max_confidence = max(stats.max_confidence, detection.confidence)
    
    def _update_confidence_distribution(self, confidence: float) -> None:
        """更新置信度分布"""
        if confidence < 0.2:
            self.detection_stats['confidence_distribution']['0.0-0.2'] += 1
        elif confidence < 0.4:
            self.detection_stats['confidence_distribution']['0.2-0.4'] += 1
        elif confidence < 0.6:
            self.detection_stats['confidence_distribution']['0.4-0.6'] += 1
        elif confidence < 0.8:
            self.detection_stats['confidence_distribution']['0.6-0.8'] += 1
        else:
            self.detection_stats['confidence_distribution']['0.8-1.0'] += 1
    
    def _update_performance_stats(self, timing_info: TimingInfo) -> None:
        """更新性能統計"""
        if not self.current_session:
            return
        
        total_time = timing_info.total_time
        self.current_session.total_processing_time += total_time
        
        # 更新性能統計
        self.performance_stats['total_inference_time'] += timing_info.inference_time
        if hasattr(timing_info, 'preprocessing_time'):
            self.performance_stats['total_preprocessing_time'] += timing_info.preprocessing_time
        if hasattr(timing_info, 'postprocessing_time'):
            self.performance_stats['total_postprocessing_time'] += timing_info.postprocessing_time
        if hasattr(timing_info, 'visualization_time'):
            self.performance_stats['total_visualization_time'] += timing_info.visualization_time
        
        self.performance_stats['min_processing_time'] = min(
            self.performance_stats['min_processing_time'], total_time
        )
        self.performance_stats['max_processing_time'] = max(
            self.performance_stats['max_processing_time'], total_time
        )
        self.performance_stats['processing_times'].append(total_time)
    
    def _output_progress_report(self) -> None:
        """輸出進度報告"""
        if not self.current_session:
            return
        
        session = self.current_session
        
        # 計算統計指標
        avg_detections = (
            sum(self.detection_stats['detections_per_image']) / 
            len(self.detection_stats['detections_per_image'])
            if self.detection_stats['detections_per_image'] else 0
        )
        
        # 輸出進度信息
        self.logger.info("=" * 60)
        self.logger.info(f"📊 處理進度報告 - {session.session_id}")
        self.logger.info("=" * 60)
        self.logger.info(f"🎯 進度: {session.processed_images}/{session.total_images} ({session.progress_percent:.1f}%)")
        self.logger.info(f"✅ 成功: {session.successful_images} ({session.success_rate:.1f}%)")
        self.logger.info(f"❌ 失敗: {session.failed_images}")
        self.logger.info(f"🔍 檢測總數: {session.total_detections}")
        self.logger.info(f"📈 平均檢測/圖: {avg_detections:.1f}")
        self.logger.info(f"⏱️ 已用時間: {session.elapsed_time:.1f}秒")
        self.logger.info(f"🕐 預估剩餘: {session.estimated_remaining_time:.1f}秒")
        self.logger.info(f"⚡ 平均處理時間: {session.average_processing_time:.3f}秒/圖")
        
        # 輸出前3個最多檢測的類別
        if self.class_stats:
            top_classes = sorted(
                self.class_stats.values(), 
                key=lambda x: x.detection_count, 
                reverse=True
            )[:3]
            
            self.logger.info("🏆 檢測最多的類別:")
            for i, stats in enumerate(top_classes, 1):
                self.logger.info(
                    f"   {i}. {stats.class_name}: {stats.detection_count}次 "
                    f"(平均置信度: {stats.average_confidence:.3f})"
                )
        
        self.logger.info("=" * 60)
    
    def generate_final_report(self) -> Dict[str, Any]:
        """
        生成最終處理報告
        
        Returns:
            Dict: 完整的處理報告
        """
        if not self.current_session:
            return {}
        
        session = self.current_session
        
        # 計算最終統計
        report = {
            'session_info': asdict(session),
            'class_statistics': {
                str(class_id): asdict(stats) for class_id, stats in self.class_stats.items()
            },
            'performance_statistics': self.performance_stats.copy(),
            'detection_statistics': self.detection_stats.copy(),
            'summary': {
                'total_processing_time': session.elapsed_time,
                'average_time_per_image': session.average_processing_time,
                'detection_rate': session.total_detections / max(session.successful_images, 1),
                'throughput_images_per_second': session.processed_images / session.elapsed_time,
                'most_detected_class': max(
                    self.class_stats.items(), 
                    key=lambda x: x[1].detection_count,
                    default=(0, ClassStatistics(0, "None"))
                )[1].class_name if self.class_stats else "None"
            }
        }
        
        # 保存報告到文件
        report_file = self.reports_dir / f"final_report_{session.session_id}.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            self.logger.info(f"📋 最終報告已保存: {report_file}")
        except Exception as e:
            self.logger.error(f"❌ 保存最終報告失敗: {e}")
        
        return report
    
    def get_live_statistics(self) -> Dict[str, Any]:
        """
        獲取即時統計信息
        
        Returns:
            Dict: 當前統計信息
        """
        if not self.current_session:
            return {}
        
        session = self.current_session
        
        return {
            'session_id': session.session_id,
            'progress_percent': session.progress_percent,
            'processed_images': session.processed_images,
            'total_images': session.total_images,
            'success_rate': session.success_rate,
            'total_detections': session.total_detections,
            'current_image': session.current_image,
            'elapsed_time': session.elapsed_time,
            'estimated_remaining_time': session.estimated_remaining_time,
            'average_processing_time': session.average_processing_time,
            'class_count': len(self.class_stats),
            'detection_rate': session.total_detections / max(session.successful_images, 1)
        }
    
    def end_session(self) -> Dict[str, Any]:
        """
        結束當前會話並生成最終報告
        
        Returns:
            Dict: 最終報告
        """
        if not self.current_session:
            return {}
        
        self.logger.info(f"🏁 處理會話結束: {self.current_session.session_id}")
        self._output_progress_report()  # 最後一次進度報告
        
        final_report = self.generate_final_report()
        
        # 輸出最終摘要
        session = self.current_session
        self.logger.info("🎉 處理完成摘要:")
        self.logger.info(f"   總處理時間: {session.elapsed_time:.2f}秒")
        self.logger.info(f"   成功率: {session.success_rate:.1f}%")
        self.logger.info(f"   總檢測數: {session.total_detections}")
        self.logger.info(f"   處理吞吐量: {session.processed_images/session.elapsed_time:.2f}圖/秒")
        
        return final_report
    
    def cleanup(self):
        """清理報告器資源"""
        if self.current_session:
            self.end_session()
        self.logger.debug("🧹 清理即時報告器資源")