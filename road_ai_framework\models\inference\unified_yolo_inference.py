#!/usr/bin/env python3
"""
統一YOLO推理引擎
整合所有YOLO推理功能：檢測、分割、SAHI、智能過濾、三視圖、字體設定等
"""

from .config_manager import UnifiedYOLOConfigManager, ClassConfig
from .advanced_slice_inference import AdvancedFusionEngine, FusionConfig, FusionStrategy
from core.import_helper import setup_project_paths
import os
import sys
import cv2
import json
import yaml
import time
import logging
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime
from collections import defaultdict
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib import font_manager
import gc
import psutil

# 使用統一導入管理
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent.parent
sys.path.insert(0, str(project_root))

setup_project_paths()

try:
    import torch
    from ultralytics import YOLO
    from ultralytics.utils.plotting import Annotator, colors
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False


try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

# 導入配置管理器


class FontManager:
    """字體管理器 - 支持中文字體和自定義字體"""

    def __init__(self, font_path: str = "", font_size: float = 1.0,
                 font_thickness: int = 2, font_scale: float = 1.0):
        """
        初始化字體管理器

        Args:
            font_path: 自定義字體路徑
            font_size: 字體大小倍數
            font_thickness: 字體粗細
            font_scale: 字體縮放比例
        """
        self.font_path = font_path
        self.font_size = font_size
        self.font_thickness = font_thickness
        self.font_scale = font_scale
        self.matplotlib_font = None

        self._setup_fonts()

    def _setup_fonts(self):
        """設置字體"""
        # 設置matplotlib中文字體
        if self.font_path and Path(self.font_path).exists():
            # 使用自定義字體
            font_prop = fm.FontProperties(fname=self.font_path)
            plt.rcParams['font.family'] = font_prop.get_name()
            self.matplotlib_font = font_prop
        else:
            # 使用系統默認中文字體
            chinese_fonts = ['Microsoft YaHei']
            for font_name in chinese_fonts:
                try:
                    plt.rcParams['font.sans-serif'] = [font_name]
                    break
                except:
                    continue
            plt.rcParams['axes.unicode_minus'] = False

    def get_cv2_font_scale(self, base_scale: float = 0.6) -> float:
        """獲取OpenCV字體縮放比例"""
        return base_scale * self.font_size * self.font_scale

    def get_cv2_font_thickness(self) -> int:
        """獲取OpenCV字體粗細"""
        return max(1, int(self.font_thickness * self.font_scale))

    def get_matplotlib_fontsize(self, base_size: int = 12) -> int:
        """獲取matplotlib字體大小"""
        return int(base_size * self.font_size * self.font_scale)


class ThreeViewGenerator:
    """三視圖生成器 - 支持原圖/GT/預測的三視圖顯示，增強GT加載功能，GT和pred顏色一致"""

    def __init__(self, font_manager: FontManager, config_manager, layout: str = "horizontal", spacing: int = 10, excluded_classes: Optional[List] = None):
        """
        初始化三視圖生成器
        🆕 新增排除類別支援

        Args:
            font_manager: 字體管理器
            config_manager: 配置管理器（用於獲取類別顏色）
            layout: 佈局方式 ("horizontal" 或 "vertical")
            spacing: 視圖間距
            excluded_classes: 排除的類別列表（ID和名稱混合）
        """
        self.font_manager = font_manager
        self.config_manager = config_manager
        self.layout = layout
        self.spacing = spacing
        self.logger = logging.getLogger(__name__)

        # 🆕 儲存排除類別配置
        self.excluded_classes = excluded_classes or []

        # 🆕 動態設定排除類別到config_manager（確保GT加載能夠獲取）
        if excluded_classes:
            # 分離ID和名稱
            excluded_ids = [c for c in excluded_classes if isinstance(c, int)]
            excluded_names = [
                c for c in excluded_classes if isinstance(c, str)]

            # 設定到config_manager以供GT加載使用
            self.config_manager.excluded_class_ids = excluded_ids
            self.config_manager.excluded_class_names = excluded_names

            self.logger.debug(
                f"三視圖生成器設定排除類別: IDs={excluded_ids}, Names={excluded_names}")

    def create_three_view(self, original_img: np.ndarray, pred_img: np.ndarray,
                          gt_img: Optional[np.ndarray] = None,
                          titles: Optional[List[str]] = None,
                          image_path: Optional[str] = None,
                          labelme_dir: Optional[str] = None,
                          detection_count: int = 0,
                          gt_count: int = 0,
                          detections: Optional[List] = None,
                          roi_offset: Optional[Tuple[int, int]] = None) -> np.ndarray:
        """
        創建三視圖，支持自動加載GT標註和TP/FP/FN統計

        Args:
            original_img: 原始圖像
            pred_img: 預測結果圖像
            gt_img: GT標註圖像（可選，如果為None且提供image_path和labelme_dir則自動加載）
            titles: 標題列表
            image_path: 圖像路徑（用於查找對應的GT標註）
            labelme_dir: LabelMe標註目錄
            detection_count: 檢測數量
            gt_count: GT標註數量
            detections: 檢測結果列表（用於TP/FP/FN計算）
            roi_offset: ROI偏移量（用於座標轉換），格式為 (x_offset, y_offset)

        Returns:
            合併後的三視圖圖像
        """
        # Debug: 印出圖像 shape
        print(f"[三視圖DEBUG] original_img.shape: {original_img.shape}")
        print(f"[三視圖DEBUG] pred_img.shape: {pred_img.shape}")
        if gt_img is not None:
            print(f"[三視圖DEBUG] gt_img.shape: {gt_img.shape}")
        else:
            print(f"[三視圖DEBUG] gt_img: None")
        # Debug: 印出 bbox 資訊
        if detections:
            for i, det in enumerate(detections):
                bbox = det.get('bbox', None)
                if bbox:
                    print(f"[三視圖DEBUG] detection[{i}] bbox: {bbox}")

        # 如果沒有GT圖像但提供了路徑信息，嘗試加載GT
        gt_data = None
        if gt_img is None and image_path and labelme_dir:
            gt_img, gt_count, gt_data = self._load_gt_visualization_with_data(
                original_img, image_path, labelme_dir)
        
        # 🟢 修復ROI座標轉換：只有當檢測結果確實在ROI空間中時才進行轉換
        # 注意：目前的檢測結果已經在原圖座標系統中，不需要再次轉換
        # 這個轉換會造成座標不正確的問題
        if roi_offset and detections:
            # 檢查檢測結果是否需要轉換（此處禁用轉換以修復座標問題）
            self.logger.debug(f"🟢 跳過ROI座標轉換：檢測結果已經在原圖座標系統中")
            # detections = self._transform_detections_to_original_space(detections, roi_offset)

        # 計算TP/FP/FN統計以及P、R、F1
        tp_count, fp_count, fn_count, precision, recall, f1 = self._calculate_tp_fp_fn(
            detections, gt_data) if detections and gt_data else (0, 0, 0, 0.0, 0.0, 0.0)

        # 設置默認標題（包含P、R、F1及TP/FP/FN統計）
        if titles is None:
            if gt_img is not None:
                titles = [
                    "原始圖像",
                    f"Ground Truth ({gt_count} 個標註)",
                    f"預測結果-分割 ({detection_count} 個檢測)\nP:{precision:.3f} R:{recall:.3f} F1:{f1:.3f}\nTP:{tp_count} FP:{fp_count} FN:{fn_count}"
                ]
            else:
                titles = [
                    "原始圖像",
                    f"預測結果-分割 ({detection_count} 個檢測)"
                ]

        # 確保所有圖像尺寸一致
        h, w = original_img.shape[:2]
        pred_img = cv2.resize(pred_img, (w, h))
        if gt_img is not None:
            gt_img = cv2.resize(gt_img, (w, h))

        # 準備圖像列表
        images = [original_img]
        if gt_img is not None:
            images.append(gt_img)
        images.append(pred_img)

        # 調整標題數量
        titles = titles[:len(images)]

        # 添加標題
        titled_images = []
        for img, title in zip(images, titles):
            titled_img = self._add_title_to_image(img, title)
            titled_images.append(titled_img)

        # 合併圖像
        if self.layout == "horizontal":
            return self._merge_horizontal(titled_images)
        else:
            return self._merge_vertical(titled_images)

    def _add_title_to_image(self, img: np.ndarray, title: str) -> np.ndarray:
        """為圖像添加標題，支持多行文字並解決中文字體問題"""
        # 分割多行標題
        lines = title.split('\n')

        # 計算需要的標題高度（增加行間距）
        font_scale = self.font_manager.get_cv2_font_scale(0.8)  # 略微放大字體
        thickness = self.font_manager.get_cv2_font_thickness()

        # 計算單行文字高度
        text_height = cv2.getTextSize(
            'A', cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)[0][1]
        line_spacing = 50  # 增加行間距從5到15
        title_height = len(lines) * (text_height +
                                     line_spacing) + 60  # 增加上下邊距從20到40

        title_img = np.ones(
            (title_height, img.shape[1], 3), dtype=np.uint8) * 255

        # 繪制每一行文字
        for i, line in enumerate(lines):
            if line.strip():  # 只繪制非空行
                # 處理中文字符，將中文替換為英文適配版本
                display_line = line
                # 簡單的中文轉換
                display_line = display_line.replace('原始圖像', 'Original Image')
                display_line = display_line.replace(
                    '預測結果-分割', 'Prediction-Segmentation')
                display_line = display_line.replace('個檢測', ' detections')
                display_line = display_line.replace('個標註', ' annotations')

                text_size = cv2.getTextSize(
                    display_line, cv2.FONT_HERSHEY_SIMPLEX, font_scale, thickness)[0]
                text_x = (title_img.shape[1] - text_size[0]) // 2
                text_y = 25 + i * (text_height + line_spacing) + \
                    text_height  # 增加起始位置

                cv2.putText(title_img, display_line, (text_x, text_y),
                            cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), thickness)

        # 合併標題和圖像
        return np.vstack([title_img, img])

    def _merge_horizontal(self, images: List[np.ndarray]) -> np.ndarray:
        """水平合併圖像"""
        max_height = max(img.shape[0] for img in images)

        # 調整所有圖像到相同高度
        resized_images = []
        for img in images:
            if img.shape[0] != max_height:
                ratio = max_height / img.shape[0]
                new_width = int(img.shape[1] * ratio)
                img = cv2.resize(img, (new_width, max_height))
            resized_images.append(img)

        # 添加間距
        spacing_bar = np.ones(
            (max_height, self.spacing, 3), dtype=np.uint8) * 200

        merged_images = []
        for i, img in enumerate(resized_images):
            merged_images.append(img)
            if i < len(resized_images) - 1:
                merged_images.append(spacing_bar)

        return np.hstack(merged_images)

    def _merge_vertical(self, images: List[np.ndarray]) -> np.ndarray:
        """垂直合併圖像"""
        max_width = max(img.shape[1] for img in images)

        # 調整所有圖像到相同寬度
        resized_images = []
        for img in images:
            if img.shape[1] != max_width:
                ratio = max_width / img.shape[1]
                new_height = int(img.shape[0] * ratio)
                img = cv2.resize(img, (max_width, new_height))
            resized_images.append(img)

        # 添加間距
        spacing_bar = np.ones(
            (self.spacing, max_width, 3), dtype=np.uint8) * 200

        merged_images = []
        for i, img in enumerate(resized_images):
            merged_images.append(img)
            if i < len(resized_images) - 1:
                merged_images.append(spacing_bar)

        return np.vstack(merged_images)

    def _load_gt_visualization_with_data(self, original_img: np.ndarray, image_path: str, labelme_dir: str) -> Tuple[Optional[np.ndarray], int, Optional[List]]:
        """
        加載GT標註並生成可視化圖像，同時返回GT數據
        🆕 新增功能：支援排除類別過濾、improved mask渲染

        Args:
            original_img: 原始圖像
            image_path: 圖像路徑
            labelme_dir: LabelMe標註目錄

        Returns:
            GT可視化圖像、標註數量和GT數據列表
        """
        try:
            import json

            # 獲取對應的LabelMe JSON文件
            image_name = Path(image_path).stem
            json_path = Path(labelme_dir) / f"{image_name}.json"

            if not json_path.exists():
                print(f"🔍 GT加載: 未找到GT標註文件: {json_path}")
                self.logger.debug(f"未找到GT標註文件: {json_path}")
                return None, 0, None

            print(f"📂 GT加載: 找到標註文件: {json_path}")

            # 讀取LabelMe標註
            with open(json_path, 'r', encoding='utf-8') as f:
                labelme_data = json.load(f)

            # 🆕 獲取排除類別配置（從config_manager中獲取）
            excluded_class_ids = getattr(
                self.config_manager, 'excluded_class_ids', [])
            excluded_class_names = getattr(
                self.config_manager, 'excluded_class_names', [])
            included_class_ids = getattr(
                self.config_manager, 'included_class_ids', None)

            # 🔧 修復：移除錯誤的硬編碼預設排除邏輯
            # 空的排除列表應該意味著「不排除任何類別」，而不是使用硬編碼預設值
            # 原始邏輯有問題：即使用戶明確設定了空列表，也會被硬編碼排除覆蓋
            
            # 新邏輯：尊重用戶配置，不進行任何預設排除
            # 如果用戶需要排除特定類別，可以在配置中明確指定
            
            # 原本的有問題的邏輯：
            # if not excluded_class_names and not excluded_class_ids and included_class_ids is None:
            #     excluded_class_names = ['joint', 'manhole', 'dirt', 'lane_line_linear']
            
            self.logger.debug(
                f"使用用戶配置的排除設定 - "
                f"excluded_class_names: {excluded_class_names}, "
                f"excluded_class_ids: {excluded_class_ids}, "
                f"included_class_ids: {included_class_ids}"
            )

            gt_img = original_img.copy()
            gt_count = 0
            gt_data = []
            excluded_count = 0  # 記錄被排除的標註數量

            # 處理每個標註
            for shape in labelme_data.get('shapes', []):
                label = shape.get('label', '')
                points = shape.get('points', [])
                shape_type = shape.get('shape_type', 'polygon')

                if not points:
                    continue

                # 使用原始標籤名稱作為顯示標籤（移除不存在的map_label_to_standard方法）

                # 🆕 關鍵修復：先清理標籤，再進行解析
                # 1. 清理原始標籤，移除尾隨的下劃線
                cleaned_label = label.rstrip('_')

                # 2. 使用清理後的標籤進行別名解析
                display_label = self.config_manager.resolve_label_alias(
                    cleaned_label)

                # 🆕 檢查是否需要排除此類別
                should_exclude = False

                # 根據類別名稱檢查排除
                if display_label in excluded_class_names:
                    should_exclude = True

                # 檢查原始label是否在排除列表中
                if label in excluded_class_names:
                    should_exclude = True

                # 根據類別ID檢查排除（從classes配置中查找）
                class_id = None
                try:
                    # 嘗試從classes配置中查找對應的class_id
                    for cid, class_info in self.config_manager.classes.items():
                        if hasattr(class_info, 'name') and class_info.name == display_label:
                            class_id = cid
                            break
                        elif isinstance(class_info, dict) and class_info.get('name') == display_label:
                            class_id = cid
                            break
                except:
                    class_id = None

                if class_id is not None and class_id in excluded_class_ids:
                    should_exclude = True

                # 檢查included_class_ids（如果設定了，只保留指定的類別）
                if included_class_ids is not None:
                    if class_id is None or class_id not in included_class_ids:
                        should_exclude = True

                if should_exclude:
                    excluded_count += 1
                    self.logger.debug(f"排除GT標註: {display_label} (原始: {label})")
                    continue

                gt_count += 1

                # 轉換點坐標
                points = np.array(points, dtype=np.float32)

                # 獲取縮放比例
                resize_ratio = self.config_manager.inference.resize_ratio
                if resize_ratio != 1.0:
                    points *= resize_ratio

                points = np.array(points, dtype=np.int32)

                # 計算邊界框
                x_coords = points[:, 0]
                y_coords = points[:, 1]
                bbox = [x_coords.min(), y_coords.min(),
                        x_coords.max(), y_coords.max()]

                # 保存GT數據
                gt_data.append({
                    'bbox': bbox,
                    'class_name': display_label,
                    'class_id': class_id,
                    'shape_type': shape_type,
                    'points': points
                })

                # 🎨 獲取與預測結果一致的類別顏色
                class_color = self._get_class_color_for_gt(display_label)

                # 🆕 改進的mask渲染邏輯 - 與預測結果保持一致
                self._render_gt_mask(gt_img, points, class_color, shape_type)

                # 繪制GT標籤文字（與pred完全一致的樣式）
                label_text = f"GT: {display_label}"
                label_pos = (int(points[:, 0].min()),
                             int(points[:, 1].min()) - 10)
                self._draw_label_text(gt_img, label_text,
                                      label_pos, class_color)

            # 🆕 輸出過濾統計
            if excluded_count > 0:
                print(f"🎯 GT加載完成: 有效標註 {gt_count} 個，排除標註 {excluded_count} 個")
                self.logger.debug(
                    f"GT加載完成: 有效標註 {gt_count} 個，排除標註 {excluded_count} 個")
            else:
                print(f"🎯 GT加載完成: 有效標註 {gt_count} 個")
                self.logger.debug(f"GT加載完成: 有效標註 {gt_count} 個")

            if gt_count > 0:
                print(f"✅ 成功加載GT標註: {json_path.name}，標註數量: {gt_count}")
                self.logger.debug(f"成功加載GT標註: {json_path}，標註數量: {gt_count}")
                return gt_img, gt_count, gt_data
            else:
                print(f"⚠️ GT標註文件存在但沒有有效標註: {json_path.name}")
                return None, 0, None

        except Exception as e:
            print(f"❌ 加載GT標註時出錯: {e}")
            print(f"   檔案路徑: {json_path}")
            self.logger.debug(f"加載GT標註時出錯: {e}")
            return None, 0, None

    def _render_gt_mask(self, img: np.ndarray, points: np.ndarray, class_color: tuple, shape_type: str, fill_alpha: float = 0.3):
        """
        渲染GT mask，與預測結果的mask渲染保持一致
        🆕 支援多種mask渲染模式

        Args:
            img: 目標圖像
            points: 點座標
            class_color: 類別顏色
            shape_type: 形狀類型
            fill_alpha: 填充透明度
        """
        try:
            # 獲取mask渲染模式（如果config_manager有的話）
            mask_render_mode = getattr(
                self.config_manager, 'mask_render_mode', 'outline_only')

            if mask_render_mode == 'outline_only':
                # 只繪製邊框，不填充mask（避免變暗）
                if shape_type == 'polygon':
                    cv2.polylines(img, [points], True, class_color, 3)
                elif shape_type == 'rectangle' and len(points) >= 2:
                    pt1 = tuple(points[0])
                    pt2 = tuple(points[1])
                    cv2.rectangle(img, pt1, pt2, class_color, 3)
                else:
                    cv2.polylines(img, [points], False, class_color, 3)

            elif mask_render_mode == 'unified':
                # 統一mask圖層方法，一次性混合
                mask = np.zeros(img.shape[:2], dtype=np.uint8)
                if shape_type == 'polygon':
                    cv2.fillPoly(mask, [points], 255)
                elif shape_type == 'rectangle' and len(points) >= 2:
                    pt1 = tuple(points[0])
                    pt2 = tuple(points[1])
                    cv2.rectangle(mask, pt1, pt2, 255, -1)

                # 創建彩色mask
                colored_mask = np.zeros_like(img)
                colored_mask[mask > 0] = class_color

                # 一次性混合
                cv2.addWeighted(img, 1.0, colored_mask, fill_alpha, 0, img)

                # 繪製邊框
                if shape_type == 'polygon':
                    cv2.polylines(img, [points], True, class_color, 2)
                elif shape_type == 'rectangle' and len(points) >= 2:
                    pt1 = tuple(points[0])
                    pt2 = tuple(points[1])
                    cv2.rectangle(img, pt1, pt2, class_color, 2)

            else:
                # 簡單模式或其他模式 - 使用較低的透明度
                overlay = img.copy()
                if shape_type == 'polygon':
                    cv2.fillPoly(overlay, [points], class_color)
                    cv2.polylines(img, [points], True, class_color, 3)
                elif shape_type == 'rectangle' and len(points) >= 2:
                    pt1 = tuple(points[0])
                    pt2 = tuple(points[1])
                    cv2.rectangle(overlay, pt1, pt2, class_color, -1)
                    cv2.rectangle(img, pt1, pt2, class_color, 3)
                else:
                    cv2.polylines(img, [points], False, class_color, 3)
                    return

                cv2.addWeighted(img, 0.8, overlay, 0.2, 0, img)

        except Exception as e:
            self.logger.warning(f"GT mask渲染失敗: {e}")
            # 降級到簡單邊框渲染
            if shape_type == 'polygon':
                cv2.polylines(img, [points], True, class_color, 3)
            elif shape_type == 'rectangle' and len(points) >= 2:
                pt1 = tuple(points[0])
                pt2 = tuple(points[1])
                cv2.rectangle(img, pt1, pt2, class_color, 3)

    def _draw_label_text(self, img: np.ndarray, label_text: str, label_pos: tuple, class_color: tuple):
        """
        繪製標籤文字，與預測結果保持一致的樣式

        Args:
            img: 目標圖像
            label_text: 標籤文字
            label_pos: 文字位置
            class_color: 類別顏色
        """
        try:
            font_scale = self.font_manager.get_cv2_font_scale(0.6)
            thickness = self.font_manager.get_cv2_font_thickness()

            # 計算文字大小
            (text_w, text_h), _ = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX,
                                                  font_scale, thickness)

            # 繪制標籤背景（使用類別顏色背景，與pred一致）
            bg_x1, bg_y1 = label_pos
            bg_x2, bg_y2 = bg_x1 + text_w, bg_y1 + text_h + 5
            cv2.rectangle(img, (bg_x1, bg_y1 - text_h - 5),
                          (bg_x2, bg_y2), class_color, -1)

            # 繪制黑色文字（與pred一致）
            cv2.putText(img, label_text, label_pos, cv2.FONT_HERSHEY_SIMPLEX,
                        font_scale, (0, 0, 0), thickness)
        except Exception as e:
            self.logger.warning(f"標籤文字繪製失敗: {e}")

    def _calculate_tp_fp_fn(self, detections: List, gt_data: List) -> Tuple[int, int, int, float, float, float]:
        """
        計算TP/FP/FN統計以及P、R、F1指標

        Args:
            detections: 檢測結果列表
            gt_data: GT數據列表

        Returns:
            TP, FP, FN數量以及Precision, Recall, F1分數
        """
        if not detections or not gt_data:
            return 0, 0, 0, 0.0, 0.0, 0.0

        tp = 0
        matched_gt = set()
        matched_pred = set()

        # 🔧 修復：基於IoU和類別匹配 (確保label_aliases一致性)
        for i, det in enumerate(detections):
            det_bbox = det.get('bbox', [])
            det_class_raw = det.get('class_name', '')

            # 🆕 確保檢測結果的類別名稱也通過label_aliases處理
            det_class = self.config_manager.resolve_label_alias(det_class_raw)

            best_iou = 0
            best_gt_idx = -1

            for j, gt in enumerate(gt_data):
                if j in matched_gt:
                    continue

                gt_bbox = gt.get('bbox', [])
                gt_class = gt.get('class_name', '')  # GT已經在加載時處理過label_aliases

                # 🔧 修復：類別必須匹配 (現在兩邊都經過label_aliases處理)
                if det_class != gt_class:
                    continue

                # 計算IoU
                iou = self._calculate_bbox_iou_simple(det_bbox, gt_bbox)

                if iou > best_iou and iou > 0.5:  # IoU闾值0.5
                    best_iou = iou
                    best_gt_idx = j

            if best_gt_idx >= 0:
                tp += 1
                matched_gt.add(best_gt_idx)
                matched_pred.add(i)

        fp = len(detections) - tp  # 未匹配的檢測
        fn = len(gt_data) - tp      # 未匹配的GT

        # 計算P、R、F1
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * (precision * recall) / (precision +
                                         recall) if (precision + recall) > 0 else 0.0

        return tp, fp, fn, precision, recall, f1

    def _calculate_bbox_iou_simple(self, bbox1: List, bbox2: List) -> float:
        """
        簡化的邊界框IoU計算

        Args:
            bbox1: [x1, y1, x2, y2]
            bbox2: [x1, y1, x2, y2]

        Returns:
            IoU值
        """
        if len(bbox1) < 4 or len(bbox2) < 4:
            return 0.0

        # 計算交集
        x1 = max(bbox1[0], bbox2[0])
        y1 = max(bbox1[1], bbox2[1])
        x2 = min(bbox1[2], bbox2[2])
        y2 = min(bbox1[3], bbox2[3])

        if x2 <= x1 or y2 <= y1:
            return 0.0

        intersection = (x2 - x1) * (y2 - y1)

        # 計算並集
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    def _get_class_color_for_gt(self, class_name: str) -> Tuple[int, int, int]:
        """
        獲取GT標註的類別顏色（與預測結果保持一致）

        Args:
            class_name: 類別名稱

        Returns:
            BGR顏色元組
        """
        try:
            # 通過類別名稱查找類別ID
            class_id = None

            # 輸出調試信息
            print(f"Debug: GT類別名稱: {class_name}")
            print(f"Debug: 可用類別配置: {list(self.config_manager.classes.keys())}")

            for cid, class_config in self.config_manager.classes.items():
                print(
                    f"Debug: 檢查類別ID {cid}: name='{class_config.name}', display_name='{class_config.display_name}'")
                if class_config.name == class_name or class_config.display_name == class_name:
                    class_id = cid
                    print(f"Debug: 找到匹配的類別ID: {class_id}")
                    break

            if class_id is not None:
                # 獲取類別配置的顏色
                color = self.config_manager.get_class_color(class_id)
                return color
            else:
                # 如果找不到對應類別，使用默認顏色
                print(f"Debug: 未找到類別 {class_name} 的顏色配置，使用默認灰色")
                self.logger.debug(f"未找到類別 {class_name} 的顏色配置，使用默認顏色")
                return (128, 128, 128)  # 灰色作為默認顏色

        except Exception as e:
            print(f"Debug: 獲取GT顏色時出錯: {e}")
            self.logger.debug(f"獲取GT顏色時出錯: {e}，使用默認顏色")
            return (128, 128, 128)  # 灰色作為默認顏色

    def generate_three_view_advanced(self,
                                     image: np.ndarray,
                                     detections: List[Dict],
                                     gt_data: Optional[List[Dict]],
                                     image_path: str,
                                     output_path: str,
                                     roi_offset: Optional[Tuple[int, int]] = None):
        """
        為高級推理系統生成三視圖 - 兼容新的高級推理接口

        Args:
            image: 原始圖像
            detections: 檢測結果列表（統一格式）
            gt_data: GT數據（可選）
            image_path: 圖像路徑
            output_path: 輸出路徑
        """
        try:
            image_name = Path(image_path).stem

            # 創建預測結果圖像
            pred_img = self._draw_detections_on_image(image.copy(), detections)

            # 創建GT圖像（如果有GT數據）
            gt_img = None
            gt_count = 0
            if gt_data:
                gt_img = self._draw_gt_data_on_image(image.copy(), gt_data)
                gt_count = len(gt_data)

            # 創建三視圖
            three_view = self.create_three_view(
                original_img=image,
                pred_img=pred_img,
                gt_img=gt_img,
                detection_count=len(detections),
                gt_count=gt_count,
                detections=detections,
                roi_offset=roi_offset
            )

            # 保存三視圖
            output_dir = Path(output_path) / "images"
            output_dir.mkdir(parents=True, exist_ok=True)

            three_view_path = output_dir / f"{image_name}_three_view.jpg"
            cv2.imwrite(str(three_view_path), three_view)

            self.logger.info(f"✅ 三視圖已保存: {three_view_path}")

        except Exception as e:
            self.logger.error(f"❌ 生成三視圖失敗: {e}")
            import traceback
            traceback.print_exc()

    def _transform_detections_to_original_space(self, detections: List[Dict], roi_offset: Tuple[int, int], from_roi_space: bool = True) -> List[Dict]:
        """
        將檢測座標轉換到原圖空間
        
        Args:
            detections: 檢測結果列表
            roi_offset: ROI偏移量 (x_offset, y_offset)
            from_roi_space: 是否從ROI空間轉換（True）還是從原圖空間轉換到ROI空間（False）
            
        Returns:
            轉換後的檢測結果列表
        """
        if not detections or not roi_offset:
            return detections
            
        x_offset, y_offset = roi_offset
        transformed_detections = []
        
        for det in detections:
            transformed_det = det.copy()
            
            # 轉換bbox座標
            if 'bbox' in det:
                x1, y1, x2, y2 = det['bbox']
                
                if from_roi_space:
                    # 從ROI空間轉換到原圖空間（加上偏移）
                    transformed_det['bbox'] = [
                        x1 + x_offset,  # x1
                        y1 + y_offset,  # y1
                        x2 + x_offset,  # x2
                        y2 + y_offset   # y2
                    ]
                else:
                    # 從原圖空間轉換到ROI空間（減去偏移）
                    transformed_det['bbox'] = [
                        x1 - x_offset,  # x1
                        y1 - y_offset,  # y1
                        x2 - x_offset,  # x2
                        y2 - y_offset   # y2
                    ]
                
            # 轉換polygon座標
            if 'points' in det and det['points'] is not None:
                if from_roi_space:
                    # 從ROI空間轉換到原圖空間
                    transformed_det['points'] = [
                        [x + x_offset, y + y_offset] for x, y in det['points']]
                else:
                    # 從原圖空間轉換到ROI空間
                    transformed_det['points'] = [
                        [x - x_offset, y - y_offset] for x, y in det['points']]
                
            transformed_detections.append(transformed_det)
            
        direction = "ROI->Original" if from_roi_space else "Original->ROI"
        self.logger.debug(f"🔧 座標轉換 ({direction}): 偏移量=({x_offset}, {y_offset}), 轉換了{len(transformed_detections)}個檢測")
        return transformed_detections

    def _draw_detections_on_image(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """在圖像上繪製檢測結果"""
        for det in detections:
            bbox = det['bbox']
            confidence = det['confidence']
            class_id = det['class_id']
            class_name = det.get('class_name', f'class_{class_id}')
            mask = det.get('mask')

            # 獲取類別顏色
            color = self.config_manager.get_class_color(class_id)

            # 繪製邊界框
            x1, y1, x2, y2 = map(int, bbox)
            cv2.rectangle(image, (x1, y1), (x2, y2), color, 2)

            # 繪製mask（如果有）
            if mask is not None:
                mask_overlay = np.zeros_like(image)
                if mask.shape != image.shape[:2]:
                    mask = cv2.resize(mask, (image.shape[1], image.shape[0]))
                mask_overlay[mask > 0] = color
                image = cv2.addWeighted(image, 0.7, mask_overlay, 0.3, 0)

            # 繪製標籤
            label = f"{class_name}: {confidence:.2f}"
            font_scale = self.font_manager.get_cv2_font_scale(0.6)
            thickness = self.font_manager.get_cv2_font_thickness()

            # 計算文字大小
            (text_w, text_h), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX,
                                                  font_scale, thickness)

            # 繪製標籤背景
            cv2.rectangle(image, (x1, y1 - text_h - 5),
                          (x1 + text_w, y1), color, -1)

            # 繪製文字
            cv2.putText(image, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX,
                        font_scale, (0, 0, 0), thickness)

        return image

    def _draw_gt_data_on_image(self, image: np.ndarray, gt_data: List[Dict]) -> np.ndarray:
        """在圖像上繪製GT數據"""
        for gt in gt_data:
            if 'points' in gt and 'label' in gt:
                points = np.array(gt['points'], dtype=np.int32)
                label = gt['label']
                shape_type = gt.get('shape_type', 'polygon')

                # 獲取類別顏色
                color = self._get_gt_color_by_label(label)

                # 繪製形狀
                if shape_type == "polygon" and len(points) > 2:
                    cv2.polylines(image, [points], True, color, 3)
                elif shape_type == "rectangle" and len(points) == 2:
                    pt1, pt2 = tuple(points[0]), tuple(points[1])
                    cv2.rectangle(image, pt1, pt2, color, 3)
                else:
                    cv2.polylines(image, [points], False, color, 3)

                # 繪製標籤
                label_text = f"GT: {label}"
                label_pos = (int(points[0][0]), int(points[0][1]) - 10)

                font_scale = self.font_manager.get_cv2_font_scale(0.6)
                thickness = self.font_manager.get_cv2_font_thickness()

                # 計算文字大小
                (text_w, text_h), _ = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX,
                                                      font_scale, thickness)

                # 繪製標籤背景
                bg_x1, bg_y1 = label_pos
                bg_x2, bg_y2 = bg_x1 + text_w, bg_y1 + text_h + 5
                cv2.rectangle(image, (bg_x1, bg_y1 - text_h - 5),
                              (bg_x2, bg_y2), color, -1)

                # 繪製文字
                cv2.putText(image, label_text, label_pos, cv2.FONT_HERSHEY_SIMPLEX,
                            font_scale, (0, 0, 0), thickness)

        return image

    def _get_gt_color_by_label(self, label: str) -> Tuple[int, int, int]:
        """根據標籤獲取GT顏色，與預測結果保持一致"""
        # 清理標籤名稱（移除尾部下劃線等）
        clean_label = label.rstrip('_')

        # 先嘗試在別名映射中查找
        if hasattr(self.config_manager, 'label_aliases'):
            for alias, actual_name in self.config_manager.label_aliases.items():
                if alias == label or alias == clean_label:
                    clean_label = actual_name
                    break

        # 查找類別ID
                class_id = None
        for cid, class_config in self.config_manager.classes.items():
            if class_config.name == clean_label or class_config.display_name == clean_label:
                class_id = cid
                break

        # 如果找到類別ID，返回對應顏色
        if class_id is not None and class_id in self.config_manager.classes:
            color = self.config_manager.classes[class_id].color
            return tuple(color[:3])  # 確保只返回RGB三個值

        # 如果沒找到，返回默認黃色（GT標準顏色）
        return (0, 255, 255)  # 黃色 (BGR格式)


class IntelligentFilter:
    """智能過濾器 - 實現Step1和Step2過濾邏輯"""

    def __init__(self, config_manager: UnifiedYOLOConfigManager):
        """
        初始化智能過濾器

        Args:
            config_manager: 配置管理器
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)

    def apply_filtering(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        應用智能過濾

        Args:
            detections: 檢測結果列表

        Returns:
            過濾後的檢測結果
        """
        if not self.config.filtering.enable_intelligent_filtering:
            return detections

        filtered_detections = list(detections)

        # Step 1: linear_crack vs Alligator_crack
        filtered_detections = self._apply_step1_filtering(filtered_detections)

        # Step 2: linear_crack vs joint
        filtered_detections = self._apply_step2_filtering(filtered_detections)

        # 檢測合併
        if self.config.filtering.enable_detection_merge:
            filtered_detections = self._apply_detection_merge(
                filtered_detections)

        return filtered_detections

    def _apply_step1_filtering(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Step 1: linear_crack vs Alligator_crack 過濾

        當linear_crack和Alligator_crack重疊時：
        - 計算linear_crack的長寬比和兩者的面積比
        - 如果長寬比<0.8且面積比<0.4，保留linear_crack，移除Alligator_crack
        - 否則保留Alligator_crack，移除linear_crack
        """
        to_remove = set()

        # 找到linear_crack和Alligator_crack
        linear_cracks = [(i, det) for i, det in enumerate(detections)
                         if det.get('class_name') == 'linear_crack' and i not in to_remove]
        alligator_cracks = [(i, det) for i, det in enumerate(detections)
                            if det.get('class_name') == 'Alligator_crack' and i not in to_remove]

        for linear_idx, linear_det in linear_cracks:
            if linear_idx in to_remove:
                continue

            for alligator_idx, alligator_det in alligator_cracks:
                if alligator_idx in to_remove:
                    continue

                # 計算IoU
                iou = self._calculate_bbox_iou(linear_det, alligator_det)

                # 先計算長寬比（linear_crack的）
                linear_bbox = linear_det.get('bbox', [0, 0, 0, 0])
                if len(linear_bbox) != 4:
                    continue  # 跳過異常資料
                width = linear_bbox[2] - linear_bbox[0]
                height = linear_bbox[3] - linear_bbox[1]
                if max(width, height) > 0:
                    aspect_ratio = min(width, height) / max(width, height)
                else:
                    aspect_ratio = 0

                # 計算面積比
                linear_area = width * height
                alligator_bbox = alligator_det.get('bbox', [0, 0, 0, 0])
                alligator_width = alligator_bbox[2] - alligator_bbox[0]
                alligator_height = alligator_bbox[3] - alligator_bbox[1]
                alligator_area = alligator_width * alligator_height

                area_ratio = min(linear_area, alligator_area) / max(linear_area,
                                                                    alligator_area) if max(linear_area, alligator_area) > 0 else 0

                if iou > self.config.filtering.step1_iou_threshold:
                    # 判斷保留哪個
                    if (aspect_ratio < self.config.filtering.linear_aspect_ratio_threshold and
                            area_ratio < self.config.filtering.area_ratio_threshold):
                        # 保留linear_crack，移除Alligator_crack
                        to_remove.add(alligator_idx)
                        self.logger.debug(
                            f"Step1: 保留linear_crack(長寬比:{aspect_ratio:.3f}, 面積比:{area_ratio:.3f})")
                    else:
                        # 保留Alligator_crack，移除linear_crack
                        to_remove.add(linear_idx)
                        self.logger.debug(
                            f"Step1: 保留Alligator_crack(長寬比:{aspect_ratio:.3f}, 面積比:{area_ratio:.3f})")
                        break

        # 移除標記的檢測
        return [det for i, det in enumerate(detections) if i not in to_remove]

    def _apply_step2_filtering(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Step 2: linear_crack vs joint 過濾

        當linear_crack和joint的IoU大於閾值時，移除linear_crack
        """
        to_remove = set()

        # 找到linear_crack和joint
        linear_cracks = [(i, det) for i, det in enumerate(detections)
                         if det.get('class_name') == 'linear_crack' and i not in to_remove]
        joints = [(i, det) for i, det in enumerate(detections)
                  if det.get('class_name') == 'joint' and i not in to_remove]

        for linear_idx, linear_det in linear_cracks:
            if linear_idx in to_remove:
                continue

            for joint_idx, joint_det in joints:
                if joint_idx in to_remove:
                    continue

                # 計算IoU
                iou = self._calculate_bbox_iou(linear_det, joint_det)

                if iou > self.config.filtering.step2_iou_threshold:
                    # 移除linear_crack
                    to_remove.add(linear_idx)
                    self.logger.debug(
                        f"Step2: 移除linear_crack(IoU:{iou:.3f} > {self.config.filtering.step2_iou_threshold})")
                    break

        # 移除標記的檢測
        return [det for i, det in enumerate(detections) if i not in to_remove]

    def _apply_detection_merge(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """應用檢測合併"""
        if not self.config.filtering.merge_classes:
            return detections

        # 實現合併邏輯（簡化版本）
        # 這裡可以根據需要實現更複雜的合併邏輯
        return detections

    def _calculate_bbox_iou(self, det1: Dict[str, Any], det2: Dict[str, Any]) -> float:
        """計算兩個邊界框的IoU"""
        bbox1 = det1.get('bbox', [0, 0, 0, 0])
        bbox2 = det2.get('bbox', [0, 0, 0, 0])

        # 計算交集
        x1 = max(bbox1[0], bbox2[0])
        y1 = max(bbox1[1], bbox2[1])
        x2 = min(bbox1[2], bbox2[2])
        y2 = min(bbox1[3], bbox2[3])

        if x2 <= x1 or y2 <= y1:
            return 0.0

        intersection = (x2 - x1) * (y2 - y1)

        # 計算並集
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0


class CSVManager:
    """CSV報告管理器"""

    def __init__(self, config_manager, stats, three_view_generator):
        """初始化CSV管理器"""
        self.config_manager = config_manager
        self.stats = stats  # Store the stats object
        # Store ThreeViewGenerator instance
        self.three_view_generator = three_view_generator
        self.logger = logging.getLogger(__name__)
        self.reports_dir = None  # Initialize reports_dir here
        
        # 🔧 修復：添加圖像追蹤以防止重複計算TP值
        self.processed_images = set()  # 追蹤已處理的圖像
        self.image_level_metrics = defaultdict(lambda: defaultdict(lambda: {'TP': 0, 'FP': 0, 'FN': 0}))  # 圖像級別指標
        
        # 🔧 修復：添加所有必需的字段到class_metrics_data初始化
        self.class_metrics_data = defaultdict(
            lambda: {
                'TP': 0, 'FP': 0, 'FN': 0, 'total': 0,
                'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 
                '誤判率': 0.0, '漏判率': 0.0
            }
        )

    def update_incremental_reports(self, result: Dict, output_path: str):
        """更新增量CSV報告"""
        if not self.config_manager.visualization.save_statistics:
            self.logger.debug("CSV報告功能未啟用 (save_statistics=False)")
            return

        try:
            # Ensure reports_dir is set
            if self.reports_dir is None:
                output_path_obj = Path(output_path)
                output_path_obj.mkdir(parents=True, exist_ok=True)
                self.reports_dir = output_path_obj / self.config_manager.output.reports_subdir
                self.reports_dir.mkdir(exist_ok=True)

            self._update_image_metrics_csv(result, self.reports_dir)
            # 🔧 修復：傳遞圖像名稱以支援重複檢測防護
            image_name = Path(result['image_path']).name
            # Update in-memory class metrics with image tracking  
            self._update_class_metrics_data(result, image_name)
            # Write updated class metrics to CSV
            self._write_class_metrics_to_csv(self.reports_dir)
        except Exception as e:
            self.logger.error(f"更新CSV報告失敗: {e}")

    def _update_image_metrics_csv(self, result: Dict, reports_dir: Path):
        """保存單張圖像的CSV數據 - 增量更新模式"""
        import csv
        from datetime import datetime

        image_path = result['image_path']
        image_name = Path(image_path).stem
        detections = result['detections']

        # 確定CSV文件路徑
        image_csv_path = reports_dir / "image_metrics_incremental.csv"

        # 檢查CSV文件是否存在，不存在則創建表頭（擴展版本）
        file_exists = image_csv_path.exists()
        if not file_exists:
            with open(image_csv_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    '圖像名稱', '圖像路徑', '類別', '類別ID', '信心度',
                    'x1', 'y1', 'x2', 'y2', '邊界框寬度', '邊界框高度', '邊界框面積',
                    '是否有Mask', 'Mask面積', '圖像寬度', '圖像高度', '圖像面積',
                    'TP', 'FP', 'FN', '推理時間(秒)', '模型路徑', 'SAHI啟用',
                    '信心度閾值', 'IoU閾值', '處理時間', '備註'
                ])

        # 獲取當前時間戳
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 增量寫入圖像級別數據
        with open(image_csv_path, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # 獲取圖像尺寸和其他元數據
            image_meta = self._get_image_metadata(result, image_name)

            if not detections:
                # 無檢測結果 - 使用擴展格式
                writer.writerow([
                    image_name, image_path, '無檢測結果', -1, 0.0,
                    0, 0, 0, 0, 0, 0, 0,  # 邊界框信息
                    # 圖像信息
                    False, 0, image_meta['width'], image_meta['height'], image_meta['area'],
                    # 指標和模型信息
                    0, 0, 0, image_meta['inference_time'], image_meta['model_path'], image_meta['sahi_enabled'],
                    # 配置和備註
                    image_meta['conf_threshold'], image_meta['iou_threshold'], current_time, '無檢測結果'
                ])
            else:
                for det in detections:
                    bbox = det['bbox']
                    print(bbox)
                    x1, y1, x2, y2 = bbox
                    width = x2 - x1
                    height = y2 - y1
                    bbox_area = width * height

                    class_id = det['class_id']
                    # 🆕 使用config_manager獲取類別名稱
                    class_name = self.config_manager.get_class_name(class_id)
                    confidence = det['confidence']
                    has_mask = det.get('mask') is not None

                    # 計算mask面積
                    mask_area = 0
                    if has_mask and det['mask'] is not None:
                        try:
                            mask = det['mask']
                            if mask.ndim == 2:
                                mask_area = np.sum(mask > 0.5)
                            else:
                                # 處理多維度mask
                                if mask.ndim == 3:
                                    mask = mask[0] if mask.shape[0] == 1 else mask.max(
                                        axis=0)
                                mask_area = np.sum(mask > 0.5)
                        except Exception as e:
                            self.logger.debug(f"計算mask面積時出錯: {e}")
                            mask_area = 0

                    # 🔧 修復：使用真正的GT數據計算TP/FP/FN（每個檢測單獨計算）
                    tp, fp, fn = self._calculate_detection_metrics(det, result)

                    # 生成備註信息
                    notes = []
                    if confidence > 0.8:
                        notes.append('高信心度')
                    if has_mask:
                        notes.append('含有mask')
                    if bbox_area > 10000:
                        notes.append('大目標')
                    note_text = '; '.join(notes) if notes else '正常檢測'

                    # 寫入擴展的CSV記錄
                    writer.writerow([
                        image_name, image_path, class_name, class_id, round(
                            confidence, 4),
                        round(x1, 1), round(y1, 1), round(x2, 1), round(y2, 1),
                        round(width, 1), round(height, 1), round(bbox_area, 1),
                        has_mask, round(mask_area, 1),
                        image_meta['width'], image_meta['height'], image_meta['area'],
                        tp, fp, fn, round(image_meta['inference_time'], 4),
                        image_meta['model_path'], image_meta['sahi_enabled'],
                        image_meta['conf_threshold'], image_meta['iou_threshold'],
                        current_time, note_text
                    ])

    def _get_image_metadata(self, result: Dict[str, Any], image_name: str) -> Dict[str, Any]:
        """
        獲取圖像元數據和推理參數

        Args:
            result: 推理結果
            image_name: 圖像名稱

        Returns:
            元數據字典
        """
        try:
            # 獲取圖像尺寸
            image_shape = result.get('image_shape')
            if image_shape is not None:
                height, width = image_shape[:2]
                area = width * height
            else:
                # 嘗試從圖像路徑獲取尺寸
                try:
                    import cv2
                    img = cv2.imread(result['image_path'])
                    if img is not None:
                        height, width = img.shape[:2]
                        area = width * height
                    else:
                        width, height, area = 0, 0, 0
                except:
                    width, height, area = 0, 0, 0

            # 獲取推理時間（從統計中獲取最新的）
            inference_time = self.stats['processing_times'][-1] if self.stats['processing_times'] else 0.0

            # 獲取模型路徑
            model_path = (self.config_manager.model.segmentation_model_path or
                          self.config_manager.model.detection_model_path or
                          "unknown")

            # 獲取配置參數
            sahi_enabled = self.config_manager.sahi.enable_sahi
            conf_threshold = self.config_manager.inference.global_conf
            iou_threshold = self.config_manager.inference.iou_threshold

            return {
                'width': int(width),
                'height': int(height),
                'area': int(area),
                'inference_time': float(inference_time),
                'model_path': str(model_path),
                'sahi_enabled': bool(sahi_enabled),
                'conf_threshold': float(conf_threshold),
                'iou_threshold': float(iou_threshold)
            }

        except Exception as e:
            self.logger.debug(f"獲取圖像元數據時出錯: {e}")
            return {
                'width': 0, 'height': 0, 'area': 0,
                'inference_time': 0.0, 'model_path': 'unknown',
                'sahi_enabled': False, 'conf_threshold': 0.05, 'iou_threshold': 0.45
            }

    def _calculate_detection_metrics(self, detection: Dict, result: Dict) -> Tuple[int, int, int]:
        """
        計算單個檢測的TP/FP/FN指標

        Args:
            detection: 單個檢測結果
            result: 完整推理結果（包含image_path等）

        Returns:
            Tuple[int, int, int]: (TP, FP, FN)
        """
        try:
            # 1. 嘗試加載GT數據
            gt_data = None
            if (self.config_manager.ground_truth.enable_gt_comparison and
                    self.config_manager.ground_truth.gt_path):
                try:
                    import cv2
                    original_img = cv2.imread(result['image_path'])
                    if original_img is not None:
                        _, _, gt_data = self.three_view_generator._load_gt_visualization_with_data(
                            original_img, result['image_path'], self.config_manager.ground_truth.gt_path
                        )
                except Exception as e:
                    self.logger.debug(f"加載GT數據失敗: {e}")
                    gt_data = None

            # 2. 如果沒有GT數據，使用confidence-based估算
            if not gt_data:
                # 無GT時的基本統計：高confidence視為TP，低confidence視為可能的FP
                confidence = detection['confidence']
                if confidence > 0.7:
                    return (1, 0, 0)  # 高confidence -> TP
                elif confidence > 0.3:
                    return (0, 1, 0)  # 中等confidence -> FP
                else:
                    return (0, 1, 0)  # 低confidence -> FP

            # 3. 有GT數據時進行精確匹配
            det_class_name_raw = detection['class_name']
            # 🔧 修復：確保檢測結果類別名稱經過label_aliases處理
            det_class_name = self.config_manager.resolve_label_alias(
                det_class_name_raw)
            det_bbox = detection['bbox']

            # 尋找同類別的GT目標進行匹配 (GT已在加載時處理過label_aliases)
            class_gt_data = [
                gt for gt in gt_data if gt['class_name'] == det_class_name]

            if not class_gt_data:
                # 檢測到的類別在GT中不存在 -> FP
                return (0, 1, 0)

            # 計算與所有同類別GT的IoU，找最佳匹配
            best_iou = 0.0
            for gt in class_gt_data:
                gt_bbox = gt['bbox']
                iou = self._calculate_bbox_iou(det_bbox, gt_bbox)
                best_iou = max(best_iou, iou)

            # 根據IoU閾值判斷
            iou_threshold = 0.5  # 標準IoU閾值
            if best_iou >= iou_threshold:
                return (1, 0, 0)  # TP: 檢測正確
            else:
                return (0, 1, 0)  # FP: 檢測錯誤（位置不準確）

        except Exception as e:
            self.logger.debug(f"計算檢測指標時出錯: {e}")
            # 發生錯誤時的保守估算
            confidence = detection.get('confidence', 0.0)
            if confidence > 0.5:
                return (1, 0, 0)
            else:
                return (0, 1, 0)

    def _calculate_bbox_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """
        計算兩個邊界框的IoU

        Args:
            bbox1, bbox2: [x1, y1, x2, y2] 格式的邊界框

        Returns:
            float: IoU值 (0-1)
        """
        try:
            x1_1, y1_1, x2_1, y2_1 = bbox1
            x1_2, y1_2, x2_2, y2_2 = bbox2

            # 計算交集區域
            x1_inter = max(x1_1, x1_2)
            y1_inter = max(y1_1, y1_2)
            x2_inter = min(x2_1, x2_2)
            y2_inter = min(y2_1, y2_2)

            if x2_inter <= x1_inter or y2_inter <= y1_inter:
                return 0.0

            inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)

            # 計算聯集區域
            area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
            area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
            union_area = area1 + area2 - inter_area

            if union_area <= 0:
                return 0.0

            return inter_area / union_area

        except Exception as e:
            self.logger.debug(f"計算IoU時出錯: {e}")
            return 0.0

    def _update_class_metrics_data(self, result: Dict, image_name: str):
        """
        更新內存中的類別指標數據 (TP/FP/FN)
        Args:
            result: 單張圖像的推理結果，包含 detections 和 image_path
            image_name: 圖像名稱，用於重複檢測防護
        """
        # 🔧 修復：防止重複計算同一圖像的指標
        if image_name in self.processed_images:
            self.logger.debug(f"圖像 {image_name} 已處理過，跳過重複計算")
            return
        image_path = result['image_path']
        detections = result['detections']

        # 嘗試加載GT數據
        gt_data = None
        if self.config_manager.ground_truth.enable_gt_comparison and self.config_manager.ground_truth.gt_path:
            try:
                import cv2
                original_img = cv2.imread(image_path)
                if original_img is None:
                    self.logger.warning(f"無法讀取圖像 {image_path} 以加載GT數據。")
                    return

                _, _, gt_data = self.three_view_generator._load_gt_visualization_with_data(
                    original_img, image_path, self.config_manager.ground_truth.gt_path
                )
            except Exception as e:
                self.logger.warning(f"加載GT數據用於類別指標更新失敗: {e}")
                gt_data = None

        # 獲取所有類別名稱
        all_class_names = set()
        for det in detections:
            all_class_names.add(det['class_name'])
        if gt_data:
            for gt in gt_data:
                all_class_names.add(gt['class_name'])

        # 🔧 修復：先將指標保存到圖像級別，再應用FP優先規則
        for class_name in all_class_names:
            # 過濾出當前類別的檢測和GT
            class_detections = [
                d for d in detections if d['class_name'] == class_name]
            class_gt_data = [g for g in (
                gt_data if gt_data else []) if g['class_name'] == class_name]

            # 計算指標
            if gt_data:
                tp, fp, fn, _, _, _ = self.three_view_generator._calculate_tp_fp_fn(
                    class_detections, class_gt_data
                )
            else:
                tp, fp, fn = self._estimate_metrics_without_gt(class_detections)
                self.logger.debug(
                    f"無GT數據，使用confidence-based估算: {class_name} - TP:{tp}, FP:{fp}, FN:{fn}")

            # 🔧 修復：移除錯誤的FP優先規則 - FP和FN可以同時存在
            # FP: 模型檢測到但GT中沒有的目標
            # FN: GT中存在但模型沒檢測到的目標
            # 這兩者在邏輯上完全可以同時存在，不應該互斥
            self.logger.debug(f"{class_name} 指標: TP={tp}, FP={fp}, FN={fn}")

            # 保存到圖像級別指標
            self.image_level_metrics[image_name][class_name]['TP'] = tp
            self.image_level_metrics[image_name][class_name]['FP'] = fp
            self.image_level_metrics[image_name][class_name]['FN'] = fn
            
            # 累加到類別總指標 (每個圖像只累加一次)
            self.class_metrics_data[class_name]['TP'] += tp
            self.class_metrics_data[class_name]['FP'] += fp
            self.class_metrics_data[class_name]['FN'] += fn
            self.class_metrics_data[class_name]['total'] += len(class_detections)

        # 🔧 修復：標記圖像為已處理
        self.processed_images.add(image_name)
        self.logger.debug(f"已更新 {image_path} 的類別指標數據，現已處理 {len(self.processed_images)} 張圖像")

    def _estimate_metrics_without_gt(self, detections: List[Dict]) -> Tuple[int, int, int]:
        """
        🆕 無GT數據時基於confidence的指標估算

        Args:
            detections: 檢測結果列表

        Returns:
            Tuple[int, int, int]: (估算的TP, FP, FN)
        """
        if not detections:
            return (0, 0, 0)

        tp = 0
        fp = 0
        fn = 0

        # 基於confidence閾值的分層估算策略
        for det in detections:
            confidence = det.get('confidence', 0.0)

            if confidence >= 0.8:
                # 高confidence -> 很可能是TP
                tp += 1
            elif confidence >= 0.5:
                # 中等confidence -> 可能是TP，但也可能是FP
                # 使用保守估算：60%TP, 40%FP
                if confidence >= 0.65:
                    tp += 1
                else:
                    fp += 1
            else:
                # 低confidence -> 很可能是FP
                fp += 1

        # 🔧 修復：更合理的FN估算邏輯
        # 基於檢測密度和confidence分佈估算可能遺漏的目標
        total_detections = len(detections)
        high_conf_detections = sum(1 for det in detections if det.get('confidence', 0.0) >= 0.7)
        medium_conf_detections = sum(1 for det in detections if 0.3 <= det.get('confidence', 0.0) < 0.7)
        
        # 估算邏輯：
        # - 如果有很多中低置信度檢測，可能遺漏了一些目標
        # - 如果檢測數量較少，可能存在遺漏
        if total_detections == 0:
            fn = 0  # 無檢測則無法估算遺漏
        elif medium_conf_detections > high_conf_detections:
            # 中等置信度檢測多於高置信度，可能存在遺漏
            fn = max(1, total_detections // 8)  # 約12.5%的遺漏率
        elif total_detections < 3:
            # 檢測數量較少，可能有遺漏
            fn = 1
        else:
            # 正常情況，保守估算
            fn = max(0, high_conf_detections // 15)  # 約6.7%的遺漏率

        return (tp, fp, fn)

    def _calculate_final_metrics(self):
        """🆕 計算最終指標：Precision、Recall、F1等"""
        for class_name, data in self.class_metrics_data.items():
            tp, fp, fn = data['TP'], data['FP'], data['FN']
            
            # 計算指標，加入除零保護
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
            
            # 計算誤判率和漏判率
            false_positive_rate = fp / (tp + fp) if (tp + fp) > 0 else 0.0
            false_negative_rate = fn / (tp + fn) if (tp + fn) > 0 else 0.0
            
            # 更新數據
            data.update({
                'Precision': round(precision, 4),
                'Recall': round(recall, 4),
                'F1': round(f1, 4),
                '誤判率': round(false_positive_rate, 4),
                '漏判率': round(false_negative_rate, 4)
            })
            
            self.logger.debug(
                f"已計算 {class_name} 的最終指標: P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}"
            )

    def _write_class_metrics_to_csv(self, reports_dir: Path):
        """將累計的類別統計數據寫入CSV - 先計算指標再寫入"""
        import csv
        from datetime import datetime
        import traceback

        csv_path = reports_dir / "class_metrics_incremental.csv"

        # 🔧 修復：在寫入前計算所有必需的指標
        self._calculate_final_metrics()
        
        # 直接使用內存中的 self.class_metrics_data 作為要寫入的數據
        data_to_write = self.class_metrics_data

        # 🔧 修復：創建備份再寫入，確保數據安全
        try:
            # 創建備份（如果原文件存在）
            if csv_path.exists():
                backup_path = csv_path.with_suffix('.backup.csv')
                import shutil
                shutil.copy2(csv_path, backup_path)
                self.logger.debug(f"已創建備份: {backup_path}")

            # Write all data back to CSV
            with open(csv_path, 'w', newline='', encoding='utf-8') as f:
                self.logger.debug(f"File opened for writing: {csv_path}")
                fieldnames = [
                    '類別名稱', 'TP', 'FP', 'FN', '類別總數',
                    'Precision', 'Recall', 'F1', '誤判率', '漏判率', '更新時間'
                ]
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                # 遍歷排序後的類別名稱以保證順序一致
                for class_name in sorted(data_to_write.keys()):
                    data = data_to_write[class_name]
                    self.logger.debug(
                        f"  Writing row for {class_name}: TP={data['TP']}, FP={data['FP']}, FN={data['FN']}")
                    writer.writerow({
                        '類別名稱': class_name,
                        'TP': data['TP'],
                        'FP': data['FP'],
                        'FN': data['FN'],
                        '類別總數': data['total'],
                        'Precision': data['Precision'],
                        'Recall': data['Recall'],
                        'F1': data['F1'],
                        '誤判率': data['誤判率'],
                        '漏判率': data['漏判率'],
                        '更新時間': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    })
                self.logger.debug(f"Finished writing to: {csv_path}")
        except Exception as e:
            self.logger.error(f"更新類別CSV時發生錯誤: {e}")
            self.logger.error(traceback.format_exc())
            # 嘗試從備份恢復
            if csv_path.exists():
                backup_path = csv_path.with_suffix('.backup.csv')
                if backup_path.exists():
                    try:
                        import shutil
                        shutil.copy2(backup_path, csv_path)
                        self.logger.info(f"已從備份恢復CSV文件: {csv_path}")
                    except Exception as restore_error:
                        self.logger.error(f"從備份恢復失敗: {restore_error}")

    def _try_recover_from_backup(self, backup_path: Path) -> Optional[Dict]:
        """
        🔧 嘗試從備份文件恢復數據

        Args:
            backup_path: 備份文件路徑

        Returns:
            Optional[Dict]: 恢復的數據，如果失敗則返回None
        """
        if not backup_path.exists():
            self.logger.debug(f"備份文件不存在: {backup_path}")
            return None

        try:
            import csv
            recovered_data = defaultdict(lambda: {
                                         'TP': 0, 'FP': 0, 'FN': 0, 'total': 0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, '誤判率': 0.0, '漏判率': 0.0})

            with open(backup_path, 'r', newline='', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    class_name = row['類別名稱']
                    recovered_data[class_name]['TP'] = int(row.get('TP', 0))
                    recovered_data[class_name]['FP'] = int(row.get('FP', 0))
                    recovered_data[class_name]['FN'] = int(row.get('FN', 0))
                    recovered_data[class_name]['total'] = int(
                        row.get('類別總數', 0))
                    recovered_data[class_name]['Precision'] = float(
                        row.get('Precision', 0.0))
                    recovered_data[class_name]['Recall'] = float(
                        row.get('Recall', 0.0))
                    recovered_data[class_name]['F1'] = float(
                        row.get('F1', 0.0))
                    # 🆕 支援新增的誤判率和漏判率字段
                    recovered_data[class_name]['誤判率'] = float(
                        row.get('誤判率', 0.0))
                    recovered_data[class_name]['漏判率'] = float(
                        row.get('漏判率', 0.0))

            self.logger.info(f"成功從備份恢復 {len(recovered_data)} 個類別的數據")
            return dict(recovered_data)

        except Exception as e:
            self.logger.error(f"備份文件恢復失敗: {e}")
            return None
    
    def reset_class_metrics(self):
        """🆕 重設類別指標數據，用於開始新的批次處理"""
        self.processed_images.clear()
        self.image_level_metrics.clear()
        self.class_metrics_data.clear()
        self.logger.info("已重設類別指標數據，可開始新的批次處理")
    
    def get_processing_stats(self) -> Dict:
        """📊 獲取處理統計信息"""
        return {
            'processed_images_count': len(self.processed_images),
            'total_classes': len(self.class_metrics_data),
            'processed_images': list(self.processed_images),
            'class_summary': {class_name: {
                'TP': data['TP'], 'FP': data['FP'], 'FN': data['FN'], 'total': data['total']
            } for class_name, data in self.class_metrics_data.items()}
        }


class UnifiedYOLOInference:
    """統一YOLO推理引擎"""

    def __init__(self, config_path: Optional[str] = None, labelme_integration: Optional[Any] = None):
        """
        初始化統一推理引擎

        Args:
            config_path: 配置文件路徑
            labelme_integration: LabelMe整合器（可選）
        """
        # 設置日誌
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # 存儲LabelMe整合器
        self.labelme_integration = labelme_integration

        # 加載配置
        self.config_manager = UnifiedYOLOConfigManager(config_path)

        # 初始化組件
        self.font_manager = FontManager(
            font_path=self.config_manager.visualization.font_path,
            font_size=self.config_manager.visualization.font_size,
            font_thickness=self.config_manager.visualization.font_thickness,
            font_scale=self.config_manager.visualization.font_scale
        )

        self.three_view_generator = ThreeViewGenerator(
            font_manager=self.font_manager,
            config_manager=self.config_manager,
            layout=self.config_manager.visualization.three_view_layout,
            spacing=self.config_manager.visualization.three_view_spacing
        )

        self.intelligent_filter = IntelligentFilter(self.config_manager)

        # 🚀 新增：初始化物件融合引擎
        self._init_fusion_engine()

        # 🏷️ LabelMe整合器
        self.labelme_integration = labelme_integration

        # 初始化模型
        self.model = None
        self._load_model()

        # 統計信息
        self.stats = {
            'total_images': 0,
            'total_detections': 0,
            'total_labelme_detections': 0,  # 🆕 添加LabelMe檢測數量統計
            'class_counts': defaultdict(int),
            'processing_times': [],
            'start_time': time.time()
        }

        # JSON標籤信息緩存
        self.json_labels_summary = None

        self.csv_manager = CSVManager(
            self.config_manager, self.stats, self.three_view_generator)

        # 🧹 記憶體管理
        self.memory_cleanup_interval = 10  # 每處理10張圖像清理一次記憶體
        self.processed_count = 0
        self.max_image_size = 4096  # 最大圖像尺寸（像素）
        self.enable_image_resize = True  # 啟用大圖像自動縮放

        # 🔄 中斷回復管理
        self.resume_enabled = False
        self.resume_checkpoint_interval = 10
        self.resume_skip_existing = True
        self.resume_progress_file = None
        self.resume_force_overwrite = False
        self.processed_files = set()  # 已處理的文件集合

    def _load_historical_class_metrics(self, reports_dir: Path):
        """在批次處理開始時加載一次歷史類別指標"""
        import csv
        csv_path = reports_dir / "class_metrics_incremental.csv"
        if not csv_path.exists():
            self.logger.info("未找到歷史類別指標文件，將從零開始統計。")
            return

        try:
            with open(csv_path, 'r', newline='', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    class_name = row['類別名稱']
                    self.class_metrics_data[class_name]['TP'] = int(
                        row.get('TP', 0))
                    self.class_metrics_data[class_name]['FP'] = int(
                        row.get('FP', 0))
                    self.class_metrics_data[class_name]['FN'] = int(
                        row.get('FN', 0))
                    self.class_metrics_data[class_name]['total'] = int(
                        row.get('類別總數', 0))
            self.logger.info(f"✅ 成功加載歷史類別指標: {csv_path}")
        except Exception as e:
            self.logger.error(f"加載歷史類別指標失敗: {e}")

    def _init_fusion_engine(self):
        """初始化物件融合引擎"""
        fusion_cfg = self.config_manager.fusion
        fusion_config = FusionConfig(
            strategy=FusionStrategy(fusion_cfg.strategy),
            iou_threshold=fusion_cfg.iou_threshold,
            confidence_threshold=fusion_cfg.confidence_threshold
        )
        self.fusion_engine = AdvancedFusionEngine(fusion_config)
        self.logger.info(f"🚀 物件融合引擎已初始化，策略: {fusion_cfg.strategy}")

    def _load_model(self) -> bool:
        """加載YOLO模型"""
        if not ULTRALYTICS_AVAILABLE:
            self.logger.error("Ultralytics不可用")
            return False

        model_path = (self.config_manager.model.segmentation_model_path or
                      self.config_manager.model.detection_model_path)

        if not model_path or not Path(model_path).exists():
            self.logger.error(f"模型路徑不存在: {model_path}")
            return False

        try:
            self.model = YOLO(model_path)
            self.model.to(self.config_manager.model.device)

            if self.config_manager.model.half_precision:
                self.model.half()

            self.logger.info(f"模型加載成功: {model_path}")
            return True

        except Exception as e:
            self.logger.error(f"模型加載失敗: {e}")
            return False

    def cleanup_memory(self, force: bool = False):
        """
        記憶體清理方法 - 加強版

        Args:
            force: 強制清理，忽略清理間隔
        """
        try:
            import gc

            # 檢查是否需要清理
            if not force and self.processed_count % self.memory_cleanup_interval != 0:
                return

            # 🧹 Step 1: 清理Python垃圾回收（多次執行以確保徹底清理）
            collected = 0
            for _ in range(3):  # 執行3次gc.collect()以確保徹底清理
                collected += gc.collect()

            # 🧹 Step 2: 清理matplotlib圖形緩存
            try:
                import matplotlib.pyplot as plt
                plt.close('all')  # 關閉所有matplotlib圖形
                # 清理matplotlib的字體緩存
                plt.rcParams.clear()
                # 重新設置中文字體
                plt.rcParams['font.sans-serif'] = ['Microsoft YaHei',
                                                   'Times New Roman']
                plt.rcParams['axes.unicode_minus'] = False
            except:
                pass

            # 🧹 Step 3: 清理GPU記憶體（如果使用CUDA）
            gpu_memory = 0
            max_memory = 0
            if ULTRALYTICS_AVAILABLE and hasattr(self, 'model') and self.model is not None:
                try:
                    import torch
                    if torch.cuda.is_available() and self.config_manager.model.device.startswith('cuda'):
                        # 多次清理GPU緩存
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()  # 確保所有CUDA操作完成
                        torch.cuda.empty_cache()  # 再次清理

                        # 獲取GPU記憶體使用情況
                        gpu_memory = torch.cuda.memory_allocated() / 1024**3  # GB
                        max_memory = torch.cuda.max_memory_allocated() / 1024**3  # GB

                        # 重置記憶體統計
                        if force:
                            torch.cuda.reset_peak_memory_stats()

                except ImportError:
                    pass

            # 🧹 Step 4: 清理OpenCV記憶體緩存
            try:
                import cv2
                # OpenCV沒有直接的記憶體清理方法，但可以確保沒有保留的圖像引用
                pass
            except:
                pass

            # 🧹 Step 5: 清理大型暫存變量和緩存
            temp_vars_cleared = 0
            temp_vars = ['_temp_images', '_temp_detections', '_temp_results', '_temp_masks',
                         '_cached_images', '_visualization_cache', '_temp_scaled_images']

            for var_name in temp_vars:
                if hasattr(self, var_name):
                    delattr(self, var_name)
                    temp_vars_cleared += 1

            # 🧹 Step 6: 清理統計列表中的舊數據（保留最近的數據）
            if force or len(self.stats['processing_times']) > 1000:
                # 只保留最近的100個處理時間記錄
                self.stats['processing_times'] = self.stats['processing_times'][-100:]

            # 🧹 記錄清理結果
            if force or self.processed_count % (self.memory_cleanup_interval * 2) == 0:
                self.logger.info(f"🧹 記憶體清理完成:")
                self.logger.info(f"   🗑️ GC回收: {collected} 個對象")
                self.logger.info(f"   📦 臨時變量清理: {temp_vars_cleared} 個")
                if gpu_memory > 0:
                    self.logger.info(
                        f"   🖥️ GPU記憶體: {gpu_memory:.2f}GB/{max_memory:.2f}GB")
                self.logger.info(f"   📊 已處理圖像: {self.processed_count}")

        except Exception as e:
            self.logger.warning(f"記憶體清理失敗: {e}")

    def _lightweight_cleanup(self):
        """
        輕量級記憶體清理 - 每張圖像處理後執行
        只執行基本的清理操作，避免影響性能
        """
        try:
            import gc

            # 只執行一次快速垃圾回收
            gc.collect()

            # 清理matplotlib緩存（如果太多的話）
            try:
                import matplotlib.pyplot as plt
                # 只在有很多圖形時才關閉
                if len(plt.get_fignums()) > 5:
                    plt.close('all')
            except:
                pass

            # 輕量級GPU緩存清理
            if ULTRALYTICS_AVAILABLE:
                try:
                    import torch
                    if torch.cuda.is_available() and self.config_manager.model.device.startswith('cuda'):
                        # 只在記憶體使用過多時才清理
                        if torch.cuda.memory_allocated() > 2 * 1024**3:  # 超過2GB才清理
                            torch.cuda.empty_cache()
                except:
                    pass

        except Exception as e:
            # 輕量級清理失敗不記錄錯誤，避免日誌污染
            pass

    def _release_image_memory(self, *images):
        """
        🖼️ 主動釋放圖像記憶體
        
        Args:
            *images: 需要釋放的圖像對象（可以是多個）
        """
        try:
            import gc
            
            released_count = 0
            total_size = 0
            
            for img in images:
                if img is not None:
                    # 計算圖像大小（估算）
                    if hasattr(img, 'shape') and hasattr(img, 'dtype'):
                        size_bytes = img.nbytes if hasattr(img, 'nbytes') else 0
                        total_size += size_bytes
                    
                    # 主動刪除圖像引用
                    try:
                        if hasattr(img, '__del__'):
                            img.__del__()
                    except:
                        pass
                    
                    released_count += 1
            
            # 執行垃圾回收以立即釋放記憶體
            if released_count > 0:
                gc.collect()
                
                # 記錄釋放情況（僅在調試模式下）
                if total_size > 50 * 1024 * 1024:  # 超過50MB才記錄
                    size_mb = total_size / (1024 * 1024)
                    self.logger.debug(f"🖼️ 圖像記憶體釋放: {released_count} 個圖像, ~{size_mb:.1f}MB")
                    
        except Exception as e:
            self.logger.debug(f"圖像記憶體釋放失敗: {e}")

    def _release_detection_memory(self, detections):
        """
        🎯 釋放檢測結果中的大型數據（如mask）
        
        Args:
            detections: 檢測結果列表
        """
        try:
            import gc
            
            mask_count = 0
            for det in detections:
                if isinstance(det, dict) and 'mask' in det:
                    mask = det.get('mask')
                    if mask is not None:
                        # 釋放mask記憶體
                        try:
                            if hasattr(mask, '__del__'):
                                mask.__del__()
                            det['mask'] = None  # 明確設為None
                            mask_count += 1
                        except:
                            pass
            
            # 執行垃圾回收
            if mask_count > 0:
                gc.collect()
                self.logger.debug(f"🎯 檢測mask記憶體釋放: {mask_count} 個mask")
                
        except Exception as e:
            self.logger.debug(f"檢測記憶體釋放失敗: {e}")

    def _batch_final_cleanup(self, results):
        """
        🧹 批次處理最終記憶體清理
        
        Args:
            results: 批次處理結果列表
        """
        try:
            import gc
            
            total_mask_count = 0
            total_results = len(results)
            
            self.logger.info(f"🧹 開始批次最終記憶體清理: {total_results} 個結果")
            
            # 清理所有results中的大型數據
            for i, result in enumerate(results):
                if isinstance(result, dict):
                    detections = result.get('detections', [])
                    for det in detections:
                        if isinstance(det, dict) and 'mask' in det:
                            if det.get('mask') is not None:
                                det['mask'] = None
                                total_mask_count += 1
                    
                    # 清理結果中的其他大型數據
                    large_keys = ['roi_cropped_image', 'original_image', 'resized_image']
                    for key in large_keys:
                        if key in result:
                            result[key] = None
            
            # 清理統計數據中的舊數據，只保留最近的100個
            if len(self.stats['processing_times']) > 100:
                self.stats['processing_times'] = self.stats['processing_times'][-100:]
            
            # 清理臨時緩存變量
            temp_vars = [
                '_temp_images', '_temp_detections', '_temp_results', '_temp_masks',
                '_cached_images', '_visualization_cache', '_temp_scaled_images',
                '_batch_cache', '_roi_cache', '_labelme_cache'
            ]
            
            cleared_vars = 0
            for var_name in temp_vars:
                if hasattr(self, var_name):
                    delattr(self, var_name)
                    cleared_vars += 1
            
            # 執行多輪垃圾回收
            for i in range(3):
                collected = gc.collect()
                
            # GPU記憶體清理
            gpu_cleared = False
            if ULTRALYTICS_AVAILABLE:
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                        torch.cuda.synchronize()
                        torch.cuda.reset_peak_memory_stats()
                        gpu_cleared = True
                except:
                    pass
            
            # 記錄清理結果
            self.logger.info(f"🧹 批次最終清理完成:")
            self.logger.info(f"   🎯 釋放mask: {total_mask_count} 個")
            self.logger.info(f"   📦 清理變量: {cleared_vars} 個")
            self.logger.info(f"   🖥️ GPU清理: {'是' if gpu_cleared else '否'}")
            self.logger.info(f"   🗑️ 垃圾回收: 完成3輪清理")
                
        except Exception as e:
            self.logger.warning(f"批次最終記憶體清理失敗: {e}")

    def log_memory_usage(self):
        """記錄記憶體使用情況"""
        try:
            import psutil
            import os

            # 進程記憶體使用
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024  # MB

            # GPU記憶體使用（如果可用）
            gpu_info = ""
            if ULTRALYTICS_AVAILABLE:
                try:
                    import torch
                    if torch.cuda.is_available():
                        gpu_memory = torch.cuda.memory_allocated() / 1024**3  # GB
                        gpu_info = f", GPU: {gpu_memory:.2f}GB"
                except ImportError:
                    pass

            self.logger.info(f"💾 記憶體使用: RAM: {memory_mb:.1f}MB{gpu_info}")

        except ImportError:
            # psutil 不可用時的簡化記錄
            self.logger.debug("記憶體監控需要安裝 psutil")
        except Exception as e:
            self.logger.debug(f"記憶體監控失敗: {e}")

    def setup_resume(self, enable_resume: bool = True, checkpoint_interval: int = 10,
                     skip_existing: bool = True, progress_file: str = "processing_progress.json",
                     force_overwrite: bool = False):
        """
        設置中斷回復功能

        Args:
            enable_resume: 是否啟用中斷回復
            checkpoint_interval: 檢查點保存間隔
            skip_existing: 是否跳過已處理的文件
            progress_file: 進度文件名
            force_overwrite: 是否強制覆蓋已存在的文件
        """
        self.resume_enabled = enable_resume
        self.resume_checkpoint_interval = checkpoint_interval
        self.resume_skip_existing = skip_existing
        self.resume_progress_file = progress_file
        self.resume_force_overwrite = force_overwrite

        if enable_resume:
            self.logger.info(f"🔄 中斷回復功能已啟用:")
            self.logger.info(f"   📁 進度文件: {progress_file}")
            self.logger.info(f"   🔄 檢查點間隔: 每{checkpoint_interval}張圖像")
            self.logger.info(f"   ⏭️ 跳過已處理: {'是' if skip_existing else '否'}")
            self.logger.info(f"   🔄 強制覆蓋: {'是' if force_overwrite else '否'}")

    def _load_progress(self, output_dir: str) -> Dict:
        """
        加載處理進度

        Args:
            output_dir: 輸出目錄

        Returns:
            進度數據字典
        """
        if not self.resume_enabled or not self.resume_progress_file:
            return {}

        progress_path = Path(output_dir) / self.resume_progress_file

        if not progress_path.exists():
            self.logger.info("🆕 首次運行，創建新的進度文件")
            return {}

        try:
            with open(progress_path, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)

            processed_files = set(progress_data.get('processed_files', []))
            self.processed_files = processed_files

            self.logger.info(f"🔄 加載進度文件成功:")
            self.logger.info(f"   📊 已處理文件: {len(processed_files)} 個")
            self.logger.info(
                f"   📅 上次更新: {progress_data.get('last_update', 'N/A')}")

            return progress_data

        except Exception as e:
            self.logger.warning(f"加載進度文件失敗: {e}")
            return {}

    def _save_progress(self, output_dir: str, total_files: int, current_index: int,
                       current_file: str, stats: Dict = None):
        """
        保存處理進度

        Args:
            output_dir: 輸出目錄
            total_files: 總文件數
            current_index: 當前文件索引
            current_file: 當前處理的文件名
            stats: 統計信息
        """
        if not self.resume_enabled or not self.resume_progress_file:
            return

        try:
            progress_path = Path(output_dir) / self.resume_progress_file

            progress_data = {
                'last_update': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'total_files': total_files,
                'current_index': current_index,
                'current_file': current_file,
                'processed_files': list(self.processed_files),
                'processed_count': len(self.processed_files),
                'completion_percentage': (len(self.processed_files) / total_files * 100) if total_files > 0 else 0,
                'stats': stats or {},
                'config': {
                    'checkpoint_interval': self.resume_checkpoint_interval,
                    'skip_existing': self.resume_skip_existing,
                    'force_overwrite': self.resume_force_overwrite
                }
            }

            # 原子性寫入：先寫入臨時文件，再重命名
            temp_path = progress_path.with_suffix('.tmp')
            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, indent=2, ensure_ascii=False)

            temp_path.replace(progress_path)

            # 定期日誌進度
            if current_index % self.resume_checkpoint_interval == 0:
                self.logger.info(f"💾 進度已保存: {len(self.processed_files)}/{total_files} "
                                 f"({progress_data['completion_percentage']:.1f}%)")

        except Exception as e:
            self.logger.warning(f"保存進度文件失敗: {e}")

    def _should_skip_file(self, image_file: Path, output_dir: str) -> bool:
        """
        判斷是否應該跳過文件

        Args:
            image_file: 圖像文件路徑
            output_dir: 輸出目錄

        Returns:
            是否跳過該文件
        """
        image_name = image_file.name

        # 檢查是否在已處理列表中
        if self.resume_skip_existing and image_name in self.processed_files:
            return True

        # 檢查輸出文件是否已存在
        if not self.resume_force_overwrite:
            output_path = Path(output_dir)

            # 檢查可視化圖像
            vis_path = output_path / "images" / f"{image_file.stem}_vis.jpg"
            if vis_path.exists():
                self.processed_files.add(image_name)
                return True

            # 檢查三視圖
            three_view_path = output_path / "images" / \
                f"{image_file.stem}_three_view.jpg"
            if three_view_path.exists():
                self.processed_files.add(image_name)
                return True

        return False

    def _cleanup_interrupted_files(self, output_dir: str):
        """
        清理中斷時可能產生的不完整文件

        Args:
            output_dir: 輸出目錄
        """
        try:
            output_path = Path(output_dir)

            # 清理臨時文件
            temp_files = []
            if output_path.exists():
                temp_files.extend(output_path.rglob("*.tmp"))
                temp_files.extend(output_path.rglob("*_partial.*"))
                temp_files.extend(output_path.rglob("temp_resized/*"))

            for temp_file in temp_files:
                try:
                    if temp_file.is_file():
                        temp_file.unlink()
                        self.logger.debug(f"清理臨時文件: {temp_file}")
                    elif temp_file.is_dir() and not list(temp_file.iterdir()):
                        temp_file.rmdir()
                        self.logger.debug(f"清理空目錄: {temp_file}")
                except:
                    pass

        except Exception as e:
            self.logger.debug(f"清理中斷文件失敗: {e}")

    def _preprocess_large_image(self, image_path: str) -> Tuple[str, bool, Tuple[int, int]]:
        """
        預處理大尺寸圖像，如果太大則縮放

        Args:
            image_path: 原始圖像路徑

        Returns:
            (處理後圖像路徑, 是否進行了縮放, 原始尺寸)
        """
        try:
            # 先讀取圖像尺寸（不載入完整圖像）
            import cv2

            # 使用cv2.imread讀取圖像頭信息
            temp_img = cv2.imread(image_path)
            if temp_img is None:
                return image_path, False, (0, 0)

            original_height, original_width = temp_img.shape[:2]
            original_size = (original_width, original_height)

            # 計算圖像佔用記憶體大小 (MB)
            memory_mb = (original_width * original_height * 3) / (1024 * 1024)

            self.logger.info(
                f"圖像尺寸: {original_width}x{original_height}, 預估記憶體: {memory_mb:.1f}MB")

            # 檢查是否需要縮放
            max_dimension = max(original_width, original_height)

            if not self.enable_image_resize or max_dimension <= self.max_image_size:
                # 不需要縮放，但釋放臨時圖像
                del temp_img
                return image_path, False, original_size

            # 需要縮放
            scale_factor = self.max_image_size / max_dimension
            new_width = int(original_width * scale_factor)
            new_height = int(original_height * scale_factor)

            self.logger.warning(
                f"圖像過大，縮放至 {new_width}x{new_height} (縮放比例: {scale_factor:.2f})")

            # 縮放圖像
            resized_img = cv2.resize(
                temp_img, (new_width, new_height), interpolation=cv2.INTER_AREA)

            # 創建臨時文件
            import tempfile
            temp_dir = Path(image_path).parent / "temp_resized"
            temp_dir.mkdir(exist_ok=True)

            temp_path = temp_dir / f"resized_{Path(image_path).name}"
            cv2.imwrite(str(temp_path), resized_img)

            # 清理記憶體
            del temp_img, resized_img

            self.logger.info(f"縮放完成，臨時文件: {temp_path}")
            return str(temp_path), True, original_size

        except Exception as e:
            self.logger.error(f"預處理大圖像失敗: {e}")
            return image_path, False, (0, 0)

    def _cleanup_temp_files(self, image_path: str):
        """清理臨時縮放文件"""
        try:
            temp_path = Path(image_path)
            if temp_path.exists() and "temp_resized" in str(temp_path):
                temp_path.unlink()
                self.logger.debug(f"已清理臨時文件: {temp_path}")

                # 如果臨時目錄為空，也刪除
                temp_dir = temp_path.parent
                if temp_dir.exists() and len(list(temp_dir.iterdir())) == 0:
                    temp_dir.rmdir()

        except Exception as e:
            self.logger.debug(f"清理臨時文件失敗: {e}")

    def scan_json_labels(self, json_dir: str) -> Dict[str, Any]:
        """
        掃描指定目錄下的所有JSON標註文件，收集標籤信息

        Args:
            json_dir: JSON標註文件目錄

        Returns:
            標籤統計信息字典
        """
        json_path = Path(json_dir)
        if not json_path.exists():
            self.logger.warning(f"JSON目錄不存在: {json_dir}")
            return {'error': f"目錄不存在: {json_dir}"}

        # 查找所有JSON文件
        json_files = list(json_path.glob("*.json"))
        if not json_files:
            self.logger.warning(f"在{json_dir}中未找到JSON文件")
            return {'error': f"未找到JSON文件", 'dir': json_dir}

        # 統計信息
        labels_stats = {
            'total_files': len(json_files),
            'total_annotations': 0,
            'label_counts': defaultdict(int),
            'label_types': defaultdict(set),  # 存儲每種標籤的形狀類型
            'files_with_labels': [],
            'files_without_labels': [],
            'unique_labels': set(),
            'shape_type_stats': defaultdict(int),
            'scan_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        self.logger.info(f"🔍 開始掃描JSON標註文件: {json_dir}")
        self.logger.info(f"📁 找到 {len(json_files)} 個JSON文件")

        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                shapes = data.get('shapes', [])
                if shapes:
                    labels_stats['files_with_labels'].append(json_file.name)
                else:
                    labels_stats['files_without_labels'].append(json_file.name)

                # 處理每個標註
                for shape in shapes:
                    label = shape.get('label', '')
                    shape_type = shape.get('shape_type', 'unknown')

                    if label:
                        labels_stats['label_counts'][label] += 1
                        labels_stats['label_types'][label].add(shape_type)
                        labels_stats['unique_labels'].add(label)
                        labels_stats['shape_type_stats'][shape_type] += 1
                        labels_stats['total_annotations'] += 1

            except Exception as e:
                self.logger.warning(f"讀取JSON文件失敗 {json_file.name}: {e}")
                continue

        # 轉換set為list以便JSON序列化
        for label in labels_stats['label_types']:
            labels_stats['label_types'][label] = list(
                labels_stats['label_types'][label])
        labels_stats['unique_labels'] = sorted(
            list(labels_stats['unique_labels']))

        # 緩存結果
        self.json_labels_summary = labels_stats

        return labels_stats

    def display_json_labels_summary(self, labels_stats: Optional[Dict[str, Any]] = None) -> str:
        """
        顯示JSON標籤掃描結果摘要

        Args:
            labels_stats: 標籤統計信息（如果為None則使用緩存的結果）

        Returns:
            格式化的摘要字符串
        """
        if labels_stats is None:
            labels_stats = self.json_labels_summary

        if not labels_stats or 'error' in labels_stats:
            return f"❌ 掃描失敗: {labels_stats.get('error', '未知錯誤')}"

        summary = []
        summary.append("=" * 60)
        summary.append("🏷️  JSON標註文件掃描報告")
        summary.append("=" * 60)

        # 基本統計
        summary.append(f"📁 掃描時間: {labels_stats['scan_time']}")
        summary.append(f"📊 文件統計:")
        summary.append(f"  • 總JSON文件數: {labels_stats['total_files']}")
        summary.append(
            f"  • 有標註的文件: {len(labels_stats['files_with_labels'])} 個")
        summary.append(
            f"  • 無標註的文件: {len(labels_stats['files_without_labels'])} 個")
        summary.append(f"  • 總標註數量: {labels_stats['total_annotations']} 個")

        # 標籤類別統計
        summary.append(
            f"\n🎯 發現的標籤類別 ({len(labels_stats['unique_labels'])} 種):")
        if labels_stats['unique_labels']:
            for i, label in enumerate(labels_stats['unique_labels'], 1):
                count = labels_stats['label_counts'][label]
                shape_types = ', '.join(labels_stats['label_types'][label])
                summary.append(
                    f"  {i:2d}. {label:<30} - {count:4d} 個 (類型: {shape_types})")
        else:
            summary.append("  ❌ 未發現任何標籤")

        # 形狀類型統計
        if labels_stats['shape_type_stats']:
            summary.append(f"\n📐 標註形狀類型統計:")
            for shape_type, count in sorted(labels_stats['shape_type_stats'].items()):
                summary.append(f"  • {shape_type:<15}: {count:4d} 個")

        # 檔案分析
        if labels_stats['files_without_labels']:
            summary.append(
                f"\n⚠️  無標註的文件 ({len(labels_stats['files_without_labels'])} 個):")
            for filename in labels_stats['files_without_labels'][:5]:  # 只顯示前5個
                summary.append(f"  • {filename}")
            if len(labels_stats['files_without_labels']) > 5:
                summary.append(
                    f"  • ... 還有 {len(labels_stats['files_without_labels']) - 5} 個文件")

        summary.append("=" * 60)

        return "\n".join(summary)

    def predict_single_image(self, image_path: str, output_dir: str = "", save_roi_preview: bool = True) -> Dict[str, Any]:
        """
        單張圖像推理

        Args:
            image_path: 圖像路徑
            output_dir: 輸出目錄
            save_roi_preview: 是否保存ROI預覽

        Returns:
            推理結果
        """
        if not self.model:
            raise RuntimeError("模型未加載")

        start_time = time.time()

        # 🧹 預處理大圖像
        processed_image_path, was_resized, original_size = self._preprocess_large_image(
            image_path)

        # 讀取圖像
        original_image = cv2.imread(image_path)
        if original_image is None:
            raise ValueError(f"無法讀取圖像: {image_path}")

        # 🎯 檢查是否啟用ROI功能
        roi_offset = None
        roi_enabled = self._check_roi_enabled()
        
        if roi_enabled:
            # 計算ROI區域並裁切圖像
            original_image, roi_offset = self._apply_roi_cropping(original_image)
            print(f"🎯 ROI裁切已套用: offset={roi_offset}")

        # 直接應用resize_ratio縮放，且只保留縮小後的圖像與LabelMe JSON
        resize_ratio = self.config_manager.inference.resize_ratio
        apply_resize = resize_ratio != 1.0 and resize_ratio > 0
        
        # 🔧 調試信息：顯示resize決策
        print(f"🔍 Resize決策: resize_ratio={resize_ratio}, apply_resize={apply_resize}")
        
        if apply_resize:
            original_height, original_width = original_image.shape[:2]
            new_width = int(original_width * resize_ratio)
            new_height = int(original_height * resize_ratio)
            image = cv2.resize(
                original_image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            # 如果有ROI，也需要調整ROI偏移量
            # if roi_offset:
            #     roi_offset = (roi_offset[0] * resize_ratio, roi_offset[1] * resize_ratio)
        else:
            image = original_image

        # 執行推理（直接使用記憶體中的圖像）
        results = self.model(image,
                             imgsz=self.config_manager.inference.img_size,
                             conf=self.config_manager.inference.global_conf,
                             iou=self.config_manager.inference.iou_threshold)
        # 🟢 傳遞實際推理圖像尺寸和ROI偏移信息
        actual_image_shape = image.shape[:2]  # (height, width)
        result = self._convert_ultralytics_result(
            results[0], image_path, 
            actual_image_shape=actual_image_shape,
            roi_offset=roi_offset)

        # 記錄ROI信息
        if roi_enabled and roi_offset:
            result['roi_applied'] = True
            result['roi_offset'] = roi_offset
            result['roi_cropped_image'] = image.copy() if not apply_resize else original_image.copy()
        else:
            result['roi_applied'] = False

        # 記錄resize信息
        if apply_resize:
            result['resize_applied'] = True
            result['resize_ratio'] = resize_ratio
            result['resized_from'] = (original_width, original_height)
            result['resized_to'] = (new_width, new_height)
            # 🟢 修正：不再進行in-place修改，保持原始座標供後續使用
            # 座標縮放將在各自的輸出模組中處理
            print(f"📐 Resize applied: {resize_ratio:.2f}x ({original_width}x{original_height} -> {new_width}x{new_height})")
        else:
            result['resize_applied'] = False
            print(f"✅ No resize applied: 保持原始尺寸 {original_image.shape[:2]}")

        # 🚀 新增：在直接推理模式下應用物件融合
        if self.fusion_engine:
            fused_detections = self.fusion_engine.fuse_detections(
                result['detections'])
            self.logger.info(
                f"融合策略應用: {len(result['detections'])} -> {len(fused_detections)} 個檢測結果")
            result['detections'] = fused_detections

        # 應用智能過濾
        original_count = len(result['detections'])
        result['detections'] = self.intelligent_filter.apply_filtering(
            result['detections'])
        filtered_count = len(result['detections'])

        # 🆕 明確日誌：顯示過濾前後的數量變化
        if original_count != filtered_count:
            print(
                f"🧠 智能過濾: {original_count} -> {filtered_count} 個檢測結果 (移除 {original_count - filtered_count} 個)")
            self.logger.info(
                f"智能過濾: {original_count} -> {filtered_count} 個檢測結果")
        else:
            print(f"🧠 智能過濾: {original_count} 個檢測結果 (無變化)")
            self.logger.debug(f"智能過濾: {original_count} 個檢測結果 (無變化)")

        # 更新統計信息
        processing_time = time.time() - start_time
        self.stats['total_images'] += 1
        self.stats['total_detections'] += len(result['detections'])
        self.stats['processing_times'].append(processing_time)

        for det in result['detections']:
            self.stats['class_counts'][det['class_name']] += 1

        # 🧹 記憶體管理：增加處理計數器並檢查是否需要清理
        self.processed_count += 1

        # 🧹 每張圖像都進行輕量級清理
        self._lightweight_cleanup()

        # 🧹 定期執行深度清理
        self.cleanup_memory()  # 會根據interval自動判斷是否清理

        # 🧹 清理臨時縮放文件
        if was_resized:
            self._cleanup_temp_files(processed_image_path)

        # 🖼️ 圖像釋放：主動釋放大型圖像對象（可配置）
        if getattr(self, 'enable_image_memory_release', True):
            self._release_image_memory(original_image, image, roi_offset)

        # 🎯 應用可視化過濾邏輯，為可視化和LabelMe JSON準備相同的檢測結果
        visualization_filtered_detections = self._filter_detections_for_visualization(
            result['detections'])

        # 🔧 修復：當沒有任何檢測結果時，跳過LabelMe處理但保留三視圖生成
        no_detections = not result.get('detections')
        if no_detections:
            print(f"ℹ️ 圖像無檢測結果，將跳過LabelMe處理但仍生成三視圖: {Path(result['image_path']).name}")

        # 保存結果（使用過濾後的檢測結果）
        if output_dir:
            # 創建一個包含過濾後檢測結果的新result字典，用於可視化
            filtered_result = result.copy()
            filtered_result['detections'] = visualization_filtered_detections
            
            # 🔧 修復：確保三視圖功能不受檢測結果影響
            if not visualization_filtered_detections:
                print(f"ℹ️ 沒有檢測結果，但仍會生成三視圖（如果啟用）: {Path(result['image_path']).name}")
                # 保存統計信息
                if self.config_manager.visualization.save_statistics:
                    self.csv_manager.update_incremental_reports(result, output_dir)
                
                # 🆕 即使沒有檢測結果也要生成三視圖（如果啟用）
                if self.config_manager.visualization.enable_three_view_output:
                    self._generate_three_view_only(result, image, output_dir)
            else:
                self._save_single_result(filtered_result, image, output_dir)
            # 🎯 ROI預覽：在ROI啟用時自動生成預覽圖
            if (self.config_manager.visualization.force_single_image_roi_preview or 
                (self.config_manager.visualization.enable_roi_preview and self._check_roi_enabled())):
                self._save_roi_preview(image_path, output_dir)

        # 🏷️ 自動LabelMe JSON處理（如果有提供整合器）
        if self.labelme_integration and self.labelme_integration.enabled and result.get('detections'):
            try:
                labelme_filtered_detections = self._filter_detections_for_labelme(
                    result['detections'])
                if labelme_filtered_detections:
                    # 🔥 修復座標超出範圍問題：移除雙重變換，採用正確的座標變換流程
                    # 
                    # 問題原因：座標被變換了兩次，導致座標超出圖像邊界
                    # 
                    # 新的變換流程：
                    # 1. _convert_ultralytics_result: 推理座標 -> 原始圖像座標（含ROI偏移）
                    # 2. 本段代碼: 原始圖像座標 -> LabelMe輸出座標（ROI裁切/resize）
                    # 3. 邊界檢查: 確保座標在LabelMe圖像範圍內
                    # 
                    # 修復前：座標被ROI偏移和resize變換兩次，造成座標錯誤
                    # 修復後：清晰的單向變換流程，確保座標正確性
                    
                    detections_for_json = []
                    for det in labelme_filtered_detections:
                        det_json = det.copy()
                        print(f"🔍 原始檢測座標: bbox={det_json.get('bbox', 'None')}, class={det_json.get('class_name', 'unknown')}")
                        
                        # 🚫 移除所有重複的座標變換邏輯
                        # 座標已經在正確的座標系統中，無需額外處理
                        
                        # 🔥 重要：由於座標現在在原始圖像座標系統中，需要根據LabelMe最終輸出確定邊界
                        # 座標邊界檢查的目標尺寸 = LabelMe最終輸出的圖像尺寸
                        
                        # 🎯 確定LabelMe輸出的最終圖像尺寸（用於座標邊界檢查）
                        if roi_enabled and result.get('roi_applied'):
                            # ROI模式：座標需要在ROI裁切區域內，但已經轉換為原始圖像座標系統
                            # 因此邊界應該是原始圖像尺寸
                            target_height, target_width = original_image.shape[:2]
                            print(f"🔍 ROI模式-使用原始圖像尺寸作為邊界: {target_width}x{target_height}")
                        elif apply_resize:
                            # Resize模式：座標在原始圖像座標系統，但LabelMe輸出resize圖像
                            # 邊界檢查使用原始圖像尺寸，稍後會處理到resize的最終座標轉換
                            target_height, target_width = original_image.shape[:2]
                            print(f"🔍 Resize模式-使用原始圖像尺寸作為邊界: {target_width}x{target_height}")
                        else:
                            # 標準模式：直接使用原始圖像尺寸
                            target_height, target_width = original_image.shape[:2]
                            print(f"🔍 標準模式-使用原始圖像尺寸: {target_width}x{target_height}")
                        
                        # 🔥 嚴格的座標邊界驗證和修正
                        def clamp_coordinate(coord, min_val, max_val):
                            clamped = max(min_val, min(coord, max_val))
                            if clamped != coord:
                                print(f"🔧 座標修正: {coord} -> {clamped} (範圍: {min_val}-{max_val})")
                            return clamped
                        
                        # 記錄修正前的座標
                        original_bbox = det_json['bbox'].copy() if 'bbox' in det_json else None
                        original_points = det_json['points'].copy() if 'points' in det_json and det_json['points'] is not None else None
                        
                        # 修正bbox座標
                        if 'bbox' in det_json:
                            det_json['bbox'] = [
                                clamp_coordinate(det_json['bbox'][0], 0, target_width-1),   # x1
                                clamp_coordinate(det_json['bbox'][1], 0, target_height-1),  # y1
                                clamp_coordinate(det_json['bbox'][2], 0, target_width-1),   # x2
                                clamp_coordinate(det_json['bbox'][3], 0, target_height-1)   # y2
                            ]
                            
                            # 檢查是否有座標被修正
                            if original_bbox != det_json['bbox']:
                                print(f"🔧 BBox修正: {original_bbox} -> {det_json['bbox']}")
                        
                        # 修正polygon點座標
                        if 'points' in det_json and det_json['points'] is not None:
                            new_points = []
                            points_modified = False
                            for x, y in det_json['points']:
                                new_x = clamp_coordinate(x, 0, target_width-1)
                                new_y = clamp_coordinate(y, 0, target_height-1)
                                if new_x != x or new_y != y:
                                    points_modified = True
                                new_points.append([new_x, new_y])
                            
                            det_json['points'] = new_points
                            if points_modified:
                                print(f"🔧 Points修正數量: {len([p for p in zip(original_points, new_points) if p[0] != p[1]])}")
                        
                        # 驗證bbox有效性
                        if 'bbox' in det_json and (det_json['bbox'][2] <= det_json['bbox'][0] or 
                            det_json['bbox'][3] <= det_json['bbox'][1]):
                            print(f"⚠️ 無效bbox跳過: {det_json['bbox']} (目標尺寸: {target_width}x{target_height})")
                            continue
                        
                        print(f"✅ 座標檢查完成: 目標尺寸={target_width}x{target_height}, 類別={det_json.get('class_name', 'unknown')}")
                        
                        detections_for_json.append(det_json)

                    # 🔥 準備LabelMe圖像並確定最終座標變換
                    labelme_image_data = None
                    final_target_width = None
                    final_target_height = None
                    coordinate_transform_needed = False
                    
                    if roi_enabled and result.get('roi_applied'):
                        # ROI模式：使用ROI裁切圖像，需要將座標從原始圖像座標系轉換為ROI座標系
                        labelme_image_data = result['roi_cropped_image']
                        final_target_height, final_target_width = labelme_image_data.shape[:2]
                        coordinate_transform_needed = True
                        roi_offset = result.get('roi_offset', (0, 0))
                        print(f"🏷️ LabelMe使用ROI裁切圖像，尺寸: {final_target_width}x{final_target_height}, 偏移: {roi_offset}")
                    elif apply_resize:
                        # Resize模式：使用resize圖像，需要將座標從原始座標系轉換為resize座標系
                        labelme_image_data = image
                        final_target_height, final_target_width = image.shape[:2]
                        coordinate_transform_needed = True
                        print(f"🏷️ LabelMe使用resize圖像，尺寸: {final_target_width}x{final_target_height}, 縮放比例: {resize_ratio}")
                    else:
                        # 標準模式：使用原始圖像，座標系統一致，無需變換
                        labelme_image_data = original_image
                        final_target_height, final_target_width = original_image.shape[:2]
                        coordinate_transform_needed = False
                        print(f"🏷️ LabelMe使用原始圖像，尺寸: {final_target_width}x{final_target_height}")
                    
                    # 🎯 執行最終座標變換（從原始圖像座標系 -> LabelMe輸出座標系）
                    if coordinate_transform_needed:
                        print(f"🔄 執行座標變換：原始圖像座標 -> LabelMe輸出座標")
                        for det_json in detections_for_json:
                            if roi_enabled and result.get('roi_applied'):
                                # ROI變換：減去ROI偏移量
                                roi_offset = result.get('roi_offset', (0, 0))
                                if 'bbox' in det_json:
                                    det_json['bbox'] = [
                                        det_json['bbox'][0] - roi_offset[0],  # x1
                                        det_json['bbox'][1] - roi_offset[1],  # y1
                                        det_json['bbox'][2] - roi_offset[0],  # x2
                                        det_json['bbox'][3] - roi_offset[1]   # y2
                                    ]
                                if 'points' in det_json and det_json['points'] is not None:
                                    det_json['points'] = [
                                        [x - roi_offset[0], y - roi_offset[1]] 
                                        for x, y in det_json['points']]
                            
                            elif apply_resize:
                                # Resize變換：乘以縮放比例
                                if 'bbox' in det_json:
                                    det_json['bbox'] = [c * resize_ratio for c in det_json['bbox']]
                                if 'points' in det_json and det_json['points'] is not None:
                                    det_json['points'] = [
                                        [x * resize_ratio, y * resize_ratio] for x, y in det_json['points']]
                        print(f"✅ 座標變換完成")
                    else:
                        print(f"ℹ️ 無需座標變換（座標系統一致）")
                    
                    # 🔥🔥 最終座標邊界檢查：使用實際LabelMe圖像尺寸
                    print(f"🔥 執行最終座標邊界檢查，目標尺寸: {final_target_width}x{final_target_height}")
                    final_detections_for_json = []
                    for det_json in detections_for_json:
                        # 定義邊界檢查函數
                        def final_clamp_coordinate(coord, min_val, max_val):
                            clamped = max(min_val, min(coord, max_val))
                            return clamped
                        
                        # 最終修正bbox座標
                        if 'bbox' in det_json:
                            original_bbox = det_json['bbox'].copy()
                            det_json['bbox'] = [
                                final_clamp_coordinate(det_json['bbox'][0], 0, final_target_width-1),   # x1
                                final_clamp_coordinate(det_json['bbox'][1], 0, final_target_height-1),  # y1
                                final_clamp_coordinate(det_json['bbox'][2], 0, final_target_width-1),   # x2
                                final_clamp_coordinate(det_json['bbox'][3], 0, final_target_height-1)   # y2
                            ]
                            
                            # 檢查是否有座標被修正
                            if original_bbox != det_json['bbox']:
                                print(f"🔥 最終BBox修正: {original_bbox} -> {det_json['bbox']}")
                        
                        # 最終修正polygon點座標
                        if 'points' in det_json and det_json['points'] is not None:
                            original_points = det_json['points'].copy()
                            new_points = []
                            points_modified = False
                            for x, y in det_json['points']:
                                new_x = final_clamp_coordinate(x, 0, final_target_width-1)
                                new_y = final_clamp_coordinate(y, 0, final_target_height-1)
                                if new_x != x or new_y != y:
                                    points_modified = True
                                    print(f"🔥 Point修正: ({x},{y}) -> ({new_x},{new_y})")
                                new_points.append([new_x, new_y])
                            
                            det_json['points'] = new_points
                            if points_modified:
                                print(f"🔥 共修正了 {len([1 for (ox,oy), (nx,ny) in zip(original_points, new_points) if ox!=nx or oy!=ny])} 個點")
                        
                        # 驗證bbox有效性
                        if 'bbox' in det_json and (det_json['bbox'][2] <= det_json['bbox'][0] or 
                            det_json['bbox'][3] <= det_json['bbox'][1]):
                            print(f"🔥 最終檢查：無效bbox跳過: {det_json['bbox']}")
                            continue
                        
                        final_detections_for_json.append(det_json)
                    
                    # 使用最終檢查過的座標
                    detections_for_json = final_detections_for_json
                    print(f"🔥 最終座標檢查完成，有效檢測數量: {len(detections_for_json)}")
                    
                    labelme_result = self.labelme_integration.process_single_image_result(
                        image_path,
                        detections_for_json,
                        resized_image_data=labelme_image_data,
                        resize_ratio=1.0  # 🔥 座標已正確變換，LabelMe整合器無需額外縮放
                    )
                    if labelme_result:
                        self.stats['total_labelme_detections'] += len(
                            labelme_filtered_detections)
            except Exception as e:
                print(f"   ❌ LabelMe JSON生成失敗: {e}")

        # 🧹 清理臨時文件（如果有的話）
        if was_resized:
            self._cleanup_temp_files(processed_image_path)

        # 🧹 更新處理計數並執行記憶體清理
        self.processed_count += 1
        self.cleanup_memory()

        return result

    def _filter_detections_for_visualization(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        為可視化過濾檢測結果，應用排除類別邏輯
        這個方法確保可視化和LabelMe JSON使用相同的檢測結果
        """
        filtered_detections = []
        excluded_ids = getattr(self.config_manager, 'excluded_class_ids', [])
        excluded_names = getattr(
            self.config_manager, 'excluded_class_names', [])

        original_count = len(detections)
        excluded_by_class = 0

        for det in detections:
            class_id = det['class_id']
            class_name = det['class_name']

            # 檢查是否在排除列表中
            if class_id in excluded_ids or class_name in excluded_names:
                excluded_by_class += 1
                continue  # 跳過被排除的類別

            filtered_detections.append(det)

        filtered_count = len(filtered_detections)

        if original_count != filtered_count:
            print(
                f"🎨 可視化預過濾: {original_count} -> {filtered_count} 個檢測結果 (移除 {excluded_by_class} 個排除類別)")
        else:
            print(f"🎨 可視化預過濾: {original_count} 個檢測結果 (無變化)")

        return filtered_detections

    def _filter_detections_for_labelme(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        為LabelMe過濾檢測結果，應用與三視圖相同的過濾邏輯以確保一致性
        包括：排除類別 + enabled設定檢查，但不包括置信度過濾
        """
        filtered_detections = []
        excluded_ids = getattr(self.config_manager, 'excluded_class_ids', [])
        excluded_names = getattr(
            self.config_manager, 'excluded_class_names', [])

        original_count = len(detections)
        excluded_by_class = 0
        excluded_by_disabled = 0

        print(f"🏷️ LabelMe過濾詳細統計:")
        print(f"   📊 輸入檢測數量: {original_count}")

        for det in detections:
            class_id = det['class_id']
            class_name = det['class_name']

            # 檢查是否在排除列表中
            if class_id in excluded_ids or class_name in excluded_names:
                excluded_by_class += 1
                print(f"   ❌ {class_name} (ID:{class_id}): 被排除類別過濾")
                continue  # 跳過被排除的類別

            # 🆕 新增：檢查類別enabled設定（與三視圖保持一致）
            class_config = self.config_manager.get_class_config(class_id)
            if not class_config or not class_config.enabled:
                excluded_by_disabled += 1
                print(
                    f"   ❌ {class_name} (ID:{class_id}): 類別未啟用 (enabled=False)")
                continue

            # ✅ 通過所有過濾條件（LabelMe不檢查置信度）
            print(f"   ✅ {class_name} (ID:{class_id}): 通過過濾，將儲存至LabelMe")
            filtered_detections.append(det)

        filtered_count = len(filtered_detections)

        print(f"   📈 LabelMe過濾統計:")
        print(f"      排除類別過濾: {excluded_by_class} 個")
        print(f"      類別未啟用: {excluded_by_disabled} 個")
        print(f"      ✅ 將儲存至LabelMe: {filtered_count} 個")

        return filtered_detections

    def predict_batch(self, input_dir: str, output_dir: str, json_dir: Optional[str] = None,
                      enable_resume: bool = False) -> List[Dict[str, Any]]:
        """
        批量推理（增強版：支持JSON標籤掃描與中斷回復）

        Args:
            input_dir: 輸入目錄
            output_dir: 輸出目錄
            json_dir: JSON標註目錄（可選，用於預先掃描標籤）
            enable_resume: 是否啟用中斷回復功能

        Returns:
            推理結果列表
        """
        input_path = Path(input_dir)
        if not input_path.exists():
            raise ValueError(f"輸入目錄不存在: {input_dir}")

        # 準備輸出目錄
        output_dir_path = Path(output_dir)
        images_dir = output_dir_path / self.config_manager.output.images_subdir
        reports_dir = output_dir_path / self.config_manager.output.reports_subdir
        images_dir.mkdir(parents=True, exist_ok=True)
        reports_dir.mkdir(parents=True, exist_ok=True)

        # 🆕 在批次處理開始時，加載一次歷史數據
        self._load_historical_class_metrics(reports_dir)

        # 🔄 如果啟用中斷回復，更新設置
        if enable_resume and not self.resume_enabled:
            self.setup_resume(enable_resume=True)

        # 🔄 清理中斷時的不完整文件
        if self.resume_enabled:
            self._cleanup_interrupted_files(output_dir)

        # 🔄 加載處理進度
        progress_data = self._load_progress(
            output_dir) if self.resume_enabled else {}

        # 🏷️ JSON標籤預掃描（如果提供了json_dir）
        if json_dir:
            self.logger.info("🔍 開始預掃描JSON標註文件...")
            labels_stats = self.scan_json_labels(json_dir)
            summary = self.display_json_labels_summary(labels_stats)
            print(summary)

            # 保存JSON掃描報告
            scan_report_path = reports_dir / "json_labels_scan_report.txt"
            with open(scan_report_path, 'w', encoding='utf-8') as f:
                f.write(summary)
            self.logger.info(f"📄 JSON掃描報告已保存: {scan_report_path}")

            # 保存JSON統計數據
            if not ('error' in labels_stats):
                stats_json_path = reports_dir / "json_labels_statistics.json"
                import json
                with open(stats_json_path, 'w', encoding='utf-8') as f:
                    json.dump(labels_stats, f, ensure_ascii=False,
                              indent=2, default=str)
                self.logger.info(f"📊 JSON統計數據已保存: {stats_json_path}")

        # 獲取所有圖像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        # 去重並排序，確保只對唯一第一張圖產生 roi 預覽
        image_files = sorted(set(image_files))

        if not image_files:
            self.logger.warning(f"在{input_dir}中未找到圖像文件")
            return []

        # 🔄 過濾已處理的文件
        if self.resume_enabled:
            original_count = len(image_files)
            image_files = [
                f for f in image_files if not self._should_skip_file(f, output_dir)]
            skipped_count = original_count - len(image_files)

            if skipped_count > 0:
                self.logger.info(f"🔄 中斷回復: 跳過 {skipped_count} 個已處理文件，"
                                 f"剩餘 {len(image_files)} 個文件待處理")

            if not image_files:
                self.logger.info("🎉 所有文件已處理完成！")
                return []

        # 確定CSV文件路徑
        class_csv_path = reports_dir / "class_metrics_incremental.csv"

        # 🔄 獲取總文件數（包括已處理的）用於進度計算
        total_files = progress_data.get('total_files', len(
            image_files) + len(self.processed_files))

        self.logger.info(f"🚀 開始批量推理:")
        self.logger.info(f"   📊 總文件數: {total_files}")
        self.logger.info(f"   ✅ 已處理: {len(self.processed_files)}")
        self.logger.info(f"   📋 待處理: {len(image_files)}")

        results = []
        first_image_for_preview = None

        try:
            for i, image_file in enumerate(image_files, 1):
                try:
                    # 計算全局索引（包括已處理的文件）
                    global_index = len(self.processed_files) + i

                    self.logger.info(
                        f"📷 處理第 {global_index}/{total_files} 張: {image_file.name}")

                    # 🎯 批次時在第一張產生ROI預覽（如果ROI啟用）
                    is_first_image = (i == 1 and len(self.processed_files) == 0)
                    result = self.predict_single_image(str(image_file), output_dir)
                    
                    # 為第一張圖像單獨生成ROI預覽（如果ROI啟用且未在predict_single_image中生成）
                    if is_first_image and self._check_roi_enabled() and self.config_manager.visualization.enable_roi_preview:
                        if not (self.config_manager.visualization.force_single_image_roi_preview):
                            self._save_roi_preview(str(image_file), output_dir)
                    results.append(result)

                    # 記錄已處理文件
                    self.processed_files.add(image_file.name)

                    if i == 1 and len(self.processed_files) == 1:
                        first_image_for_preview = str(image_file)

                    # 🖼️ 強制釋放結果中的大型數據（可配置）
                    if getattr(self, 'enable_detection_memory_release', True):
                        self._release_detection_memory(result.get('detections', []))

                    # 🧹 可配置的批次記憶體清理間隔
                    cleanup_interval = getattr(self, 'batch_memory_cleanup_interval', 10)
                    if i % cleanup_interval == 0:
                        self.cleanup_memory(force=True)
                        self.logger.info(f"🧹 批次記憶體清理完成 ({i}/{len(image_files)})")

                    # 🔄 定期保存進度
                    if self.resume_enabled:
                        self._save_progress(output_dir, total_files, global_index,
                                            image_file.name, self.stats)

                except KeyboardInterrupt:
                    self.logger.info(f"\n⚠️ 用戶中斷，已處理 {len(results)} 張圖像")
                    if self.resume_enabled:
                        self._save_progress(output_dir, total_files, global_index,
                                            image_file.name, self.stats)
                        self.logger.info(f"💾 進度已保存，可使用中斷回復功能繼續處理")
                    raise
                except Exception as e:
                    error_msg = str(e)
                    if "Unable to allocate" in error_msg and "MiB for an array" in error_msg:
                        # 記憶體不足錯誤
                        self.logger.error(f"❌ 記憶體不足處理 {image_file.name}: {e}")
                        self.logger.error(f"💡 建議解決方案:")
                        self.logger.error(
                            f"   1. 降低 max_image_size 參數 (當前: {self.max_image_size})")
                        self.logger.error(
                            f"   2. 設置 enable_image_resize = True")
                        self.logger.error(
                            f"   3. 減少 memory_cleanup_interval (當前: {self.memory_cleanup_interval})")
                        self.logger.error(f"   4. 關閉其他記憶體密集型程序")

                        # 強制執行記憶體清理
                        self.cleanup_memory(force=True)
                    else:
                        self.logger.error(f"❌ 處理失敗 {image_file.name}: {e}")

                    # 🔄 即使失敗也保存進度
                    if self.resume_enabled:
                        self._save_progress(output_dir, total_files, global_index,
                                            image_file.name, self.stats)
                    continue

        except KeyboardInterrupt:
            self.logger.info(f"\n⚠️ 批次處理被用戶中斷")
            if self.resume_enabled:
                self.logger.info(
                    f"💾 進度已保存至: {output_dir}/{self.resume_progress_file}")
                self.logger.info(f"🔄 重新運行程序即可從中斷點繼續處理")
            raise
        except Exception as e:
            self.logger.error(f"❌ 批次處理異常: {e}")
            if self.resume_enabled:
                self._save_progress(output_dir, total_files, len(self.processed_files),
                                    "error", self.stats)
            raise

        # 批次處理完成日誌
        success_count = len(results)
        failed_count = len(image_files) - success_count

        self.logger.info("="*50)
        self.logger.info("🎉 批量處理完成！")
        self.logger.info(f"✅ 成功處理: {success_count} 張圖像")
        if failed_count > 0:
            self.logger.info(f"❌ 處理失敗: {failed_count} 張圖像")

        # 顯示詳細統計對比
        total_three_view_detections = self.stats['total_detections']
        total_labelme_detections = self.stats['total_labelme_detections']

        if self.labelme_integration and self.labelme_integration.enabled:
            labelme_dir = Path(self.labelme_integration.output_dir)
            if labelme_dir.exists():
                json_files = list(labelme_dir.glob("*.json"))
                self.logger.info(f"🏷️ LabelMe JSON檔案數: {len(json_files)} 個")
                print(f"   📈 總檢測數量對比:")
                print(
                    f"      三視圖顯示: {total_three_view_detections} 個檢測 (enabled + 置信度過濾)")
                print(
                    f"      LabelMe儲存: {total_labelme_detections} 個檢測 (enabled過濾，無置信度過濾)")
                if total_three_view_detections == total_labelme_detections:
                    print(f"   ✅ 數據一致性: 檢測數量完全一致 (所有檢測都通過置信度閾值)")
                else:
                    diff = total_labelme_detections - total_three_view_detections
                    print(f"   ⚠️ 數據差異: {diff} 個檢測因置信度低於閾值而僅在LabelMe中保存")
                print(f"   ✅ 統一過濾: 排除類別 + enabled設定已同步")
                print(f"   ✅ bbox+mask AND關係已正確實施")

        self.logger.info(f"📊 總檢測數量: {total_three_view_detections} 個")
        self.logger.info(f"📁 輸出目錄: {output_dir}")
        self.logger.info(f"📈 CSV報告: {output_dir}/reports/")

        # 顯示實時統計摘要
        stats_summary = self.get_current_statistics_summary()
        print("\n" + stats_summary)

        # 批次處理後寫入最終的類別CSV
        # self._write_final_class_csv(class_csv_path) # Handled by CSVManager

        # 🔄 完成最終進度保存
        if self.resume_enabled:
            self._save_progress(output_dir, total_files, len(self.processed_files),
                                "completed", self.stats)
            self.logger.info(
                f"🎉 批次處理完成！進度: {len(self.processed_files)}/{total_files}")

            # 清理進度文件（可選）
            if len(self.processed_files) >= total_files:
                progress_path = Path(output_dir) / self.resume_progress_file
                if progress_path.exists():
                    # 重命名為完成標記而不是刪除，便於查看最終統計
                    completed_path = progress_path.with_suffix(
                        '.completed.json')
                    progress_path.rename(completed_path)
                    self.logger.info(f"💾 進度文件已標記為完成: {completed_path}")

        # 🧹 批次處理完成後執行最終記憶體清理（可配置）
        if getattr(self, 'enable_batch_final_cleanup', True):
            self._batch_final_cleanup(results)
        self.cleanup_memory(force=True)
        self.log_memory_usage()

        # 批次處理後不再額外產生 roi 預覽圖
        return results

    def _save_single_result(self, result: Dict[str, Any], original_image: np.ndarray, output_dir: str):
        """保存單張圖像的結果"""
        output_path = Path(output_dir)

        # 創建子目錄
        images_dir = output_path / self.config_manager.output.images_subdir
        reports_dir = output_path / self.config_manager.output.reports_subdir
        images_dir.mkdir(parents=True, exist_ok=True)
        reports_dir.mkdir(parents=True, exist_ok=True)

        image_name = Path(result['image_path']).stem

        # 🆕 為可視化和LabelMe預先過濾檢測結果
        visualization_filtered_detections = self._filter_detections_for_visualization(
            result['detections'])

        # 生成可視化圖像
        vis_image = self._create_visualization(
            original_image, visualization_filtered_detections)

        # 保存可視化結果
        if self.config_manager.visualization.save_visualizations:
            vis_path = images_dir / f"{image_name}_vis.jpg"
            cv2.imwrite(str(vis_path), vis_image,
                        [cv2.IMWRITE_JPEG_QUALITY, self.config_manager.visualization.output_image_quality])

        # 生成三視圖（增強版本，支持GT加載和TP/FP/FN統計）
        if self.config_manager.visualization.enable_three_view_output:
            detection_count = len(
                visualization_filtered_detections)  # 🆕 使用過濾後的數量

            # 🆕 修正：確保bbox已根據resize_ratio縮放
            resize_ratio = self.config_manager.inference.resize_ratio

            # def scale_bbox(det):
            #     det = det.copy()
            #     det['bbox'] = [c * resize_ratio for c in det['bbox']]
            #     if 'points' in det:
            #         det['points'] = [[x * resize_ratio, y * resize_ratio]
            #                          for x, y in det['points']]
            #     return det
            # scaled_detections = [scale_bbox(
            #     det) for det in visualization_filtered_detections]

            # 嘗試獲取GT目錄
            gt_dir = None
            if hasattr(self.config_manager, 'paths') and hasattr(self.config_manager.paths, 'labelme_dir'):
                gt_dir = self.config_manager.paths.labelme_dir
            elif hasattr(self.config_manager, 'ground_truth') and hasattr(self.config_manager.ground_truth, 'gt_path'):
                gt_dir = self.config_manager.ground_truth.gt_path

            three_view_image = self.three_view_generator.create_three_view(
                original_image,
                vis_image,
                gt_img=None,
                image_path=result['image_path'],
                labelme_dir=gt_dir,
                detection_count=detection_count,
                detections=visualization_filtered_detections,  # 🆕 傳遞已縮放的檢測結果
                roi_offset=result.get('roi_offset')  # 🔧 傳遞ROI偏移量
            )
            three_view_path = images_dir / f"{image_name}_three_view.jpg"
            cv2.imwrite(str(three_view_path), three_view_image,
                        [cv2.IMWRITE_JPEG_QUALITY, self.config_manager.visualization.output_image_quality])

        # 生成CSV報告數據
        """ # 保存預測結果為JSON"""
        if self.config_manager.visualization.save_predictions:
            # 修正：確保reports_dir已創建
            reports_dir.mkdir(parents=True, exist_ok=True)

            # 修正：確保result是可序列化的
            serializable_result = result.copy()
            if 'detections' in serializable_result:
                for det in serializable_result['detections']:
                    if 'mask' in det and isinstance(det['mask'], np.ndarray):
                        # 將mask轉換為list以便JSON序列化
                        det['mask'] = det['mask'].tolist()

            pred_json_path = reports_dir / f"{image_name}_predictions.json"
            try:
                with open(pred_json_path, 'w', encoding='utf-8') as f:
                    json.dump(serializable_result, f,
                              ensure_ascii=False, indent=2)
                self.logger.debug(f"📄 預測JSON已保存: {pred_json_path}")
            except TypeError as e:
                self.logger.error(f"❌ 無法序列化預測結果為JSON: {e}")
                # 如果仍然失敗，嘗試移除mask後再保存
                try:
                    for det in serializable_result['detections']:
                        if 'mask' in det:
                            del det['mask']
                    with open(pred_json_path, 'w', encoding='utf-8') as f:
                        json.dump(serializable_result, f,
                                  ensure_ascii=False, indent=2)
                    self.logger.info(
                        f"📄 預測JSON已保存 (無mask數據): {pred_json_path}")
                except Exception as final_e:
                    self.logger.error(f"❌ 移除mask後JSON保存仍然失敗: {final_e}")

        # 🏷️ 整合LabelMe JSON輸出
        if self.labelme_integration and self.labelme_integration.enabled:
            if result.get('detections'):
                try:
                    labelme_file = self.labelme_integration.process_single_image_result(
                        image_path=result['image_path'],
                        detections=result['detections']
                    )
                    if labelme_file:
                        self.logger.info(
                            f"🏷️ LabelMe JSON已生成: {Path(labelme_file).name}")
                    else:
                        self.logger.warning(
                            f"⚠️ LabelMe JSON生成失敗 (無有效mask): {image_name}")
                except Exception as e:
                    self.logger.error(f"❌ LabelMe處理失敗: {e}")
            else:
                self.logger.info(f"ℹ️ 沒有檢測結果，跳過LabelMe JSON生成: {image_name}")

        # 生成CSV報告數據
        if self.config_manager.visualization.save_statistics:
            self.csv_manager.update_incremental_reports(result, output_dir)

        # 🖼️ 釋放可視化過程中產生的圖像記憶體
        try:
            # 釋放可視化圖像
            if 'vis_image' in locals():
                self._release_image_memory(vis_image)
            if 'three_view_image' in locals():
                self._release_image_memory(three_view_image)
            
            # 釋放檢測結果中的mask記憶體
            if visualization_filtered_detections:
                self._release_detection_memory(visualization_filtered_detections)
                
            self.logger.debug(f"🖼️ 可視化記憶體已釋放: {image_name}")
        except Exception as e:
            self.logger.debug(f"可視化記憶體釋放失敗: {e}")

    def _generate_three_view_only(self, result: Dict[str, Any], original_image: np.ndarray, output_dir: str):
        """
        專門用於在沒有檢測結果時生成三視圖
        確保即使沒有預測結果，只要有原圖和對應的JSON標註，也要顯示三視圖
        """
        output_path = Path(output_dir)
        images_dir = output_path / self.config_manager.output.images_subdir
        images_dir.mkdir(parents=True, exist_ok=True)

        image_name = Path(result['image_path']).stem

        # 創建空的可視化圖像（只有原圖，沒有檢測結果）
        vis_image = original_image.copy()

        # 嘗試獲取GT目錄
        gt_dir = None
        if hasattr(self.config_manager, 'paths') and hasattr(self.config_manager.paths, 'labelme_dir'):
            gt_dir = self.config_manager.paths.labelme_dir
        elif hasattr(self.config_manager, 'ground_truth') and hasattr(self.config_manager.ground_truth, 'gt_path'):
            gt_dir = self.config_manager.ground_truth.gt_path

        # 生成三視圖（即使沒有檢測結果也要生成）
        three_view_image = self.three_view_generator.create_three_view(
            original_image,
            vis_image,  # 使用原圖作為預測圖像
            gt_img=None,
            image_path=result['image_path'],
            labelme_dir=gt_dir,
            detection_count=0,  # 檢測數量為0
            detections=[],  # 空的檢測結果列表
            roi_offset=result.get('roi_offset')
        )
        
        # 保存三視圖
        three_view_path = images_dir / f"{image_name}_three_view.jpg"
        cv2.imwrite(str(three_view_path), three_view_image,
                    [cv2.IMWRITE_JPEG_QUALITY, self.config_manager.visualization.output_image_quality])
        
        print(f"🖼️ 三視圖已生成（無檢測結果）: {three_view_path.name}")

    def _create_visualization(self, image: np.ndarray, detections: List[Dict[str, Any]]) -> np.ndarray:
        """創建可視化圖像"""
        vis_image = image.copy()

        # 🔧 新增：獲取縮放比例並計算座標轉換係數
        resize_ratio = self.config_manager.inference.resize_ratio
        scale_factor = resize_ratio
        
        print(f"🎨 可視化生成詳細統計:")
        print(f"   📊 輸入檢測數量: {len(detections)}")
        print(f"   📐 縮放比例: {resize_ratio}, 座標轉換係數: {scale_factor}")

        # 🔍 調試統計
        total_input = len(detections)
        filtered_by_excluded = 0
        filtered_by_disabled = 0
        filtered_by_confidence = 0
        actually_drawn = 0

        for det in detections:
            class_id = det['class_id']
            class_name = det['class_name']
            confidence = det['confidence']
            bbox = det['bbox']
            mask = det.get('mask')

            # 獲取類別配置
            class_config = self.config_manager.get_class_config(class_id)

            # 🆕 新增：檢查類別是否在排除列表中
            excluded_ids = getattr(self.config_manager,
                                   'excluded_class_ids', [])
            excluded_names = getattr(
                self.config_manager, 'excluded_class_names', [])

            if class_id in excluded_ids or class_name in excluded_names:
                filtered_by_excluded += 1
                print(f"   ❌ {class_name} (ID:{class_id}): 被排除類別過濾")
                continue  # 跳過被排除的類別

            if not class_config or not class_config.enabled:
                filtered_by_disabled += 1
                print(
                    f"   ❌ {class_name} (ID:{class_id}): 類別未啟用 (enabled=False)")
                continue

            # 檢查置信度閾值
            use_sahi = self.config_manager.sahi.enable_sahi
            conf_threshold = self.config_manager.get_class_confidence(
                class_id, use_sahi)
            if confidence < conf_threshold:
                filtered_by_confidence += 1
                print(
                    f"   ❌ {class_name} (ID:{class_id}): 置信度過濾 ({confidence:.3f} < {conf_threshold:.3f})")
                continue

            print(
                f"   ✅ {class_name} (ID:{class_id}): 通過所有過濾 (confidence:{confidence:.3f})")

            # 獲取顯示屬性 (使用更穩健的方式)
            color = tuple(
                map(int, self.config_manager.get_class_color(class_id)))

            # 🔧 修復：穩健地獲取顯示名稱，並確保經過label_aliases處理
            class_config = self.config_manager.get_class_config(class_id)
            display_name = f'class_{class_id}'  # 設定最終預設值
            if class_config:
                # 優先使用 name，其次是 display_name
                raw_display_name = getattr(class_config, 'name', getattr(
                    class_config, 'display_name', f'class_{class_id}'))
                # 🆕 確保顯示名稱經過label_aliases處理，與GT保持一致
                display_name = self.config_manager.resolve_label_alias(
                    raw_display_name)

            # 🔧 新增：轉換座標到原始圖像尺寸
            x1, y1, x2, y2 = bbox
            if resize_ratio != 1.0:
                x1 = int(x1 * scale_factor)
                y1 = int(y1 * scale_factor)
                x2 = int(x2 * scale_factor)
                y2 = int(y2 * scale_factor)
                print(f"   📐 座標轉換: {class_name} ({bbox[0]:.0f},{bbox[1]:.0f},{bbox[2]:.0f},{bbox[3]:.0f}) -> ({x1},{y1},{x2},{y2})")
                
                # 🔧 新增：如果有 points 字段，也需要轉換
                if 'points' in det and det['points'] is not None:
                    original_points = det['points']
                    scaled_points = [[x * scale_factor, y * scale_factor] for x, y in original_points]
                    det['points'] = scaled_points  # 更新 detection 中的 points
                    print(f"   📐 Points座標轉換: {len(original_points)} 個點已縮放")
            else:
                x1, y1, x2, y2 = map(int, bbox)

            # 繪制邊界框
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), color,
                          self.config_manager.visualization.line_thickness)

            # 繪制mask（如果有）
            if mask is not None:
                self.logger.debug(
                    f"開始繪制mask: class={class_name}, mask shape={mask.shape}, dtype={mask.dtype}")
                try:
                    mask_overlay = np.zeros_like(vis_image)

                    # 統一mask處理邏輯，確保正確的維度和尺寸
                    processed_mask = self._process_mask_for_visualization(
                        mask, vis_image.shape[:2])
                    if processed_mask is not None:
                        self.logger.debug(
                            f"Mask處理成功: {class_name}, processed shape={processed_mask.shape}")
                        mask_overlay[processed_mask > 0.5] = color

                        # 應用mask覆蓋
                        vis_image = cv2.addWeighted(vis_image, 1.0, mask_overlay,
                                                    self.config_manager.visualization.fill_alpha, 0)
                        self.logger.debug(f"Mask可視化完成: {class_name}")
                    else:
                        self.logger.warning(f"Mask處理失敗: {class_name}")

                except Exception as e:
                    self.logger.error(
                        f"繪制mask時出錯: {e}, class={class_name}, mask形狀: {mask.shape if mask is not None else 'None'}")
                    # 如果mask處理失敗，跳過mask繪制，只顯示邊界框
            else:
                self.logger.debug(f"檢測結果無mask: {class_name}")

            # 繪制標籤
            label = f"{display_name}: {confidence:.2f}"
            font_scale = self.font_manager.get_cv2_font_scale()
            thickness = self.font_manager.get_cv2_font_thickness()

            (label_w, label_h), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX,
                                                    font_scale, thickness)

            # 繪制標籤背景
            cv2.rectangle(vis_image, (x1, y1 - label_h - 10),
                          (x1 + label_w, y1), color, -1)

            # 繪制標籤文字（黑色字體）
            cv2.putText(vis_image, label, (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX,
                        font_scale, (0, 0, 0), thickness)

            actually_drawn += 1

        # 🔍 輸出詳細統計
        print(f"   📈 過濾統計:")
        print(f"      排除類別過濾: {filtered_by_excluded} 個")
        print(f"      類別未啟用: {filtered_by_disabled} 個")
        print(f"      置信度過濾: {filtered_by_confidence} 個")
        print(f"      ✅ 實際繪制: {actually_drawn} 個")

        # 🧹 清理大型臨時變量
        if 'unified_mask' in locals():
            del unified_mask
        if 'overlay' in locals():
            del overlay

        return vis_image

    def _process_mask_for_visualization(self, mask: np.ndarray, target_shape: Tuple[int, int]) -> Optional[np.ndarray]:
        """
        統一處理mask以用於可視化，修復SAHI後三視圖mask顯示問題

        Args:
            mask: 原始mask數據
            target_shape: 目標圖像尺寸 (height, width)

        Returns:
            處理後的2D mask，或None如果處理失敗
        """
        if mask is None:
            return None

        try:
            # 確保mask是numpy數組
            if not isinstance(mask, np.ndarray):
                mask = np.array(mask)

            # 處理不同維度的mask
            processed_mask = mask

            if mask.ndim == 4:  # 4D mask (batch, height, width, channels)
                # 選擇第一個batch的第一個channel
                processed_mask = mask[0, :, :,
                                      0] if mask.shape[3] > 0 else mask[0, :, :]
            elif mask.ndim == 3:  # 3D mask
                if mask.shape[0] == 1:  # (1, height, width)
                    processed_mask = mask[0]
                elif mask.shape[2] == 1:  # (height, width, 1)
                    processed_mask = mask[:, :, 0]
                else:  # 多channel，取第一個channel或最大值
                    processed_mask = mask[:, :, 0] if mask.shape[2] > 1 else mask.max(
                        axis=2)
            elif mask.ndim == 2:  # 2D mask，理想狀態
                processed_mask = mask
            else:
                self.logger.warning(f"Unsupported mask dimension: {mask.ndim}")
                return None

            # 確保是2D
            if processed_mask.ndim != 2:
                self.logger.warning(
                    f"Failed to convert mask to 2D, current shape: {processed_mask.shape}")
                return None

            # 檢查尺寸是否匹配
            target_h, target_w = target_shape
            if processed_mask.shape != (target_h, target_w):
                # 需要resize
                self.logger.debug(
                    f"Resizing mask from {processed_mask.shape} to {target_shape}")
                processed_mask = cv2.resize(
                    processed_mask.astype(np.float32),
                    (target_w, target_h),
                    interpolation=cv2.INTER_NEAREST
                )

            # 確保mask值在合理範圍內
            if processed_mask.dtype != np.float32:
                processed_mask = processed_mask.astype(np.float32)

            # 規範化到0-1範圍
            if processed_mask.max() > 1.0:
                processed_mask = processed_mask / 255.0

            return processed_mask

        except Exception as e:
            self.logger.error(f"處理mask時發生錯誤: {e}, mask shape: {mask.shape}")
            return None

    def _calculate_class_metrics(self, tp: int, fp: int, fn: int) -> Tuple[float, float, float, float, float]:
        """計算類別性能指標"""
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * (precision * recall) / (precision +
                                         recall) if (precision + recall) > 0 else 0.0
        false_positive_rate = fp / (tp + fp) if (tp + fp) > 0 else 0.0
        false_negative_rate = fn / (tp + fn) if (tp + fn) > 0 else 0.0

        return precision, recall, f1, false_positive_rate, false_negative_rate

    def get_current_statistics_summary(self) -> str:
        """獲取當前統計摘要（用於實時查看進度）"""
        total_images = self.stats['total_images']
        total_detections = self.stats['total_detections']
        avg_time = np.mean(
            self.stats['processing_times']) if self.stats['processing_times'] else 0

        summary = []
        summary.append(f"📊 處理進度統計:")
        summary.append(f"  - 已處理圖像: {total_images} 張")
        summary.append(f"  - 總檢測數量: {total_detections} 個")
        summary.append(f"  - 平均處理時間: {avg_time:.2f} 秒/張")

        if hasattr(self, 'csv_manager') and self.csv_manager.class_metrics_data:
            summary.append(f"  - 檢測類別分布:")
            for class_name, data in self.csv_manager.class_metrics_data.items():
                summary.append(f"    • {class_name}: {data['total']} 個")

        return "\n".join(summary)

    def get_config_summary(self) -> str:
        """獲取配置摘要"""
        return self.config_manager.get_summary()

    def update_class_confidence(self, class_id: int, confidence: Optional[float] = None,
                                sahi_confidence: Optional[float] = None) -> bool:
        """更新類別置信度"""
        return self.config_manager.update_class_confidence(class_id, confidence, sahi_confidence)

    def _draw_roi_and_sahi_grid(self, img, roi_params, sahi_params):
        import cv2
        import numpy as np
        h, w = img.shape[:2]

        # ROI 座標計算
        cx, cy = w // 2, h // 2

        # 計算裁剪因子 (1.0 = 邊緣, 5.0 = 中心)
        # 映射 1.0-5.0 到 0.0-0.5 (0.0 = 無裁剪, 0.5 = 裁剪到中心)
        # 轉換公式: (ratio - 1) * (0.5 / 4) = (ratio - 1) / 8
        crop_factor_top = (roi_params['top'] - 1) / 8
        crop_factor_bottom = (roi_params['bottom'] - 1) / 8
        crop_factor_left = (roi_params['left'] - 1) / 8
        crop_factor_right = (roi_params['right'] - 1) / 8

        # 計算 ROI 座標
        y1_roi = int(h * crop_factor_top)
        y2_roi = int(h - h * crop_factor_bottom)
        x1_roi = int(w * crop_factor_left)
        x2_roi = int(w - w * crop_factor_right)

        # 確保 ROI 座標在圖像範圍內
        y1_roi = max(0, y1_roi)
        y2_roi = min(h, y2_roi)
        x1_roi = max(0, x1_roi)
        x2_roi = min(w, x2_roi)

        # 驗證 ROI 尺寸是否有效，如果無效則不繪製 ROI 相關內容
        if y1_roi >= y2_roi or x1_roi >= x2_roi:
            self.logger.warning(
                f"繪製 ROI 時尺寸無效: y1={y1_roi}, y2={y2_roi}, x1={x1_roi}, x2={x2_roi}。跳過 ROI 繪製。")
            return img  # 返回原始圖像

        preview = img.copy()
        overlay = preview.copy()
        alpha = 0.3  # 透明度

        # 定義顏色 (BGR 格式)
        color_top = (0, 0, 255)      # 紅色
        color_bottom = (255, 0, 0)   # 藍色
        color_left = (0, 255, 255)   # 黃色
        color_right = (255, 255, 0)  # 青色

        # 繪製上下左右四個區域的遮罩
        # 上方區域
        cv2.rectangle(overlay, (0, 0), (w, y1_roi), color_top, -1)
        # 下方區域
        cv2.rectangle(overlay, (0, y2_roi), (w, h), color_bottom, -1)
        # 左方區域 (排除上下重疊部分)
        cv2.rectangle(overlay, (0, y1_roi), (x1_roi, y2_roi), color_left, -1)
        # 右方區域 (排除上下重疊部分)
        cv2.rectangle(overlay, (x2_roi, y1_roi), (w, y2_roi), color_right, -1)

        # 將遮罩疊加到圖像上
        preview = cv2.addWeighted(overlay, alpha, preview, 1 - alpha, 0)

        # 繪製 ROI 綠框（加粗）
        cv2.rectangle(preview, (x1_roi, y1_roi),
                      (x2_roi, y2_roi), (0, 255, 0), 6)

        # SAHI 格線（亮藍色加粗），只在 ROI 內部繪製
        sh, sw = sahi_params['slice_height'], sahi_params['slice_width']
        oh, ow = sahi_params['overlap_height_ratio'], sahi_params['overlap_width_ratio']

        # 計算步長
        step_h = int(sh * (1 - oh))
        step_w = int(sw * (1 - ow))

        # 確保步長至少為1，避免除以零或無限循環
        if step_h <= 0:
            step_h = sh
        if step_w <= 0:
            step_w = sw

        # 遍歷 ROI 區域，繪製 SAHI 切片網格
        for top in range(y1_roi, y2_roi, step_h):
            for left in range(x1_roi, x2_roi, step_w):
                # 計算切片的右下角座標，確保不超出 ROI 邊界
                btm = min(top + sh, y2_roi)
                rgt = min(left + sw, x2_roi)

                # 繪製切片矩形
                cv2.rectangle(preview, (left, top),
                              (rgt, btm), (0, 255, 255), 2)

        return preview

    def _save_roi_preview(self, image_path: str, output_dir: str):
        """產生 ROI 遮罩與 SAHI 格線預覽圖，存到 output/roi_preview.jpg"""
        try:
            # 🆕 修正：只要ROI啟用就生成預覽，不需要SAHI啟用
            if not self._check_roi_enabled():
                print("⚠️ ROI功能未啟用，跳過ROI預覽")
                return
                
            import cv2
            img = cv2.imread(image_path)
            if img is None:
                print(f"❌ 無法讀取圖像進行ROI預覽: {image_path}")
                return
                
            # 獲取ROI參數
            roi_params = {
                'top': getattr(self.config_manager.sahi, 'roi_top_ratio', 2.0),
                'bottom': getattr(self.config_manager.sahi, 'roi_bottom_ratio', 2.0),
                'left': getattr(self.config_manager.sahi, 'roi_left_ratio', 2.0),
                'right': getattr(self.config_manager.sahi, 'roi_right_ratio', 2.0),
            }
            
            # SAHI參數（用於格線顯示，即使不啟用SAHI也可以顯示格線作為參考）
            sahi_params = {
                'slice_height': getattr(self.config_manager.sahi, 'slice_height', 640),
                'slice_width': getattr(self.config_manager.sahi, 'slice_width', 640),
                'overlap_height_ratio': getattr(self.config_manager.sahi, 'overlap_height_ratio', 0.2),
                'overlap_width_ratio': getattr(self.config_manager.sahi, 'overlap_width_ratio', 0.2),
            }
            
            # 生成增強版ROI預覽
            preview = self._draw_enhanced_roi_preview(img, roi_params, sahi_params)
            
            # 確保輸出目錄存在
            out_dir = Path(output_dir)
            out_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存預覽圖
            preview_path = out_dir / "roi_preview.jpg"
            cv2.imwrite(str(preview_path), preview)
            
            print(f"✅ ROI區域預覽圖已生成: {preview_path}")
            print(f"   📊 ROI參數: top={roi_params['top']}, bottom={roi_params['bottom']}, left={roi_params['left']}, right={roi_params['right']}")
            
        except Exception as e:
            print(f"❌ ROI預覽圖生成失敗: {e}")
            import traceback
            traceback.print_exc()

    def _draw_enhanced_roi_preview(self, img, roi_params, sahi_params):
        """
        繪製增強版ROI預覽圖，更清晰地顯示ROI區域
        
        Args:
            img: 原始圖像
            roi_params: ROI參數字典
            sahi_params: SAHI參數字典（用於顯示切片格線）
            
        Returns:
            增強版預覽圖像
        """
        import cv2
        import numpy as np
        
        h, w = img.shape[:2]
        preview = img.copy()
        
        # 計算ROI邊界
        crop_factor_top = (roi_params['top'] - 1) / 8
        crop_factor_bottom = (roi_params['bottom'] - 1) / 8
        crop_factor_left = (roi_params['left'] - 1) / 8
        crop_factor_right = (roi_params['right'] - 1) / 8
        
        y1_roi = int(h * crop_factor_top)
        y2_roi = int(h - h * crop_factor_bottom)
        x1_roi = int(w * crop_factor_left)
        x2_roi = int(w - w * crop_factor_right)
        
        # 確保邊界有效
        y1_roi = max(0, y1_roi)
        y2_roi = min(h, y2_roi)
        x1_roi = max(0, x1_roi)
        x2_roi = min(w, x2_roi)
        
        # 檢查ROI是否有效
        if y1_roi >= y2_roi or x1_roi >= x2_roi:
            print(f"⚠️ ROI尺寸無效: ({x1_roi}, {y1_roi}) -> ({x2_roi}, {y2_roi})")
            return preview
        
        # 創建覆蓋層
        overlay = preview.copy()
        
        # 1. 繪製排除區域（半透明深色）
        excluded_alpha = 0.6
        
        # 上方排除區域（紅色）
        if y1_roi > 0:
            cv2.rectangle(overlay, (0, 0), (w, y1_roi), (0, 0, 128), -1)
        
        # 下方排除區域（藍色）
        if y2_roi < h:
            cv2.rectangle(overlay, (0, y2_roi), (w, h), (128, 0, 0), -1)
        
        # 左方排除區域（黃色），排除上下重疊部分
        if x1_roi > 0:
            cv2.rectangle(overlay, (0, y1_roi), (x1_roi, y2_roi), (0, 128, 128), -1)
        
        # 右方排除區域（青色），排除上下重疊部分
        if x2_roi < w:
            cv2.rectangle(overlay, (x2_roi, y1_roi), (w, y2_roi), (128, 128, 0), -1)
        
        # 混合排除區域
        preview = cv2.addWeighted(preview, 1 - excluded_alpha, overlay, excluded_alpha, 0)
        
        # 2. 繪製ROI邊界（粗綠色邊框）
        roi_border_thickness = max(3, min(w, h) // 200)  # 動態調整邊框粗細
        cv2.rectangle(preview, (x1_roi, y1_roi), (x2_roi, y2_roi), (0, 255, 0), roi_border_thickness)
        
        # 3. 在ROI內部繪製參考格線（可選）
        if sahi_params and roi_params.get('show_grid', True):
            grid_color = (0, 255, 255)  # 黃色格線
            grid_thickness = max(1, roi_border_thickness // 2)
            
            slice_h = sahi_params.get('slice_height', 640)
            slice_w = sahi_params.get('slice_width', 640)
            
            # 在ROI區域內繪製格線
            for y in range(y1_roi, y2_roi, slice_h):
                if y > y1_roi:  # 跳過第一條線（邊界）
                    cv2.line(preview, (x1_roi, y), (x2_roi, y), grid_color, grid_thickness)
            
            for x in range(x1_roi, x2_roi, slice_w):
                if x > x1_roi:  # 跳過第一條線（邊界）
                    cv2.line(preview, (x, y1_roi), (x, y2_roi), grid_color, grid_thickness)
        
        # 4. 添加文字標注
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = max(0.8, min(w, h) / 1000)  # 動態調整字體大小
        font_thickness = max(2, int(font_scale * 2))
        
        # ROI尺寸標注
        roi_width = x2_roi - x1_roi
        roi_height = y2_roi - y1_roi
        roi_text = f"ROI: {roi_width}x{roi_height}"
        
        # 計算文字位置（ROI區域內部的左上角）
        text_x = x1_roi + 10
        text_y = y1_roi + 30
        
        # 繪製文字背景
        text_size = cv2.getTextSize(roi_text, font, font_scale, font_thickness)[0]
        cv2.rectangle(preview, (text_x - 5, text_y - text_size[1] - 5), 
                     (text_x + text_size[0] + 5, text_y + 5), (0, 0, 0), -1)
        
        # 繪製文字
        cv2.putText(preview, roi_text, (text_x, text_y), font, font_scale, 
                   (0, 255, 0), font_thickness, cv2.LINE_AA)
        
        # 5. 添加參數說明
        param_texts = [
            f"Top: {roi_params['top']:.1f}",
            f"Bottom: {roi_params['bottom']:.1f}",
            f"Left: {roi_params['left']:.1f}",
            f"Right: {roi_params['right']:.1f}"
        ]
        
        # 在圖像左下角添加參數說明
        param_start_y = h - 120
        for i, param_text in enumerate(param_texts):
            param_y = param_start_y + i * 25
            
            # 文字背景
            param_size = cv2.getTextSize(param_text, font, font_scale * 0.7, font_thickness)[0]
            cv2.rectangle(preview, (10, param_y - param_size[1] - 3), 
                         (10 + param_size[0] + 6, param_y + 3), (0, 0, 0), -1)
            
            # 文字
            cv2.putText(preview, param_text, (13, param_y), font, font_scale * 0.7, 
                       (255, 255, 255), font_thickness, cv2.LINE_AA)
        
        # 6. 添加標題
        title = "ROI Preview - Green: Active Region"
        title_size = cv2.getTextSize(title, font, font_scale, font_thickness)[0]
        title_x = (w - title_size[0]) // 2
        title_y = 40
        
        # 標題背景
        cv2.rectangle(preview, (title_x - 10, title_y - title_size[1] - 5), 
                     (title_x + title_size[0] + 10, title_y + 5), (0, 0, 0), -1)
        
        # 標題文字
        cv2.putText(preview, title, (title_x, title_y), font, font_scale, 
                   (0, 255, 0), font_thickness, cv2.LINE_AA)
        
        return preview

    def _check_roi_enabled(self) -> bool:
        """檢查是否啟用ROI功能"""
        try:
            # 檢查是否啟用ROI處理的條件
            # 1. 配置中有enable_roi_processing且為True
            if hasattr(self.config_manager, 'paths') and hasattr(self.config_manager.paths, 'enable_roi_processing'):
                return getattr(self.config_manager.paths, 'enable_roi_processing', False)
            
            # 2. 或者透過SAHI配置啟用ROI
            if hasattr(self.config_manager, 'sahi'):
                sahi_roi_enabled = getattr(self.config_manager.sahi, 'enable_sahi_roi', False)
                if sahi_roi_enabled:
                    # 檢查ROI參數是否有效（不是默認的1.0值）
                    roi_top = getattr(self.config_manager.sahi, 'roi_top_ratio', 1.0)
                    roi_bottom = getattr(self.config_manager.sahi, 'roi_bottom_ratio', 1.0)
                    roi_left = getattr(self.config_manager.sahi, 'roi_left_ratio', 1.0)
                    roi_right = getattr(self.config_manager.sahi, 'roi_right_ratio', 1.0)
                    
                    # 如果有任何非1.0的比例，則認為啟用了ROI
                    if any(ratio != 1.0 for ratio in [roi_top, roi_bottom, roi_left, roi_right]):
                        return True
                        
            return False
        except Exception as e:
            self.logger.warning(f"檢查ROI啟用狀態失敗: {e}")
            return False

    def _apply_roi_cropping(self, image: np.ndarray) -> Tuple[np.ndarray, Optional[Tuple[int, int]]]:
        """對圖像應用ROI裁切
        
        Args:
            image: 輸入圖像
            
        Returns:
            Tuple[cropped_image, roi_offset]: 裁切後的圖像和ROI偏移量(x_offset, y_offset)
        """
        try:
            if not self._check_roi_enabled():
                return image, None
                
            h, w = image.shape[:2]
            
            # 從配置獲取ROI參數
            if hasattr(self.config_manager, 'sahi'):
                roi_top_ratio = getattr(self.config_manager.sahi, 'roi_top_ratio', 2.0)
                roi_bottom_ratio = getattr(self.config_manager.sahi, 'roi_bottom_ratio', 2.0)
                roi_left_ratio = getattr(self.config_manager.sahi, 'roi_left_ratio', 2.0)
                roi_right_ratio = getattr(self.config_manager.sahi, 'roi_right_ratio', 2.0)
            else:
                # 默認ROI參數
                roi_top_ratio = roi_bottom_ratio = roi_left_ratio = roi_right_ratio = 2.0
            
            # 計算ROI裁切邊界（使用相同的邏輯作為預覽）
            crop_factor_top = (roi_top_ratio - 1) / 8
            crop_factor_bottom = (roi_bottom_ratio - 1) / 8
            crop_factor_left = (roi_left_ratio - 1) / 8
            crop_factor_right = (roi_right_ratio - 1) / 8
            
            # 計算ROI邊界
            y1_roi = int(h * crop_factor_top)
            y2_roi = int(h - h * crop_factor_bottom)
            x1_roi = int(w * crop_factor_left)
            x2_roi = int(w - w * crop_factor_right)
            
            # 確保邊界有效
            y1_roi = max(0, y1_roi)
            y2_roi = min(h, y2_roi)
            x1_roi = max(0, x1_roi)
            x2_roi = min(w, x2_roi)
            
            # 確保ROI有有效尺寸
            if y2_roi <= y1_roi or x2_roi <= x1_roi:
                self.logger.warning(f"ROI尺寸無效: ({x1_roi}, {y1_roi}, {x2_roi}, {y2_roi}), 使用原圖")
                return image, None
            
            # 裁切圖像
            cropped_image = image[y1_roi:y2_roi, x1_roi:x2_roi]
            roi_offset = (x1_roi, y1_roi)
            
            self.logger.info(f"ROI裁切完成: 原圖({w}x{h}) -> ROI({x2_roi-x1_roi}x{y2_roi-y1_roi}), offset=({x1_roi}, {y1_roi})")
            
            return cropped_image, roi_offset
            
        except Exception as e:
            self.logger.error(f"ROI裁切失敗: {e}")
            return image, None

    def _convert_ultralytics_result(self, result, image_path, actual_image_shape=None, roi_offset=None):
        """
        將 ultralytics YOLO 推理結果轉換為標準格式
        包含正確的座標轉換以匹配原圖尺寸
        
        Args:
            result: YOLO推理結果
            image_path: 圖像路徑
            actual_image_shape: 實際推理時使用的圖像尺寸 (height, width)
            roi_offset: ROI偏移量 (x_offset, y_offset)
        """
        detections = []

        # 獲取目標尺寸（原圖尺寸）
        import cv2
        original_image = cv2.imread(image_path)
        if original_image is None:
            self.logger.error(f"無法讀取原圖: {image_path}")
            return {'image_path': image_path, 'detections': []}

        original_height, original_width = original_image.shape[:2]
        
        # 🟢 使用實際推理時的圖像尺寸（如果提供）
        if actual_image_shape is not None:
            inference_height, inference_width = actual_image_shape
            self.logger.debug(f"🟢 使用實際推理尺寸: {inference_width}x{inference_height}")
        else:
            # 回落到原始方式
            inference_height, inference_width = result.orig_shape
            self.logger.debug(f"⚠️ 使用result.orig_shape: {inference_width}x{inference_height}")

        if hasattr(result, 'boxes') and result.boxes is not None:
            boxes = result.boxes.xyxy.cpu().numpy()
            confidences = result.boxes.conf.cpu().numpy()
            class_ids = result.boxes.cls.cpu().numpy().astype(int)
            masks = None

            # 計算縮放比例：從推理尺寸到原圖尺寸
            scale_x = original_width / inference_width
            scale_y = original_height / inference_height

            self.logger.debug(
                f"🟢 座標縮放: 原圖({original_width}x{original_height}) <- 推理圖({inference_width}x{inference_height})")
            self.logger.debug(
                f"🟢 縮放比例: scale_x={scale_x:.3f}, scale_y={scale_y:.3f}")
            if roi_offset:
                self.logger.debug(f"🟢 ROI偏移: {roi_offset}")

            if hasattr(result, 'masks') and result.masks is not None:
                masks = result.masks.data.cpu().numpy()

            for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                mask = None
                if masks is not None and i < len(masks):
                    mask = masks[i]
                    if mask.ndim == 3:
                        mask = mask[0]

                    # 🔧 關鍵修復：將mask縮放回原圖尺寸
                    if mask.shape != (original_height, original_width):
                        mask_resized = cv2.resize(
                            mask,
                            (original_width, original_height),
                            interpolation=cv2.INTER_NEAREST
                        )
                        mask = mask_resized
                        self.logger.debug(
                            f"Mask縮放: {masks[i].shape} -> {mask.shape}")

                    if mask.dtype != np.uint8:
                        mask = (mask * 255).astype(np.uint8)

                # 🟢 縮放bbox座標到原圖尺寸
                scaled_box = [
                    box[0] * scale_x,  # x1
                    box[1] * scale_y,  # y1
                    box[2] * scale_x,  # x2
                    box[3] * scale_y   # y2
                ]
                
                # 🟢 如果有ROI偏移，加上偏移量
                if roi_offset:
                    x_offset, y_offset = roi_offset
                    scaled_box = [
                        scaled_box[0] + x_offset,  # x1
                        scaled_box[1] + y_offset,  # y1
                        scaled_box[2] + x_offset,  # x2
                        scaled_box[3] + y_offset   # y2
                    ]
                
                # 🟢 座標驗證：確保座標在合理範圍內
                scaled_box = [
                    max(0, min(scaled_box[0], original_width)),   # x1
                    max(0, min(scaled_box[1], original_height)),  # y1
                    max(0, min(scaled_box[2], original_width)),   # x2
                    max(0, min(scaled_box[3], original_height))   # y2
                ]
                
                # 檢查bbox有效性
                if scaled_box[2] <= scaled_box[0] or scaled_box[3] <= scaled_box[1]:
                    self.logger.warning(f"⚠️ 無效bbox跳過: {scaled_box}")
                    continue

                # 🔧 修復：確保class_name經過label_aliases處理，與GT保持一致
                raw_class_name = self.model.names.get(
                    cls_id, f'class_{cls_id}')
                processed_class_name = self.config_manager.resolve_label_alias(
                    raw_class_name)

                detections.append({
                    'bbox': scaled_box,
                    'confidence': float(conf),
                    'class_id': int(cls_id),
                    'class_name': processed_class_name,  # 🆕 使用經過label_aliases處理的名稱
                    'mask': mask,
                    # 🆕 添加原圖尺寸信息
                    'original_size': (original_width, original_height),
                    'scale_factors': (scale_x, scale_y)  # 🆕 添加縮放比例信息
                })

        # 🧹 清理臨時變量以釋放記憶體
        del original_image
        if 'boxes' in locals():
            del boxes
        if 'masks' in locals() and masks is not None:
            del masks

        # 🧹 額外的記憶體清理：確保結果對象也被適當管理
        if hasattr(result, 'boxes') and result.boxes is not None:
            # ultralytics結果對象的清理
            del result.boxes
        if hasattr(result, 'masks') and result.masks is not None:
            del result.masks

        result_dict = {
            'image_path': image_path,
            'detections': detections,
            'original_size': (original_width, original_height),
            'coordinate_transform_info': {
                'original_size': (original_width, original_height),
                'inference_size': (inference_width, inference_height),
                'scale_factors': (scale_x, scale_y),
                'roi_offset': roi_offset,
                'has_roi': roi_offset is not None
            }
        }
        
        self.logger.debug(f"🟢 座標轉換完成: {len(detections)}個檢測")
        return result_dict


class GroundTruthLoader:
    """GT數據加載器"""

    def __init__(self, config_manager):
        """初始化GT加載器"""
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)

    def load_ground_truth(self, image_path: str) -> Optional[List[Dict]]:
        """加載GT標註數據"""
        try:
            image_name = Path(image_path).stem

            # 尋找對應的JSON文件
            if hasattr(self.config_manager, 'ground_truth') and self.config_manager.ground_truth.gt_path:
                json_path = Path(
                    self.config_manager.ground_truth.gt_path) / f"{image_name}.json"
            else:
                json_path = Path(image_path).parent / f"{image_name}.json"

            if not json_path.exists():
                return None

            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 轉換LabelMe格式為統一格式
            gt_data = []
            if 'shapes' in data:
                for shape in data['shapes']:
                    gt_item = {
                        'label': shape['label'],
                        'points': shape['points'],
                        'shape_type': shape.get('shape_type', 'polygon')
                    }
                    gt_data.append(gt_item)

            return gt_data

        except Exception as e:
            self.logger.debug(f"加載GT數據失敗: {e}")
            return None

    def scan_json_labels(self, json_dir: str) -> Dict:
        """掃描JSON標籤目錄"""
        try:
            json_path = Path(json_dir)
            if not json_path.exists():
                return {}

            label_stats = {}
            file_count = 0

            for json_file in json_path.glob("*.json"):
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    file_count += 1
                    if 'shapes' in data:
                        for shape in data['shapes']:
                            label = shape['label']
                            if label not in label_stats:
                                label_stats[label] = 0
                            label_stats[label] += 1

                except Exception as e:
                    self.logger.debug(f"讀取JSON文件失敗 {json_file}: {e}")

            return {
                'file_count': file_count,
                'label_stats': label_stats,
                'total_annotations': sum(label_stats.values())
            }

        except Exception as e:
            self.logger.error(f"掃描JSON標籤失敗: {e}")
            return {}

    def display_json_labels_summary(self, labels_stats: Dict) -> str:
        """顯示JSON標籤摘要"""
        if not labels_stats:
            return "📊 JSON標籤掃描結果: 無數據"

        summary = f"""📊 JSON標籤掃描結果:
📁 JSON文件數量: {labels_stats.get('file_count', 0)}
🏷️ 總標註數量: {labels_stats.get('total_annotations', 0)}

📈 各類別標註統計:"""

        label_stats = labels_stats.get('label_stats', {})
        if label_stats:
            for label, count in sorted(label_stats.items(), key=lambda x: x[1], reverse=True):
                summary += f"\n  🔸 {label}: {count} 個"
        else:
            summary += "\n  (無標註數據)"

        return summary


# 便利函數
def create_unified_yolo_inference(config_path: Optional[str] = None) -> UnifiedYOLOInference:
    """創建統一YOLO推理實例"""
    return UnifiedYOLOInference(config_path)


if __name__ == "__main__":
    # 測試代碼（增強版：支持JSON掃描）
    import argparse

    parser = argparse.ArgumentParser(
        description="統一YOLO推理引擎測試（增強版：支持JSON標籤掃描）")
    parser.add_argument("--config", type=str, help="配置文件路徑")
    parser.add_argument("--input", type=str, required=True, help="輸入圖像或目錄")
    parser.add_argument("--output", type=str, required=True, help="輸出目錄")
    parser.add_argument("--batch", action="store_true", help="批次處理模式")
    parser.add_argument("--json-dir", type=str, help="JSON標註目錄（用於預掃描標籤信息）")
    parser.add_argument("--scan-only", action="store_true",
                        help="僅掃描JSON標籤，不執行推理")

    args = parser.parse_args()

    # 創建推理引擎
    inference = create_unified_yolo_inference(args.config)

    print("🚀 統一YOLO推理引擎 - 增強版")
    print("="*50)
    print(inference.get_config_summary())

    # 如果只是掃描JSON
    if args.scan_only and args.json_dir:
        print("\n🔍 執行JSON標籤掃描...")
        labels_stats = inference.scan_json_labels(args.json_dir)
        summary = inference.display_json_labels_summary(labels_stats)
        print(summary)

        # 保存掃描報告
        from pathlib import Path
        output_path = Path(args.output)
        output_path.mkdir(parents=True, exist_ok=True)

        scan_report_path = output_path / "json_labels_scan_report.txt"
        with open(scan_report_path, 'w', encoding='utf-8') as f:
            f.write(summary)
        print(f"\n📄 掃描報告已保存: {scan_report_path}")

    # 執行推理
    print("\n🚀 開始執行推理...")
    if args.batch:
        results = inference.predict_batch(
            args.input, args.output, args.json_dir)
        print(f"\n🎉 批次處理完成！共處理 {len(results)} 張圖像")
    else:
        # 單張圖像處理時，如果提供了json_dir，先掃描一下
        if args.json_dir:
            print(f"\n🔍 掃描JSON標籤目錄: {args.json_dir}")
            labels_stats = inference.scan_json_labels(args.json_dir)
            summary = inference.display_json_labels_summary(labels_stats)
            print(summary)

        result = inference.predict_single_image(args.input, args.output)
        print(f"\n🎉 單張處理完成！檢測到 {len(result['detections'])} 個目標")

        # 顯示檢測結果詳情
        if result['detections']:
            print("\n📊 檢測結果詳情:")
            for i, det in enumerate(result['detections'], 1):
                mask_info = "有mask" if det.get('mask') is not None else "無mask"
                print(
                    f"  {i}. {det['class_name']} - 信心度: {det['confidence']:.3f} ({mask_info})")

    print("\n✅ 程序執行完成！")
