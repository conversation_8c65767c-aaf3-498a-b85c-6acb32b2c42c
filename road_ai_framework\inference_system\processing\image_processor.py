#!/usr/bin/env python3
"""
🖼️ 圖像處理器
提供圖像縮放、ROI處理等功能
"""

import cv2
import numpy as np
import logging
from typing import Tuple, Dict, Any, Optional, List
from pathlib import Path

from ..config import ImageProcessingConfig, ROIConfig


class ImageProcessor:
    """
    圖像處理器
    
    功能:
    - 圖像縮放
    - ROI區域定義和預覽
    - 座標轉換
    """
    
    def __init__(self, image_config: ImageProcessingConfig, roi_config: ROIConfig):
        """
        初始化圖像處理器
        
        Args:
            image_config: 圖像處理配置
            roi_config: ROI配置
        """
        self.image_config = image_config
        self.roi_config = roi_config
        self.logger = logging.getLogger(__name__)
        
        # 記錄縮放比例
        self.current_scale = 1.0
        
        # ROI設定狀態
        self.roi_initialized = False
        self.roi_reference_shape = None  # ROI設定時的圖像尺寸
        self._roi_cache = None  # 緩存ROI遮罩
        self._roi_relative_coords = None  # 相對座標比例（用於適配不同尺寸圖像）
        
    def resize_image(self, image: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        縮放圖像
        
        Args:
            image: 輸入圖像
            
        Returns:
            Tuple[np.ndarray, float]: (縮放後圖像, 縮放比例)
        """
        if not self.image_config.enable_resize:
            return image, 1.0
            
        scale = self.image_config.input_scale
        if scale == 1.0:
            return image, 1.0
            
        h, w = image.shape[:2]
        new_w = int(w * scale)
        new_h = int(h * scale)
        
        # 🧠 使用記憶體高效的縮放方式
        try:
            resized_image = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
            self.current_scale = scale
            self.logger.debug(f"🖼️ 圖像縮放: {w}x{h} → {new_w}x{new_h} (比例: {scale:.2f})")
            
            # 🗑️ 明確釋放原始圖像引用(如果是副本)
            if id(image) != id(resized_image):
                del image  # 提示垃圾回收器可以回收
                
            return resized_image, scale
            
        except Exception as e:
            self.logger.error(f"❌ 圖像縮放失敗: {e}")
            return image, 1.0
    
    def calculate_roi_coordinates(self, image_shape: Tuple[int, int]) -> Tuple[int, int, int, int]:
        """
        計算ROI座標 (0-5範圍，5是圖像中心)
        
        Args:
            image_shape: 圖像形狀 (height, width)
            
        Returns:
            Tuple[int, int, int, int]: (top, bottom, left, right) 像素座標
        """
        h, w = image_shape
        margins = self.roi_config.margins
        
        # 將0-5範圍轉換為像素座標
        # 0 = 邊緣, 5 = 中心 (50%)
        def margin_to_pixels(margin_value: float, dimension: int) -> int:
            # margin_value範圍: 0.0-5.0
            # 轉換為比例: 0.0-0.5 (從邊緣到中心)
            ratio = min(max(margin_value, 0.0), 5.0) / 10.0  # 0.0-0.5
            return int(dimension * ratio)
        
        top = margin_to_pixels(margins['top'], h)
        bottom = h - margin_to_pixels(margins['bottom'], h)
        left = margin_to_pixels(margins['left'], w)
        right = w - margin_to_pixels(margins['right'], w)
        
        # 確保座標有效
        top = max(0, min(top, h // 2))
        bottom = max(h // 2, min(bottom, h))
        left = max(0, min(left, w // 2))
        right = max(w // 2, min(right, w))
        
        self.logger.debug(f"🎯 ROI座標: top={top}, bottom={bottom}, left={left}, right={right}")
        
        return top, bottom, left, right
    
    def create_roi_mask(self, image_shape: Tuple[int, int]) -> np.ndarray:
        """
        創建ROI遮罩
        
        Args:
            image_shape: 圖像形狀 (height, width)
            
        Returns:
            np.ndarray: ROI遮罩 (True為檢測區域)
        """
        h, w = image_shape
        
        if not self.roi_config.enabled:
            # ROI未啟用，整個圖像都是檢測區域
            mask = np.ones((h, w), dtype=bool)
            return mask
        
        # 檢查是否需要初始化ROI
        if not self.roi_initialized or self.roi_reference_shape != image_shape:
            self._initialize_roi(image_shape)
        
        # 如果圖像尺寸與參考尺寸相同，直接返回緩存的遮罩
        if image_shape == self.roi_reference_shape and self._roi_cache is not None:
            return self._roi_cache.copy()
        
        # 如果尺寸不同，優先使用相對座標重新計算
        if self._roi_relative_coords is not None:
            # 使用相對座標計算新的ROI區域
            top = int(h * self._roi_relative_coords['top_ratio'])
            bottom = int(h * self._roi_relative_coords['bottom_ratio'])
            left = int(w * self._roi_relative_coords['left_ratio'])
            right = int(w * self._roi_relative_coords['right_ratio'])
            
            # 確保座標在有效範圍內
            top = max(0, min(top, h-1))
            bottom = max(top+1, min(bottom, h))
            left = max(0, min(left, w-1))
            right = max(left+1, min(right, w))
            
            # 創建新的遮罩
            mask = np.zeros((h, w), dtype=bool)
            mask[top:bottom, left:right] = True
            
            self.logger.debug(f"🎯 使用相對座標適配ROI: 尺寸{w}x{h}, 區域({left},{top})-({right},{bottom})")
            return mask
        
        # 備用方案：縮放現有遮罩
        if self._roi_cache is not None:
            scaled_mask = cv2.resize(
                self._roi_cache.astype(np.uint8), 
                (w, h), 
                interpolation=cv2.INTER_NEAREST
            ).astype(bool)
            self.logger.debug(f"🎯 使用遮罩縮放適配ROI: 尺寸{w}x{h}")
            return scaled_mask
        
        # 備用方案：重新計算
        mask = np.zeros((h, w), dtype=bool)
        top, bottom, left, right = self.calculate_roi_coordinates(image_shape)
        mask[top:bottom, left:right] = True
        
        return mask
    
    def _initialize_roi(self, image_shape: Tuple[int, int]):
        """初始化ROI設定"""
        h, w = image_shape
        
        # 計算ROI座標
        top, bottom, left, right = self.calculate_roi_coordinates(image_shape)
        
        # 創建ROI遮罩
        mask = np.zeros((h, w), dtype=bool)
        mask[top:bottom, left:right] = True
        
        # 緩存ROI設定
        self._roi_cache = mask
        self.roi_reference_shape = image_shape
        self.roi_initialized = True
        
        # 🎯 計算並存儲相對座標比例（用於不同尺寸圖像的適配）
        self._roi_relative_coords = {
            'top_ratio': top / h,
            'bottom_ratio': bottom / h,
            'left_ratio': left / w,
            'right_ratio': right / w
        }
        
        self.logger.info(f"🎯 ROI已初始化: 圖像尺寸{w}x{h}, 檢測區域({left},{top})-({right},{bottom})")
        self.logger.debug(f"   相對座標: top={self._roi_relative_coords['top_ratio']:.3f}, bottom={self._roi_relative_coords['bottom_ratio']:.3f}, left={self._roi_relative_coords['left_ratio']:.3f}, right={self._roi_relative_coords['right_ratio']:.3f}")
        
        # 🌈 生成ROI預覽圖像（如果啟用）
        if self.roi_config.enable_preview:
            self._create_roi_preview(image_shape)
    
    def _create_roi_preview(self, image_shape: Tuple[int, int]) -> Optional[np.ndarray]:
        """
        創建ROI預覽圖像，用不同顏色顯示上下左右邊界區域
        
        Args:
            image_shape: 圖像形狀 (height, width)
            
        Returns:
            Optional[np.ndarray]: ROI預覽圖像，失敗返回None
        """
        try:
            h, w = image_shape
            
            # 創建預覽圖像（彩色）
            preview = np.zeros((h, w, 3), dtype=np.uint8)
            
            # 獲取ROI座標
            top, bottom, left, right = self.calculate_roi_coordinates(image_shape)
            
            # 獲取顏色配置
            colors = self.roi_config.preview_colors
            
            # 📍 上邊界區域 (紅色)
            if top > 0:
                preview[0:top, :] = colors['top']  # [255, 0, 0]
            
            # 📍 下邊界區域 (綠色)
            if bottom < h:
                preview[bottom:h, :] = colors['bottom']  # [0, 255, 0]
            
            # 📍 左邊界區域 (藍色) - 僅中間區域（避免與上下重疊）
            if left > 0:
                preview[top:bottom, 0:left] = colors['left']  # [0, 0, 255]
            
            # 📍 右邊界區域 (黃色) - 僅中間區域（避免與上下重疊）
            if right < w:
                preview[top:bottom, right:w] = colors['right']  # [255, 255, 0]
            
            # 🎯 中心檢測區域保持透明（黑色）
            # preview[top:bottom, left:right] = [0, 0, 0]  # 黑色，表示檢測區域
            
            # 添加邊框線條以清楚劃分區域
            line_thickness = 2
            
            # 頂部邊界線
            if top > 0:
                cv2.line(preview, (0, top), (w, top), (255, 255, 255), line_thickness)
            
            # 底部邊界線
            if bottom < h:
                cv2.line(preview, (0, bottom), (w, bottom), (255, 255, 255), line_thickness)
            
            # 左側邊界線
            if left > 0:
                cv2.line(preview, (left, 0), (left, h), (255, 255, 255), line_thickness)
            
            # 右側邊界線
            if right < w:
                cv2.line(preview, (right, 0), (right, h), (255, 255, 255), line_thickness)
            
            # 添加文字標籤
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.8
            font_thickness = 2
            
            # 標記各區域
            if top > 20:
                cv2.putText(preview, "TOP", (w//2-30, top//2), font, font_scale, (255, 255, 255), font_thickness)
            if h - bottom > 20:
                cv2.putText(preview, "BOTTOM", (w//2-50, bottom + (h-bottom)//2), font, font_scale, (255, 255, 255), font_thickness)
            if left > 50:
                cv2.putText(preview, "LEFT", (left//2-30, h//2), font, font_scale, (255, 255, 255), font_thickness)
            if w - right > 50:
                cv2.putText(preview, "RIGHT", (right + (w-right)//2-40, h//2), font, font_scale, (255, 255, 255), font_thickness)
            
            # 中心區域標記
            center_x, center_y = (left + right) // 2, (top + bottom) // 2
            if right - left > 100 and bottom - top > 40:
                cv2.putText(preview, "DETECTION AREA", (center_x-80, center_y), font, font_scale*0.7, (255, 255, 255), font_thickness)
            
            self.logger.info(f"🌈 ROI預覽圖像已生成: {w}x{h}")
            return preview
            
        except Exception as e:
            self.logger.error(f"❌ 生成ROI預覽失敗: {e}")
            return None
    
    def create_sahi_roi_preview(self, image_shape: Tuple[int, int], slice_height: int, slice_width: int, overlap_ratio: float) -> Optional[np.ndarray]:
        """
        創建SAHI切片的ROI預覽圖像，顯示切片和重疊區域
        
        Args:
            image_shape: 圖像形狀 (height, width)
            slice_height: 切片高度
            slice_width: 切片寬度
            overlap_ratio: 重疊比例
            
        Returns:
            Optional[np.ndarray]: SAHI ROI預覽圖像
        """
        try:
            h, w = image_shape
            
            # 創建預覽圖像
            preview = self._create_roi_preview(image_shape)
            if preview is None:
                preview = np.zeros((h, w, 3), dtype=np.uint8)
            
            # 計算重疊像素
            overlap_h = int(slice_height * overlap_ratio)
            overlap_w = int(slice_width * overlap_ratio)
            
            # 獲取ROI座標
            top, bottom, left, right = self.calculate_roi_coordinates(image_shape)
            
            # 計算ROI區域內的切片
            roi_width = right - left
            roi_height = bottom - top
            
            # 計算切片網格
            step_h = slice_height - overlap_h
            step_w = slice_width - overlap_w
            
            # 在ROI區域內繪製切片網格
            slice_color = (128, 128, 255)  # 淺紫色
            overlap_color = (255, 128, 128)  # 淺紅色
            grid_thickness = 1
            
            # 垂直切片線
            x = left
            while x < right:
                if x + slice_width <= right:
                    # 切片邊界
                    cv2.line(preview, (x, top), (x, bottom), slice_color, grid_thickness)
                    # 重疊區域（如果不是最後一個切片）
                    if x + slice_width < right:
                        overlap_x = x + slice_width - overlap_w
                        cv2.line(preview, (overlap_x, top), (overlap_x, bottom), overlap_color, grid_thickness)
                x += step_w
            
            # 水平切片線
            y = top
            while y < bottom:
                if y + slice_height <= bottom:
                    # 切片邊界
                    cv2.line(preview, (left, y), (right, y), slice_color, grid_thickness)
                    # 重疊區域（如果不是最後一個切片）
                    if y + slice_height < bottom:
                        overlap_y = y + slice_height - overlap_h
                        cv2.line(preview, (left, overlap_y), (right, overlap_y), overlap_color, grid_thickness)
                y += step_h
            
            # 添加SAHI信息文字
            info_text = f"SAHI: {slice_width}x{slice_height}, Overlap: {overlap_ratio:.1%}"
            cv2.putText(preview, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # 統計切片數量
            num_slices_w = max(1, (roi_width - slice_width) // step_w + 1)
            num_slices_h = max(1, (roi_height - slice_height) // step_h + 1)
            total_slices = num_slices_w * num_slices_h
            
            slice_info = f"Slices: {num_slices_w}x{num_slices_h} = {total_slices}"
            cv2.putText(preview, slice_info, (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            self.logger.info(f"🧩 SAHI ROI預覽已生成: {total_slices}個切片 ({num_slices_w}x{num_slices_h})")
            return preview
            
        except Exception as e:
            self.logger.error(f"❌ 生成SAHI ROI預覽失敗: {e}")
            return None
    
    def save_roi_preview(self, preview_image: np.ndarray, save_path: str) -> bool:
        """
        保存ROI預覽圖像
        
        Args:
            preview_image: 預覽圖像
            save_path: 保存路徑
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 確保目錄存在
            Path(save_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 保存圖像
            success = cv2.imwrite(save_path, preview_image)
            
            if success:
                self.logger.info(f"💾 ROI預覽已保存: {save_path}")
                return True
            else:
                self.logger.error(f"❌ ROI預覽保存失敗: {save_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 保存ROI預覽時發生錯誤: {e}")
            return False
    
    def reset_roi(self):
        """重置ROI設定（用於處理新的圖像序列）"""
        # 🗑️ 清理緩存數據以釋放記憶體
        if self._roi_cache is not None:
            del self._roi_cache
            self._roi_cache = None
        
        self.roi_initialized = False
        self.roi_reference_shape = None
        self._roi_relative_coords = None
        
        # 🧠 強制垃圾回收以釋放ROI相關記憶體
        import gc
        gc.collect()
        
        self.logger.debug("🎯 ROI設定已重置，緩存已清理")
    
    def create_roi_preview(self, image: np.ndarray, show_sahi_slices: bool = False, slice_height: int = 512, slice_width: int = 512, overlap_ratio: float = 0.2) -> np.ndarray:
        """
        創建ROI預覽圖像
        
        Args:
            image: 輸入圖像
            show_sahi_slices: 是否顯示SAHI切片資訊
            slice_height: SAHI切片高度
            slice_width: SAHI切片寬度 
            overlap_ratio: SAHI重疊比例
            
        Returns:
            np.ndarray: 帶ROI標示的預覽圖像
        """
        if not self.roi_config.enabled or not self.roi_config.enable_preview:
            return image.copy()
        
        preview = image.copy()
        h, w = image.shape[:2]
        
        # 🎯 獲取ROI座標（使用相對座標如果已初始化）
        if self._roi_relative_coords is not None:
            # 使用第一張圖像設定的相對座標比例
            top = int(h * self._roi_relative_coords['top_ratio'])
            bottom = int(h * self._roi_relative_coords['bottom_ratio'])
            left = int(w * self._roi_relative_coords['left_ratio'])
            right = int(w * self._roi_relative_coords['right_ratio'])
            
            # 確保座標有效
            top = max(0, min(top, h-1))
            bottom = max(top+1, min(bottom, h))
            left = max(0, min(left, w-1))
            right = max(left+1, min(right, w))
        else:
            # 使用配置計算ROI座標
            top, bottom, left, right = self.calculate_roi_coordinates((h, w))
        
        # 創建半透明覆蓋層
        overlay = preview.copy()
        colors = getattr(self.roi_config, 'preview_colors', {
            'top': [100, 100, 100],
            'bottom': [100, 100, 100], 
            'left': [100, 100, 100],
            'right': [100, 100, 100]
        })
        
        # 繪製各個邊界區域
        # 上邊界
        if top > 0:
            overlay[0:top, :] = colors.get('top', [100, 100, 100])
        
        # 下邊界
        if bottom < h:
            overlay[bottom:h, :] = colors.get('bottom', [100, 100, 100])
        
        # 左邊界
        if left > 0:
            overlay[top:bottom, 0:left] = colors.get('left', [100, 100, 100])
        
        # 右邊界
        if right < w:
            overlay[top:bottom, right:w] = colors.get('right', [100, 100, 100])
        
        # 混合原圖和覆蓋層
        alpha = 0.3  # 透明度
        preview = cv2.addWeighted(preview, 1 - alpha, overlay, alpha, 0)
        
        # 繪製ROI邊界線
        cv2.rectangle(preview, (left, top), (right, bottom), (255, 255, 255), 2)
        
        # 🧩 顯示SAHI切片資訊（如果啟用）
        # 條件：用戶要求顯示SAHI切片 或者 ROI推理模式是sahi
        show_slices = (show_sahi_slices or 
                      (self.roi_config.enabled and self.roi_config.inference_mode == "sahi"))
        
        if show_slices:
            try:
                # 獲取SAHI切片資訊
                sahi_slices = self.get_roi_sahi_slices(
                    image_shape=(h, w),
                    slice_height=slice_height,
                    slice_width=slice_width,
                    overlap_ratio=overlap_ratio
                )
                
                self.logger.info(f"🧩 ROI預覽中顯示 {len(sahi_slices)} 個SAHI切片")
                
                # 繪製切片邊界
                for slice_info in sahi_slices:
                    slice_x = slice_info['x']
                    slice_y = slice_info['y']
                    slice_w = slice_info['width']
                    slice_h = slice_info['height']
                    slice_id = slice_info['slice_id']
                    
                    # 繪製切片邊框（青色，較細線條）
                    cv2.rectangle(preview, 
                                (slice_x, slice_y), 
                                (slice_x + slice_w, slice_y + slice_h), 
                                (0, 255, 255), 1)
                    
                    # 添加切片編號 - 更大字體，更清楚
                    text = f"S{slice_id}"
                    text_position = (slice_x + 5, slice_y + 25)
                    cv2.putText(preview, text, text_position, 
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
                
                # SAHI 切片數量將在配置面板中顯示，這裡保留為後備
                if len(sahi_slices) > 0:
                    sahi_info = f"Generated {len(sahi_slices)} SAHI slices"
                    cv2.putText(preview, sahi_info, (left + 10, top + 60), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)
                
            except Exception as e:
                self.logger.warning(f"⚠️ SAHI切片資訊顯示失敗: {e}")
        
        # 🎯 在左上角添加詳細配置信息面板
        # 如果顯示SAHI切片，先獲取切片數量
        slice_count = 0
        if show_slices:
            try:
                temp_slices = self.get_roi_sahi_slices((h, w), slice_height, slice_width, overlap_ratio)
                slice_count = len(temp_slices)
            except:
                slice_count = 0
        
        self._draw_config_panel(preview, h, w, show_slices, slice_height, slice_width, overlap_ratio, slice_count)
        
        # 在ROI區域添加簡要說明
        roi_text = "ROI Detection Area"
        if self._roi_relative_coords is not None:
            roi_text += " (Fixed from 1st image)"
        
        cv2.putText(preview, roi_text, (left + 10, top + 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # 在ROI底部顯示尺寸
        roi_size = f"ROI Size: {right-left}x{bottom-top}px"
        cv2.putText(preview, roi_size, (left + 10, bottom - 15), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        return preview
    
    def _draw_config_panel(self, image: np.ndarray, img_h: int, img_w: int, 
                          show_slices: bool, slice_height: int, slice_width: int, overlap_ratio: float, slice_count: int = 0):
        """
        在左上角繪製配置信息面板
        
        Args:
            image: 預覽圖像
            img_h: 圖像高度
            img_w: 圖像寬度
            show_slices: 是否顯示切片
            slice_height: 切片高度
            slice_width: 切片寬度
            overlap_ratio: 重疊比例
        """
        # 配置面板設定
        panel_width = 320
        panel_height = 220  # 增加高度以容納更多信息
        panel_x = 10
        panel_y = 10
        
        # 創建半透明背景
        overlay = image.copy()
        cv2.rectangle(overlay, (panel_x, panel_y), 
                     (panel_x + panel_width, panel_y + panel_height), 
                     (0, 0, 0), -1)  # 黑色背景
        
        # 混合透明度
        alpha = 0.7
        cv2.addWeighted(overlay, alpha, image, 1 - alpha, 0, image)
        
        # 繪製邊框
        cv2.rectangle(image, (panel_x, panel_y), 
                     (panel_x + panel_width, panel_y + panel_height), 
                     (255, 255, 255), 2)  # 白色邊框
        
        # 文字設定
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.5
        font_thickness = 1
        line_height = 20
        text_x = panel_x + 10
        text_y = panel_y + 25
        
        # 標題
        cv2.putText(image, "ROI-SAHI Configuration", (text_x, text_y), 
                   font, 0.6, (0, 255, 255), 2)  # 青色標題
        text_y += 25
        
        # ROI 配置信息
        roi_enabled = "ON" if self.roi_config.enabled else "OFF"
        roi_mode = self.roi_config.inference_mode.upper()
        cv2.putText(image, f"ROI Status: {roi_enabled} ({roi_mode})", (text_x, text_y), 
                   font, font_scale, (255, 255, 255), font_thickness)
        text_y += line_height
        
        # ROI 邊界設定
        margins = self.roi_config.margins
        cv2.putText(image, f"ROI Margins: T:{margins['top']}, B:{margins['bottom']}", (text_x, text_y), 
                   font, font_scale, (255, 255, 255), font_thickness)
        text_y += line_height
        
        cv2.putText(image, f"             L:{margins['left']}, R:{margins['right']}", (text_x, text_y), 
                   font, font_scale, (255, 255, 255), font_thickness)
        text_y += line_height
        
        # 圖像信息
        cv2.putText(image, f"Image Size: {img_w}x{img_h}px", (text_x, text_y), 
                   font, font_scale, (255, 255, 255), font_thickness)
        text_y += line_height
        
        # SAHI 配置信息
        sahi_status = "ON" if show_slices else "OFF"
        cv2.putText(image, f"SAHI Slicing: {sahi_status}", (text_x, text_y), 
                   font, font_scale, (0, 255, 255) if show_slices else (128, 128, 128), font_thickness)
        text_y += line_height
        
        if show_slices:
            cv2.putText(image, f"Slice Size: {slice_width}x{slice_height}px", (text_x, text_y), 
                       font, font_scale, (0, 255, 255), font_thickness)
            text_y += line_height
            
            overlap_percent = int(overlap_ratio * 100)
            cv2.putText(image, f"Overlap: {overlap_percent}%", (text_x, text_y), 
                       font, font_scale, (0, 255, 255), font_thickness)
            text_y += line_height
            
            # 顯示切片數量
            if slice_count > 0:
                cv2.putText(image, f"Total Slices: {slice_count}", (text_x, text_y), 
                           font, font_scale, (0, 255, 0), font_thickness)  # 綠色顯示切片數量
                text_y += line_height
        
        # ROI 狀態指示
        if self._roi_relative_coords is not None:
            cv2.putText(image, "ROI: Fixed from 1st image", (text_x, text_y), 
                       font, font_scale, (0, 255, 0), font_thickness)  # 綠色表示已固定
        else:
            cv2.putText(image, "ROI: Dynamic calculation", (text_x, text_y), 
                       font, font_scale, (255, 255, 0), font_thickness)  # 黃色表示動態
    
    def get_roi_sahi_slices(self, image_shape: Tuple[int, int], slice_height: int, slice_width: int, overlap_ratio: float) -> List[Dict[str, Any]]:
        """
        獲取ROI區域內的SAHI切片信息
        
        Args:
            image_shape: 圖像形狀 (height, width)
            slice_height: 切片高度
            slice_width: 切片寬度
            overlap_ratio: 重疊比例
            
        Returns:
            List[Dict]: 切片信息列表
        """
        if not self.roi_config.enabled:
            # ROI未啟用，返回標準SAHI切片
            return self._generate_standard_sahi_slices(image_shape, slice_height, slice_width, overlap_ratio)
        
        h, w = image_shape
        
        # 🎯 優先使用相對座標計算ROI（如果已初始化）
        if self._roi_relative_coords is not None:
            # 使用第一張圖像設定的相對座標比例
            top = int(h * self._roi_relative_coords['top_ratio'])
            bottom = int(h * self._roi_relative_coords['bottom_ratio'])
            left = int(w * self._roi_relative_coords['left_ratio'])
            right = int(w * self._roi_relative_coords['right_ratio'])
            
            # 確保座標有效
            top = max(0, min(top, h-1))
            bottom = max(top+1, min(bottom, h))
            left = max(0, min(left, w-1))
            right = max(left+1, min(right, w))
            
            self.logger.debug(f"🎯 SAHI使用相對座標ROI: ({left},{top})-({right},{bottom})")
        else:
            # 備用方案：重新計算ROI區域
            top, bottom, left, right = self.calculate_roi_coordinates(image_shape)
        
        # 在ROI區域內生成切片
        roi_width = right - left
        roi_height = bottom - top
        
        slices = []
        overlap_height = int(slice_height * overlap_ratio)
        overlap_width = int(slice_width * overlap_ratio)
        
        # 計算步長
        step_height = slice_height - overlap_height
        step_width = slice_width - overlap_width
        
        slice_id = 0
        
        # 在ROI區域內生成切片
        for y in range(top, bottom - slice_height + 1, step_height):
            for x in range(left, right - slice_width + 1, step_width):
                # 確保切片不超出ROI邊界
                actual_y = min(y, bottom - slice_height)
                actual_x = min(x, right - slice_width)
                
                slice_info = {
                    'slice_id': slice_id,
                    'x': actual_x,
                    'y': actual_y,
                    'width': slice_width,
                    'height': slice_height,
                    'roi_slice': True,  # 標記為ROI切片
                    'roi_bounds': (left, top, right, bottom)
                }
                
                slices.append(slice_info)
                slice_id += 1
        
        # 處理邊界切片
        if roi_width > slice_width:
            # 右邊界切片
            for y in range(top, bottom - slice_height + 1, step_height):
                actual_y = min(y, bottom - slice_height)
                slice_info = {
                    'slice_id': slice_id,
                    'x': right - slice_width,
                    'y': actual_y,
                    'width': slice_width,
                    'height': slice_height,
                    'roi_slice': True,
                    'roi_bounds': (left, top, right, bottom)
                }
                slices.append(slice_info)
                slice_id += 1
        
        if roi_height > slice_height:
            # 下邊界切片
            for x in range(left, right - slice_width + 1, step_width):
                actual_x = min(x, right - slice_width)
                slice_info = {
                    'slice_id': slice_id,
                    'x': actual_x,
                    'y': bottom - slice_height,
                    'width': slice_width,
                    'height': slice_height,
                    'roi_slice': True,
                    'roi_bounds': (left, top, right, bottom)
                }
                slices.append(slice_info)
                slice_id += 1
        
        # 角落切片
        if roi_width > slice_width and roi_height > slice_height:
            slice_info = {
                'slice_id': slice_id,
                'x': right - slice_width,
                'y': bottom - slice_height,
                'width': slice_width,
                'height': slice_height,
                'roi_slice': True,
                'roi_bounds': (left, top, right, bottom)
            }
            slices.append(slice_info)
        
        self.logger.info(f"🧩 ROI-SAHI: 在ROI區域({left},{top})-({right},{bottom})內生成{len(slices)}個切片")
        
        return slices
    
    def _generate_standard_sahi_slices(self, image_shape: Tuple[int, int], slice_height: int, slice_width: int, overlap_ratio: float) -> List[Dict[str, Any]]:
        """生成標準SAHI切片（無ROI限制）"""
        h, w = image_shape
        slices = []
        overlap_height = int(slice_height * overlap_ratio)
        overlap_width = int(slice_width * overlap_ratio)
        step_height = slice_height - overlap_height
        step_width = slice_width - overlap_width
        
        slice_id = 0
        for y in range(0, h, step_height):
            for x in range(0, w, step_width):
                actual_height = min(slice_height, h - y)
                actual_width = min(slice_width, w - x)
                
                if actual_height > 0 and actual_width > 0:
                    slice_info = {
                        'slice_id': slice_id,
                        'x': x,
                        'y': y,
                        'width': actual_width,
                        'height': actual_height,
                        'roi_slice': False
                    }
                    slices.append(slice_info)
                    slice_id += 1
        
        return slices
    
    def save_roi_preview(self, image: np.ndarray, output_path: str, 
                        show_sahi_slices: bool = None, 
                        slice_height: int = 512, slice_width: int = 512, overlap_ratio: float = 0.2) -> str:
        """
        保存ROI預覽圖像
        
        Args:
            image: 輸入圖像
            output_path: 輸出目錄
            show_sahi_slices: 是否顯示SAHI切片資訊（None時自動判斷）
            slice_height: SAHI切片高度
            slice_width: SAHI切片寬度
            overlap_ratio: SAHI重疊比例
            
        Returns:
            str: 保存的文件路徑
        """
        # 🧩 自動判斷是否顯示SAHI切片資訊
        if show_sahi_slices is None:
            # 檢測多種SAHI啟用條件
            show_sahi_slices = (
                # ROI模式是SAHI
                (self.roi_config.enabled and self.roi_config.inference_mode == "sahi") or
                # 或者有傳入切片參數且切片尺寸不是預設值（說明用戶想要SAHI）
                (slice_height != 512 or slice_width != 512 or overlap_ratio != 0.2)
            )
        
        preview = self.create_roi_preview(
            image, show_sahi_slices, slice_height, slice_width, overlap_ratio
        )
        
        # 🗂️ 修正: 保存到output_path/images/目錄下
        images_dir = Path(output_path)
        images_dir.mkdir(parents=True, exist_ok=True)
        
        # 根據是否包含SAHI資訊決定文件名
        filename = "roi_preview_with_sahi.jpg" if show_sahi_slices else "roi_preview.jpg"
        preview_path = images_dir / filename
        
        cv2.imwrite(str(preview_path), preview, [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        preview_info = f"💾 ROI預覽已保存: {preview_path}"
        if show_sahi_slices:
            preview_info += f" (包含SAHI切片資訊)"
        
        self.logger.info(preview_info)
        
        return str(preview_path)
    
    def scale_gt_annotations(self, gt_annotations: Optional[list[Dict]], scale: float) -> Optional[list[Dict]]:
        """
        縮放GT標註座標
        
        Args:
            gt_annotations: GT標註列表
            scale: 縮放比例
            
        Returns:
            Optional[List[Dict]]: 縮放後的GT標註
        """
        if not gt_annotations or scale == 1.0:
            return gt_annotations
        
        scaled_annotations = []
        
        for annotation in gt_annotations:
            scaled_annotation = annotation.copy()
            
            # 縮放bbox座標（如果有）
            if 'bbox' in annotation:
                bbox = annotation['bbox']
                if isinstance(bbox, list) and len(bbox) >= 4:
                    scaled_annotation['bbox'] = [
                        bbox[0] * scale,  # x1
                        bbox[1] * scale,  # y1  
                        bbox[2] * scale,  # x2
                        bbox[3] * scale   # y2
                    ]
            
            # 縮放多邊形座標（labelme格式）
            if 'shapes' in annotation:
                scaled_shapes = []
                for shape in annotation['shapes']:
                    scaled_shape = shape.copy()
                    if 'points' in shape:
                        scaled_points = []
                        for point in shape['points']:
                            if isinstance(point, list) and len(point) >= 2:
                                scaled_points.append([
                                    point[0] * scale,  # x
                                    point[1] * scale   # y
                                ])
                        scaled_shape['points'] = scaled_points
                    scaled_shapes.append(scaled_shape)
                scaled_annotation['shapes'] = scaled_shapes
            
            # 縮放segmentation座標（COCO格式）
            if 'segmentation' in annotation:
                segmentation = annotation['segmentation']
                if isinstance(segmentation, list):
                    scaled_segmentation = []
                    for seg in segmentation:
                        if isinstance(seg, list):
                            # 座標格式: [x1, y1, x2, y2, ...]
                            scaled_seg = []
                            for i in range(0, len(seg), 2):
                                if i + 1 < len(seg):
                                    scaled_seg.extend([
                                        seg[i] * scale,      # x
                                        seg[i + 1] * scale   # y
                                    ])
                            scaled_segmentation.append(scaled_seg)
                    scaled_annotation['segmentation'] = scaled_segmentation
            
            scaled_annotations.append(scaled_annotation)
        
        return scaled_annotations
    
    def scale_detection_coordinates(self, detections: list, scale: float) -> list:
        """
        縮放檢測結果座標到原始圖像尺寸
        
        Args:
            detections: 檢測結果列表
            scale: 縮放比例
            
        Returns:
            list: 縮放後的檢測結果
        """
        if scale == 1.0:
            return detections
        
        from dataclasses import replace
        from ..core.base_inference import Detection
        
        scaled_detections = []
        for detection in detections:
            # 縮放bbox座標
            scaled_bbox = detection.bbox
            if detection.bbox:
                scaled_bbox = [
                    detection.bbox[0] / scale,  # x1
                    detection.bbox[1] / scale,  # y1
                    detection.bbox[2] / scale,  # x2
                    detection.bbox[3] / scale   # y2
                ]
            
            # 縮放mask (如果有)
            scaled_mask = detection.mask
            if detection.mask is not None:
                original_h = int(detection.mask.shape[0] * scale)
                original_w = int(detection.mask.shape[1] * scale)
                scaled_mask = cv2.resize(
                    detection.mask.astype(np.uint8), 
                    (original_w, original_h), 
                    interpolation=cv2.INTER_NEAREST
                ).astype(bool)
            
            # 更新面積
            scaled_area = detection.area
            if detection.area:
                scaled_area = detection.area / (scale * scale)
            
            # 創建新的Detection對象
            scaled_detection = Detection(
                bbox=scaled_bbox,
                confidence=detection.confidence,
                class_id=detection.class_id,
                class_name=detection.class_name,
                mask=scaled_mask,
                area=scaled_area
            )
            
            scaled_detections.append(scaled_detection)
        
        return scaled_detections
    
    def get_processing_info(self) -> Dict[str, Any]:
        """獲取處理信息"""
        return {
            'image_scale': self.current_scale,
            'resize_enabled': self.image_config.enable_resize,
            'roi_enabled': self.roi_config.enabled,
            'roi_margins': self.roi_config.margins,
            'roi_inference_mode': self.roi_config.inference_mode,
            'roi_initialized': self.roi_initialized,
            'roi_reference_shape': self.roi_reference_shape,
            'roi_relative_coords': self._roi_relative_coords
        }
    
    def get_roi_status(self) -> Dict[str, Any]:
        """獲取 ROI 詳細狀態信息"""
        return {
            'enabled': self.roi_config.enabled,
            'initialized': self.roi_initialized,
            'reference_shape': self.roi_reference_shape,
            'margins': self.roi_config.margins,
            'inference_mode': self.roi_config.inference_mode,
            'relative_coords': self._roi_relative_coords,
            'has_cache': self._roi_cache is not None
        }