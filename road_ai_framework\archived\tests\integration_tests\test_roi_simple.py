#!/usr/bin/env python3
"""
簡化ROI功能測試
"""

import sys
from pathlib import Path

# 添加路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_import():
    """測試導入"""
    print("🔧 測試導入...")
    
    try:
        from models.inference.config_manager import UnifiedYOLOConfigManager
        print("   ✅ UnifiedYOLOConfigManager 導入成功")
        
        # 測試配置創建
        config_manager = UnifiedYOLOConfigManager()
        print("   ✅ 配置管理器創建成功")
        
        # 檢查SAHI配置
        sahi_config = config_manager.sahi
        print(f"   ✅ SAHI配置: enable_sahi_roi={sahi_config.enable_sahi_roi}")
        print(f"   ✅ ROI參數: top={sahi_config.roi_top_ratio}, bottom={sahi_config.roi_bottom_ratio}")
        print(f"   ✅ ROI參數: left={sahi_config.roi_left_ratio}, right={sahi_config.roi_right_ratio}")
        
        return True
    except Exception as e:
        print(f"❌ 導入測試失敗: {e}")
        return False

def test_roi_calculation():
    """測試ROI計算邏輯"""
    print("\n📐 測試ROI計算邏輯...")
    
    try:
        # 模擬原圖尺寸
        h, w = 1000, 1500
        
        # ROI參數（run_unified_yolo.py中的參數）
        roi_top_ratio = 3.0
        roi_bottom_ratio = 2.8
        roi_left_ratio = 3.0  # 使用不同的左右比例來測試
        roi_right_ratio = 4.0
        
        # 計算ROI邊界（與unified_yolo_inference.py中相同的邏輯）
        crop_factor_top = (roi_top_ratio - 1) / 8
        crop_factor_bottom = (roi_bottom_ratio - 1) / 8
        crop_factor_left = (roi_left_ratio - 1) / 8
        crop_factor_right = (roi_right_ratio - 1) / 8
        
        y1_roi = int(h * crop_factor_top)
        y2_roi = int(h - h * crop_factor_bottom)
        x1_roi = int(w * crop_factor_left)
        x2_roi = int(w - w * crop_factor_right)
        
        print(f"   ✅ 原圖尺寸: {w}x{h}")
        print(f"   ✅ crop_factor: top={crop_factor_top:.3f}, bottom={crop_factor_bottom:.3f}")
        print(f"   ✅ crop_factor: left={crop_factor_left:.3f}, right={crop_factor_right:.3f}")
        print(f"   ✅ ROI邊界: ({x1_roi}, {y1_roi}) -> ({x2_roi}, {y2_roi})")
        print(f"   ✅ ROI尺寸: {x2_roi-x1_roi}x{y2_roi-y1_roi}")
        print(f"   ✅ 裁切比例: {(x2_roi-x1_roi)/w:.2%} x {(y2_roi-y1_roi)/h:.2%}")
        
        # 驗證ROI是否有效
        if y2_roi > y1_roi and x2_roi > x1_roi:
            print(f"   ✅ ROI座標計算正確")
            return True
        else:
            print(f"   ❌ ROI座標計算錯誤")
            return False
            
    except Exception as e:
        print(f"❌ ROI座標計算測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 ROI功能簡化測試")
    print("=" * 60)
    
    tests = [test_import, test_roi_calculation]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 測試總結:")
    print(f"   總測試數: {len(tests)}")
    print(f"   通過測試: {sum(results)}")
    print(f"   失敗測試: {len(tests) - sum(results)}")
    
    if all(results):
        print("\n🎉 基礎測試通過！ROI配置正確")
        print("\n✅ 確認:")
        print("   - 配置管理器正常工作")
        print("   - ROI座標計算邏輯正確")
        print("   - 可以進行實際ROI推理測試")
    else:
        print("\n❌ 基礎測試失敗")
    
    print("=" * 60)

if __name__ == "__main__":
    main()