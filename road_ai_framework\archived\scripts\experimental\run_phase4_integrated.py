# 🚀 Phase 4 統一集成運行腳本
# 智能優化與企業擴展 - 完整功能展示

import os
import sys
import logging
import asyncio
import threading
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any

# 添加項目路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Phase 4 核心模組導入
try:
    # 智能模型選擇
    from intelligence.model_selector import (
        IntelligentModelManager, ScenarioType, ModelProfile, 
        ModelType, EdgeDeviceType
    )
    
    # 多租戶架構
    from enterprise.multi_tenant import (
        TenantManager, TenantType, SubscriptionTier, 
        TenantMiddleware, ResourceQuota
    )
    
    # 邊緣計算部署
    from edge.edge_deployment import (
        EdgeDeploymentManager, EdgeDevice, EdgeDeviceSpec,
        DeploymentStatus
    )
    
    # 模型版本管理
    from versioning.model_registry import (
        ModelRegistryManager, ModelVersion, ModelStatus,
        ModelMetrics
    )
    
    # 智能負載均衡
    from load_balancing.intelligent_balancer import (
        IntelligentLoadBalancer, LoadBalancingStrategy,
        ServerNode, ServerMetrics, ServerStatus
    )
    
    # 分散式推理
    from distributed.distributed_inference import (
        DistributedInferenceEngine, DistributedCluster,
        WorkerNode, NodeType, InferenceTask, TaskPriority
    )
    
    PHASE4_AVAILABLE = True
    
except ImportError as e:
    print(f"⚠️ Phase 4 模組導入失敗: {e}")
    PHASE4_AVAILABLE = False


class Phase4IntegratedSystem:
    """Phase 4 整合系統 - 智能優化與企業擴展"""
    
    def __init__(self):
        self.logger = self._setup_logging()
        self.components = {}
        self.running = False
        
        if not PHASE4_AVAILABLE:
            self.logger.error("Phase 4 模組不可用，請檢查安裝")
            return
        
        self.logger.info("🚀 初始化 Phase 4 整合系統")
        self._initialize_components()

    def _setup_logging(self) -> logging.Logger:
        """設置日誌系統"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('phase4_integrated.log', encoding='utf-8')
            ]
        )
        return logging.getLogger(__name__)

    def _initialize_components(self):
        """初始化所有Phase 4組件"""
        try:
            # 1. 智能模型管理器
            self.logger.info("🧠 初始化智能模型管理器")
            self.components['model_manager'] = IntelligentModelManager()
            
            # 2. 多租戶管理器
            self.logger.info("🏢 初始化多租戶管理器")
            self.components['tenant_manager'] = TenantManager()
            
            # 3. 邊緣部署管理器
            self.logger.info("🌐 初始化邊緣部署管理器")
            self.components['edge_manager'] = EdgeDeploymentManager()
            
            # 4. 模型註冊中心
            self.logger.info("📦 初始化模型註冊中心")
            self.components['model_registry'] = ModelRegistryManager()
            
            # 5. 智能負載均衡器
            self.logger.info("⚖️ 初始化智能負載均衡器")
            self.components['load_balancer'] = IntelligentLoadBalancer(
                LoadBalancingStrategy.INTELLIGENT
            )
            
            # 6. 分散式推理引擎
            self.logger.info("🌐 初始化分散式推理引擎")
            cluster_config = DistributedCluster(
                cluster_id="phase4_cluster",
                cluster_name="Phase 4 智能集群",
                coordinator_node="coordinator_001",
                load_balancing_strategy="intelligent",
                enable_auto_scaling=True,
                max_batch_size=8,
                enable_caching=True
            )
            self.components['distributed_engine'] = DistributedInferenceEngine(cluster_config)
            
            self.logger.info("✅ 所有 Phase 4 組件初始化完成")
            
        except Exception as e:
            self.logger.error(f"組件初始化失敗: {e}")
            raise

    def setup_demo_data(self):
        """設置演示數據"""
        self.logger.info("📊 設置演示數據")
        
        try:
            # 1. 創建演示租戶
            self._setup_demo_tenants()
            
            # 2. 註冊演示模型
            self._setup_demo_models()
            
            # 3. 添加邊緣設備
            self._setup_demo_edge_devices()
            
            # 4. 配置負載均衡服務器
            self._setup_demo_servers()
            
            # 5. 添加分散式工作節點
            self._setup_demo_worker_nodes()
            
            self.logger.info("✅ 演示數據設置完成")
            
        except Exception as e:
            self.logger.error(f"演示數據設置失敗: {e}")

    def _setup_demo_tenants(self):
        """設置演示租戶"""
        tenant_manager = self.components['tenant_manager']
        
        # 創建不同層級的租戶
        tenants = [
            {
                'name': '智慧城市管理局',
                'type': TenantType.GOVERNMENT,
                'tier': SubscriptionTier.ENTERPRISE,
                'email': '<EMAIL>',
                'organization': '市政府智慧城市部門'
            },
            {
                'name': '道路檢測公司',
                'type': TenantType.ENTERPRISE,
                'tier': SubscriptionTier.PROFESSIONAL,
                'email': '<EMAIL>',
                'organization': 'RoadAI Technologies Ltd.'
            },
            {
                'name': '建設工程承包商',
                'type': TenantType.SMALL_BUSINESS,
                'tier': SubscriptionTier.BASIC,
                'email': '<EMAIL>',
                'organization': '優質建設有限公司'
            }
        ]
        
        for tenant_data in tenants:
            tenant = tenant_manager.create_tenant(
                tenant_name=tenant_data['name'],
                tenant_type=tenant_data['type'],
                subscription_tier=tenant_data['tier'],
                contact_email=tenant_data['email'],
                organization=tenant_data.get('organization')
            )
            self.logger.info(f"   創建租戶: {tenant.tenant_name}")

    def _setup_demo_models(self):
        """設置演示模型"""
        registry_manager = self.components['model_registry']
        
        # 創建模型註冊表
        registries = [
            {
                'name': '道路裂縫檢測模型',
                'description': '專用於道路裂縫檢測的YOLO模型系列',
                'models': [
                    {
                        'version': '1.0.0',
                        'type': ModelType.SEGMENTATION,
                        'metrics': ModelMetrics(
                            accuracy=0.89, mIoU=0.85, inference_time_ms=45.2,
                            model_size_mb=128.5, memory_usage_mb=512.0
                        )
                    },
                    {
                        'version': '1.1.0',
                        'type': ModelType.SEGMENTATION,
                        'metrics': ModelMetrics(
                            accuracy=0.91, mIoU=0.87, inference_time_ms=42.1,
                            model_size_mb=135.2, memory_usage_mb=480.0
                        )
                    }
                ]
            },
            {
                'name': '路面病害檢測模型',
                'description': '多類別路面病害檢測模型',
                'models': [
                    {
                        'version': '2.0.0',
                        'type': ModelType.DETECTION,
                        'metrics': ModelMetrics(
                            accuracy=0.87, mAP=0.82, inference_time_ms=38.5,
                            model_size_mb=96.8, memory_usage_mb=384.0
                        )
                    }
                ]
            }
        ]
        
        for registry_data in registries:
            registry_id = registry_manager.create_registry(
                name=registry_data['name'],
                description=registry_data['description'],
                owners=['AI_Team'],
                public=True
            )
            
            for model_data in registry_data['models']:
                # 模擬模型文件
                model_path = f"./demo_models/{registry_data['name']}_v{model_data['version']}.pt"
                
                success = registry_manager.register_model_version(
                    registry_id=registry_id,
                    model_path=model_path,
                    version=model_data['version'],
                    model_type=model_data['type'],
                    description=f"Version {model_data['version']} with improved performance",
                    created_by="AI_Engineer",
                    tags=["production", "road_detection"]
                )
                
                if success:
                    # 更新性能指標
                    registry_manager.update_model_metrics(
                        registry_id, model_data['version'], model_data['metrics']
                    )
                    
                    # 提升到生產狀態
                    registry_manager.promote_version(
                        registry_id, model_data['version'], ModelStatus.PRODUCTION
                    )
                    
                    self.logger.info(f"   註冊模型: {registry_data['name']} v{model_data['version']}")

    def _setup_demo_edge_devices(self):
        """設置演示邊緣設備"""
        edge_manager = self.components['edge_manager']
        
        # 創建不同類型的邊緣設備
        devices = [
            {
                'id': 'jetson_road_001',
                'name': '信義區道路檢測站',
                'spec': EdgeDeviceSpec(
                    device_type=EdgeDeviceType.NVIDIA_JETSON,
                    cpu_cores=6, memory_gb=8.0, storage_gb=32.0,
                    gpu_available=True, gpu_memory_gb=4.0,
                    supports_tensorrt=True
                ),
                'location': '台北市信義區',
                'ip': '*************'
            },
            {
                'id': 'pi_mobile_001',
                'name': '移動檢測車載設備',
                'spec': EdgeDeviceSpec(
                    device_type=EdgeDeviceType.RASPBERRY_PI,
                    cpu_cores=4, memory_gb=4.0, storage_gb=64.0,
                    gpu_available=False
                ),
                'location': '檢測車輛',
                'ip': '*************'
            },
            {
                'id': 'nuc_highway_001',
                'name': '高速公路監控點',
                'spec': EdgeDeviceSpec(
                    device_type=EdgeDeviceType.INTEL_NUC,
                    cpu_cores=8, memory_gb=16.0, storage_gb=512.0,
                    gpu_available=False, supports_openvino=True
                ),
                'location': '國道一號 100K',
                'ip': '*************'
            }
        ]
        
        for device_data in devices:
            device = EdgeDevice(
                device_id=device_data['id'],
                device_name=device_data['name'],
                device_spec=device_data['spec'],
                location=device_data['location'],
                ip_address=device_data['ip']
            )
            
            edge_manager.register_device(device)
            self.logger.info(f"   註冊邊緣設備: {device.device_name}")

    def _setup_demo_servers(self):
        """設置演示負載均衡服務器"""
        load_balancer = self.components['load_balancer']
        
        servers = [
            {
                'id': 'gpu_server_001',
                'hostname': 'ai-gpu-1.datacenter.com',
                'ip': '*********', 'port': 8000,
                'weight': 150, 'max_conn': 100,
                'region': 'asia-east',
                'metrics': ServerMetrics(
                    cpu_usage=45.2, memory_usage=62.8, gpu_usage=35.5,
                    active_connections=25, average_response_time=120.5,
                    error_rate=0.01
                )
            },
            {
                'id': 'gpu_server_002',
                'hostname': 'ai-gpu-2.datacenter.com',
                'ip': '*********', 'port': 8000,
                'weight': 120, 'max_conn': 80,
                'region': 'asia-east',
                'metrics': ServerMetrics(
                    cpu_usage=67.3, memory_usage=78.9, gpu_usage=82.1,
                    active_connections=45, average_response_time=180.3,
                    error_rate=0.02
                )
            },
            {
                'id': 'cpu_server_001',
                'hostname': 'ai-cpu-1.datacenter.com',
                'ip': '*********', 'port': 8000,
                'weight': 100, 'max_conn': 50,
                'region': 'us-west',
                'metrics': ServerMetrics(
                    cpu_usage=55.1, memory_usage=68.4, gpu_usage=0.0,
                    active_connections=15, average_response_time=250.7,
                    error_rate=0.015
                )
            }
        ]
        
        for server_data in servers:
            server = ServerNode(
                node_id=server_data['id'],
                hostname=server_data['hostname'],
                ip_address=server_data['ip'],
                port=server_data['port'],
                weight=server_data['weight'],
                max_connections=server_data['max_conn'],
                region=server_data['region'],
                status=ServerStatus.HEALTHY
            )
            
            load_balancer.add_server(server)
            load_balancer.update_server_metrics(server_data['id'], server_data['metrics'])
            
            self.logger.info(f"   添加負載均衡服務器: {server.hostname}")

    def _setup_demo_worker_nodes(self):
        """設置演示分散式工作節點"""
        distributed_engine = self.components['distributed_engine']
        
        workers = [
            {
                'id': 'worker_gpu_001',
                'hostname': 'distributed-gpu-1',
                'ip': '*********', 'port': 8000,
                'max_tasks': 4, 'has_gpu': True, 'gpu_memory': 8.0,
                'cpu_cores': 8, 'memory': 32.0,
                'models': ['yolo11_seg', 'yolo12_det'],
                'region': 'asia-east'
            },
            {
                'id': 'worker_gpu_002',
                'hostname': 'distributed-gpu-2',
                'ip': '*********', 'port': 8000,
                'max_tasks': 6, 'has_gpu': True, 'gpu_memory': 16.0,
                'cpu_cores': 12, 'memory': 64.0,
                'models': ['yolo11_seg', 'vision_mamba'],
                'region': 'asia-east'
            },
            {
                'id': 'worker_cpu_001',
                'hostname': 'distributed-cpu-1',
                'ip': '*********', 'port': 8000,
                'max_tasks': 2, 'has_gpu': False,
                'cpu_cores': 16, 'memory': 32.0,
                'models': ['yolo_lite', 'csp_iformer'],
                'region': 'us-west'
            },
            {
                'id': 'worker_edge_001',
                'hostname': 'distributed-edge-1',
                'ip': '*********', 'port': 8000,
                'max_tasks': 1, 'has_gpu': False,
                'cpu_cores': 4, 'memory': 8.0,
                'models': ['yolo_edge'],
                'region': 'europe-west'
            }
        ]
        
        for worker_data in workers:
            worker = WorkerNode(
                node_id=worker_data['id'],
                node_type=NodeType.WORKER,
                hostname=worker_data['hostname'],
                ip_address=worker_data['ip'],
                port=worker_data['port'],
                max_concurrent_tasks=worker_data['max_tasks'],
                supported_models=worker_data['models'],
                has_gpu=worker_data['has_gpu'],
                gpu_memory_gb=worker_data.get('gpu_memory', 0.0),
                cpu_cores=worker_data['cpu_cores'],
                memory_gb=worker_data['memory'],
                region=worker_data['region']
            )
            
            distributed_engine.add_worker_node(worker)
            self.logger.info(f"   添加分散式工作節點: {worker.hostname}")

    def run_comprehensive_demo(self):
        """運行綜合演示"""
        if not PHASE4_AVAILABLE:
            self.logger.error("Phase 4 不可用，無法運行演示")
            return
        
        self.logger.info("=" * 80)
        self.logger.info("🚀 開始 Phase 4 綜合功能演示")
        self.logger.info("=" * 80)
        
        try:
            # 設置演示數據
            self.setup_demo_data()
            
            # 演示各個組件功能
            self._demo_intelligent_model_selection()
            self._demo_multi_tenant_system()
            self._demo_edge_deployment()
            self._demo_model_registry()
            self._demo_load_balancing()
            self._demo_distributed_inference()
            
            # 顯示系統總覽
            self._display_system_overview()
            
            self.logger.info("✅ Phase 4 綜合演示完成")
            
        except Exception as e:
            self.logger.error(f"演示運行失敗: {e}")

    def _demo_intelligent_model_selection(self):
        """演示智能模型選擇"""
        self.logger.info("\n🧠 === 智能模型選擇演示 ===")
        
        model_manager = self.components['model_manager']
        
        # 測試不同場景的模型選擇
        scenarios = [
            (ScenarioType.REAL_TIME, "實時處理"),
            (ScenarioType.HIGH_ACCURACY, "高精度要求"),
            (ScenarioType.EDGE_COMPUTING, "邊緣計算"),
            (ScenarioType.BATCH_PROCESSING, "批量處理")
        ]
        
        for scenario, desc in scenarios:
            result = model_manager.smart_inference(
                image_path="./demo_image.jpg",
                scenario=scenario,
                accuracy_requirement=0.85,
                speed_requirement=30.0
            )
            
            if result:
                self.logger.info(f"   {desc}: 使用模型 {result['model_id']}, "
                               f"處理時間 {result['processing_time']:.3f}s")
            else:
                self.logger.warning(f"   {desc}: 模型選擇失敗")
        
        # 顯示管理器統計
        stats = model_manager.get_manager_stats()
        self.logger.info(f"   當前使用模型: {stats['current_model']}")
        self.logger.info(f"   模型切換次數: {stats['model_switches']}")

    def _demo_multi_tenant_system(self):
        """演示多租戶系統"""
        self.logger.info("\n🏢 === 多租戶系統演示 ===")
        
        tenant_manager = self.components['tenant_manager']
        
        # 列出所有租戶
        tenants = tenant_manager.list_tenants()
        self.logger.info(f"   管理租戶數量: {len(tenants)}")
        
        for tenant in tenants[:3]:  # 顯示前3個租戶
            # 獲取租戶分析
            analytics = tenant_manager.get_tenant_analytics(tenant['tenant_id'])
            
            self.logger.info(f"   租戶: {tenant['tenant_name']}")
            self.logger.info(f"     訂閱層級: {tenant['subscription_tier']}")
            self.logger.info(f"     API調用: {analytics['usage_summary']['total_api_calls']}")
            self.logger.info(f"     配額使用率: {analytics['quota_utilization']['api_calls_daily']:.1%}")

    def _demo_edge_deployment(self):
        """演示邊緣部署"""
        self.logger.info("\n🌐 === 邊緣計算部署演示 ===")
        
        edge_manager = self.components['edge_manager']
        
        # 列出所有邊緣設備
        devices = edge_manager.list_devices()
        self.logger.info(f"   管理邊緣設備: {len(devices)}")
        
        # 為第一個設備創建部署
        if devices:
            device_id = devices[0]['device_id']
            deployment_id = edge_manager.create_deployment(
                device_id=device_id,
                model_path="./models/road_detection_edge.pt",
                config_overrides={
                    'optimization_level': 2,
                    'enable_fp16': True,
                    'batch_size': 2
                }
            )
            
            if deployment_id:
                self.logger.info(f"   創建部署: {deployment_id}")
                
                # 等待部署完成
                time.sleep(3)
                
                # 檢查部署狀態
                status = edge_manager.get_device_status(device_id)
                if status:
                    self.logger.info(f"   設備狀態: {status['deployment_status']}")
        
        # 獲取集群概覽
        overview = edge_manager.get_fleet_overview()
        self.logger.info(f"   在線設備: {overview['online_devices']}/{overview['total_devices']}")
        self.logger.info(f"   運行部署: {overview['running_deployments']}")
        self.logger.info(f"   整體健康度: {overview['overall_health']:.1f}%")

    def _demo_model_registry(self):
        """演示模型註冊中心"""
        self.logger.info("\n📦 === 模型版本管理演示 ===")
        
        registry_manager = self.components['model_registry']
        
        # 搜索模型
        search_results = registry_manager.search_models(
            query="道路",
            model_type=ModelType.SEGMENTATION
        )
        
        self.logger.info(f"   搜索結果: {len(search_results)} 個模型")
        
        for result in search_results[:2]:  # 顯示前2個結果
            self.logger.info(f"   模型: {result['registry_name']} v{result['version']}")
            self.logger.info(f"     準確率: {result['accuracy']:.3f}")
            self.logger.info(f"     狀態: {result['status']}")
        
        # 獲取註冊中心統計
        stats = registry_manager.get_registry_statistics()
        self.logger.info(f"   總註冊表數: {stats['total_registries']}")
        self.logger.info(f"   總版本數: {stats['total_versions']}")
        self.logger.info(f"   存儲使用: {stats['storage_size_gb']:.2f} GB")

    def _demo_load_balancing(self):
        """演示智能負載均衡"""
        self.logger.info("\n⚖️ === 智能負載均衡演示 ===")
        
        load_balancer = self.components['load_balancer']
        
        # 模擬多個請求
        request_contexts = [
            {'client_region': 'asia-east', 'request_type': 'inference'},
            {'client_region': 'us-west', 'request_type': 'batch'},
            {'client_region': 'asia-east', 'request_type': 'real_time'},
            {'client_region': 'europe', 'request_type': 'inference'}
        ]
        
        for i, context in enumerate(request_contexts):
            selected_server = load_balancer.select_server(context)
            if selected_server:
                self.logger.info(f"   請求 {i+1}: 選擇服務器 {selected_server.node_id} "
                               f"({selected_server.region})")
                # 模擬請求完成
                load_balancer.release_connection(selected_server.node_id)
            else:
                self.logger.warning(f"   請求 {i+1}: 無可用服務器")
        
        # 獲取負載均衡統計
        stats = load_balancer.get_statistics()
        self.logger.info(f"   總服務器: {stats['total_servers']}")
        self.logger.info(f"   健康服務器: {stats['healthy_servers']}")
        self.logger.info(f"   成功路由: {stats['routing_stats']['successful_routes']}")

    def _demo_distributed_inference(self):
        """演示分散式推理"""
        self.logger.info("\n🌐 === 分散式推理演示 ===")
        
        distributed_engine = self.components['distributed_engine']
        
        # 提交多個推理任務
        task_ids = []
        for i in range(8):
            task = InferenceTask(
                task_id=f"demo_task_{i:03d}",
                input_data={'image_path': f'/demo/test_image_{i}.jpg'},
                model_id="yolo11_seg",
                priority=TaskPriority.NORMAL if i % 2 == 0 else TaskPriority.HIGH,
                requires_gpu=True
            )
            
            task_id = distributed_engine.submit_task(task)
            task_ids.append(task_id)
        
        self.logger.info(f"   提交任務: {len(task_ids)} 個")
        
        # 等待任務處理
        time.sleep(4)
        
        # 檢查任務狀態
        completed = failed = 0
        for task_id in task_ids:
            task = distributed_engine.get_task_status(task_id)
            if task:
                if task.status.value == 'completed':
                    completed += 1
                elif task.status.value == 'failed':
                    failed += 1
        
        self.logger.info(f"   任務結果: {completed} 完成, {failed} 失敗")
        
        # 獲取集群狀態
        cluster_status = distributed_engine.get_cluster_status()
        self.logger.info(f"   集群節點: {cluster_status['online_nodes']}/{cluster_status['total_nodes']}")
        self.logger.info(f"   集群利用率: {cluster_status['utilization_rate']:.1f}%")
        self.logger.info(f"   緩存命中率: {cluster_status['cluster_stats']['cache_hit_rate']:.1%}")

    def _display_system_overview(self):
        """顯示系統總覽"""
        self.logger.info("\n" + "=" * 80)
        self.logger.info("📊 Phase 4 智能優化與企業擴展 - 系統總覽")
        self.logger.info("=" * 80)
        
        # 統計各組件狀態
        overview = {
            '🧠 智能模型管理': '✅ 多場景自動選擇, 動態切換',
            '🏢 多租戶架構': '✅ 企業級隔離, 配額管理',
            '🌐 邊緣計算': '✅ 多設備類型, 自動優化',
            '📦 版本管理': '✅ 語義版本, 性能追蹤',
            '⚖️ 負載均衡': '✅ AI驅動選擇, 實時監控',
            '🌐 分散式推理': '✅ 跨地區部署, 容錯處理'
        }
        
        for component, status in overview.items():
            self.logger.info(f"   {component}: {status}")
        
        self.logger.info(f"\n🎯 Phase 4 核心成就:")
        self.logger.info(f"   • 智能化: AI驅動的模型選擇和負載均衡")
        self.logger.info(f"   • 企業級: 多租戶隔離和資源配額管理")
        self.logger.info(f"   • 可擴展: 邊緣到雲端的全覆蓋部署")
        self.logger.info(f"   • 可靠性: 分散式容錯和版本管理")
        self.logger.info(f"   • 高性能: 智能負載均衡和緩存優化")

    def stop_all_components(self):
        """停止所有組件"""
        self.logger.info("🛑 停止所有 Phase 4 組件")
        
        try:
            if 'load_balancer' in self.components:
                self.components['load_balancer'].stop()
            
            if 'distributed_engine' in self.components:
                self.components['distributed_engine'].stop()
            
            if 'edge_manager' in self.components:
                self.components['edge_manager'].stop()
            
            self.logger.info("✅ 所有組件已停止")
            
        except Exception as e:
            self.logger.error(f"停止組件時發生錯誤: {e}")


def main():
    """主函數"""
    print("🚀 Phase 4 重構 - 智能優化與企業擴展")
    print("=" * 60)
    
    if not PHASE4_AVAILABLE:
        print("❌ Phase 4 模組不可用")
        print("請確保所有必要的模組都已正確安裝")
        return
    
    try:
        # 創建整合系統
        system = Phase4IntegratedSystem()
        
        # 運行綜合演示
        system.run_comprehensive_demo()
        
        # 等待用戶輸入
        print("\n" + "=" * 60)
        print("💡 Phase 4 演示已完成!")
        print("   系統將繼續運行，按 Enter 鍵停止...")
        input()
        
        # 停止所有組件
        system.stop_all_components()
        
        print("✅ Phase 4 系統已完全停止")
        
    except KeyboardInterrupt:
        print("\n⚠️ 收到中斷信號，正在停止系統...")
        if 'system' in locals():
            system.stop_all_components()
    except Exception as e:
        print(f"❌ 系統運行失敗: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()