#!/usr/bin/env python3
"""
🔍 測試UnifiedConfig屬性修復
"""

import sys
from pathlib import Path

# 添加路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_unified_config_structure():
    """測試UnifiedConfig結構"""
    print("🔍 測試UnifiedConfig結構...")
    
    try:
        from inference_system.config import UnifiedConfig, ClassConfig
        
        # 創建配置實例
        config = UnifiedConfig()
        
        print("✅ UnifiedConfig創建成功")
        print(f"   有模型配置: {hasattr(config, 'model')}")
        print(f"   有處理配置: {hasattr(config, 'processing')}")
        print(f"   有視覺化配置: {hasattr(config, 'visualization')}")
        print(f"   有輸出配置: {hasattr(config, 'output')}")
        print(f"   有類別配置: {hasattr(config, 'classes')}")
        
        # 測試處理配置的子結構
        print(f"   有切片配置: {hasattr(config.processing, 'slice')}")
        print(f"   有融合配置: {hasattr(config.processing, 'fusion')}")
        print(f"   有過濾配置: {hasattr(config.processing, 'filtering')}")
        
        return True
        
    except Exception as e:
        print(f"❌ UnifiedConfig結構測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_attribute_access():
    """測試配置屬性存取"""
    print("\n🔍 測試配置屬性存取...")
    
    try:
        from inference_system.config import UnifiedConfig, ClassConfig
        
        config = UnifiedConfig()
        
        # 測試正確的屬性存取方式
        print("📝 測試模型配置...")
        config.model.model_path = "test_model.pt"
        print(f"   模型路徑: {config.model.model_path}")
        
        print("📝 測試切片配置...")
        config.processing.slice.enabled = True
        config.processing.slice.height = 640
        config.processing.slice.width = 640
        config.processing.slice.overlap_ratio = 0.2
        print(f"   切片啟用: {config.processing.slice.enabled}")
        print(f"   切片尺寸: {config.processing.slice.height}x{config.processing.slice.width}")
        
        print("📝 測試過濾配置...")
        config.processing.filtering.enabled = True
        config.processing.filtering.linear_aspect_ratio_threshold = 0.8
        print(f"   過濾啟用: {config.processing.filtering.enabled}")
        print(f"   長寬比閾值: {config.processing.filtering.linear_aspect_ratio_threshold}")
        
        print("📝 測試視覺化配置...")
        config.visualization.enable_three_view = True
        config.visualization.font_size = 1.0
        print(f"   三視圖啟用: {config.visualization.enable_three_view}")
        print(f"   字體大小: {config.visualization.font_size}")
        
        print("📝 測試類別配置...")
        test_class = ClassConfig(
            name="linear_crack",
            display_name="縱向裂縫",
            color=[0, 255, 0],
            confidence=0.3,
            sahi_confidence=0.1,
            enabled=True
        )
        config.classes[6] = test_class
        print(f"   類別數量: {len(config.classes)}")
        
        print("📝 測試路徑配置...")
        config.output_path = "/test/output"
        print(f"   輸出路徑: {config.output_path}")
        
        print("✅ 所有屬性存取測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 屬性存取測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_create_ultimate_config():
    """測試創建終極配置（模擬）"""
    print("\n🔍 測試創建終極配置...")
    
    try:
        from inference_system.config import UnifiedConfig, ClassConfig
        
        # 模擬class_configs
        class_configs = {
            6: ["linear_crack", "裂縫", [0, 255, 0], 0.3, 0.1, True],
        }
        
        # 創建類別配置
        classes = {}
        for class_id, config_list in class_configs.items():
            name, display_name, color, conf_thresh, sahi_thresh, enabled = config_list
            classes[class_id] = ClassConfig(
                name=name,
                display_name=display_name,
                color=color,
                confidence=conf_thresh,
                sahi_confidence=sahi_thresh,
                enabled=enabled
            )
        
        # 創建統一配置
        config = UnifiedConfig()
        
        # 模型配置
        config.model.model_path = "test_model.pt"
        
        # SAHI配置
        config.processing.slice.enabled = True
        config.processing.slice.height = 512
        config.processing.slice.width = 512
        config.processing.slice.overlap_ratio = 0.2
        
        # 視覺化配置
        config.visualization.enable_three_view = True
        config.visualization.font_size = 1.0
        config.visualization.font_thickness = 2
        
        # 智能過濾配置
        config.processing.filtering.enabled = True
        config.processing.filtering.linear_aspect_ratio_threshold = 0.8
        config.processing.filtering.area_ratio_threshold = 0.4
        config.processing.filtering.step2_iou_threshold = 0.3
        
        # 類別配置
        config.classes = classes
        
        # 系統配置
        config.output_path = "/test/output"
        
        print("✅ 終極配置創建成功")
        print(f"   模型路徑: {config.model.model_path}")
        print(f"   切片啟用: {config.processing.slice.enabled}")
        print(f"   過濾啟用: {config.processing.filtering.enabled}")
        print(f"   三視圖啟用: {config.visualization.enable_three_view}")
        print(f"   類別數量: {len(config.classes)}")
        print(f"   輸出路徑: {config.output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 終極配置創建失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🚀 UnifiedConfig屬性修復驗證")
    print("=" * 50)
    
    test1_success = test_unified_config_structure()
    test2_success = test_config_attribute_access()
    test3_success = test_create_ultimate_config()
    
    print("\n" + "=" * 50)
    if test1_success and test2_success and test3_success:
        print("✅ 所有測試通過，UnifiedConfig屬性問題已修復！")
        print("💡 現在可以正常運行終極版本了")
        print("\n🚀 建議測試運行:")
        print("   python run_unified_yolo_ultimate.py")
    else:
        print("❌ 部分測試失敗，需要進一步檢查")

if __name__ == "__main__":
    main()