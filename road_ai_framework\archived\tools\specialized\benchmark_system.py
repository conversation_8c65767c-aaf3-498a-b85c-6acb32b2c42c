#!/usr/bin/env python3
"""
📊 效能基準測試系統
提供全面的性能基準測試和對比分析
"""

import time
import statistics
import logging
import json
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import sys
import psutil
import numpy as np

# 設置路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from inference_system import create_inference_system, UnifiedConfig, ClassConfig
from inference_system.performance import ConcurrentProcessor, CacheManager, MemoryOptimizer


class BenchmarkResult:
    """基準測試結果"""
    
    def __init__(self, test_name: str):
        self.test_name = test_name
        self.start_time = None
        self.end_time = None
        self.processing_times = []
        self.memory_usage = []
        self.cpu_usage = []
        self.gpu_memory_usage = []
        self.error_count = 0
        self.success_count = 0
        self.total_detections = 0
        
    def start(self):
        """開始測試"""
        self.start_time = time.time()
        
    def end(self):
        """結束測試"""
        self.end_time = time.time()
        
    def add_result(self, processing_time: float, 
                   detections_count: int = 0,
                   memory_mb: float = 0,
                   cpu_percent: float = 0,
                   gpu_memory_mb: float = 0,
                   success: bool = True):
        """添加測試結果"""
        self.processing_times.append(processing_time)
        self.memory_usage.append(memory_mb)
        self.cpu_usage.append(cpu_percent)
        self.gpu_memory_usage.append(gpu_memory_mb)
        
        if success:
            self.success_count += 1
            self.total_detections += detections_count
        else:
            self.error_count += 1
    
    def get_summary(self) -> Dict[str, Any]:
        """獲取测試摘要"""
        if not self.processing_times:
            return {'error': '無測試數據'}
        
        total_time = self.end_time - self.start_time if self.end_time else 0
        total_count = len(self.processing_times)
        
        return {
            'test_name': self.test_name,
            'total_images': total_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'success_rate': (self.success_count / total_count) * 100 if total_count > 0 else 0,
            'total_time_seconds': total_time,
            'total_detections': self.total_detections,
            'processing_time_stats': {
                'mean': statistics.mean(self.processing_times),
                'median': statistics.median(self.processing_times),
                'std': statistics.stdev(self.processing_times) if len(self.processing_times) > 1 else 0,
                'min': min(self.processing_times),
                'max': max(self.processing_times),
                'p95': np.percentile(self.processing_times, 95),
                'p99': np.percentile(self.processing_times, 99)
            },
            'throughput': {
                'images_per_second': total_count / total_time if total_time > 0 else 0,
                'detections_per_second': self.total_detections / total_time if total_time > 0 else 0
            },
            'resource_usage': {
                'memory_mb': {
                    'mean': statistics.mean(self.memory_usage) if self.memory_usage else 0,
                    'max': max(self.memory_usage) if self.memory_usage else 0
                },
                'cpu_percent': {
                    'mean': statistics.mean(self.cpu_usage) if self.cpu_usage else 0,
                    'max': max(self.cpu_usage) if self.cpu_usage else 0
                },
                'gpu_memory_mb': {
                    'mean': statistics.mean(self.gpu_memory_usage) if self.gpu_memory_usage else 0,
                    'max': max(self.gpu_memory_usage) if self.gpu_memory_usage else 0
                }
            }
        }


class BenchmarkSuite:
    """基準測試套件"""
    
    def __init__(self, config: UnifiedConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.results: Dict[str, BenchmarkResult] = {}
        
        # 測試配置
        self.test_images = []
        self.warmup_runs = 3
        self.benchmark_runs = 10
        
        self.logger.info("📊 基準測試套件初始化完成")
    
    def setup_test_images(self, image_dir: str, max_images: int = 50):
        """設置測試圖像"""
        image_path = Path(image_dir)
        if not image_path.exists():
            raise ValueError(f"測試圖像目錄不存在: {image_dir}")
        
        # 收集測試圖像
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        for ext in image_extensions:
            self.test_images.extend(image_path.glob(f"*{ext}"))
            self.test_images.extend(image_path.glob(f"*{ext.upper()}"))
        
        # 限制圖像數量
        if len(self.test_images) > max_images:
            self.test_images = self.test_images[:max_images]
        
        self.logger.info(f"📸 設置測試圖像: {len(self.test_images)}張")
        
        if not self.test_images:
            raise ValueError("未找到測試圖像")
    
    def run_baseline_benchmark(self) -> BenchmarkResult:
        """運行基線基準測試（單線程、無緩存）"""
        self.logger.info("🔄 開始基線基準測試...")
        
        result = BenchmarkResult("baseline")
        result.start()
        
        # 創建基線配置（禁用所有優化）
        baseline_config = UnifiedConfig()
        baseline_config.model = self.config.model
        baseline_config.classes = self.config.classes
        baseline_config.enable_cache = False
        baseline_config.max_workers = 1
        
        try:
            with create_inference_system(config=baseline_config) as system:
                # 預熱
                self._warmup(system)
                
                # 基準測試
                for i, image_path in enumerate(self.test_images[:self.benchmark_runs]):
                    start_time = time.time()
                    
                    try:
                        # 記錄資源使用
                        memory_before = psutil.virtual_memory().used / (1024**2)
                        cpu_before = psutil.cpu_percent()
                        
                        # 執行推理
                        inference_result = system.process_single_image(str(image_path))
                        
                        processing_time = time.time() - start_time
                        
                        # 記錄資源使用
                        memory_after = psutil.virtual_memory().used / (1024**2)
                        cpu_after = psutil.cpu_percent()
                        
                        # 獲取GPU記憶體使用（如果可用）
                        gpu_memory = self._get_gpu_memory_usage()
                        
                        # 添加結果
                        detections_count = len(inference_result.get('detections', []))
                        result.add_result(
                            processing_time=processing_time,
                            detections_count=detections_count,
                            memory_mb=memory_after - memory_before,
                            cpu_percent=(cpu_before + cpu_after) / 2,
                            gpu_memory_mb=gpu_memory,
                            success=inference_result.get('success', False)
                        )
                        
                        if (i + 1) % 5 == 0:
                            self.logger.info(f"   基線測試進度: {i+1}/{self.benchmark_runs}")
                    
                    except Exception as e:
                        result.add_result(processing_time=time.time() - start_time, success=False)
                        self.logger.warning(f"基線測試失敗 {image_path}: {str(e)}")
        
        except Exception as e:
            self.logger.error(f"基線基準測試失敗: {str(e)}")
            raise
        
        result.end()
        self.results["baseline"] = result
        
        summary = result.get_summary()
        self.logger.info(f"✅ 基線基準測試完成:")
        self.logger.info(f"   平均處理時間: {summary['processing_time_stats']['mean']:.3f}秒")
        self.logger.info(f"   處理速度: {summary['throughput']['images_per_second']:.2f}張/秒")
        
        return result
    
    def run_concurrent_benchmark(self, max_workers: int = 4) -> BenchmarkResult:
        """運行並發基準測試"""
        self.logger.info(f"⚡ 開始並發基準測試 (workers: {max_workers})...")
        
        result = BenchmarkResult(f"concurrent_{max_workers}")
        result.start()
        
        # 創建並發配置
        concurrent_config = UnifiedConfig()
        concurrent_config.model = self.config.model
        concurrent_config.classes = self.config.classes
        concurrent_config.enable_cache = False
        concurrent_config.max_workers = max_workers
        
        try:
            with create_inference_system(config=concurrent_config) as system:
                # 創建並發處理器
                processor = ConcurrentProcessor(
                    concurrent_config,
                    max_workers=max_workers,
                    use_processes=False
                )
                
                # 預熱
                self._warmup(system)
                
                # 定義推理函數
                def inference_func(image_path: str) -> Dict[str, Any]:
                    return system.process_single_image(image_path)
                
                # 並發基準測試
                test_images = [str(img) for img in self.test_images[:self.benchmark_runs]]
                
                start_time = time.time()
                results_list = processor.process_batch_concurrent(
                    test_images,
                    inference_func
                )
                total_time = time.time() - start_time
                
                # 處理結果
                for inference_result in results_list:
                    processing_time = inference_result.get('processing_time', 0)
                    detections_count = len(inference_result.get('detections', []))
                    success = inference_result.get('success', False)
                    
                    result.add_result(
                        processing_time=processing_time,
                        detections_count=detections_count,
                        success=success
                    )
                
                processor.cleanup()
        
        except Exception as e:
            self.logger.error(f"並發基準測試失敗: {str(e)}")
            raise
        
        result.end()
        self.results[f"concurrent_{max_workers}"] = result
        
        summary = result.get_summary()
        self.logger.info(f"✅ 並發基準測試完成 ({max_workers} workers):")
        self.logger.info(f"   平均處理時間: {summary['processing_time_stats']['mean']:.3f}秒")
        self.logger.info(f"   處理速度: {summary['throughput']['images_per_second']:.2f}張/秒")
        
        return result
    
    def run_cache_benchmark(self) -> BenchmarkResult:
        """運行緩存基準測試"""
        self.logger.info("💾 開始緩存基準測試...")
        
        result = BenchmarkResult("cache")
        result.start()
        
        # 創建緩存配置
        cache_config = UnifiedConfig()
        cache_config.model = self.config.model
        cache_config.classes = self.config.classes
        cache_config.enable_cache = True
        cache_config.max_workers = 1
        
        try:
            with create_inference_system(config=cache_config) as system:
                cache_manager = CacheManager(cache_config)
                
                # 預熱
                self._warmup(system)
                
                # 首次運行（填充緩存）
                test_images = self.test_images[:self.benchmark_runs]
                for image_path in test_images:
                    system.process_single_image(str(image_path))
                
                self.logger.info("   緩存預熱完成，開始緩存命中測試...")
                
                # 緩存命中測試
                for i, image_path in enumerate(test_images):
                    start_time = time.time()
                    
                    try:
                        inference_result = system.process_single_image(str(image_path))
                        processing_time = time.time() - start_time
                        
                        detections_count = len(inference_result.get('detections', []))
                        result.add_result(
                            processing_time=processing_time,
                            detections_count=detections_count,
                            success=inference_result.get('success', False)
                        )
                        
                        if (i + 1) % 5 == 0:
                            self.logger.info(f"   緩存測試進度: {i+1}/{len(test_images)}")
                    
                    except Exception as e:
                        result.add_result(processing_time=time.time() - start_time, success=False)
                        self.logger.warning(f"緩存測試失敗 {image_path}: {str(e)}")
                
                # 獲取緩存統計
                cache_stats = cache_manager.get_cache_statistics()
                self.logger.info(f"   緩存命中率: {cache_stats.get('hit_rate_percent', 0):.1f}%")
                
                cache_manager.cleanup()
        
        except Exception as e:
            self.logger.error(f"緩存基準測試失敗: {str(e)}")
            raise
        
        result.end()
        self.results["cache"] = result
        
        summary = result.get_summary()
        self.logger.info(f"✅ 緩存基準測試完成:")
        self.logger.info(f"   平均處理時間: {summary['processing_time_stats']['mean']:.3f}秒")
        self.logger.info(f"   處理速度: {summary['throughput']['images_per_second']:.2f}張/秒")
        
        return result
    
    def run_memory_optimization_benchmark(self) -> BenchmarkResult:
        """運行記憶體優化基準測試"""
        self.logger.info("🧠 開始記憶體優化基準測試...")
        
        result = BenchmarkResult("memory_optimized")
        result.start()
        
        # 創建記憶體優化配置
        memory_config = UnifiedConfig()
        memory_config.model = self.config.model
        memory_config.classes = self.config.classes
        memory_config.enable_cache = False
        memory_config.max_workers = 1
        
        try:
            with create_inference_system(config=memory_config) as system:
                memory_optimizer = MemoryOptimizer(
                    memory_config,
                    auto_cleanup_threshold=80.0
                )
                memory_optimizer.start_auto_cleanup()
                
                # 預熱
                self._warmup(system)
                
                # 記憶體優化測試
                for i, image_path in enumerate(self.test_images[:self.benchmark_runs]):
                    start_time = time.time()
                    memory_before = psutil.virtual_memory().used / (1024**2)
                    
                    try:
                        inference_result = system.process_single_image(str(image_path))
                        processing_time = time.time() - start_time
                        memory_after = psutil.virtual_memory().used / (1024**2)
                        
                        detections_count = len(inference_result.get('detections', []))
                        result.add_result(
                            processing_time=processing_time,
                            detections_count=detections_count,
                            memory_mb=memory_after - memory_before,
                            success=inference_result.get('success', False)
                        )
                        
                        # 每5張圖像執行一次記憶體清理
                        if (i + 1) % 5 == 0:
                            memory_optimizer.cleanup_memory()
                            self.logger.info(f"   記憶體優化測試進度: {i+1}/{self.benchmark_runs}")
                    
                    except Exception as e:
                        result.add_result(processing_time=time.time() - start_time, success=False)
                        self.logger.warning(f"記憶體優化測試失敗 {image_path}: {str(e)}")
                
                memory_optimizer.cleanup()
        
        except Exception as e:
            self.logger.error(f"記憶體優化基準測試失敗: {str(e)}")
            raise
        
        result.end()
        self.results["memory_optimized"] = result
        
        summary = result.get_summary()
        self.logger.info(f"✅ 記憶體優化基準測試完成:")
        self.logger.info(f"   平均處理時間: {summary['processing_time_stats']['mean']:.3f}秒")
        self.logger.info(f"   平均記憶體使用: {summary['resource_usage']['memory_mb']['mean']:.1f}MB")
        
        return result
    
    def run_comprehensive_benchmark(self, test_image_dir: str, output_dir: str = "./benchmark_results"):
        """運行全面基準測試"""
        self.logger.info("🚀 開始全面基準測試套件...")
        
        # 設置測試圖像
        self.setup_test_images(test_image_dir)
        
        # 創建輸出目錄
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 執行各項測試
        tests = [
            ("基線測試", self.run_baseline_benchmark),
            ("並發測試(4)", lambda: self.run_concurrent_benchmark(4)),
            ("並發測試(8)", lambda: self.run_concurrent_benchmark(8)),
            ("緩存測試", self.run_cache_benchmark),
            ("記憶體優化測試", self.run_memory_optimization_benchmark)
        ]
        
        for test_name, test_func in tests:
            try:
                self.logger.info(f"\n📊 執行 {test_name}...")
                test_func()
            except Exception as e:
                self.logger.error(f"❌ {test_name} 失敗: {str(e)}")
        
        # 生成對比報告
        self.generate_comparison_report(output_path)
        
        self.logger.info("🎉 全面基準測試完成！")
    
    def generate_comparison_report(self, output_dir: Path):
        """生成對比報告"""
        self.logger.info("📋 生成基準測試對比報告...")
        
        # 收集所有結果摘要
        comparison_data = {
            'timestamp': datetime.now().isoformat(),
            'test_configuration': {
                'test_images_count': len(self.test_images),
                'benchmark_runs': self.benchmark_runs,
                'warmup_runs': self.warmup_runs
            },
            'results': {}
        }
        
        for test_name, result in self.results.items():
            comparison_data['results'][test_name] = result.get_summary()
        
        # 計算性能提升比較（相對於基線）
        if 'baseline' in self.results:
            baseline_summary = self.results['baseline'].get_summary()
            baseline_throughput = baseline_summary['throughput']['images_per_second']
            
            performance_comparison = {}
            for test_name, result in self.results.items():
                if test_name != 'baseline':
                    summary = result.get_summary()
                    current_throughput = summary['throughput']['images_per_second']
                    improvement = ((current_throughput - baseline_throughput) / baseline_throughput) * 100
                    performance_comparison[test_name] = {
                        'throughput_improvement_percent': improvement,
                        'speedup_factor': current_throughput / baseline_throughput if baseline_throughput > 0 else 0
                    }
            
            comparison_data['performance_comparison'] = performance_comparison
        
        # 保存JSON報告
        json_file = output_dir / f"benchmark_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(comparison_data, f, indent=2, ensure_ascii=False)
        
        # 生成文本摘要
        txt_file = output_dir / f"benchmark_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write("📊 統一YOLO推理系統 - 基準測試報告\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"測試時間: {comparison_data['timestamp']}\n")
            f.write(f"測試圖像數量: {len(self.test_images)}\n")
            f.write(f"基準測試輪數: {self.benchmark_runs}\n\n")
            
            # 性能對比表
            f.write("🚀 性能對比結果:\n")
            f.write("-" * 60 + "\n")
            f.write(f"{'測試項目':<20} {'處理速度(張/秒)':<15} {'平均處理時間(秒)':<15} {'相對提升':<15}\n")
            f.write("-" * 60 + "\n")
            
            for test_name, result in self.results.items():
                summary = result.get_summary()
                throughput = summary['throughput']['images_per_second']
                avg_time = summary['processing_time_stats']['mean']
                
                if test_name == 'baseline':
                    improvement = "基線"
                else:
                    comp = comparison_data.get('performance_comparison', {}).get(test_name, {})
                    improvement_pct = comp.get('throughput_improvement_percent', 0)
                    improvement = f"+{improvement_pct:.1f}%"
                
                f.write(f"{test_name:<20} {throughput:<15.2f} {avg_time:<15.3f} {improvement:<15}\n")
        
        self.logger.info(f"📋 基準測試報告已生成:")
        self.logger.info(f"   JSON報告: {json_file}")
        self.logger.info(f"   文本摘要: {txt_file}")
    
    def _warmup(self, system, warmup_runs: int = None):
        """預熱系統"""
        warmup_runs = warmup_runs or self.warmup_runs
        if warmup_runs > 0 and self.test_images:
            for i in range(min(warmup_runs, len(self.test_images))):
                try:
                    system.process_single_image(str(self.test_images[i]))
                except Exception:
                    pass  # 預熱失敗不影響測試
    
    def _get_gpu_memory_usage(self) -> float:
        """獲取GPU記憶體使用量"""
        try:
            import torch
            if torch.cuda.is_available():
                return torch.cuda.memory_allocated() / (1024**2)  # MB
        except ImportError:
            pass
        return 0.0


def main():
    """主函數"""
    print("📊 統一YOLO推理系統 - 效能基準測試")
    print("=" * 70)
    
    # 設置日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建測試配置
    config = UnifiedConfig()
    
    # 設置模型路徑（請根據實際情況修改）
    config.model.model_path = r"D:\4_road_crack\best_0728.pt"
    
    # 添加測試類別
    config.classes[6] = ClassConfig(
        id=6,
        name="linear_crack",
        display_name="裂縫",
        color=[0, 255, 0],
        confidence_threshold=0.3,
        sahi_confidence_threshold=0.1,
        enabled=True
    )
    
    # 創建基準測試套件
    benchmark = BenchmarkSuite(config)
    
    try:
        # 執行全面基準測試
        test_image_dir = r"D:\image\road_crack\test_600_resize"  # 請修改為實際測試圖像目錄
        output_dir = "./benchmark_results"
        
        benchmark.run_comprehensive_benchmark(test_image_dir, output_dir)
        
        print(f"\n🎉 基準測試完成！")
        print(f"📁 結果保存至: {output_dir}")
        
    except Exception as e:
        print(f"❌ 基準測試失敗: {str(e)}")
        raise


if __name__ == "__main__":
    main()