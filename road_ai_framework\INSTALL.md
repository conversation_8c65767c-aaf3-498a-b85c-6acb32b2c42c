# 🚀 YOLO推理系統 - 安裝指南

## 📋 系統需求
- **Python**: >= 3.8 (推薦 Python 3.9+)
- **作業系統**: Windows 10/11, Ubuntu 18.04+, macOS 10.15+
- **RAM**: >= 8GB (推薦 16GB+)
- **GPU** (可選): NVIDIA GPU with CUDA 11.7+ (推薦 8GB+ VRAM)

## 🔧 快速安裝

### 1. 基礎安裝
```bash
# 克隆專案
git clone <repository_url>
cd road_ai_framework

# 安裝依賴
pip install -r requirements.txt
```

### 2. GPU支援 (推薦)
```bash
# 安裝CUDA支援版本的PyTorch
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118

# 驗證GPU可用性
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

### 3. 驗證安裝
```bash
cd scripts
python run_unified_yolo_ultimate.py
```

## 🛠️ 故障排除

### ❌ 記憶體清理功能已啟用但系統不可用
**問題**: 在其他電腦上顯示記憶體清理功能不可用

**解決方案**:
```bash
# 安裝缺失的系統監控庫
pip install psutil

# 如果仍有問題，重新安裝
pip uninstall psutil
pip install psutil
```

### ❌ CUDA/GPU相關錯誤
**問題**: GPU記憶體錯誤或CUDA不可用

**解決方案**:
```bash
# 檢查CUDA版本
nvidia-smi

# 重新安裝對應CUDA版本的PyTorch
pip uninstall torch torchvision
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### ❌ 模型文件不存在
**問題**: 找不到模型文件

**解決方案**:
1. 檢查模型路徑設定 (`run_unified_yolo_ultimate.py` 第58行)
2. 確保模型文件存在於指定路徑
3. 使用絕對路徑避免相對路徑問題

### ❌ 依賴庫版本衝突
**問題**: 庫版本不兼容

**解決方案**:
```bash
# 創建虛擬環境
python -m venv yolo_env
source yolo_env/bin/activate  # Linux/Mac
# 或
yolo_env\Scripts\activate     # Windows

# 重新安裝依賴
pip install -r requirements.txt
```

## 🎯 配置說明

### 記憶體清理設定
```python
# 在 run_unified_yolo_ultimate.py 中調整
enable_memory_cleanup = True                    # 啟用記憶體清理功能
memory_cleanup_trigger = "hybrid"              # 觸發模式
memory_cpu_threshold = 70.0                    # CPU記憶體閾值（降低=更積極清理）
memory_gpu_threshold = 70.0                    # GPU記憶體閾值
```

### 智能過濾優化設定
```python
# 🧠 智能過濾設定（已優化）
enable_intelligent_filtering = True            # 啟用智能過濾
alligator_contains_linear_threshold = 0.2     # 重疊檢測（有重疊時大box優先）

# 📏 獨立功能（可選）
enable_aspect_ratio_filter = False            # 長寬比獨立過濾
enable_area_ratio_filter = False              # 面積比獨立過濾
```

## 📚 使用範例

### 基本使用
```python
# 修改 run_unified_yolo_ultimate.py 中的路徑
segmentation_model_path = r"your_model_path.pt"
input_path = r"your_input_images"
output_path = r"your_output_directory"

# 運行推理
python run_unified_yolo_ultimate.py
```

### 進階配置
```python
# 🎯 運行模式選擇
RUNNING_MODE = RunningMode.CLASSIC     # CLASSIC/INTELLIGENT/ENTERPRISE/ULTIMATE

# 🔀 融合策略
fusion_strategy = "largest_object"     # standard_nms/largest_object/no_fusion
fusion_iou_threshold = 0.4

# 🔗 物件連結
enable_object_connection = True
connection_distance_threshold = 30.0   # 連接距離（pixels）
```

## 📞 支援

如果遇到問題：
1. 檢查 `requirements.txt` 中的版本需求
2. 確認Python版本兼容性
3. 驗證CUDA/GPU設定（如適用）
4. 檢查日誌文件 `ultimate_yolo.log`

## 🔄 更新系統
```bash
# 拉取最新版本
git pull origin main

# 更新依賴
pip install -r requirements.txt --upgrade
```