# resize_labelme_dataset.py
import argparse
import os
import json
import cv2
from PIL import Image
import numpy as np
import base64 # 導入 base64 模組

def resize_image_and_annotations(image_path, json_path, output_image_path, output_json_path, target_width, target_height, quality, output_format, simplify_epsilon):
    try:
        # 讀取圖像 (處理中文路徑)
        img = cv2.imdecode(np.fromfile(image_path, dtype=np.uint8), cv2.IMREAD_UNCHANGED)
        if img is None:
            print(f"Error: Could not read image '{image_path}'. Skipping.")
            return

        original_height, original_width = img.shape[:2]

        # 計算縮放比例
        width_scale = target_width / original_width
        height_scale = target_height / original_height

        # 調整圖像尺寸
        resized_img = cv2.resize(img, (target_width, target_height), interpolation=cv2.INTER_LINEAR)
        
        # 根據輸出格式和品質保存圖像
        encode_params = []
        if output_format.lower() == 'jpg' or output_format.lower() == 'jpeg':
            encode_params = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
            output_extension = '.jpg'
        elif output_format.lower() == 'png':
            # 將 0-100 的品質映射到 PNG 的壓縮比 0-9
            png_compression_level = round(quality / 100 * 9)
            encode_params = [int(cv2.IMWRITE_PNG_COMPRESSION), png_compression_level]
            output_extension = '.png'
        else:
            print(f"Warning: Unsupported output format '{output_format}'. Defaulting to PNG.")
            output_extension = '.png'
            png_compression_level = round(quality / 100 * 9)
            encode_params = [int(cv2.IMWRITE_PNG_COMPRESSION), png_compression_level]

        # 更新輸出圖像路徑的副檔名
        output_image_path_with_ext = os.path.splitext(output_image_path)[0] + output_extension

        _, buffer = cv2.imencode(output_extension, resized_img, encode_params)
        buffer.tofile(output_image_path_with_ext)

        # 讀取LabelMe JSON檔案 (處理中文路徑)
        with open(json_path, 'r', encoding='utf-8', errors='ignore') as f:
            data = json.load(f)

        # 更新圖像尺寸資訊
        data['imageWidth'] = target_width
        data['imageHeight'] = target_height

        # 更新 imageData 字段
        with open(output_image_path_with_ext, "rb") as f:
            data['imageData'] = base64.b64encode(f.read()).decode('utf-8')

        # 調整LabelMe座標
        for shape in data['shapes']:
            # 轉換為 numpy array 進行簡化
            points = np.array(shape['points'], dtype=np.float32)

            if simplify_epsilon > 0:
                # 簡化點 (Douglas-Peucker 演算法)
                # 對於閉合多邊形，closed=True
                # epsilon 的值通常是原始點集邊長的一個百分比
                epsilon_abs = simplify_epsilon # 絕對值，如果需要相對值，可以考慮根據 bbox diagonal
                if shape['shape_type'] in ['polygon', 'line']: # 只對多邊形和線條進行簡化
                    points = cv2.approxPolyDP(points, epsilon_abs, closed=(shape['shape_type'] == 'polygon'))
                    points = points.reshape(-1, 2) # 確保形狀為 (N, 2)
            
            # 將 numpy 數組轉換為 Python 列表，解決 float32 不可序列化的問題
            points = points.tolist()

            # 縮放點
            scaled_points = []
            for point in points:
                scaled_points.append([point[0] * width_scale, point[1] * height_scale])
            shape['points'] = scaled_points


        # 更新圖像檔名 (如果需要，通常會保持原始檔名，只是路徑改變)
        data['imagePath'] = os.path.basename(output_image_path_with_ext) # 更新為新的副檔名

        # 保存調整後的JSON檔案
        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        print(f"Processed '{os.path.basename(image_path)}' to {target_width}x{target_height} with quality {quality} and format {output_format}, points simplified with epsilon {simplify_epsilon}")

    except Exception as e:
        print(f"Error processing '{image_path}' and '{json_path}': {e}")


def main():
    parser = argparse.ArgumentParser(description="Resize images and LabelMe annotations in a dataset.")
    parser.add_argument("input_folder", type=str, help="Path to the input folder containing images and LabelMe JSON files.")
    parser.add_argument("output_folder", type=str, help="Path to the output folder to save resized images and annotations.")
    parser.add_argument("--target_width", type=int, required=True, help="Target width for resizing.")
    parser.add_argument("--target_height", type=int, required=True, help="Target height for resizing.")
    parser.add_argument("--quality", type=int, default=95, choices=range(0, 101), metavar="[0-100]",
                        help="Output image quality (0-100). Applies to JPG, for PNG it's mapped to compression level (0-9). Default is 95.")
    parser.add_argument("--output_format", type=str, default="png", choices=["jpg", "jpeg", "png"],
                        help="Output image format. Choose from 'jpg', 'jpeg', or 'png'. Default is 'png'.")
    parser.add_argument("--simplify_epsilon", type=float, default=0.0,
                        help="Douglas-Peucker algorithm epsilon for polygon/line simplification. A larger value results in fewer points. Set to 0 for no simplification. (e.g., 2.0)")


    args = parser.parse_args()

    input_folder = args.input_folder
    output_folder = args.output_folder
    target_width = args.target_width
    target_height = args.target_height
    quality = args.quality
    output_format = args.output_format
    simplify_epsilon = args.simplify_epsilon

    if not os.path.exists(input_folder):
        print(f"Error: Input folder '{input_folder}' does not exist.")
        return

    os.makedirs(output_folder, exist_ok=True)

    image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif')
    json_extensions = ('.json',)

    image_files = []
    json_files = {} # {filename_without_ext: full_path}

    print(f"Scanning folder: {input_folder}")
    for filename in os.listdir(input_folder):
        file_path = os.path.join(input_folder, filename)
        if os.path.isfile(file_path):
            if filename.lower().endswith(image_extensions):
                image_files.append(file_path)
                try:
                    with Image.open(file_path) as img:
                        print(f"Found image: {filename}, Size: {img.size[0]}x{img.size[1]}")
                except Exception as e:
                    print(f"Warning: Could not read image properties for {filename}: {e}")
            elif filename.lower().endswith(json_extensions):
                base_name = os.path.splitext(filename)[0]
                json_files[base_name] = file_path

    if not image_files:
        print("No image files found in the input folder. Exiting.")
        return

    print("\n--- Starting resizing process ---")
    for image_path in image_files:
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        json_path = json_files.get(base_name)

        if json_path:
            output_image_path = os.path.join(output_folder, os.path.basename(image_path))
            output_json_path = os.path.join(output_folder, f"{base_name}.json")
            resize_image_and_annotations(image_path, json_path, output_image_path, output_json_path, target_width, target_height, quality, output_format, simplify_epsilon)
        else:
            print(f"Warning: No matching JSON file found for image '{os.path.basename(image_path)}'. Skipping.")
            # Optionally, you could still resize the image even without a JSON.
            # However, for a LabelMe dataset, the JSON is crucial.

    print("\n--- Resizing process completed ---")


if __name__ == "__main__":
    main()