# 🚀 高級推理系統使用指南

## ✅ 修復完成

所有 TODO 項目已完成，系統現在支援：

### 🎭 完整的 Mask 處理
- ✅ **檢測和分割模型支援**：自動識別並處理 mask 數據
- ✅ **ROI 座標映射**：切片推理後正確映射 mask 到原圖座標
- ✅ **三視圖顯示**：原圖、GT、預測結果完美顯示 mask 區域
- ✅ **Mask 合併邏輯**：重疊 mask 智能合併

### 🔀 6種先進融合策略
- ✅ **Standard NMS**：經典非極大值抑制
- ✅ **Soft-NMS**：軟抑制，保留更多候選框
- ✅ **WBF**：加權框融合，最佳精度
- ✅ **DIoU-NMS**：距離 IoU 增強
- ✅ **Cluster-NMS**：聚類 NMS，適合密集場景
- ✅ **Largest Object**：保留最大物件

### 🏗️ 多框架支援
- ✅ **YOLO**：完整支援檢測和分割
- ✅ **Detectron2**：包含 mask 處理
- ✅ **MMDetection**：支援檢測和分割結果
- ✅ **HuggingFace**：Transformers 檢測模型

### ⏱️ 詳細時間分解
- ✅ **6個處理階段**：切片推理、整體推理、類別過濾、結果融合、相鄰合併等
- ✅ **性能分析**：各階段時間佔比和瓶頸分析

## 🚀 快速使用

### 1. 基本設定
```python
# 編輯 run_unified_yolo.py 頂部參數
inference_engine = "advanced"                    # 使用高級推理引擎
segmentation_model_path = "path/to/your/model.pt"  # 您的模型路徑
input_path = "path/to/input/images"              # 輸入圖像目錄
output_path = "path/to/output"                   # 輸出目錄

# 高級推理配置
fusion_strategy = "wbf"                          # 選擇融合策略
enable_overall_inference = True                 # 啟用整體推理
enable_adjacent_merge = True                    # 啟用相鄰合併
```

### 2. ROI + Advanced 檢測流程
```python
# ROI 配置（自動裁剪圖像中心區域）
enable_sahi_roi = True
roi_top_ratio = 3.0      # 控制 ROI 區域大小
roi_bottom_ratio = 2.8
roi_left_ratio = 1.3
roi_right_ratio = 1.7

# 啟用 ROI 預覽（用於調試）
force_single_image_roi_preview = True
```

### 3. 執行推理
```bash
cd road_ai_framework/
python run_unified_yolo.py
```

## 🎯 處理流程

### 完整的 ROI + Advanced 檢測流程：
```
原圖 (640x640) 
  ↓ 
🔍 ROI 中心區域裁剪 (320x320)
  ↓
🔪 Advanced 切片推理 (4個切片 + 整體推理)
  ↓  
🔀 6種融合策略處理 (WBF/Cluster-NMS等)
  ↓
🎭 Mask 座標映射回原圖 (640x640)
  ↓
🖼️ 三視圖顯示（原圖|GT|預測結果）
```

## 📊 輸出結果

### 文件結構：
```
output/
├── images/
│   ├── image_name_three_view.jpg      # 三視圖顯示 
│   └── roi_preview.jpg                # ROI 預覽圖
└── reports/
    ├── image_metrics_incremental.csv  # 圖像級別統計
    ├── class_metrics_incremental.csv  # 類別級別統計
    └── batch_summary.json            # 批次處理摘要
```

### 三視圖內容：
- **左側**：原始圖像
- **中間**：Ground Truth 標註（黃色 polygon）
- **右側**：預測結果（含 mask 和邊界框）

## 🔧 融合策略選擇

| 策略 | 適用場景 | 特點 | 推薦指數 |
|------|----------|------|----------|
| **WBF** | 多模型融合 | 最佳精度，保留更多信息 | ⭐⭐⭐⭐⭐ |
| **Cluster-NMS** | 密集場景 | 智能聚類，適合擁擠場景 | ⭐⭐⭐⭐ |
| **Soft-NMS** | 重疊物件 | 軟抑制，避免過度移除 | ⭐⭐⭐⭐ |
| **DIoU-NMS** | 一般場景 | 考慮距離，更智能 | ⭐⭐⭐ |
| **Standard NMS** | 快速處理 | 經典算法，速度最快 | ⭐⭐⭐ |
| **Largest Object** | 去重場景 | 保留最大物件 | ⭐⭐ |

## ⚡ 性能監控

啟用詳細時間分解：
```python
advanced_log_level = "detailed"  # 查看各階段耗時
```

輸出示例：
```
⏱️ 詳細時間分解:
  📊 切片推理: 0.234s (55.8%)
  📊 整體推理: 0.156s (36.4%)  
  📊 結果融合: 0.023s (5.7%)
  📊 相鄰合併: 0.012s (3.4%)
  📊 類別過濾: 0.003s (1.1%)
```

## 🎭 Mask 顯示特色

### 自動 Mask 處理：
- ✅ **格式統一**：自動轉換為 uint8 格式
- ✅ **尺寸匹配**：自動調整 mask 尺寸
- ✅ **座標映射**：切片和 ROI 座標完美映射
- ✅ **可視化**：三視圖中完美顯示 mask 區域

### 支援的模型類型：
- 🟢 **檢測模型**：顯示邊界框
- 🟡 **分割模型**：顯示 mask + 邊界框  
- 🔵 **混合模型**：智能識別並處理

## 🐛 故障排除

### 常見問題：

1. **ImportError**: 確保已添加 GroundTruthLoader 和 CSVManager 類別
2. **Mask 不顯示**: 檢查模型是否為分割模型，確認模型輸出包含 mask
3. **座標錯位**: 確認 ROI 參數設定正確
4. **性能問題**: 調整 fusion_strategy，使用 "standard_nms" 獲得最快速度
5. **✅ ThreeViewGenerator 錯誤**: `_get_gt_color_by_label` 方法已修復（2024-06-27）

### 調試建議：
```python
# 啟用詳細日誌
advanced_log_level = "detailed"

# 啟用 ROI 預覽
force_single_image_roi_preview = True

# 檢查模型輸出
print(f"模型類型: {type(model)}")
print(f"模型輸出: {model(test_image)}")
```

## 🎉 總結

現在您擁有了比 SAHI 更強大的高級推理系統：

- 🎯 **完美的 ROI 支援**：自動中心區域檢測
- 🎭 **企業級 Mask 處理**：檢測和分割模型完美支援
- 🔀 **6種先進融合策略**：適應各種場景需求
- ⏱️ **詳細性能監控**：瓶頸分析和優化建議
- 🖼️ **專業級可視化**：三視圖完美展示結果

立即體驗：修改 `run_unified_yolo.py` 頂部參數，然後運行！🚀