#!/usr/bin/env python3
"""
🧠 記憶體優化器
提供智能記憶體管理、垃圾回收和記憶體洩漏預防
"""

import gc
import logging
import threading
import time
import psutil
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime
import weakref
from contextlib import contextmanager

from ..config import UnifiedConfig


@dataclass
class MemorySnapshot:
    """記憶體快照"""
    timestamp: datetime
    total_memory_mb: float
    available_memory_mb: float
    used_memory_mb: float
    memory_percent: float
    gpu_memory_mb: Optional[float] = None
    process_memory_mb: Optional[float] = None


class MemoryOptimizer:
    """
    記憶體優化器
    
    特色功能:
    - 智能垃圾回收
    - 記憶體洩漏監測
    - GPU記憶體管理
    - 自動記憶體清理
    - 記憶體使用統計
    """
    
    def __init__(self, 
                 config: UnifiedConfig,
                 auto_cleanup_threshold: float = 85.0,  # 記憶體使用率閾值(%)
                 cleanup_interval: int = 60,  # 自動清理間隔(秒)
                 enable_gpu_optimization: bool = True):
        """
        初始化記憶體優化器
        
        Args:
            config: 統一配置
            auto_cleanup_threshold: 自動清理閾值
            cleanup_interval: 清理間隔
            enable_gpu_optimization: 啟用GPU優化
        """
        self.config = config
        self.auto_cleanup_threshold = auto_cleanup_threshold
        self.cleanup_interval = cleanup_interval
        self.enable_gpu_optimization = enable_gpu_optimization
        
        # 記憶體監控
        self.memory_snapshots: List[MemorySnapshot] = []
        self.max_snapshots = 100
        
        # 清理統計
        self.cleanup_count = 0
        self.last_cleanup_time = None
        
        # 自動清理控制
        self._auto_cleanup_enabled = False
        self._cleanup_thread: Optional[threading.Thread] = None
        self._cleanup_lock = threading.RLock()
        
        # 弱引用追蹤器（檢測記憶體洩漏）
        self._tracked_objects: List[weakref.ref] = []
        
        # GPU檢測
        self._gpu_available = self._check_gpu_availability()
        
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("🧠 記憶體優化器初始化完成")
        self.logger.info(f"   自動清理閾值: {auto_cleanup_threshold}%")
        self.logger.info(f"   清理間隔: {cleanup_interval}秒")
        self.logger.info(f"   GPU優化: {'啟用' if enable_gpu_optimization and self._gpu_available else '禁用'}")
    
    def _check_gpu_availability(self) -> bool:
        """檢查GPU可用性"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    def start_auto_cleanup(self):
        """開始自動記憶體清理"""
        with self._cleanup_lock:
            if not self._auto_cleanup_enabled:
                self._auto_cleanup_enabled = True
                self._cleanup_thread = threading.Thread(
                    target=self._auto_cleanup_loop,
                    daemon=True
                )
                self._cleanup_thread.start()
                self.logger.info("🧠 啟用自動記憶體清理")
    
    def stop_auto_cleanup(self):
        """停止自動記憶體清理"""
        with self._cleanup_lock:
            if self._auto_cleanup_enabled:
                self._auto_cleanup_enabled = False
                if self._cleanup_thread and self._cleanup_thread.is_alive():
                    self._cleanup_thread.join(timeout=2.0)
                self.logger.info("🧠 停用自動記憶體清理")
    
    def _auto_cleanup_loop(self):
        """自動清理循環"""
        while self._auto_cleanup_enabled:
            try:
                snapshot = self.get_memory_snapshot()
                
                # 檢查是否需要清理
                if snapshot.memory_percent > self.auto_cleanup_threshold:
                    self.logger.info(f"🧠 記憶體使用率過高 ({snapshot.memory_percent:.1f}%)，執行自動清理")
                    self.cleanup_memory(force=True)
                
                time.sleep(self.cleanup_interval)
                
            except Exception as e:
                self.logger.warning(f"⚠️ 自動記憶體清理錯誤: {str(e)}")
                time.sleep(self.cleanup_interval)
    
    def get_memory_snapshot(self) -> MemorySnapshot:
        """獲取當前記憶體快照"""
        # 系統記憶體
        memory = psutil.virtual_memory()
        total_memory_mb = memory.total / (1024**2)
        available_memory_mb = memory.available / (1024**2)
        used_memory_mb = memory.used / (1024**2)
        memory_percent = memory.percent
        
        # 進程記憶體
        process = psutil.Process()
        process_memory_mb = process.memory_info().rss / (1024**2)
        
        # GPU記憶體
        gpu_memory_mb = None
        if self._gpu_available and self.enable_gpu_optimization:
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_memory_mb = torch.cuda.memory_allocated() / (1024**2)
            except Exception:
                pass
        
        snapshot = MemorySnapshot(
            timestamp=datetime.now(),
            total_memory_mb=total_memory_mb,
            available_memory_mb=available_memory_mb,
            used_memory_mb=used_memory_mb,
            memory_percent=memory_percent,
            gpu_memory_mb=gpu_memory_mb,
            process_memory_mb=process_memory_mb
        )
        
        # 保存快照
        self.memory_snapshots.append(snapshot)
        if len(self.memory_snapshots) > self.max_snapshots:
            self.memory_snapshots.pop(0)
        
        return snapshot
    
    def cleanup_memory(self, force: bool = False) -> Dict[str, Any]:
        """
        執行記憶體清理
        
        Args:
            force: 強制清理所有可回收記憶體
            
        Returns:
            Dict: 清理統計信息
        """
        start_time = time.time()
        before_snapshot = self.get_memory_snapshot()
        
        # 統計信息
        stats = {
            'before_memory_mb': before_snapshot.used_memory_mb,
            'before_gpu_memory_mb': before_snapshot.gpu_memory_mb,
            'cleanup_actions': []
        }
        
        with self._cleanup_lock:
            try:
                # 1. Python垃圾回收
                if force:
                    # 強制完整垃圾回收
                    for generation in range(3):
                        collected = gc.collect()
                        if collected > 0:
                            stats['cleanup_actions'].append(f'GC gen{generation}: {collected}個對象')
                else:
                    # 輕量級垃圾回收
                    collected = gc.collect()
                    if collected > 0:
                        stats['cleanup_actions'].append(f'GC: {collected}個對象')
                
                # 2. GPU記憶體清理
                if self._gpu_available and self.enable_gpu_optimization:
                    try:
                        import torch
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()
                            torch.cuda.synchronize()  # 同步確保清理完成
                            stats['cleanup_actions'].append('GPU緩存清理')
                    except Exception as e:
                        self.logger.debug(f"GPU記憶體清理失敗: {str(e)}")
                
                # 3. 清理弱引用追蹤器中的失效引用
                before_tracked = len(self._tracked_objects)
                self._tracked_objects = [ref for ref in self._tracked_objects if ref() is not None]
                after_tracked = len(self._tracked_objects)
                if before_tracked != after_tracked:
                    stats['cleanup_actions'].append(f'清理弱引用: {before_tracked - after_tracked}個')
                
                # 4. 強制清理特定類型的對象（如果需要）
                if force:
                    # 這裡可以添加特定對象類型的清理邏輯
                    pass
                
                # 更新統計
                self.cleanup_count += 1
                self.last_cleanup_time = datetime.now()
                
            except Exception as e:
                self.logger.error(f"❌ 記憶體清理失敗: {str(e)}")
                stats['error'] = str(e)
        
        # 獲取清理後的記憶體狀態
        after_snapshot = self.get_memory_snapshot()
        cleanup_time = time.time() - start_time
        
        # 計算清理效果
        memory_freed_mb = before_snapshot.used_memory_mb - after_snapshot.used_memory_mb
        gpu_memory_freed_mb = 0
        if (before_snapshot.gpu_memory_mb is not None and 
            after_snapshot.gpu_memory_mb is not None):
            gpu_memory_freed_mb = before_snapshot.gpu_memory_mb - after_snapshot.gpu_memory_mb
        
        stats.update({
            'after_memory_mb': after_snapshot.used_memory_mb,
            'after_gpu_memory_mb': after_snapshot.gpu_memory_mb,
            'memory_freed_mb': memory_freed_mb,
            'gpu_memory_freed_mb': gpu_memory_freed_mb,
            'cleanup_time_seconds': cleanup_time,
            'cleanup_count': self.cleanup_count
        })
        
        # 記錄清理結果
        if stats['cleanup_actions']:
            self.logger.info(f"🧠 記憶體清理完成:")
            self.logger.info(f"   執行動作: {', '.join(stats['cleanup_actions'])}")
            self.logger.info(f"   釋放記憶體: {memory_freed_mb:.1f}MB")
            if gpu_memory_freed_mb > 0:
                self.logger.info(f"   釋放GPU記憶體: {gpu_memory_freed_mb:.1f}MB")
            self.logger.info(f"   清理耗時: {cleanup_time:.2f}秒")
        
        return stats
    
    @contextmanager
    def memory_context(self, cleanup_on_exit: bool = True):
        """
        記憶體管理上下文管理器
        
        Args:
            cleanup_on_exit: 退出時是否執行清理
        """
        start_snapshot = self.get_memory_snapshot()
        
        try:
            yield start_snapshot
        finally:
            if cleanup_on_exit:
                self.cleanup_memory()
    
    def track_object(self, obj: Any, name: Optional[str] = None):
        """
        追蹤對象以檢測記憶體洩漏
        
        Args:
            obj: 要追蹤的對象
            name: 對象名稱
        """
        def cleanup_callback(ref):
            if name:
                self.logger.debug(f"🗑️ 被追蹤的對象已釋放: {name}")
        
        weak_ref = weakref.ref(obj, cleanup_callback)
        self._tracked_objects.append(weak_ref)
        
        if name:
            self.logger.debug(f"📝 開始追蹤對象: {name}")
    
    def check_memory_leaks(self) -> Dict[str, Any]:
        """檢查記憶體洩漏"""
        alive_objects = [ref for ref in self._tracked_objects if ref() is not None]
        leaked_count = len(alive_objects)
        
        leak_info = {
            'tracked_objects': len(self._tracked_objects),
            'alive_objects': leaked_count,
            'potentially_leaked': leaked_count,
            'memory_snapshots_count': len(self.memory_snapshots)
        }
        
        if leaked_count > 0:
            self.logger.warning(f"⚠️ 檢測到 {leaked_count} 個可能的記憶體洩漏對象")
        
        # 分析記憶體使用趨勢
        if len(self.memory_snapshots) >= 10:
            recent_snapshots = self.memory_snapshots[-10:]
            memory_trend = []
            
            for i in range(1, len(recent_snapshots)):
                diff = recent_snapshots[i].used_memory_mb - recent_snapshots[i-1].used_memory_mb
                memory_trend.append(diff)
            
            avg_trend = sum(memory_trend) / len(memory_trend)
            
            leak_info['memory_trend_mb_per_snapshot'] = avg_trend
            
            if avg_trend > 10:  # 每次快照平均增長超過10MB
                leak_info['potential_leak_detected'] = True
                self.logger.warning(f"⚠️ 檢測到記憶體使用量持續增長: 平均每次 +{avg_trend:.1f}MB")
            else:
                leak_info['potential_leak_detected'] = False
        
        return leak_info
    
    def optimize_for_batch_processing(self, batch_size: int = 10):
        """
        為批量處理優化記憶體設定
        
        Args:
            batch_size: 批次大小
        """
        # 調整垃圾回收閾值
        gc.set_threshold(700, 10, 10)  # 更激進的垃圾回收
        
        # 調整自動清理頻率
        original_interval = self.cleanup_interval
        self.cleanup_interval = max(30, original_interval // 2)  # 更頻繁的清理
        
        # 降低自動清理閾值
        original_threshold = self.auto_cleanup_threshold
        self.auto_cleanup_threshold = max(70.0, original_threshold - 10)
        
        self.logger.info(f"🧠 記憶體優化已調整為批量處理模式:")
        self.logger.info(f"   批次大小: {batch_size}")
        self.logger.info(f"   清理間隔: {self.cleanup_interval}秒 (原: {original_interval}秒)")
        self.logger.info(f"   清理閾值: {self.auto_cleanup_threshold}% (原: {original_threshold}%)")
    
    def get_memory_statistics(self) -> Dict[str, Any]:
        """獲取記憶體統計信息"""
        current_snapshot = self.get_memory_snapshot()
        
        stats = {
            'current_memory': {
                'total_mb': current_snapshot.total_memory_mb,
                'used_mb': current_snapshot.used_memory_mb,
                'available_mb': current_snapshot.available_memory_mb,
                'usage_percent': current_snapshot.memory_percent,
                'process_memory_mb': current_snapshot.process_memory_mb
            },
            'optimization_stats': {
                'cleanup_count': self.cleanup_count,
                'last_cleanup': self.last_cleanup_time.isoformat() if self.last_cleanup_time else None,
                'auto_cleanup_enabled': self._auto_cleanup_enabled,
                'tracked_objects': len(self._tracked_objects),
                'snapshots_count': len(self.memory_snapshots)
            }
        }
        
        # GPU統計
        if current_snapshot.gpu_memory_mb is not None:
            stats['gpu_memory'] = {
                'used_mb': current_snapshot.gpu_memory_mb,
                'optimization_enabled': self.enable_gpu_optimization
            }
        
        # 記憶體使用趨勢
        if len(self.memory_snapshots) >= 5:
            recent_usage = [s.memory_percent for s in self.memory_snapshots[-5:]]
            stats['usage_trend'] = {
                'recent_average': sum(recent_usage) / len(recent_usage),
                'peak_usage': max(recent_usage),
                'min_usage': min(recent_usage)
            }
        
        # 垃圾回收統計
        gc_stats = gc.get_stats()
        if gc_stats:
            stats['gc_stats'] = gc_stats
        
        return stats
    
    def set_memory_limit(self, limit_mb: float):
        """
        設置記憶體使用限制（軟限制）
        
        Args:
            limit_mb: 記憶體限制(MB)
        """
        # 計算對應的百分比閾值
        total_memory = psutil.virtual_memory().total / (1024**2)
        threshold_percent = (limit_mb / total_memory) * 100
        
        self.auto_cleanup_threshold = min(90.0, threshold_percent)
        
        self.logger.info(f"🧠 設置記憶體軟限制: {limit_mb:.0f}MB ({threshold_percent:.1f}%)")
    
    def cleanup(self):
        """清理記憶體優化器資源"""
        self.logger.debug("🧹 清理記憶體優化器資源")
        
        # 停止自動清理
        self.stop_auto_cleanup()
        
        # 最後一次記憶體清理
        self.cleanup_memory(force=True)
        
        # 清理追蹤器
        self._tracked_objects.clear()
        self.memory_snapshots.clear()