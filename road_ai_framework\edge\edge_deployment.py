# 🌐 邊緣計算部署能力
# Phase 4 核心功能 - 離線和邊緣設備部署

import os
import json
import logging
import threading
import queue
import time
import sqlite3
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
import hashlib
import zipfile
import tempfile

class EdgeDeviceType(Enum):
    """邊緣設備類型枚舉"""
    NVIDIA_JETSON = "nvidia_jetson"    # NVIDIA Jetson系列
    RASPBERRY_PI = "raspberry_pi"      # 樹莓派
    INTEL_NUC = "intel_nuc"           # Intel NUC
    MOBILE_DEVICE = "mobile_device"    # 移動設備
    INDUSTRIAL_PC = "industrial_pc"    # 工業PC
    CUSTOM_DEVICE = "custom_device"    # 自定義設備

class DeploymentStatus(Enum):
    """部署狀態枚舉"""
    PENDING = "pending"               # 等待部署
    DOWNLOADING = "downloading"       # 下載中
    INSTALLING = "installing"         # 安裝中
    RUNNING = "running"              # 運行中
    STOPPED = "stopped"              # 已停止
    ERROR = "error"                  # 錯誤
    UPDATING = "updating"            # 更新中

@dataclass
class EdgeDeviceSpec:
    """邊緣設備規格"""
    device_type: EdgeDeviceType
    cpu_cores: int
    memory_gb: float
    storage_gb: float
    gpu_available: bool = False
    gpu_memory_gb: float = 0.0
    network_bandwidth_mbps: float = 100.0  # 網絡帶寬
    power_consumption_watts: float = 50.0   # 功耗
    operating_temp_range: tuple = (-10, 60)  # 工作溫度範圍
    
    # 支援的AI框架
    supports_tensorrt: bool = False
    supports_openvino: bool = False
    supports_onnx: bool = True
    supports_pytorch: bool = True

@dataclass
class EdgeDeploymentConfig:
    """邊緣部署配置"""
    deployment_id: str
    device_spec: EdgeDeviceSpec
    
    # 模型配置
    model_path: str
    model_format: str = "pytorch"  # pytorch, onnx, tensorrt
    optimization_level: int = 1    # 0-3 優化等級
    
    # 運行配置
    batch_size: int = 1
    max_concurrent_requests: int = 1
    enable_fp16: bool = False      # 半精度加速
    enable_int8: bool = False      # 8位整數量化
    
    # 緩存配置
    enable_local_cache: bool = True
    cache_size_mb: int = 500
    cache_ttl_hours: int = 24
    
    # 同步配置
    sync_interval_minutes: int = 60  # 同步間隔
    offline_mode: bool = False       # 離線模式
    auto_update: bool = True         # 自動更新
    
    # 監控配置
    enable_monitoring: bool = True
    log_level: str = "INFO"
    metrics_collection: bool = True

@dataclass
class EdgeDevice:
    """邊緣設備"""
    device_id: str
    device_name: str
    device_spec: EdgeDeviceSpec
    location: str = "Unknown"
    
    # 網絡信息
    ip_address: str = "127.0.0.1"
    last_seen: datetime = field(default_factory=datetime.now)
    is_online: bool = True
    
    # 部署信息
    deployment_config: Optional[EdgeDeploymentConfig] = None
    deployment_status: DeploymentStatus = DeploymentStatus.PENDING
    
    # 使用統計
    total_requests: int = 0
    total_processing_time: float = 0.0
    average_latency: float = 0.0
    error_count: int = 0
    
    # 系統狀態
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    storage_usage: float = 0.0
    temperature: float = 0.0

class EdgeModelOptimizer:
    """邊緣模型優化器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def optimize_for_device(self, model_path: str, device_spec: EdgeDeviceSpec,
                          output_path: str, optimization_level: int = 1) -> bool:
        """針對特定設備優化模型"""
        try:
            self.logger.info(f"開始為 {device_spec.device_type.value} 優化模型")
            
            # 根據設備類型選擇優化策略
            if device_spec.device_type == EdgeDeviceType.NVIDIA_JETSON:
                return self._optimize_for_jetson(model_path, device_spec, output_path, optimization_level)
            elif device_spec.device_type == EdgeDeviceType.RASPBERRY_PI:
                return self._optimize_for_raspberry_pi(model_path, device_spec, output_path, optimization_level)
            elif device_spec.device_type == EdgeDeviceType.MOBILE_DEVICE:
                return self._optimize_for_mobile(model_path, device_spec, output_path, optimization_level)
            else:
                return self._optimize_generic(model_path, device_spec, output_path, optimization_level)
                
        except Exception as e:
            self.logger.error(f"模型優化失敗: {e}")
            return False
    
    def _optimize_for_jetson(self, model_path: str, device_spec: EdgeDeviceSpec,
                           output_path: str, optimization_level: int) -> bool:
        """為NVIDIA Jetson優化"""
        # TensorRT優化
        if device_spec.supports_tensorrt:
            self.logger.info("使用TensorRT優化")
            # 模擬TensorRT優化過程
            optimizations = ["FP16優化", "動態batch", "層融合"]
            for opt in optimizations:
                self.logger.info(f"應用 {opt}")
                time.sleep(0.1)  # 模擬處理時間
        
        # 複製優化後的模型
        import shutil
        shutil.copy2(model_path, output_path)
        return True
    
    def _optimize_for_raspberry_pi(self, model_path: str, device_spec: EdgeDeviceSpec,
                                 output_path: str, optimization_level: int) -> bool:
        """為樹莓派優化"""
        self.logger.info("為樹莓派進行CPU優化")
        
        optimizations = ["INT8量化", "權重剪枝", "模型蒸餾"]
        for opt in optimizations[:optimization_level]:
            self.logger.info(f"應用 {opt}")
            time.sleep(0.1)
        
        import shutil
        shutil.copy2(model_path, output_path)
        return True
    
    def _optimize_for_mobile(self, model_path: str, device_spec: EdgeDeviceSpec,
                           output_path: str, optimization_level: int) -> bool:
        """為移動設備優化"""
        self.logger.info("為移動設備優化")
        
        optimizations = ["模型量化", "操作融合", "記憶體優化"]
        for opt in optimizations:
            self.logger.info(f"應用 {opt}")
            time.sleep(0.1)
        
        import shutil
        shutil.copy2(model_path, output_path)
        return True
    
    def _optimize_generic(self, model_path: str, device_spec: EdgeDeviceSpec,
                        output_path: str, optimization_level: int) -> bool:
        """通用優化"""
        self.logger.info("應用通用優化")
        
        import shutil
        shutil.copy2(model_path, output_path)
        return True


class EdgeDeploymentManager:
    """邊緣部署管理器"""
    
    def __init__(self, data_dir: str = "./edge_data"):
        self.logger = logging.getLogger(__name__)
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # 設備管理
        self.devices: Dict[str, EdgeDevice] = {}
        self.optimizer = EdgeModelOptimizer()
        
        # 任務隊列
        self.deployment_queue = queue.Queue()
        self.worker_thread = None
        self.running = False
        
        # 本地數據庫
        self.db_path = self.data_dir / "edge_deployments.db"
        self._init_database()
        
        # 啟動後台處理
        self.start_background_worker()
    
    def _init_database(self):
        """初始化本地數據庫"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS devices (
                    device_id TEXT PRIMARY KEY,
                    device_name TEXT,
                    device_spec TEXT,
                    location TEXT,
                    ip_address TEXT,
                    last_seen TEXT,
                    is_online BOOLEAN
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS deployments (
                    deployment_id TEXT PRIMARY KEY,
                    device_id TEXT,
                    config TEXT,
                    status TEXT,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (device_id) REFERENCES devices (device_id)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id TEXT,
                    timestamp TEXT,
                    cpu_usage REAL,
                    memory_usage REAL,
                    temperature REAL,
                    requests_count INTEGER,
                    average_latency REAL,
                    FOREIGN KEY (device_id) REFERENCES devices (device_id)
                )
            """)
    
    def register_device(self, device: EdgeDevice) -> bool:
        """註冊邊緣設備"""
        try:
            self.devices[device.device_id] = device
            
            # 持久化到數據庫
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO devices 
                    (device_id, device_name, device_spec, location, ip_address, last_seen, is_online)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    device.device_id,
                    device.device_name,
                    json.dumps(device.device_spec.__dict__),
                    device.location,
                    device.ip_address,
                    device.last_seen.isoformat(),
                    device.is_online
                ))
            
            self.logger.info(f"成功註冊邊緣設備: {device.device_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"註冊設備失敗: {e}")
            return False
    
    def create_deployment(self, device_id: str, model_path: str,
                         config_overrides: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """創建部署"""
        device = self.devices.get(device_id)
        if not device:
            self.logger.error(f"設備不存在: {device_id}")
            return None
        
        try:
            # 生成部署ID
            deployment_id = f"deploy_{device_id}_{int(time.time())}"
            
            # 創建部署配置
            config = EdgeDeploymentConfig(
                deployment_id=deployment_id,
                device_spec=device.device_spec,
                model_path=model_path
            )
            
            # 應用配置覆蓋
            if config_overrides:
                for key, value in config_overrides.items():
                    if hasattr(config, key):
                        setattr(config, key, value)
            
            # 優化配置基於設備規格
            self._optimize_config_for_device(config, device.device_spec)
            
            # 更新設備部署信息
            device.deployment_config = config
            device.deployment_status = DeploymentStatus.PENDING
            
            # 添加到部署隊列
            self.deployment_queue.put((device_id, deployment_id, config))
            
            # 持久化部署信息
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO deployments 
                    (deployment_id, device_id, config, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    deployment_id,
                    device_id,
                    json.dumps(config.__dict__, default=str),
                    DeploymentStatus.PENDING.value,
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                ))
            
            self.logger.info(f"創建部署任務: {deployment_id}")
            return deployment_id
            
        except Exception as e:
            self.logger.error(f"創建部署失敗: {e}")
            return None
    
    def _optimize_config_for_device(self, config: EdgeDeploymentConfig, 
                                  device_spec: EdgeDeviceSpec):
        """根據設備規格優化配置"""
        # 根據記憶體調整batch size
        if device_spec.memory_gb < 2:
            config.batch_size = 1
            config.cache_size_mb = 100
        elif device_spec.memory_gb < 4:
            config.batch_size = 2
            config.cache_size_mb = 250
        else:
            config.batch_size = min(4, config.batch_size)
        
        # 根據GPU可用性啟用優化
        if device_spec.gpu_available:
            config.enable_fp16 = True
            if device_spec.device_type == EdgeDeviceType.NVIDIA_JETSON:
                config.model_format = "tensorrt"
        else:
            config.enable_int8 = True  # CPU設備使用INT8
        
        # 根據存儲空間調整緩存
        if device_spec.storage_gb < 8:
            config.cache_size_mb = min(config.cache_size_mb, 200)
        
        # 根據網絡帶寬調整同步間隔
        if device_spec.network_bandwidth_mbps < 10:
            config.sync_interval_minutes = 120  # 低帶寬設備減少同步頻率
    
    def start_background_worker(self):
        """啟動後台工作線程"""
        if self.running:
            return
        
        self.running = True
        self.worker_thread = threading.Thread(target=self._deployment_worker, daemon=True)
        self.worker_thread.start()
        self.logger.info("邊緣部署後台工作線程已啟動")
    
    def _deployment_worker(self):
        """部署工作線程"""
        while self.running:
            try:
                # 從隊列獲取部署任務
                task = self.deployment_queue.get(timeout=1.0)
                device_id, deployment_id, config = task
                
                self.logger.info(f"開始處理部署任務: {deployment_id}")
                self._execute_deployment(device_id, deployment_id, config)
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"部署任務執行失敗: {e}")
    
    def _execute_deployment(self, device_id: str, deployment_id: str, 
                          config: EdgeDeploymentConfig):
        """執行部署任務"""
        device = self.devices.get(device_id)
        if not device:
            return
        
        try:
            # 更新狀態：下載中
            self._update_deployment_status(device_id, deployment_id, DeploymentStatus.DOWNLOADING)
            
            # 模擬下載模型
            self.logger.info(f"下載模型到設備 {device_id}")
            time.sleep(2)  # 模擬下載時間
            
            # 更新狀態：安裝中
            self._update_deployment_status(device_id, deployment_id, DeploymentStatus.INSTALLING)
            
            # 優化模型
            optimized_model_path = self.data_dir / f"{deployment_id}_optimized.pt"
            if self.optimizer.optimize_for_device(
                config.model_path, 
                config.device_spec, 
                str(optimized_model_path),
                config.optimization_level
            ):
                self.logger.info(f"模型優化完成: {optimized_model_path}")
            
            # 模擬安裝過程
            time.sleep(1)
            
            # 更新狀態：運行中
            self._update_deployment_status(device_id, deployment_id, DeploymentStatus.RUNNING)
            
            self.logger.info(f"部署完成: {deployment_id}")
            
        except Exception as e:
            self.logger.error(f"部署執行失敗: {e}")
            self._update_deployment_status(device_id, deployment_id, DeploymentStatus.ERROR)
    
    def _update_deployment_status(self, device_id: str, deployment_id: str, 
                                status: DeploymentStatus):
        """更新部署狀態"""
        device = self.devices.get(device_id)
        if device:
            device.deployment_status = status
        
        # 更新數據庫
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                UPDATE deployments 
                SET status = ?, updated_at = ?
                WHERE deployment_id = ?
            """, (status.value, datetime.now().isoformat(), deployment_id))
    
    def get_device_status(self, device_id: str) -> Optional[Dict[str, Any]]:
        """獲取設備狀態"""
        device = self.devices.get(device_id)
        if not device:
            return None
        
        return {
            'device_id': device.device_id,
            'device_name': device.device_name,
            'device_type': device.device_spec.device_type.value,
            'location': device.location,
            'is_online': device.is_online,
            'deployment_status': device.deployment_status.value,
            'system_stats': {
                'cpu_usage': device.cpu_usage,
                'memory_usage': device.memory_usage,
                'storage_usage': device.storage_usage,
                'temperature': device.temperature
            },
            'performance_stats': {
                'total_requests': device.total_requests,
                'average_latency': device.average_latency,
                'error_count': device.error_count
            }
        }
    
    def list_devices(self, online_only: bool = False) -> List[Dict[str, Any]]:
        """列出所有設備"""
        devices_list = []
        
        for device in self.devices.values():
            if online_only and not device.is_online:
                continue
            
            devices_list.append({
                'device_id': device.device_id,
                'device_name': device.device_name,
                'device_type': device.device_spec.device_type.value,
                'location': device.location,
                'is_online': device.is_online,
                'deployment_status': device.deployment_status.value,
                'last_seen': device.last_seen.isoformat()
            })
        
        return sorted(devices_list, key=lambda x: x['last_seen'], reverse=True)
    
    def get_deployment_history(self, device_id: str) -> List[Dict[str, Any]]:
        """獲取設備部署歷史"""
        history = []
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("""
                SELECT deployment_id, config, status, created_at, updated_at
                FROM deployments
                WHERE device_id = ?
                ORDER BY created_at DESC
            """, (device_id,))
            
            for row in cursor.fetchall():
                deployment_id, config_json, status, created_at, updated_at = row
                history.append({
                    'deployment_id': deployment_id,
                    'status': status,
                    'created_at': created_at,
                    'updated_at': updated_at,
                    'config': json.loads(config_json) if config_json else {}
                })
        
        return history
    
    def collect_device_metrics(self, device_id: str, metrics: Dict[str, Any]):
        """收集設備指標"""
        device = self.devices.get(device_id)
        if not device:
            return
        
        # 更新設備狀態
        device.cpu_usage = metrics.get('cpu_usage', 0.0)
        device.memory_usage = metrics.get('memory_usage', 0.0)
        device.storage_usage = metrics.get('storage_usage', 0.0)
        device.temperature = metrics.get('temperature', 0.0)
        device.last_seen = datetime.now()
        device.is_online = True
        
        # 存儲到數據庫
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO metrics 
                (device_id, timestamp, cpu_usage, memory_usage, temperature, 
                 requests_count, average_latency)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                device_id,
                datetime.now().isoformat(),
                metrics.get('cpu_usage', 0.0),
                metrics.get('memory_usage', 0.0),
                metrics.get('temperature', 0.0),
                metrics.get('requests_count', 0),
                metrics.get('average_latency', 0.0)
            ))
    
    def get_fleet_overview(self) -> Dict[str, Any]:
        """獲取設備集群概覽"""
        total_devices = len(self.devices)
        online_devices = sum(1 for d in self.devices.values() if d.is_online)
        running_deployments = sum(1 for d in self.devices.values() 
                                if d.deployment_status == DeploymentStatus.RUNNING)
        
        device_types = {}
        for device in self.devices.values():
            device_type = device.device_spec.device_type.value
            device_types[device_type] = device_types.get(device_type, 0) + 1
        
        return {
            'total_devices': total_devices,
            'online_devices': online_devices,
            'offline_devices': total_devices - online_devices,
            'running_deployments': running_deployments,
            'device_types': device_types,
            'overall_health': (online_devices / max(total_devices, 1)) * 100
        }
    
    def stop(self):
        """停止管理器"""
        self.running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        self.logger.info("邊緣部署管理器已停止")


# 使用示例和測試
if __name__ == "__main__":
    # 配置日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建邊緣部署管理器
    manager = EdgeDeploymentManager()
    
    # 創建測試設備
    jetson_spec = EdgeDeviceSpec(
        device_type=EdgeDeviceType.NVIDIA_JETSON,
        cpu_cores=6,
        memory_gb=8.0,
        storage_gb=32.0,
        gpu_available=True,
        gpu_memory_gb=4.0,
        supports_tensorrt=True
    )
    
    jetson_device = EdgeDevice(
        device_id="jetson_001",
        device_name="道路檢測站點A",
        device_spec=jetson_spec,
        location="台北市信義區",
        ip_address="*************"
    )
    
    # 註冊設備
    manager.register_device(jetson_device)
    
    # 創建部署
    deployment_id = manager.create_deployment(
        device_id="jetson_001",
        model_path="./models/road_detection.pt",
        config_overrides={
            'optimization_level': 2,
            'enable_fp16': True,
            'batch_size': 4
        }
    )
    
    print(f"✅ 部署ID: {deployment_id}")
    
    # 等待部署完成
    time.sleep(5)
    
    # 檢查設備狀態
    status = manager.get_device_status("jetson_001")
    print(f"📊 設備狀態: {status['deployment_status']}")
    
    # 模擬設備指標收集
    manager.collect_device_metrics("jetson_001", {
        'cpu_usage': 45.2,
        'memory_usage': 62.8,
        'temperature': 58.0,
        'requests_count': 150,
        'average_latency': 0.8
    })
    
    # 獲取集群概覽
    overview = manager.get_fleet_overview()
    print(f"🌐 集群概覽:")
    print(f"   總設備數: {overview['total_devices']}")
    print(f"   在線設備: {overview['online_devices']}")
    print(f"   運行部署: {overview['running_deployments']}")
    print(f"   整體健康度: {overview['overall_health']:.1f}%")
    
    # 停止管理器
    manager.stop()