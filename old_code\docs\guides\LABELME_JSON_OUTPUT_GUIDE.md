# 🏷️ LabelMe JSON輸出功能指南

## 📋 功能概述

本功能將YOLO模型的預測結果轉換為LabelMe格式的JSON檔案，支援一般推理和切片推理模式。生成的JSON檔案可直接在LabelMe工具中開啟，方便進行標註檢查、修正和後續處理。

### ✨ 核心特色

- **完整支援**: 一般推理和切片推理模式均支援
- **標準格式**: 生成標準LabelMe JSON格式，兼容性好
- **無base64**: 不使用base64編碼，包含圖像檔名
- **檔名一致**: JSON檔名與圖像檔名保持一致
- **可控參數**: 提供豐富的參數控制選項
- **批次處理**: 支援單張和批次圖像處理

## 🔧 參數配置

在`run_unified_yolo.py`中的LabelMe JSON輸出配置區域：

```python
# ===== 🏷️ LabelMe JSON輸出配置 =====
enable_labelme_output = True                              # 啟用LabelMe JSON輸出
labelme_output_dir = "labelme_json"                       # LabelMe JSON輸出目錄名稱（相對於output_path）
labelme_simplify_tolerance = 2.0                          # Polygon簡化容差（數值越大越簡化，0表示不簡化）
labelme_min_polygon_points = 3                            # 最小polygon點數（少於此數的會被忽略）
labelme_include_confidence = False                        # 是否在label中包含confidence分數
```

### 參數詳細說明

| 參數 | 類型 | 默認值 | 說明 |
|------|------|--------|------|
| `enable_labelme_output` | bool | True | 控制是否啟用LabelMe JSON輸出功能 |
| `labelme_output_dir` | str | "labelme_json" | JSON檔案輸出目錄名稱（相對於主輸出目錄） |
| `labelme_simplify_tolerance` | float | 2.0 | Polygon簡化容差，數值越大polygon越簡化 |
| `labelme_min_polygon_points` | int | 3 | 最小polygon點數，少於此數的會被忽略 |
| `labelme_include_confidence` | bool | False | 是否在類別名稱中包含confidence分數 |

## 📁 輸出結構

啟用功能後，輸出目錄將包含以下結構：

```
output_path/
├── images/          # 原有的可視化結果
├── reports/         # 原有的統計報告
└── labelme_json/    # 🆕 新增的LabelMe JSON檔案
    ├── image1.json
    ├── image2.json
    ├── image3.json
    └── ...
```

## 📄 JSON格式範例

生成的LabelMe JSON檔案格式：

```json
{
  "version": "4.5.6",
  "flags": {},
  "shapes": [
    {
      "label": "linear_crack",
      "points": [[123.5, 45.2], [126.8, 48.1], [130.2, 52.7], ...],
      "group_id": null,
      "shape_type": "polygon",
      "flags": {}
    },
    {
      "label": "potholes", 
      "points": [[200.1, 150.3], [205.4, 153.8], [210.9, 158.2], ...],
      "group_id": null,
      "shape_type": "polygon",
      "flags": {}
    }
  ],
  "imagePath": "image1.jpg",
  "imageData": null,
  "imageHeight": 1080,
  "imageWidth": 1920
}
```

## 🚀 使用方法

### 方法1: 快速啟用

1. **修改配置**: 在`run_unified_yolo.py`中設定：
   ```python
   enable_labelme_output = True
   ```

2. **運行推理**:
   ```bash
   python run_unified_yolo.py
   ```

3. **檢查結果**: JSON檔案將保存到`output_path/labelme_json/`目錄

### 🎉 儲存成功信息示例

#### 單張圖像處理成功信息
```
🎉 LabelMe JSON單張輸出成功!
   ✅ 檔案名稱: image1.json
   📁 完整路徑: /path/to/output/labelme_json/image1.json
   📋 檔案大小: 2.3 KB
   🎯 可在LabelMe工具中開啟使用
```

#### 批次處理成功信息
```
🎊 批次處理完成！總共生成 5 個LabelMe JSON檔案
   📁 儲存目錄: /path/to/output/labelme_json
   📊 總檢測物件: 23 個

🎉 LabelMe JSON批次輸出成功!
   ✅ 成功生成: 5 個檔案
   📁 輸出目錄: /path/to/output/labelme_json
   🎯 可在LabelMe工具中直接開啟使用
   📋 總檔案大小: 12.7 KB
```

### 方法2: 自定義配置

1. **調整參數**: 根據需求修改相關參數
   ```python
   enable_labelme_output = True
   labelme_output_dir = "my_labelme_json"     # 自定義目錄名
   labelme_simplify_tolerance = 1.0           # 更精細的polygon
   labelme_include_confidence = True          # 包含confidence分數
   ```

2. **運行推理**: 執行推理系統

3. **驗證結果**: 在LabelMe中開啟生成的JSON檔案

## 💡 使用技巧

### Polygon簡化調整

- **高精度**: `labelme_simplify_tolerance = 0.5` - 保留更多細節，檔案較大
- **標準**: `labelme_simplify_tolerance = 2.0` - 平衡精度和檔案大小
- **高壓縮**: `labelme_simplify_tolerance = 5.0` - 大幅簡化，檔案較小

### 檔案大小控制

- 調整`labelme_simplify_tolerance`值控制polygon複雜度
- 設定`labelme_min_polygon_points`過濾過小的檢測結果
- 較大的simplify_tolerance值會生成更小的JSON檔案

### 品質檢查

- 在LabelMe中開啟生成的JSON檔案驗證結果
- 檢查polygon是否正確覆蓋檢測區域
- 確認類別標籤是否正確

## 🔄 工作流程整合

### 標準工作流程

1. **YOLO推理** → 生成mask預測結果
2. **Mask轉Polygon** → 將二進制mask轉換為polygon點
3. **JSON生成** → 創建LabelMe格式JSON檔案
4. **檔案保存** → 保存到指定目錄
5. **後續處理** → 在LabelMe中開啟檢查/修正

### 批次處理流程

- 自動處理整個目錄的圖像
- 為每張圖像生成對應的JSON檔案
- 統一保存到`labelme_json/`目錄
- 提供批次處理統計信息

## 🛠️ 技術詳細

### Mask轉Polygon算法

1. **輪廓檢測**: 使用OpenCV的`findContours`找到mask輪廓
2. **最大輪廓**: 選擇面積最大的輪廓作為主要形狀
3. **Polygon簡化**: 使用`approxPolyDP`進行polygon簡化
4. **點格式轉換**: 轉換為LabelMe所需的`[[x,y]]`格式

### 支援的推理模式

- **一般推理**: 標準YOLO推理模式
- **切片推理**: 大圖像切片處理模式
- **批次處理**: 多張圖像批次處理
- **單張處理**: 單張圖像測試模式

## 🔍 故障排除

### 常見問題

**Q: 沒有生成JSON檔案**
- 檢查`enable_labelme_output`是否設為`True`
- 確認推理結果中包含有效的mask
- 檢查輸出目錄權限

**Q: JSON檔案過大**
- 增加`labelme_simplify_tolerance`值
- 調整`labelme_min_polygon_points`過濾小polygon
- 檢查mask品質是否過於複雜

**Q: Polygon精度不足**
- 減少`labelme_simplify_tolerance`值
- 檢查原始mask的品質
- 調整YOLO模型的預測精度

**Q: LabelMe無法開啟JSON檔案**
- 檢查JSON格式是否正確
- 確認圖像檔案與JSON在相同目錄或正確路徑
- 驗證JSON檔案完整性

### 調試方法

1. **檢查日誌**: 查看推理過程中的LabelMe相關日誌
2. **驗證JSON**: 使用JSON驗證工具檢查格式
3. **測試單張**: 先用單張圖像測試功能
4. **參數調整**: 逐步調整參數找到最佳設定

## 📊 性能優化

### 建議配置

**大批次處理**:
```python
labelme_simplify_tolerance = 3.0      # 提高處理速度
labelme_min_polygon_points = 5        # 過濾小檢測
```

**高精度需求**:
```python
labelme_simplify_tolerance = 1.0      # 保持高精度
labelme_min_polygon_points = 3        # 保留更多檢測
```

**檔案大小優化**:
```python
labelme_simplify_tolerance = 5.0      # 大幅簡化
labelme_min_polygon_points = 8        # 過濾複雜polygon
```

## 🎯 最佳實踐

1. **參數調優**: 根據具體應用場景調整參數
2. **批次測試**: 先用小批次測試參數效果
3. **品質驗證**: 定期在LabelMe中檢查生成品質
4. **檔案管理**: 建立清晰的檔案命名和組織結構
5. **備份重要**: 重要的JSON檔案要做好備份

## 🔄 版本更新

### v1.0.0 功能
- ✅ 基本mask轉polygon功能
- ✅ LabelMe JSON格式生成
- ✅ 批次處理支援
- ✅ 參數化配置
- ✅ 推理系統整合

### 未來計劃
- 🔄 更多polygon簡化算法選項
- 🔄 自定義JSON模板支援
- 🔄 增量更新模式
- 🔄 品質評估指標

---

💡 **提示**: 這個功能已經完全整合到統一YOLO推理系統中，只需要簡單的參數設定即可使用。生成的JSON檔案完全兼容LabelMe工具，可以進行進一步的標註編輯和處理。