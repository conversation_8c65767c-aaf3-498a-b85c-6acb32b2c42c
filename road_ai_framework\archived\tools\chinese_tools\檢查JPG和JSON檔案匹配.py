import os

def check_image_json_match(folder_path):
    """
    檢查指定資料夾中，同名的 JPG 或 JPEG 圖片檔和 JSON 描述檔是否匹配。

    Args:
        folder_path (str): 要檢查的資料夾路徑。

    Returns:
        tuple: 包含兩個集合的元組。
               第一個集合是缺少 .json 檔的圖片檔名（不含副檔名）。
               第二個集合是缺少圖片檔的 .json 檔名（不含副檔名）。
    """
    if not os.path.isdir(folder_path):
        print(f"錯誤：資料夾 '{folder_path}' 不存在。")
        return set(), set()

    all_files = os.listdir(folder_path)
    image_files = set()
    json_files = set()

    # 遍歷所有檔案，將 .jpg 和 .jpeg 及 .json 檔案的主檔名分別加入集合
    for file_name in all_files:
        name, ext = os.path.splitext(file_name)
        ext = ext.lower()
        if ext in ('.jpg', '.jpeg'):
            image_files.add(name)
        elif ext == '.json':
            json_files.add(name)

    # 找出缺少 .json 檔的圖片檔
    missing_json_for_img = image_files - json_files
    # 找出缺少圖片檔的 .json 檔
    missing_img_for_json = json_files - image_files

    return missing_json_for_img, missing_img_for_json


# --- 使用範例 ---
if __name__ == "__main__":
    folder_to_check = r'\\192.168.1.46\RD_Universe\專案\2025_3.道路破損辨識\1.專案執行\模型訓練用資料'

    missing_json, missing_img = check_image_json_match(folder_to_check)

    if not missing_json and not missing_img:
        print(f"在資料夾 '{folder_to_check}' 中，所有 JPG/JPEG 和 JSON 檔案都完美匹配！")
    else:
        if missing_json:
            print("\n--- 缺少 JSON 檔案的圖片檔案 ---")
            for filename in sorted(list(missing_json)):
                print(f"  - {filename}.jpg/.jpeg 缺少對應的 .json 檔案")

        if missing_img:
            print("\n--- 缺少 JPG/JPEG 檔案的 JSON 檔案 ---")
            for filename in sorted(list(missing_img)):
                print(f"  - {filename}.json 缺少對應的圖片檔案")
