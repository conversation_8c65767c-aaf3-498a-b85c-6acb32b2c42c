#!/usr/bin/env python3
"""
🏗️ 統一YOLO推理系統
現代化、模組化的YOLO推理架構

主要功能：
- 🔧 統一配置管理
- 🤖 多模型支援
- 🧩 SAHI切片推理  
- 🧠 智能過濾算法
- 🎨 三視圖視覺化
- 📊 統計報告生成
"""

from .main import UnifiedYOLOInference, create_inference_system

# 核心模組
from .core import (
    BaseInference,
    UnifiedInferenceEngine, 
    ModelAdapter
)

# 配置系統
from .config import (
    UnifiedConfig,
    ModelConfig,
    SliceConfig,
    VisualizationConfig,
    ClassConfig
)

# 處理模組
from .processing import (
    SliceProcessor,
    FusionEngine,
    PostProcessor
)

# 視覺化模組
from .visualization import (
    FontManager,
    ThreeViewGenerator
)

# IO模組
from .io import (
    CSVManager,
    GroundTruthLoader,
    ResumeManager
)

# 工具函數
from .utils import (
    calculate_iou,
    calculate_confusion_metrics,
    calculate_detection_statistics
)

__all__ = [
    # 主要接口
    "UnifiedYOLOInference",
    "create_inference_system",
    
    # 核心組件
    "BaseInference",
    "UnifiedInferenceEngine",
    "ModelAdapter",
    
    # 配置系統
    "UnifiedConfig",
    "ModelConfig", 
    "SliceConfig",
    "VisualizationConfig",
    "ClassConfig",
    
    # 處理模組
    "SliceProcessor",
    "FusionEngine", 
    "PostProcessor",
    
    # 視覺化模組
    "FontManager",
    "ThreeViewGenerator",
    
    # IO模組
    "CSVManager",
    "GroundTruthLoader",
    "ResumeManager",
    
    # 工具函數
    "calculate_iou",
    "calculate_confusion_metrics", 
    "calculate_detection_statistics"
]

__version__ = "3.0.0"
__author__ = "Road AI Framework Team"
__description__ = "統一YOLO推理系統 - 現代化模組架構"