#!/usr/bin/env python3
"""
🔄 中斷恢復管理器
提供JSON進度追蹤、斷點續傳功能
支援批量處理中的智能恢復
"""

import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from dataclasses import dataclass, asdict

from ..config import UnifiedConfig


@dataclass
class ProcessingProgress:
    """處理進度記錄"""
    image_path: str
    status: str  # "pending", "processing", "completed", "failed"
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error_message: Optional[str] = None
    detection_count: Optional[int] = None
    processing_duration: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProcessingProgress':
        """從字典創建實例"""
        return cls(**data)


@dataclass
class ResumeCheckpoint:
    """恢復檢查點"""
    session_id: str
    config_hash: str
    total_images: int
    completed_images: int
    failed_images: int
    start_time: float
    last_update_time: float
    output_path: str
    progress_records: List[ProcessingProgress]
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        data = asdict(self)
        # 轉換 ProcessingProgress 對象
        data['progress_records'] = [record.to_dict() for record in self.progress_records]
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ResumeCheckpoint':
        """從字典創建實例"""
        # 轉換 progress_records
        progress_records = [
            ProcessingProgress.from_dict(record_data) 
            for record_data in data.get('progress_records', [])
        ]
        data['progress_records'] = progress_records
        return cls(**data)


class ResumeManager:
    """
    中斷恢復管理器
    
    核心功能:
    - JSON格式的進度儲存
    - 智能斷點續傳
    - 配置變更檢測
    - 處理失敗重試
    """
    
    def __init__(self, config: UnifiedConfig, checkpoint_file: Optional[str] = None):
        """
        初始化恢復管理器
        
        Args:
            config: 統一配置
            checkpoint_file: 檢查點文件路徑
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 檢查點文件路徑
        if checkpoint_file:
            self.checkpoint_file = Path(checkpoint_file)
        else:
            output_dir = Path(config.output_path) if config.output_path else Path("./output")
            self.checkpoint_file = output_dir / "resume_checkpoint.json"
        
        # 確保目錄存在
        self.checkpoint_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 當前檢查點
        self.checkpoint: Optional[ResumeCheckpoint] = None
        
        # 配置哈希（用於檢測配置變更）
        self.config_hash = self._calculate_config_hash()
        
        # 🔄 會話ID管理：優先使用現有會話，避免重複創建
        self.session_id = self._get_or_create_session_id()
        
        self.logger.info(f"🔄 中斷恢復管理器初始化完成")
        self.logger.info(f"   檢查點文件: {self.checkpoint_file}")
        self.logger.info(f"   會話ID: {self.session_id}")
    
    def _calculate_config_hash(self) -> str:
        """計算配置哈希值 - 使用穩定的配置項"""
        import hashlib
        
        # 🔒 提取關鍵配置項目計算哈希 - 確保穩定性
        key_config = {
            'model_path': getattr(self.config.model, 'segmentation_model_path', ''),
            'detection_path': getattr(self.config.model, 'detection_model_path', ''),
            # 移除input_path避免路徑變化影響恢復
            'output_path': getattr(self.config, 'output_path', ''),
            'slice_enabled': getattr(self.config.processing.slice, 'enabled', False),
            'slice_size': (
                getattr(self.config.processing.slice, 'height', 640), 
                getattr(self.config.processing.slice, 'width', 640)
            ),
            'classes': {k: v.name for k, v in self.config.classes.items() if v.enabled} if hasattr(self.config, 'classes') else {}
        }
        
        config_str = json.dumps(key_config, sort_keys=True, ensure_ascii=True)
        return hashlib.md5(config_str.encode('utf-8')).hexdigest()[:16]
    
    def _get_or_create_session_id(self) -> str:
        """🔄 獲取或創建會話ID - 優先使用現有會話"""
        # 先嘗試從現有檢查點載入會話ID
        if self.checkpoint_file.exists():
            try:
                with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                existing_session_id = data.get('session_id', '')
                existing_config_hash = data.get('config_hash', '')
                
                # 配置相同且會話ID存在，重用會話
                if existing_config_hash == self.config_hash and existing_session_id:
                    self.logger.info(f"🔄 重用現有會話: {existing_session_id}")
                    return existing_session_id
                else:
                    self.logger.info(f"⚠️ 配置變更，創建新會話 (舊hash: {existing_config_hash}, 新hash: {self.config_hash})")
            except Exception as e:
                self.logger.debug(f"載入現有會話失敗，創建新會話: {e}")
        
        # 創建新會話ID
        new_session_id = f"session_{int(time.time())}"
        self.logger.info(f"🆕 創建新會話: {new_session_id}")
        return new_session_id
    
    def can_resume(self) -> bool:
        """
        檢查是否可以恢復
        
        Returns:
            bool: 是否可以恢復
        """
        if not self.checkpoint_file.exists():
            self.logger.info("🔄 未找到檢查點文件，將開始新的處理會話")
            return False
        
        try:
            checkpoint = self.load_checkpoint()
            if not checkpoint:
                return False
            
            # 檢查配置是否變更
            if checkpoint.config_hash != self.config_hash:
                self.logger.warning("⚠️ 配置已變更，無法恢復舊會話")
                self.logger.warning(f"   舊配置哈希: {checkpoint.config_hash}")
                self.logger.warning(f"   新配置哈希: {self.config_hash}")
                return False
            
            # 檢查輸出路徑是否一致
            if checkpoint.output_path != str(self.config.output_path):
                self.logger.warning("⚠️ 輸出路徑已變更，無法恢復舊會話")
                return False
            
            incomplete_count = len([r for r in checkpoint.progress_records 
                                  if r.status in ["pending", "processing"]])
            
            if incomplete_count > 0:
                self.logger.info(f"✅ 找到可恢復的會話")
                self.logger.info(f"   總圖像: {checkpoint.total_images}")
                self.logger.info(f"   已完成: {checkpoint.completed_images}")
                self.logger.info(f"   待處理: {incomplete_count}")
                self.logger.info(f"   失敗: {checkpoint.failed_images}")
                return True
            
            self.logger.info("ℹ️ 所有圖像已處理完成，無需恢復")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ 檢查恢復狀態時發生錯誤: {e}")
            return False
    
    def load_checkpoint(self) -> Optional[ResumeCheckpoint]:
        """
        載入檢查點
        
        Returns:
            Optional[ResumeCheckpoint]: 檢查點對象，失敗返回None
        """
        if not self.checkpoint_file.exists():
            return None
        
        try:
            with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            checkpoint = ResumeCheckpoint.from_dict(data)
            self.checkpoint = checkpoint
            self.logger.info(f"✅ 載入檢查點成功: {self.checkpoint_file}")
            return checkpoint
            
        except Exception as e:
            self.logger.error(f"❌ 載入檢查點失敗: {e}")
            return None
    
    def save_checkpoint(self):
        """保存檢查點到文件"""
        if not self.checkpoint:
            return
        
        try:
            # 更新最後修改時間
            self.checkpoint.last_update_time = time.time()
            
            # 保存到文件
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(self.checkpoint.to_dict(), f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"💾 檢查點已保存: {self.checkpoint_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 保存檢查點失敗: {e}")
    
    def initialize_session(self, image_paths: List[str]) -> ResumeCheckpoint:
        """
        初始化新的處理會話
        
        Args:
            image_paths: 圖像路徑列表
            
        Returns:
            ResumeCheckpoint: 新的檢查點
        """
        # 創建進度記錄
        progress_records = [
            ProcessingProgress(image_path=str(path), status="pending")
            for path in image_paths
        ]
        
        # 創建檢查點
        self.checkpoint = ResumeCheckpoint(
            session_id=self.session_id,
            config_hash=self.config_hash,
            total_images=len(image_paths),
            completed_images=0,
            failed_images=0,
            start_time=time.time(),
            last_update_time=time.time(),
            output_path=str(self.config.output_path),
            progress_records=progress_records
        )
        
        # 保存初始檢查點
        self.save_checkpoint()
        
        self.logger.info(f"🆕 新會話初始化完成")
        self.logger.info(f"   總圖像數: {len(image_paths)}")
        self.logger.info(f"   會話ID: {self.session_id}")
        
        return self.checkpoint
    
    def get_pending_images(self) -> List[str]:
        """
        獲取待處理的圖像列表
        
        Returns:
            List[str]: 待處理圖像路徑列表
        """
        if not self.checkpoint:
            return []
        
        pending_images = [
            record.image_path 
            for record in self.checkpoint.progress_records 
            if record.status in ["pending", "processing"]  # 包含處理中的圖像（可能中斷了）
        ]
        
        self.logger.info(f"📋 待處理圖像: {len(pending_images)} 張")
        return pending_images
    
    def mark_image_start(self, image_path: str):
        """
        標記圖像開始處理
        
        Args:
            image_path: 圖像路徑
        """
        if not self.checkpoint:
            return
        
        for record in self.checkpoint.progress_records:
            if record.image_path == image_path:
                record.status = "processing"
                record.start_time = time.time()
                break
        
        self.save_checkpoint()
        self.logger.debug(f"🏁 開始處理: {Path(image_path).name}")
    
    def mark_image_completed(self, image_path: str, detection_count: int, processing_duration: float):
        """
        標記圖像完成處理
        
        Args:
            image_path: 圖像路徑
            detection_count: 檢測到的對象數量
            processing_duration: 處理時長
        """
        if not self.checkpoint:
            return
        
        for record in self.checkpoint.progress_records:
            if record.image_path == image_path:
                record.status = "completed"
                record.end_time = time.time()
                record.detection_count = detection_count
                record.processing_duration = processing_duration
                break
        
        # 更新完成計數
        self.checkpoint.completed_images = len([
            r for r in self.checkpoint.progress_records if r.status == "completed"
        ])
        
        self.save_checkpoint()
        self.logger.debug(f"✅ 完成處理: {Path(image_path).name} ({detection_count}個檢測)")
    
    def mark_image_failed(self, image_path: str, error_message: str):
        """
        標記圖像處理失敗
        
        Args:
            image_path: 圖像路徑
            error_message: 錯誤訊息
        """
        if not self.checkpoint:
            return
        
        for record in self.checkpoint.progress_records:
            if record.image_path == image_path:
                record.status = "failed"
                record.end_time = time.time()
                record.error_message = error_message
                break
        
        # 更新失敗計數
        self.checkpoint.failed_images = len([
            r for r in self.checkpoint.progress_records if r.status == "failed"
        ])
        
        self.save_checkpoint()
        self.logger.error(f"❌ 處理失敗: {Path(image_path).name} - {error_message}")
    
    def get_progress_summary(self) -> Dict[str, Any]:
        """
        獲取進度摘要
        
        Returns:
            Dict[str, Any]: 進度摘要
        """
        if not self.checkpoint:
            return {"status": "no_session"}
        
        pending = len([r for r in self.checkpoint.progress_records if r.status == "pending"])
        processing = len([r for r in self.checkpoint.progress_records if r.status == "processing"])
        completed = self.checkpoint.completed_images
        failed = self.checkpoint.failed_images
        total = self.checkpoint.total_images
        
        progress_percent = (completed / total * 100) if total > 0 else 0
        
        # 計算平均處理時間
        completed_records = [r for r in self.checkpoint.progress_records 
                           if r.status == "completed" and r.processing_duration]
        avg_duration = (sum(r.processing_duration for r in completed_records) / 
                       len(completed_records)) if completed_records else 0
        
        # 預估剩餘時間
        remaining_images = pending + processing
        estimated_remaining_time = remaining_images * avg_duration if avg_duration > 0 else None
        
        return {
            "session_id": self.checkpoint.session_id,
            "total_images": total,
            "completed": completed,
            "failed": failed,
            "pending": pending,
            "processing": processing,
            "progress_percent": round(progress_percent, 1),
            "average_processing_time": round(avg_duration, 2) if avg_duration > 0 else 0,
            "estimated_remaining_time": round(estimated_remaining_time, 1) if estimated_remaining_time else None,
            "session_duration": time.time() - self.checkpoint.start_time,
            "last_update": datetime.fromtimestamp(self.checkpoint.last_update_time).strftime("%Y-%m-%d %H:%M:%S")
        }
    
    def cleanup_checkpoint(self):
        """清理檢查點文件（處理完成後調用）"""
        try:
            if self.checkpoint_file.exists():
                self.checkpoint_file.unlink()
                self.logger.info("🧹 檢查點文件已清理")
        except Exception as e:
            self.logger.error(f"❌ 清理檢查點文件失敗: {e}")
    
    def export_failed_list(self, output_file: Optional[str] = None) -> List[str]:
        """
        導出失敗圖像列表
        
        Args:
            output_file: 輸出文件路徑
            
        Returns:
            List[str]: 失敗圖像路徑列表
        """
        if not self.checkpoint:
            return []
        
        failed_images = [
            record.image_path 
            for record in self.checkpoint.progress_records 
            if record.status == "failed"
        ]
        
        if output_file and failed_images:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    for image_path in failed_images:
                        f.write(f"{image_path}\n")
                self.logger.info(f"📄 失敗圖像列表已導出: {output_file}")
            except Exception as e:
                self.logger.error(f"❌ 導出失敗列表失敗: {e}")
        
        return failed_images