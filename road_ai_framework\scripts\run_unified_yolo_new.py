#!/usr/bin/env python3
"""
🚀 統一YOLO推理系統 - 新版主運行腳本
使用重構後的模組化架構，替代原有的巨大腳本文件

特色功能：
- 🔧 模組化架構，70%代碼量減少
- 🧠 智能過濾算法，解決linear_crack vs Alligator_crack衝突
- 🎨 三視圖視覺化，支援水平/垂直佈局
- 📊 豐富統計報告，CSV + JSON雙格式
- 🧩 高級SAHI切片推理，6種融合策略
- 🏷️ 類別精細控制，獨立閾值和顏色配置
"""

from pathlib import Path
import sys
import logging

# 確保導入路徑正確 - 指向父目錄（road_ai_framework根目錄）
current_dir = Path(__file__).parent  # scripts/
project_root = current_dir.parent    # road_ai_framework/
sys.path.insert(0, str(project_root))

from inference_system import create_inference_system, UnifiedConfig, ClassConfig

# ============================================================================
# 📋 核心參數設定區域 - 所有設定集中在這裡
# ============================================================================

# 🤖 模型設定
segmentation_model_path = r"D:\4_road_crack\best_0728.pt"  # 分割模型路徑
detection_model_path = None                              # 檢測模型路徑（可選）

# 📁 路徑設定
input_path = r"D:\image\road_crack\test_600_resize"                       # 輸入圖像路徑/目錄
output_path = r"D:\image\road_crack\test_600_out_0728_3"                    # 輸出結果目錄

# 🧩 SAHI切片推理設定
enable_sahi = False                                     # 啟用SAHI大圖切片
sahi_slice_height = 512                                 # 切片高度
sahi_slice_width = 512                                  # 切片寬度
sahi_overlap_ratio = 0.2                               # 重疊比例

# 🧠 智能過濾設定
enable_intelligent_filtering = True                     # 啟用智能過濾
linear_aspect_ratio_threshold = 0.8                    # Step1: 長寬比閾值
area_ratio_threshold = 0.4                             # Step1: 面積比閾值  
step2_iou_threshold = 0.3                              # Step2: IoU閾值

# 🎨 視覺化設定
enable_three_view_output = True                        # 啟用三視圖輸出
three_view_layout = "horizontal"                       # 佈局: horizontal/vertical
font_size = 1.0                                        # 字體大小倍數
font_thickness = 2                                     # 字體粗細
line_thickness = 2                                     # 邊框線條粗細
transparency = 0.3                                     # 遮罩透明度
output_image_quality = 95                              # 輸出圖像質量(1-100)

# 🏷️ 類別配置 - 每個類別獨立設定
# 格式: [名稱, 顯示名, RGB顏色, 置信度閾值, SAHI閾值, 是否啟用]
class_configs = {
    0: ["Alligator_crack", "龜裂", [0, 0, 255], 0.4, 0.1, True],
    1: ["deformation", "變形", [100, 100, 100], 0.1, 0.1, True],
    2: ["dirt", "汙垢", [110, 110, 110], 0.1, 0.08, True],
    3: ["expansion_joint", "伸縮縫", [120, 120, 120], 0.1, 0.08, True],
    4: ["joint", "路面接縫", [130, 130, 130], 0.1, 0.1, True],
    5: ["lane_line_linear", "白線裂縫", [140, 140, 140], 0.1, 0.05, True],
    6: ["linear_crack", "裂縫", [0, 255, 0], 0.3, 0.1, True],
    7: ["manhole", "孔蓋", [255, 0, 255], 0.1, 0.1, True],
    8: ["patch", "補綻", [255, 0, 0], 0.1, 0.1, True],
    9: ["patch_square", "補綻_方正", [160, 160, 160], 0.1, 0.1, True],
    10: ["potholes", "坑洞", [0, 255, 255], 0.1, 0.1, True],
    11: ["rutting", "車轍", [255, 255, 0], 0.1, 0.1, True]
}

# 📊 輸出設定
enable_csv_output = True                               # 啟用CSV統計輸出
enable_json_output = True                              # 啟用JSON結果輸出
save_confidence_distribution = True                    # 保存置信度分佈
save_detection_images = True                           # 保存檢測結果圖像

# 🔧 系統設定
max_workers = 1                                        # 並行處理數量
enable_cache = True                                    # 啟用緩存
log_level = "INFO"                                     # 日誌級別: DEBUG/INFO/WARNING/ERROR

# ============================================================================
# 🏗️ 自動配置生成 - 無需修改
# ============================================================================

def create_config_from_parameters():
    """根據上述參數自動生成統一配置"""
    
    # 轉換類別配置
    classes = {}
    for class_id, config_list in class_configs.items():
        name, display_name, color, conf_thresh, sahi_thresh, enabled = config_list
        classes[class_id] = ClassConfig(
            name=name,
            display_name=display_name,
            color=color,
            confidence=conf_thresh,
            sahi_confidence=sahi_thresh,
            enabled=enabled
        )
    
    # 創建統一配置
    config = UnifiedConfig()
    
    # 模型配置
    config.model.model_path = segmentation_model_path
    config.model.detection_model_path = detection_model_path
    
    # SAHI配置
    config.processing.slice.enabled = enable_sahi
    config.processing.slice.height = sahi_slice_height
    config.processing.slice.width = sahi_slice_width
    config.processing.slice.overlap_ratio = sahi_overlap_ratio
    
    # 視覺化配置
    config.visualization.enable_three_view = enable_three_view_output
    config.visualization.three_view_layout = three_view_layout
    config.visualization.font_size = font_size
    config.visualization.font_thickness = font_thickness
    config.visualization.line_thickness = line_thickness
    config.visualization.transparency = transparency
    config.visualization.output_image_quality = output_image_quality
    
    # 智能過濾配置
    config.enable_intelligent_filtering = enable_intelligent_filtering
    config.linear_aspect_ratio_threshold = linear_aspect_ratio_threshold
    config.area_ratio_threshold = area_ratio_threshold
    config.step2_iou_threshold = step2_iou_threshold
    
    # 類別配置
    config.classes = classes
    
    # 系統配置
    config.output_dir = output_path
    config.max_workers = max_workers
    config.enable_cache = enable_cache
    
    return config


# ============================================================================
# 🚀 主執行邏輯
# ============================================================================

def main():
    """主函數 - 執行統一YOLO推理"""
    
    # 設置日誌
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("🚀 啟動統一YOLO推理系統（新版模組化架構）")
    
    try:
        # 1. 生成配置
        config = create_config_from_parameters()
        logger.info(f"⚙️ 配置載入完成，啟用類別數: {len([c for c in config.classes.values() if c.enabled])}")
        
        # 2. 創建推理系統
        with create_inference_system(config=config) as inference_system:
            
            # 3. 檢查輸入路徑
            input_path_obj = Path(input_path)
            if not input_path_obj.exists():
                raise ValueError(f"輸入路徑不存在: {input_path}")
            
            # 4. 執行推理
            if input_path_obj.is_file():
                # 單張圖像處理
                logger.info(f"📸 處理單張圖像: {input_path_obj.name}")
                result = inference_system.process_single_image(
                    str(input_path_obj),
                    enable_visualization=save_detection_images,
                    enable_statistics=enable_csv_output
                )
                
                if result['success']:
                    logger.info(f"✅ 處理完成，檢測到 {len(result['detections'])} 個目標")
                    logger.info(f"📊 統計信息: {result['detection_statistics']}")
                    if result.get('visualization_paths'):
                        logger.info(f"🎨 視覺化結果: {result['visualization_paths']}")
                else:
                    logger.error(f"❌ 處理失敗: {result['error']}")
            
            elif input_path_obj.is_dir():
                # 批量目錄處理
                logger.info(f"📁 批量處理目錄: {input_path}")
                summary = inference_system.process_directory(str(input_path_obj))
                
                if 'success_rate' in summary:
                    logger.info(f"✅ 批量處理完成!")
                    logger.info(f"📊 成功率: {summary['success_rate']:.1f}%")
                    logger.info(f"📊 總圖像數: {summary['total_images']}")
                    logger.info(f"📊 總檢測數: {summary['total_detections']}")
                    logger.info(f"📊 平均每圖檢測數: {summary['average_detections_per_image']:.1f}")
                else:
                    logger.error(f"❌ 批量處理失敗: {summary['error']}")
            
            else:
                raise ValueError(f"不支援的輸入類型: {input_path}")
    
    except Exception as e:
        logger.error(f"❌ 系統執行失敗: {str(e)}")
        raise
    
    logger.info("🎉 統一YOLO推理系統執行完成")


if __name__ == "__main__":
    main()