#!/usr/bin/env python3
"""
🔍 LabelMe JSON輸出診斷工具
用於診斷為什麼LabelMe JSON檔案沒有生成
"""

import sys
import os
from pathlib import Path
import logging

# 設置詳細日誌
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

# 添加項目路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_model_output():
    """測試模型是否輸出mask數據"""
    print("🔍 測試1: 檢查模型輸出")
    
    try:
        from ultralytics import YOLO
        model_path = r"D:\4_road_crack\best.pt"
        
        if not Path(model_path).exists():
            print(f"❌ 模型檔案不存在: {model_path}")
            return False
            
        model = YOLO(model_path)
        print(f"✅ 模型加載成功: {model_path}")
        
        # 檢查模型類型
        if hasattr(model, 'task'):
            print(f"📝 模型任務類型: {model.task}")
        
        # 測試一張圖像
        test_image_dir = r"D:\image\5_test_image\test_2_org"
        if Path(test_image_dir).exists():
            image_files = list(Path(test_image_dir).glob("*.jpg"))
            if image_files:
                test_image = str(image_files[0])
                print(f"🖼️ 測試圖像: {Path(test_image).name}")
                
                results = model(test_image)
                for result in results:
                    print(f"📊 檢測框數量: {len(result.boxes) if result.boxes else 0}")
                    
                    if hasattr(result, 'masks') and result.masks is not None:
                        print(f"✅ 檢測到分割masks: {result.masks.data.shape}")
                        return True
                    else:
                        print(f"❌ 沒有檢測到masks - 這可能是檢測模型而非分割模型")
                        return False
        else:
            print(f"❌ 測試圖像目錄不存在: {test_image_dir}")
            
    except Exception as e:
        print(f"❌ 模型測試失敗: {e}")
        return False
    
    return False

def test_config_creation():
    """測試配置創建"""
    print("\n🔍 測試2: 檢查配置創建")
    
    try:
        # 導入配置函數 - 簡化版本避免torch依賴
        config_data = {
            'enable_labelme_output': True,
            'labelme_output_dir': 'labelme_json',
            'labelme_simplify_tolerance': 2.0,
            'labelme_min_polygon_points': 3,
            'labelme_include_confidence': False
        }
        
        print("✅ LabelMe配置數據:")
        for key, value in config_data.items():
            print(f"   {key}: {value}")
            
        return True
        
    except Exception as e:
        print(f"❌ 配置創建失敗: {e}")
        return False

def test_labelme_integration():
    """測試LabelMe整合器創建"""
    print("\n🔍 測試3: 檢查LabelMe整合器")
    
    try:
        # 創建簡化的配置對象
        class MockConfig:
            def __init__(self):
                self.labelme_output = type('obj', (object,), {})()
                self.labelme_output.enable_labelme_output = True
                self.labelme_output.labelme_output_dir = 'labelme_json'
                self.labelme_output.labelme_simplify_tolerance = 2.0
                self.labelme_output.labelme_min_polygon_points = 3
                self.labelme_output.labelme_include_confidence = False
                
                self.paths = type('obj', (object,), {})()
                self.paths.output_path = r"D:\image\5_test_image\test_2_out"
                
                self.classes = {
                    0: type('obj', (object,), {'name': 'expansion_joint'})(),
                    1: type('obj', (object,), {'name': 'joint'})(),
                    2: type('obj', (object,), {'name': 'linear_crack'})(),
                }
        
        config = MockConfig()
        
        # 導入並測試LabelMe整合器
        from models.inference.labelme_integration import LabelMeIntegration
        
        integration = LabelMeIntegration(config)
        print(f"✅ LabelMe整合器創建成功")
        print(f"   啟用狀態: {integration.enabled}")
        print(f"   輸出目錄: {integration.output_dir}")
        print(f"   類別映射: {integration.class_names}")
        
        return integration.enabled
        
    except Exception as e:
        print(f"❌ LabelMe整合器創建失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mask_to_polygon():
    """測試mask轉polygon轉換"""
    print("\n🔍 測試4: 檢查mask轉polygon轉換")
    
    try:
        import numpy as np
        from models.inference.labelme_json_generator import LabelMeJSONGenerator
        
        generator = LabelMeJSONGenerator()
        
        # 創建測試mask
        test_mask = np.zeros((100, 100), dtype=np.uint8)
        test_mask[30:70, 30:70] = 255  # 創建一個方形mask
        
        polygon = generator.mask_to_polygon(test_mask)
        print(f"✅ Mask轉polygon成功")
        print(f"   Polygon點數: {len(polygon)}")
        print(f"   前3個點: {polygon[:3] if len(polygon) >= 3 else polygon}")
        
        return len(polygon) >= 3
        
    except Exception as e:
        print(f"❌ Mask轉polygon失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_permissions():
    """測試檔案權限"""
    print("\n🔍 測試5: 檢查檔案權限")
    
    output_path = Path(r"D:\image\5_test_image\test_2_out")
    labelme_dir = output_path / "labelme_json"
    
    try:
        # 檢查輸出目錄
        if not output_path.exists():
            print(f"❌ 輸出目錄不存在: {output_path}")
            return False
        
        print(f"✅ 輸出目錄存在: {output_path}")
        
        # 檢查LabelMe目錄
        if not labelme_dir.exists():
            print(f"⚠️ LabelMe目錄不存在，嘗試創建: {labelme_dir}")
            labelme_dir.mkdir(parents=True, exist_ok=True)
        
        # 測試寫入權限
        test_file = labelme_dir / "test.json"
        test_file.write_text('{"test": true}')
        test_file.unlink()  # 刪除測試檔案
        
        print(f"✅ 檔案寫入權限正常")
        return True
        
    except Exception as e:
        print(f"❌ 檔案權限檢查失敗: {e}")
        return False

def main():
    """主診斷函數"""
    print("🔍 LabelMe JSON輸出診斷工具")
    print("=" * 50)
    
    results = []
    
    # 運行所有測試
    results.append(("模型輸出檢查", test_model_output()))
    results.append(("配置創建檢查", test_config_creation()))
    results.append(("LabelMe整合器檢查", test_labelme_integration()))
    results.append(("Mask轉換檢查", test_mask_to_polygon()))
    results.append(("檔案權限檢查", test_file_permissions()))
    
    # 總結結果
    print("\n" + "=" * 50)
    print("📊 診斷結果總結:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n💡 診斷建議:")
    if not results[0][1]:  # 模型輸出檢查失敗
        print("   🔥 關鍵問題: 模型沒有輸出mask數據")
        print("   📝 解決方案: 確認使用的是分割模型(segmentation)而非檢測模型(detection)")
        print("   🔧 檢查方法: 使用YOLO分割模型(如YOLOv8s-seg.pt)重新訓練或替換模型")
    
    if not results[2][1]:  # LabelMe整合器檢查失敗
        print("   🔥 配置問題: LabelMe整合器初始化失敗")
        print("   📝 解決方案: 檢查配置傳遞和依賴模組導入")
    
    if not results[3][1]:  # Mask轉換檢查失敗
        print("   🔥 轉換問題: Mask到Polygon轉換失敗")
        print("   📝 解決方案: 檢查OpenCV和numpy依賴")
    
    if not results[4][1]:  # 檔案權限檢查失敗
        print("   🔥 權限問題: 無法寫入輸出目錄")
        print("   📝 解決方案: 檢查目錄權限或更改輸出路徑")
    
    if all_passed:
        print("   🎉 所有測試通過! 問題可能在於實際推理過程中的細節")
        print("   🔧 建議: 在run_unified_yolo.py中添加詳細的debug日誌")

if __name__ == "__main__":
    main()