#!/usr/bin/env python3
"""
🧪 測試增強的LabelMe JSON儲存成功信息
演示新增的詳細儲存成功信息顯示
"""

import json
import numpy as np
import cv2
from pathlib import Path
import tempfile
import sys
import logging

# 設置路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 設置日誌級別以查看詳細信息
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def create_test_detection_results(num_detections=3):
    """創建多個測試檢測結果"""
    detections = []
    
    for i in range(num_detections):
        # 創建不同大小的mask
        mask_size = 50 + i * 20
        mask = np.zeros((mask_size, mask_size), dtype=np.uint8)
        
        # 創建不同形狀的區域
        if i == 0:
            # 矩形
            mask[10:mask_size-10, 15:mask_size-15] = 255
        elif i == 1:
            # 圓形
            center = mask_size // 2
            radius = mask_size // 3
            y, x = np.ogrid[:mask_size, :mask_size]
            mask_circle = (x - center)**2 + (y - center)**2 <= radius**2
            mask[mask_circle] = 255
        else:
            # 不規則形狀
            mask[5:mask_size-5, 5:mask_size-5] = 255
            mask[10:20, 10:20] = 0  # 挖個洞
        
        detection = {
            'class_id': i,
            'confidence': 0.8 + i * 0.1,
            'mask': mask,
            'bbox': [10, 10, mask_size-10, mask_size-10]
        }
        detections.append(detection)
    
    return detections

def test_single_image_save_messages():
    """測試單張圖像的儲存成功信息"""
    print("🧪 測試單張圖像LabelMe JSON儲存信息...")
    
    try:
        from models.inference.labelme_integration import create_labelme_integration
        
        # 創建模擬配置
        class MockConfig:
            def __init__(self):
                self.labelme_output = self.LabelMeConfig()
                self.paths = self.PathsConfig()
                self.classes = {
                    0: self.ClassConfig('linear_crack'),
                    1: self.ClassConfig('potholes'),
                    2: self.ClassConfig('joint')
                }
        
        class LabelMeConfig:
            def __init__(self):
                self.enable_labelme_output = True
                self.labelme_output_dir = 'test_labelme_json'
                self.labelme_simplify_tolerance = 2.0
                self.labelme_min_polygon_points = 3
                self.labelme_include_confidence = False
        
        class PathsConfig:
            def __init__(self):
                self.output_path = tempfile.mkdtemp()
        
        class ClassConfig:
            def __init__(self, name):
                self.name = name
        
        # 創建整合器
        config = MockConfig()
        integration = create_labelme_integration(config)
        
        # 創建測試檢測結果
        detections = create_test_detection_results(3)
        
        # 創建測試圖像
        test_image = np.random.randint(0, 255, (200, 200, 3), dtype=np.uint8)
        test_image_path = Path(config.paths.output_path) / "test_single_image.jpg"
        cv2.imwrite(str(test_image_path), test_image)
        
        print(f"📷 創建測試圖像: {test_image_path.name}")
        print(f"📊 準備 {len(detections)} 個檢測結果")
        
        # 測試單張處理
        print("\n" + "="*50)
        print("開始單張圖像LabelMe JSON生成...")
        print("="*50)
        
        json_path = integration.process_single_image_result(
            image_path=str(test_image_path),
            detections=detections
        )
        
        print("="*50)
        
        if json_path:
            print("✅ 單張圖像測試成功")
            
            # 驗證JSON內容
            with open(json_path, 'r', encoding='utf-8') as f:
                labelme_json = json.load(f)
            
            print(f"\n📋 JSON檔案驗證:")
            print(f"   - 檢測物件數: {len(labelme_json.get('shapes', []))}")
            print(f"   - 圖像尺寸: {labelme_json.get('imageWidth')} x {labelme_json.get('imageHeight')}")
            print(f"   - 檔案大小: {Path(json_path).stat().st_size} bytes")
            
            return True
        else:
            print("❌ 單張圖像測試失敗")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_save_messages():
    """測試批次處理的儲存成功信息"""
    print("\n🧪 測試批次處理LabelMe JSON儲存信息...")
    
    try:
        from models.inference.labelme_integration import create_labelme_integration
        
        # 創建模擬配置
        class MockConfig:
            def __init__(self):
                self.labelme_output = self.LabelMeConfig()
                self.paths = self.PathsConfig()
                self.classes = {
                    0: self.ClassConfig('linear_crack'),
                    1: self.ClassConfig('potholes'),
                    2: self.ClassConfig('joint')
                }
        
        class LabelMeConfig:
            def __init__(self):
                self.enable_labelme_output = True
                self.labelme_output_dir = 'test_batch_labelme_json'
                self.labelme_simplify_tolerance = 1.5
                self.labelme_min_polygon_points = 3
                self.labelme_include_confidence = False
        
        class PathsConfig:
            def __init__(self):
                self.output_path = tempfile.mkdtemp()
        
        class ClassConfig:
            def __init__(self, name):
                self.name = name
        
        # 創建整合器
        config = MockConfig()
        integration = create_labelme_integration(config)
        
        # 創建多個測試圖像和檢測結果
        batch_results = []
        num_images = 4
        
        for i in range(num_images):
            # 創建測試圖像
            test_image = np.random.randint(0, 255, (150+i*50, 150+i*50, 3), dtype=np.uint8)
            test_image_path = Path(config.paths.output_path) / f"test_batch_image_{i+1}.jpg"
            cv2.imwrite(str(test_image_path), test_image)
            
            # 創建檢測結果
            detections = create_test_detection_results(2 + i)  # 遞增的檢測數量
            
            batch_results.append({
                'image_path': str(test_image_path),
                'detections': detections
            })
        
        print(f"📷 創建 {num_images} 個測試圖像")
        total_detections = sum(len(result['detections']) for result in batch_results)
        print(f"📊 準備 {total_detections} 個檢測結果")
        
        # 測試批次處理
        print("\n" + "="*50)
        print("開始批次LabelMe JSON生成...")
        print("="*50)
        
        generated_files = integration.process_batch_results(batch_results)
        
        print("="*50)
        
        if generated_files:
            print("✅ 批次處理測試成功")
            
            # 驗證所有JSON檔案
            total_shapes = 0
            total_size = 0
            
            for json_path in generated_files:
                with open(json_path, 'r', encoding='utf-8') as f:
                    labelme_json = json.load(f)
                total_shapes += len(labelme_json.get('shapes', []))
                total_size += Path(json_path).stat().st_size
            
            print(f"\n📋 批次處理結果驗證:")
            print(f"   - 生成檔案數: {len(generated_files)}")
            print(f"   - 總檢測物件: {total_shapes}")
            print(f"   - 總檔案大小: {total_size:,} bytes")
            
            return True
        else:
            print("❌ 批次處理測試失敗")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("🧪 增強LabelMe JSON儲存成功信息測試")
    print("=" * 60)
    print("🎯 目的: 展示新增的詳細儲存成功信息")
    print("=" * 60)
    
    tests = [
        ("單張圖像儲存信息", test_single_image_save_messages),
        ("批次處理儲存信息", test_batch_save_messages)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📝 執行測試: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: 通過")
            else:
                print(f"❌ {test_name}: 失敗")
        except Exception as e:
            print(f"❌ {test_name}: 異常 - {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 測試結果: {passed}/{total} 個測試通過")
    
    if passed == total:
        print("🎉 所有測試通過！增強的儲存成功信息顯示正常")
        print("\n📋 新增功能總結:")
        print("✅ 單張圖像: 詳細的檔案信息和儲存狀態")
        print("✅ 批次處理: 完整的批次統計和檔案列表")
        print("✅ 檔案大小: 自動計算和格式化顯示")
        print("✅ 物件統計: 檢測物件數量統計")
        print("✅ 錯誤診斷: 詳細的失敗原因和建議")
        
        print("\n💡 實際使用時將看到:")
        print("🎉 LabelMe JSON儲存完成: filename.json")
        print("   📁 輸出位置: /path/to/output/filename.json")
        print("   📊 包含物件: X 個")
        print("   📋 檔案大小: X.X KB")
        print("   🎯 可在LabelMe工具中開啟使用")
    else:
        print("❌ 部分測試失敗，請檢查實現")

if __name__ == "__main__":
    main()