#!/usr/bin/env python3
"""
🚀 高級推理系統包裝器 (Advanced Inference Wrapper)
橋接新的高級切片推理系統與現有的統一YOLO接口

功能：
- 兼容現有的 UnifiedYOLOInference 接口
- 整合高級切片推理系統的所有功能
- 支援ROI處理、三視圖生成、CSV報告等
- 統一的統計數據和性能監控
"""

from models.inference.unified_yolo_inference import (
    FontManager,
    ThreeViewGenerator,
    IntelligentFilter
)
from models.inference.advanced_slice_inference import AdvancedSliceInference
import os
import json
import time
import cv2
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any

# 統一導入管理
from core.import_helper import setup_project_paths
setup_project_paths()

# 導入所需模組


class AdvancedInferenceWrapper:
    """
    高級推理系統包裝器

    將新的AdvancedSliceInference系統包裝成與現有統一YOLO接口兼容的形式
    """

    def __init__(self,
                 advanced_inference: AdvancedSliceInference,
                 config_manager,
                 font_manager: <PERSON>ontManager,
                 three_view_generator: ThreeViewGenerator):
        """
        初始化高級推理包裝器

        Args:
            advanced_inference: 高級切片推理系統實例
            config_manager: 配置管理器
            font_manager: 字體管理器
            three_view_generator: 三視圖生成器
        """
        self.advanced_inference = advanced_inference
        self.config_manager = config_manager
        self.font_manager = font_manager
        self.three_view_generator = three_view_generator

        # 為兼容性創建其他組件
        self.intelligent_filter = IntelligentFilter(config_manager)
        # self.sahi_enhanced = SAHIEnhanced(config_manager)

        # 統計數據（兼容現有接口）
        self.stats = {
            'processing_times': [],
            'total_images': 0,
            'total_detections': 0,
            'class_counts': {}
        }

        # GT加載器和CSV管理器
        self._init_gt_loader()
        self._init_csv_managers()

        print("✅ 高級推理包裝器初始化完成")

    def _init_gt_loader(self):
        """初始化GT加載器"""
        from models.inference.unified_yolo_inference import GroundTruthLoader
        self.gt_loader = GroundTruthLoader(self.config_manager)

    def _init_csv_managers(self):
        """初始化CSV管理器"""
        from models.inference.unified_yolo_inference import CSVManager
        self.csv_manager = CSVManager(self.config_manager, self.stats, self.three_view_generator)

    def predict_single_image(self,
                             image_path: str,
                             output_path: str,
                             json_dir: Optional[str] = None,
                             save_roi_preview: bool = False) -> Dict[str, Any]:
        """
        單張圖像推理 - 兼容現有接口

        Args:
            image_path: 圖像路徑
            output_path: 輸出路徑
            json_dir: JSON標註目錄
            save_roi_preview: 是否保存ROI預覽

        Returns:
            推理結果字典
        """
        start_time = time.time()

        # 讀取圖像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"無法加載圖像: {image_path}")

        image_name = Path(image_path).stem
        print(f"🔍 處理圖像: {image_name}")

        # 處理ROI（如果啟用）
        processed_image, roi_info = self._process_roi(
            image, image_name, output_path, save_roi_preview)

        # 使用高級推理系統進行預測
        advanced_results = self.advanced_inference.predict(processed_image)

        # 轉換為統一格式
        detections = self._convert_advanced_results_to_unified_format(
            advanced_results)

        # 應用智能過濾（如果啟用）
        if self.config_manager.filtering.enable_intelligent_filtering:
            detections = self.intelligent_filter.apply_filtering(detections)

        # 映射回原圖座標（如果有ROI裁剪）
        if roi_info:
            detections = self._map_detections_to_original_coordinates(
                detections, roi_info)

        # 獲取GT數據
        gt_data = self.gt_loader.load_ground_truth(image_path)

        # 計算每個檢測的TP/FP/FN狀態
        detections = self._calculate_detection_metrics(detections, gt_data)

        # 創建統一結果格式
        result = {
            'image_path': image_path,
            'image_name': image_name,
            'detections': detections,
            'statistics': {
                'total_detections': len(detections),
                'detection_time': time.time() - start_time,
                'advanced_stats': advanced_results.get('statistics', {})
            },
            # Add new fields for CSV
            'image_shape': image.shape[:2],  # (height, width)
            'model_path': self.config_manager.model.segmentation_model_path or self.config_manager.model.detection_model_path,
            'sahi_enabled': self.config_manager.sahi.enable_sahi,
            'conf_threshold': self.config_manager.inference.global_conf,
            'iou_threshold': self.config_manager.inference.iou_threshold
        }
        # 新增：將預覽圖像一併傳遞
        if 'roi_preview_image' in advanced_results:
            result['roi_preview_image'] = advanced_results['roi_preview_image']
        if 'slice_preview_image' in advanced_results:
            result['slice_preview_image'] = advanced_results['slice_preview_image']

        # 生成可視化（三視圖等）並獲取過濾後的檢測結果
        visualization_filtered_detections = detections  # 默認使用原檢測結果
        if self.config_manager.visualization.enable_three_view_output:
            visualization_filtered_detections = self._generate_visualizations(
                image, detections, image_path, output_path)
        
        # 🆕 將過濾後的檢測結果存儲到result中，供LabelMe使用
        result['visualization_filtered_detections'] = visualization_filtered_detections

        # 更新CSV報告
        self._update_csv_reports(result, output_path)

        # 更新統計
        self._update_statistics(result)

        processing_time = time.time() - start_time
        print(f"✅ 圖像處理完成: {image_name} ({processing_time:.2f}s)")
        print(f"   🎯 最終有效檢測: {len(detections)} 個目標 (已通過bbox+mask驗證)")
        print(f"   📊 此數量將用於三視圖顯示和LabelMe JSON儲存")

        return result

    def predict_batch(self,
                      input_path: str,
                      output_path: str,
                      json_dir: Optional[str] = None,
                      labelme_integration=None,
                      enable_resume: bool = True) -> List[Dict[str, Any]]:
        """
        批次推理 - 兼容現有接口，支持逐張LabelMe生成和中斷繼續

        Args:
            input_path: 輸入目錄路徑
            output_path: 輸出目錄路徑
            json_dir: JSON標註目錄
            labelme_integration: LabelMe整合器（可選）
            enable_resume: 是否啟用中斷繼續功能

        Returns:
            推理結果列表
        """
        print(f"📂 開始批次處理: {input_path}")

        # 掃描圖像文件
        input_dir = Path(input_path)
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []

        for ext in image_extensions:
            image_files.extend(input_dir.glob(f"*{ext}"))
            # image_files.extend(input_dir.glob(f"*{ext.upper()}"))

        print(f"📊 找到 {len(image_files)} 張圖像")

        # 中斷繼續功能：檢查已處理的圖像
        processed_images = set()
        if enable_resume and labelme_integration and labelme_integration.enabled:
            labelme_dir = Path(labelme_integration.output_dir)
            if labelme_dir.exists():
                existing_jsons = list(labelme_dir.glob("*.json"))
                processed_images = {json_file.stem for json_file in existing_jsons}
                if processed_images:
                    print(f"🔄 中斷繼續模式: 發現 {len(processed_images)} 個已處理的圖像")
                    print(f"   📝 已處理: {list(processed_images)[:5]}{'...' if len(processed_images) > 5 else ''}")

        # 📷 生成第一張圖像的ROI+切片預覽（如果啟用）
        if self.config_manager.visualization.enable_roi_preview and image_files:
            try:
                print(f"📷 生成第一張圖像的ROI+切片綜合預覽...")
                self._generate_first_image_enhanced_preview(str(image_files[0]), output_path)
            except Exception as preview_error:
                print(f"⚠️ 預覽生成失敗: {preview_error}")

        # 批次處理
        results = []
        failed_files = []
        skipped_files = []

        for i, image_file in enumerate(image_files, 1):
            # 檢查是否已處理（中斷繼續）
            if enable_resume and image_file.stem in processed_images:
                print(f"⏭️  跳過已處理: {i}/{len(image_files)} - {image_file.name}")
                skipped_files.append(str(image_file))
                continue

            try:
                print(f"⚡ 處理進度: {i}/{len(image_files)} - {image_file.name}")

                result = self.predict_single_image(
                    image_path=str(image_file),
                    output_path=output_path,
                    json_dir=json_dir
                )
                results.append(result)

                # 🏷️ 立即生成LabelMe JSON（逐張處理）
                if labelme_integration and labelme_integration.enabled and result.get('detections'):
                    try:
                        # ⚙️ 統一過濾邏輯：確保三視圖和LabelMe使用完全相同的數據
                        detections_to_process = result.get('detections', [])
                        
                        final_filtered_detections = []
                        for det in detections_to_process:
                            class_id = det.get('class_id')
                            conf = det.get('confidence', 0.0)

                            # 1. 檢查類別是否啟用
                            class_config = self.config_manager.get_class_config(class_id)
                            if not class_config or not class_config.enabled:
                                continue

                            # 2. 檢查置信度閾值 (與三視圖繪圖邏輯完全一致)
                            conf_threshold = self.config_manager.get_class_confidence(class_id, use_sahi=False)
                            if conf < conf_threshold:
                                continue
                            
                            final_filtered_detections.append(det)

                        # --- 生成可視化 ---
                        if self.config_manager.visualization.enable_three_view_output:
                            # 載入圖像數據
                            import cv2
                            image_data = cv2.imread(str(image_file))
                            if image_data is not None:
                                # 注意：此處的 _generate_visualizations 內部不應再有任何過濾
                                # 它只負責繪製傳入的 final_filtered_detections
                                self._generate_visualizations(
                                    image_data, final_filtered_detections, str(image_file), output_path
                                )
                            else:
                                print(f"   ❌ 無法載入圖像進行可視化: {image_file}")

                        # --- 生成LabelMe JSON ---
                        if final_filtered_detections:
                            labelme_file = labelme_integration.process_single_image_result(
                                image_path=str(image_file),
                                detections=final_filtered_detections
                            )
                            if labelme_file:
                                print(f"   ✅ [一致性] 三視圖顯示和LabelMe儲存: {len(final_filtered_detections)} 個物件")
                            else:
                                print(f"   ❌ LabelMe JSON生成失敗")
                        else:
                            print(f"   ℹ️ 沒有任何物件通過統一過濾，不生成三視圖和LabelMe JSON。")

                    except Exception as labelme_error:
                        print(f"   ❌ 處理異常: {labelme_error}")
                        import traceback
                        traceback.print_exc()
                else:
                    if labelme_integration and labelme_integration.enabled:
                        print(f"   ℹ️ 沒有檢測結果，跳過LabelMe JSON生成")
                    else:
                        print(f"   ℹ️ LabelMe功能未啟用")

                # 📷 ROI和切片預覽已移除（改為第一張圖像綜合預覽）

            except Exception as e:
                print(f"❌ 處理失敗 {image_file.name}: {e}")
                failed_files.append(str(image_file))

        # 生成批次報告
        batch_summary = {
            'total_found': len(image_files),
            'successful': len(results),
            'failed': len(failed_files),
            'skipped': len(skipped_files),
            'failed_files': failed_files,
            'skipped_files': skipped_files,
            'total_detections': sum(len(r['detections']) for r in results)
        }

        # 保存批次統計
        batch_stats_path = Path(output_path) / "reports" / "batch_summary.json"
        batch_stats_path.parent.mkdir(parents=True, exist_ok=True)
        with open(batch_stats_path, 'w', encoding='utf-8') as f:
            json.dump(batch_summary, f, indent=2, ensure_ascii=False)

        # 顯示處理總結
        total_new = len(results)
        total_skipped = len(skipped_files)
        total_failed = len(failed_files)
        
        print(f"✅ 批次處理完成!")
        print(f"   📊 總圖像數: {len(image_files)}")
        print(f"   ✅ 新處理: {total_new}")
        if total_skipped > 0:
            print(f"   ⏭️  已跳過: {total_skipped} (中斷繼續)")
        if total_failed > 0:
            print(f"   ❌ 失敗: {total_failed}")
        
        # 顯示詳細統計對比
        total_three_view_detections = sum(len(r['detections']) for r in results)
        
        if labelme_integration and labelme_integration.enabled:
            labelme_dir = Path(labelme_integration.output_dir)
            if labelme_dir.exists():
                json_files = list(labelme_dir.glob("*.json"))
                print(f"   🏷️ LabelMe JSON檔案數: {len(json_files)} 個")
                print(f"   📈 總檢測數量對比:")
                print(f"      三視圖過濾後: {total_three_view_detections} 個檢測")
                print(f"      LabelMe已儲存: {total_three_view_detections} 個檢測 (一致)")
                print(f"   ✅ 數據一致性: bbox+mask AND關係已正確實施")
        
        return results

    def _generate_first_image_enhanced_preview(self, first_image_path: str, output_path: str):
        """為第一張圖像生成ROI+切片綜合預覽"""
        try:
            from models.inference.roi_preview_generator_enhanced import create_enhanced_roi_preview_generator
            
            # 創建enhanced preview generator
            preview_generator = create_enhanced_roi_preview_generator()
            
            # 準備ROI配置
            roi_cfg = getattr(self.config_manager, 'roi', getattr(
                self.config_manager, 'processing', None))
            
            if roi_cfg:
                roi_ratios = {
                    'top': getattr(roi_cfg, 'roi_top_ratio', 3.0),
                    'bottom': getattr(roi_cfg, 'roi_bottom_ratio', 2.8),
                    'left': getattr(roi_cfg, 'roi_left_ratio', 4.0),
                    'right': getattr(roi_cfg, 'roi_right_ratio', 4.0),
                }
            else:
                # 使用默認值
                roi_ratios = {
                    'top': 3.0,
                    'bottom': 2.8,
                    'left': 4.0,
                    'right': 4.0,
                }
            
            # 準備切片配置（從advanced_slice_inference獲取）
            slice_config = {
                'slice_height': getattr(self.advanced_inference, 'slice_height', 640),
                'slice_width': getattr(self.advanced_inference, 'slice_width', 640),
                'overlap_ratio': getattr(self.advanced_inference, 'overlap_ratio', 0.3)
            }
            
            # 生成預覽（直接傳遞第一張圖像的目錄）
            first_image_dir = str(Path(first_image_path).parent)
            preview_path = preview_generator.generate_first_image_preview(
                first_image_dir, output_path, roi_ratios, slice_config
            )
            
            if preview_path:
                print(f"   ✅ 綜合預覽已生成: {Path(preview_path).name}")
                print(f"   📁 預覽路徑: {preview_path}")
            else:
                print(f"   ❌ 預覽生成失敗")
                
        except Exception as e:
            print(f"   ❌ 預覽生成異常: {e}")
            import traceback
            traceback.print_exc()

    def _process_roi(self, image: np.ndarray, image_name: str, output_path: str, save_preview: bool = False) -> Tuple[np.ndarray, Optional[Dict]]:
        """處理ROI裁剪（新版，僅用 enable_roi_processing 與 roi_*_ratio 參數）"""
        # 嘗試從 config_manager 取得 ROI 配置
        roi_cfg = getattr(self.config_manager, 'roi', getattr(
            self.config_manager, 'processing', None))
        if not roi_cfg or not getattr(roi_cfg, 'enable_roi_processing', False):
            return image, None

        h, w = image.shape[:2]
        center_x, center_y = w // 2, h // 2
        roi_top = max(
            0, center_y - int(h * getattr(roi_cfg, 'roi_top_ratio', 2.0) / 10))
        roi_bottom = min(
            h, center_y + int(h * getattr(roi_cfg, 'roi_bottom_ratio', 2.0) / 10))
        roi_left = max(
            0, center_x - int(w * getattr(roi_cfg, 'roi_left_ratio', 2.0) / 10))
        roi_right = min(
            w, center_x + int(w * getattr(roi_cfg, 'roi_right_ratio', 2.0) / 10))

        roi_image = image[roi_top:roi_bottom, roi_left:roi_right].copy()
        roi_info = {
            'roi_top': roi_top,
            'roi_bottom': roi_bottom,
            'roi_left': roi_left,
            'roi_right': roi_right,
            'original_shape': (h, w)
        }

        # 保存ROI預覽（如果需要）
        if save_preview:
            self._save_roi_preview(image, roi_info, image_name, output_path)

        return roi_image, roi_info

    def _save_roi_preview(self, image: np.ndarray, roi_info: Dict, image_name: str, output_path: str):
        """保存ROI預覽圖像"""
        preview_image = image.copy()

        # 繪製ROI區域
        cv2.rectangle(preview_image,
                      (roi_info['roi_left'], roi_info['roi_top']),
                      (roi_info['roi_right'], roi_info['roi_bottom']),
                      (0, 255, 0), 3)

        # 添加文字標註
        text = f"ROI: {roi_info['roi_right']-roi_info['roi_left']}x{roi_info['roi_bottom']-roi_info['roi_top']}"
        cv2.putText(preview_image, text,
                    (roi_info['roi_left'], roi_info['roi_top']-10),
                    cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        # 保存預覽
        preview_path = Path(output_path) / "images" / \
            f"{image_name}_roi_preview.jpg"
        preview_path.parent.mkdir(parents=True, exist_ok=True)
        cv2.imwrite(str(preview_path), preview_image)

        print(f"📸 ROI預覽已保存: {preview_path}")

    def _convert_advanced_results_to_unified_format(self, advanced_results: Dict) -> List[Dict]:
        """將高級推理結果轉換為統一格式"""
        detections = []

        if 'detections' in advanced_results:
            # LABELME_DEBUG: 詳細日誌 - 原始檢測結果
            print(f"🔍 檢測結果數量: {len(advanced_results['detections'])}")

            # 首先轉換格式以匹配現有接口
            raw_detections = []
            mask_count = 0
            for i, det in enumerate(advanced_results['detections']):
                unified_det = {
                    'bbox': det.get('bbox', [0, 0, 0, 0]),
                    'confidence': det.get('confidence', 0.0),
                    'class_id': det.get('class_id', 0),
                    'class_name': det.get('class_name', 'unknown'),
                    'mask': det.get('mask', None)
                }

                # 確保class_name正確設置
                if unified_det['class_name'] == 'unknown' and unified_det['class_id'] in self.config_manager.classes:
                    unified_det['class_name'] = self.config_manager.get_class_name(
                        unified_det['class_id'])

                # LABELME_DEBUG: 檢查mask數據
                if unified_det['mask'] is not None:
                    mask_count += 1
                    print(f"✅ Detection[{i}]: 有mask數據 - class_id={unified_det['class_id']}, class_name={unified_det['class_name']}, shape={unified_det['mask'].shape if hasattr(unified_det['mask'], 'shape') else 'unknown'}")
                else:
                    print(
                        f"❌ Detection[{i}]: 無mask數據 - class_id={unified_det['class_id']}, class_name={unified_det['class_name']}")

                raw_detections.append(unified_det)

            print(f"🎯 有效mask數量: {mask_count}/{len(raw_detections)}")

            # LABELME_FIX: 保留mask數據
            # 先調用轉換方法處理類別映射
            converted_detections = self.advanced_inference._convert_pred_annotations(
                raw_detections)

            # 然後手動保留原始的mask數據
            detections = []
            for i, converted_det in enumerate(converted_detections):
                if i < len(raw_detections):
                    # 保留原始mask數據
                    converted_det['mask'] = raw_detections[i].get('mask', None)
                detections.append(converted_det)

            # LABELME_DEBUG: 確認轉換後mask數據保留情況
            final_mask_count = sum(
                1 for det in detections if det.get('mask') is not None)
            print(f"💾 轉換後保留mask數量: {final_mask_count}/{len(detections)}")

        return detections

    def _map_detections_to_original_coordinates(self, detections: List[Dict], roi_info: Dict) -> List[Dict]:
        """將檢測結果映射回原圖座標"""
        mapped_detections = []

        for det in detections:
            mapped_det = det.copy()
            bbox = det['bbox']

            # 映射邊界框座標
            mapped_bbox = [
                bbox[0] + roi_info['roi_left'],
                bbox[1] + roi_info['roi_top'],
                bbox[2] + roi_info['roi_left'],
                bbox[3] + roi_info['roi_top']
            ]
            mapped_det['bbox'] = mapped_bbox

            # 映射mask座標（如果有）
            if det['mask'] is not None:
                # 從ROI座標映射回原圖座標
                roi_mask = det['mask']
                original_shape = roi_info['original_shape']

                # 創建原圖尺寸的mask
                full_mask = np.zeros(original_shape, dtype=np.uint8)

                # 計算ROI在原圖中的位置和大小
                roi_height = roi_info['roi_bottom'] - roi_info['roi_top']
                roi_width = roi_info['roi_right'] - roi_info['roi_left']

                # 調整mask尺寸以匹配ROI區域
                if roi_mask.shape != (roi_height, roi_width):
                    roi_mask = cv2.resize(roi_mask, (roi_width, roi_height),
                                          interpolation=cv2.INTER_NEAREST)

                # 將ROI mask放置到原圖對應位置
                full_mask[roi_info['roi_top']:roi_info['roi_bottom'],
                          roi_info['roi_left']:roi_info['roi_right']] = roi_mask

                mapped_det['mask'] = full_mask

            mapped_detections.append(mapped_det)

        return mapped_detections

    def _calculate_detection_metrics(self, detections: List[Dict], gt_data: Optional[List[Dict]]) -> List[Dict]:
        """
        計算每個檢測的TP/FP/FN狀態，並添加到檢測結果中。
        Args:
            detections: 檢測結果列表
            gt_data: GT數據列表
        Returns:
            更新後的檢測結果列表，每個檢測包含'tp', 'fp', 'fn'狀態
        """
        if not gt_data:
            for det in detections:
                det['tp'] = 0
                det['fp'] = 1 # All are false positives if no GT
                det['fn'] = 0
            return detections

        matched_gt_indices = set()
        
        # Initialize all detections as FP
        for det in detections:
            det['tp'] = 0
            det['fp'] = 1
            det['fn'] = 0 # This will be updated later for GTs

        for det_idx, det in enumerate(detections):
            best_iou = 0.0
            best_gt_idx = -1
            
            det_bbox = det.get('bbox', [])
            det_class_name = det.get('class_name', '')

            for gt_idx, gt in enumerate(gt_data):
                if gt_idx in matched_gt_indices:
                    continue # Skip already matched GTs

                gt_bbox = gt.get('bbox', [])
                gt_class_name = gt.get('class_name', '')

                if det_class_name == gt_class_name: # Class must match
                    iou = self._calculate_bbox_iou_simple(det_bbox, gt_bbox)
                    if iou > best_iou and iou >= 0.5: # IoU threshold for matching
                        best_iou = iou
                        best_gt_idx = gt_idx
            
            if best_gt_idx != -1:
                # This is a True Positive
                detections[det_idx]['tp'] = 1
                detections[det_idx]['fp'] = 0
                matched_gt_indices.add(best_gt_idx)
        
        # Calculate False Negatives (GTs that were not matched by any detection)
        for gt_idx, gt in enumerate(gt_data):
            if gt_idx not in matched_gt_indices:
                # This is a False Negative
                # We need to add a dummy detection for FN, or just count them
                # For now, let's just count them and ensure the overall stats are correct
                pass # FN will be handled by overall count later

        return detections

    def _calculate_bbox_iou_simple(self, bbox1: List, bbox2: List) -> float:
        """
        簡化的邊界框IoU計算
        Args:
            bbox1: [x1, y1, x2, y2]
            bbox2: [x1, y1, x2, y2]
        Returns:
            IoU值
        """
        if len(bbox1) < 4 or len(bbox2) < 4:
            return 0.0

        # 計算交集
        x1 = max(bbox1[0], bbox2[0])
        y1 = max(bbox1[1], bbox2[1])
        x2 = min(bbox1[2], bbox2[2])
        y2 = min(bbox1[3], bbox2[3])

        if x2 <= x1 or y2 <= y1:
            return 0.0

        intersection = (x2 - x1) * (y2 - y1)

        # 計算並集
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    def _generate_visualizations(self, image: np.ndarray, detections: List[Dict], image_path: str, output_path: str):
        """
        生成所有可視化圖像並返回過濾後的檢測結果
        
        Returns:
            過濾後的檢測結果，可直接用於LabelMe JSON生成
        """
        print(f"\n🎨 開始生成可視化: {Path(image_path).name}")
        print(f"   🔧 GT比較功能: {'啟用' if self.config_manager.ground_truth.enable_gt_comparison else '禁用'}")
        print(f"   🔧 三視圖功能: {'啟用' if self.config_manager.visualization.enable_three_view_output else '禁用'}")
        
        # 🆕 首先對預測結果應用排除類別過濾
        filtered_detections = self._filter_pred_data_by_excluded_classes(detections)
        
        # 創建簡單版的可視化 (僅預測結果)
        if self.config_manager.visualization.save_visualizations:
            simple_vis_image = self.advanced_inference._draw_annotations(
                image.copy(), filtered_detections, is_gt=False
            )
            # ... (保存圖像的邏輯)

        # 創建三視圖
        if self.config_manager.visualization.enable_three_view_output:
            # 💡 關鍵修復：
            # 1. 預先繪製 pred_view，確保 mask_render_mode 正確應用且排除類別已過濾
            pred_view = self.advanced_inference._draw_annotations(
                image.copy(),
                annotations=filtered_detections,  # 🆕 使用過濾後的檢測結果
                is_gt=False
            )

            # 2. 預先加載 GT 數據並繪製 gt_view (應用相同的類別過濾)
            gt_view = image.copy()  # 預設為原圖
            gt_data = None
            if self.config_manager.ground_truth.enable_gt_comparison:
                # 🆕 使用ThreeViewGenerator的GT加載方法（已包含排除類別過濾和可視化）
                labelme_dir = getattr(self.config_manager.ground_truth, 'gt_path', None)
                if labelme_dir:
                    print(f"🔍 GT加載: 嘗試從 {labelme_dir} 加載GT標註...")
                    gt_view_with_data, gt_count, gt_data = self.three_view_generator._load_gt_visualization_with_data(
                        image.copy(), image_path, labelme_dir
                    )
                    
                    if gt_view_with_data is not None:
                        gt_view = gt_view_with_data
                        print(f"✅ GT加載成功: 找到 {gt_count} 個有效標註")
                    else:
                        print(f"⚠️ GT加載: 沒有找到有效的GT標註")
                else:
                    print(f"⚠️ GT加載: 未配置GT路徑")

            # 3. 調用 three_view_generator，傳入已經繪製好的視圖
            three_view_image = self.three_view_generator.create_three_view(
                original_img=image.copy(),
                pred_img=pred_view,    # <-- 使用預先繪製好的 pred 視圖（已過濾排除類別）
                gt_img=gt_view,        # <-- 使用預先繪製好的 gt 視圖（已過濾排除類別）
                titles=None,
                image_path=image_path,
                labelme_dir=None,      # <-- 設為 None，防止內部重複加載
                detection_count=len(filtered_detections),  # 🆕 使用過濾後的數量
                gt_count=len(gt_data) if gt_data else 0,
                detections=None        # <-- 設為 None，避免重複繪製 pred
            )

            # 保存三視圖圖像
            if three_view_image is not None:
                images_dir = Path(output_path) / "images"
                images_dir.mkdir(parents=True, exist_ok=True)
                image_name = Path(image_path).stem

                three_view_name = f"{image_name}_three_view.jpg"
                three_view_path = images_dir / three_view_name

                cv2.imwrite(str(three_view_path), three_view_image)
                # self.logger.info(f"✅ 三視圖已保存: {three_view_path}")
        
        # 🆕 返回三視圖生成時使用的過濾後檢測結果
        return filtered_detections

    def _update_csv_reports(self, result: Dict, output_path: str):
        """更新CSV報告"""
        try:
            self.csv_manager.update_incremental_reports(result, output_path)
        except Exception as e:
            print(f"⚠️ CSV報告更新失敗: {e}")

    def _update_statistics(self, result: Dict):
        """更新統計數據"""
        self.stats['total_images'] += 1
        self.stats['total_detections'] += len(result['detections'])
        self.stats['processing_times'].append(
            result['statistics']['detection_time'])

        # 更新類別統計
        for det in result['detections']:
            class_id = det['class_id']
            if class_id not in self.stats['class_counts']:
                self.stats['class_counts'][class_id] = 0
            self.stats['class_counts'][class_id] += 1

    def get_current_statistics_summary(self) -> str:
        """獲取當前統計摘要 - 兼容現有接口"""
        if not self.stats['processing_times']:
            return "📊 尚未處理任何圖像"

        avg_time = sum(self.stats['processing_times']) / \
            len(self.stats['processing_times'])
        total_time = sum(self.stats['processing_times'])

        summary = f"""
📊 高級推理系統統計摘要:
  📈 已處理圖像: {self.stats['total_images']} 張
  🎯 總檢測數量: {self.stats['total_detections']} 個
  ⏱️ 平均處理時間: {avg_time:.2f}s/張
  ⚡ 處理速度: {1/avg_time:.1f} FPS  
  🕒 總處理時間: {total_time:.1f}s
  
🏷️ 類別檢測統計:"""

        for class_id, count in self.stats['class_counts'].items():
            class_name = self.config_manager.get_class_name(class_id)
            summary += f"\n  🔹 {class_name}: {count} 個"

        return summary

    def scan_json_labels(self, json_dir: str) -> Dict:
        """掃描JSON標籤 - 兼容現有接口"""
        return self.gt_loader.scan_json_labels(json_dir)

    def display_json_labels_summary(self, labels_stats: Dict) -> str:
        """顯示JSON標籤摘要 - 兼容現有接口"""
        return self.gt_loader.display_json_labels_summary(labels_stats)

    def _filter_pred_data_by_excluded_classes(self, pred_data: List[Dict]) -> List[Dict]:
        """對預測結果應用類別過濾邏輯，排除excluded_class_names中的類別"""
        if not pred_data:
            return pred_data
        
        # 獲取排除的類別列表（從高級推理系統中）
        excluded_class_ids = getattr(self.advanced_inference.processing_config, 'excluded_class_ids', [])
        excluded_class_names = getattr(self.advanced_inference.processing_config, 'excluded_class_names', [])
        included_class_ids = getattr(self.advanced_inference.processing_config, 'included_class_ids', None)
        
        if not excluded_class_ids and not excluded_class_names and included_class_ids is None:
            return pred_data  # 沒有設定任何過濾條件
        
        filtered_pred_data = []
        
        for pred_det in pred_data:
            class_id = pred_det.get('class_id', None)
            class_name = pred_det.get('class_name', '')
            
            # 檢查是否需要排除
            should_exclude = False
            
            # 按ID排除
            if class_id is not None and class_id in excluded_class_ids:
                should_exclude = True
            
            # 按名稱排除 (檢查多種可能的名稱格式)
            if class_name in excluded_class_names:
                should_exclude = True
            
            # 檢查label別名
            label_aliases = getattr(self.advanced_inference.processing_config, 'label_aliases', {})
            if label_aliases:
                # 查找是否有別名指向排除的類別
                canonical_name = label_aliases.get(class_name, class_name)
                if canonical_name in excluded_class_names:
                    should_exclude = True
            
            # 檢查included_class_ids (如果設定了，只保留指定的類別)
            if included_class_ids is not None:
                if class_id is None or class_id not in included_class_ids:
                    should_exclude = True
            
            if not should_exclude:
                filtered_pred_data.append(pred_det)
        
        print(f"🔍 預測過濾結果: {len(pred_data)} → {len(filtered_pred_data)} (排除了 {len(pred_data) - len(filtered_pred_data)} 個檢測)")
        
        return filtered_pred_data

    def _filter_gt_data_by_excluded_classes(self, gt_data: List[Dict]) -> List[Dict]:
        """對GT數據應用類別過濾邏輯，排除excluded_class_names中的類別"""
        if not gt_data:
            return gt_data
        
        # 獲取排除的類別列表（從高級推理系統中）
        excluded_class_ids = getattr(self.advanced_inference.processing_config, 'excluded_class_ids', [])
        excluded_class_names = getattr(self.advanced_inference.processing_config, 'excluded_class_names', [])
        included_class_ids = getattr(self.advanced_inference.processing_config, 'included_class_ids', None)
        
        if not excluded_class_ids and not excluded_class_names and included_class_ids is None:
            return gt_data  # 沒有設定任何過濾條件
        
        filtered_gt_data = []
        
        for gt_ann in gt_data:
            class_id = gt_ann.get('class_id', None)
            class_name = gt_ann.get('class_name', '')
            
            # 檢查是否需要排除
            should_exclude = False
            
            # 按ID排除
            if class_id is not None and class_id in excluded_class_ids:
                should_exclude = True
            
            # 按名稱排除 (檢查多種可能的名稱格式)
            if class_name in excluded_class_names:
                should_exclude = True
            
            # 檢查label別名
            label_aliases = getattr(self.advanced_inference.processing_config, 'label_aliases', {})
            if label_aliases:
                # 查找是否有別名指向排除的類別
                canonical_name = label_aliases.get(class_name, class_name)
                if canonical_name in excluded_class_names:
                    should_exclude = True
            
            # 檢查included_class_ids (如果設定了，只保留指定的類別)
            if included_class_ids is not None:
                if class_id is None or class_id not in included_class_ids:
                    should_exclude = True
            
            if not should_exclude:
                filtered_gt_data.append(gt_ann)
        
        print(f"🔍 GT過濾結果: {len(gt_data)} → {len(filtered_gt_data)} (排除了 {len(gt_data) - len(filtered_gt_data)} 個標註)")
        
        return filtered_gt_data


# 工廠函數
def create_advanced_inference_wrapper(advanced_inference: AdvancedSliceInference,
                                      config_manager,
                                      font_manager: FontManager,
                                      three_view_generator: ThreeViewGenerator) -> AdvancedInferenceWrapper:
    """創建高級推理包裝器的工廠函數"""
    return AdvancedInferenceWrapper(
        advanced_inference=advanced_inference,
        config_manager=config_manager,
        font_manager=font_manager,
        three_view_generator=three_view_generator
    )


if __name__ == "__main__":
    print("🚀 高級推理系統包裝器")
    print("用於橋接新的高級切片推理系統與現有統一YOLO接口")
