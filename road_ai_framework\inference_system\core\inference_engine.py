#!/usr/bin/env python3
"""
🚀 統一推理引擎
核心推理引擎，整合所有推理功能到統一接口
替代原有的multiple推理類
"""

import time
import logging
import psutil
import os
from pathlib import Path
from typing import Dict, List, Union, Optional, Any
import numpy as np
import cv2

from .base_inference import (
    BaseInference, StandardResult, Detection, ConfusionMetrics,
    TimingInfo, InferenceStatus, InferenceProcessError
)
from .model_adapter import ModelAdapter
from ..config import UnifiedConfig
from ..processing import SliceProcessor, FusionEngine, PostProcessor, ImageProcessor, SAHIProcessor
from ..visualization import FontManager, ThreeViewGenerator
from ..io import CSVManager, GroundTruthLoader


class UnifiedInferenceEngine(BaseInference):
    """
    統一推理引擎

    核心特色:
    - 統一的推理接口
    - 模組化處理管道
    - 配置驅動的行為
    - 完整的錯誤處理
    """

    def __init__(self, config: UnifiedConfig):
        """
        初始化統一推理引擎

        Args:
            config: 統一配置對象
        """
        super().__init__(config.model.device)

        self.config = config
        self.logger = logging.getLogger(__name__)

        # 初始化組件
        try:
            self.model_adapter = ModelAdapter(config.model)
        except Exception as e:
            raise InferenceProcessError(f"模型適配器初始化失敗: {str(e)}")

        # 處理組件
        try:
            self.slice_processor = SliceProcessor(
                config.processing.slice, config.classes) if config.processing.slice.enabled else None
            self.fusion_engine = FusionEngine(config.processing.fusion)
            self.sahi_processor = SAHIProcessor(
                config.processing.sahi)   # 🎯 新增：SAHI專用處理器
            self.post_processor = PostProcessor(
                config.processing, config.classes)
            self.image_processor = ImageProcessor(
                config.processing.image_processing, config.processing.roi)

            # 🎯 設定 SliceProcessor 的 ImageProcessor 以支援 ROI-SAHI
            if self.slice_processor and self.image_processor:
                self.slice_processor.set_image_processor(self.image_processor)
                self.logger.debug(
                    "✅ SliceProcessor 已連接 ImageProcessor，支援ROI-SAHI功能")

        except Exception as e:
            self.logger.warning(f"處理組件初始化失敗，使用簡化模式: {e}")
            self.slice_processor = None
            self.fusion_engine = None
            self.post_processor = None

        # 視覺化組件（暫時簡化初始化）
        try:
            if hasattr(FontManager, 'from_config'):
                self.font_manager = FontManager.from_config(
                    config.visualization)
            else:
                self.font_manager = FontManager()

            if hasattr(ThreeViewGenerator, 'from_config'):
                self.three_view_generator = ThreeViewGenerator.from_config(
                    config, self.font_manager)
            else:
                self.three_view_generator = ThreeViewGenerator(
                    self.font_manager, config)
        except Exception as e:
            self.logger.warning(f"視覺化組件初始化失敗，使用簡化模式: {e}")
            self.font_manager = None
            self.three_view_generator = None

        # IO組件
        try:
            self.csv_manager = CSVManager(config)
            self.gt_loader = GroundTruthLoader(config)
        except Exception as e:
            self.logger.warning(f"IO組件初始化失敗，使用簡化模式: {e}")
            self.csv_manager = None
            self.gt_loader = None

        # 📊 初始記憶體監控
        self._initial_memory = self._get_memory_usage()

        # 🧠 記憶體清理器
        self.memory_cleaner = None

        self.logger.info("✅ 統一推理引擎初始化完成")
        self.logger.info(f"   模型設備: {self.device}")
        self.logger.info(
            f"   SAHI切片: {'啟用' if config.processing.slice.enabled else '停用'}")
        self.logger.info(f"   融合策略: {config.processing.fusion.strategy.value}")
        self.logger.info(f"   初始記憶體使用: {self._initial_memory:.1f} MB")

    def set_memory_cleaner(self, memory_cleaner):
        """
        設置記憶體清理器

        Args:
            memory_cleaner: 記憶體清理器實例
        """
        self.memory_cleaner = memory_cleaner
        self.logger.info("🧠 記憶體清理器已設置到推理引擎")

    def _initialize_components(self):
        """初始化所有組件"""
        try:
            # 1. 模型適配器
            self.model_adapter = ModelAdapter(self.config.model)

            # 2. 處理組件（條件性載入）
            if self.config.processing.slice.enabled:
                self.slice_processor = SliceProcessor(
                    self.config.processing.slice, self.config.classes)
            else:
                self.slice_processor = None

            self.fusion_engine = FusionEngine(self.config.processing.fusion)
            self.post_processor = PostProcessor(
                self.config.processing, self.config.classes)

            # 3. 視覺化組件（條件性載入）
            if self.config.visualization.save_visualizations:
                self.font_manager = FontManager(
                    font_path=self.config.visualization.font_path,
                    font_size=self.config.visualization.font_size,
                    font_thickness=self.config.visualization.font_thickness,
                    font_scale=self.config.visualization.font_scale
                )

                if self.config.visualization.enable_three_view:
                    self.three_view_generator = ThreeViewGenerator(
                        font_manager=self.font_manager,
                        config=self.config
                    )
                else:
                    self.three_view_generator = None
            else:
                self.font_manager = None
                self.three_view_generator = None

            # 4. IO組件
            self.csv_manager = CSVManager(
                self.config) if self.config.output.enable_csv_output else None
            self.gt_loader = GroundTruthLoader(
                self.config) if self.config.output.enable_labelme_output else None

        except Exception as e:
            raise InferenceProcessError(f"組件初始化失敗: {str(e)}")

    def predict(self,
                image: Union[str, np.ndarray],
                output_dir: Optional[str] = None,
                gt_annotations: Optional[List[Dict]] = None,
                **kwargs) -> StandardResult:
        """
        統一推理接口

        Args:
            image: 輸入圖像（路徑或numpy數組）
            output_dir: 輸出目錄
            gt_annotations: Ground Truth標註
            **kwargs: 其他參數

        Returns:
            StandardResult: 標準化推理結果
        """
        original_image_array = None  # 🗑️ 記錄原始圖像以便清理
        try:
            total_start_time = time.time()

            # 🧠 記憶體清理器 - 圖像處理開始前清理
            if self.memory_cleaner:
                try:
                    self.memory_cleaner.on_image_start()
                except Exception as memory_cleanup_error:
                    self.logger.warning(
                        f"圖像開始前記憶體清理失敗: {memory_cleanup_error}")

            # 1. 圖像預處理
            preprocessing_start = time.time()
            image_array = self._validate_image_input(image)
            original_image_array = image_array  # 保存原始引用

            # 圖像縮放處理
            if self.image_processor:
                image_array, scale_factor = self.image_processor.resize_image(
                    image_array)
                self.current_scale = scale_factor

                # 🎯 ROI預覽生成邏輯改進：確保在第一張圖像處理時生成
                if (self.config.processing.roi.enabled and
                    self.config.processing.roi.enable_preview and
                        output_dir):

                    # 檢查是否需要生成預覽（第一次或ROI重置後）
                    should_generate_preview = not self.image_processor.roi_initialized

                    if should_generate_preview:
                        self.logger.info("🎯 準備生成ROI預覽圖像")

                        # 🧩 傳遞SAHI配置參數給預覽功能
                        try:
                            # 🧩 智能判斷是否顯示SAHI切片
                            # 條件：SAHI啟用 或 ROI模式是sahi 或 有切片處理器
                            show_sahi_slices = (
                                self.config.processing.slice.enabled or  # SAHI啟用
                                self.config.processing.roi.inference_mode == "sahi" or  # ROI-SAHI模式
                                self.slice_processor is not None  # 有切片處理器
                            )

                            preview_path = self.image_processor.save_roi_preview(
                                image_array, output_dir,
                                show_sahi_slices=show_sahi_slices,
                                slice_height=self.config.processing.slice.height,
                                slice_width=self.config.processing.slice.width,
                                overlap_ratio=self.config.processing.slice.overlap_ratio
                            )
                            self.logger.info(f"✅ ROI預覽已生成: {preview_path}")
                        except Exception as e:
                            self.logger.error(f"❌ ROI預覽生成失敗: {e}")
            else:
                self.current_scale = 1.0

            preprocessing_time = time.time() - preprocessing_start

            # 2. 模型推理
            inference_start = time.time()
            raw_detections = self._execute_inference(image_array)
            inference_time = time.time() - inference_start

            # 3. 後處理
            postprocessing_start = time.time()
            processed_detections = self._execute_postprocessing(
                image_array, raw_detections)

            # 將檢測結果縮放回原始圖像尺寸（用於最終輸出）
            if self.image_processor and self.current_scale != 1.0:
                # 為視覺化保留縮小版本的檢測結果
                visualization_detections = processed_detections[:]  # 使用列表切片複製
                # 將輸出用的檢測結果縮放回原始尺寸
                processed_detections = self.image_processor.scale_detection_coordinates(
                    processed_detections, 1.0 / self.current_scale
                )
            else:
                visualization_detections = processed_detections

            postprocessing_time = time.time() - postprocessing_start

            # 4. 視覺化處理
            visualization_start = time.time()

            # 縮放GT標註以匹配縮小後的圖像
            scaled_gt_annotations = gt_annotations
            if self.image_processor and gt_annotations and self.current_scale != 1.0:
                scaled_gt_annotations = self.image_processor.scale_gt_annotations(
                    gt_annotations, self.current_scale
                )

            visualization_results = self._execute_visualization(
                image_array, visualization_detections, output_dir, scaled_gt_annotations
            )
            visualization_time = time.time() - visualization_start

            # 5. 計算指標（如果有GT）
            metrics = self._calculate_metrics(
                processed_detections, gt_annotations)

            # 6. 創建計時信息
            total_time = time.time() - total_start_time
            timing = TimingInfo(
                total_time=total_time,
                inference_time=inference_time,
                preprocessing_time=preprocessing_time,
                postprocessing_time=postprocessing_time,
                visualization_time=visualization_time
            )

            # 7. 創建標準結果
            result = self._create_standard_result(
                status=InferenceStatus.SUCCESS,
                detections=processed_detections,
                timing=timing,
                metrics=metrics,
                visualization=visualization_results,
                metadata={
                    'input_shape': image_array.shape,
                    'detection_count': len(processed_detections),
                    'config_used': self.config.__class__.__name__
                }
            )

            # 8. 更新統計
            self._update_statistics(result)

            # 🧠 記憶體清理器處理
            if self.memory_cleaner:
                try:
                    # 圖像處理結束後清理
                    self.memory_cleaner.on_image_end()
                    # 處理完一張圖像後的回調
                    self.memory_cleaner.on_image_processed()
                except Exception as memory_cleanup_error:
                    self.logger.warning(f"記憶體清理器處理失敗: {memory_cleanup_error}")

            # 🗑️ 明確釋放圖像記憶體
            try:
                if original_image_array is not None:
                    del original_image_array
                if 'image_array' in locals():
                    del image_array
                if 'visualization_detections' in locals():
                    del visualization_detections
                # 強制垃圾回收
                import gc
                gc.collect()

                # 📊 檢查記憶體增長
                self._check_memory_growth("推理完成")

            except Exception as cleanup_error:
                self.logger.debug(f"記憶體清理警告: {cleanup_error}")

            return result

        except Exception as e:
            # 創建失敗結果
            error_timing = TimingInfo(
                total_time=time.time() - total_start_time if 'total_start_time' in locals() else 0.0,
                inference_time=0.0
            )

            error_result = self._create_standard_result(
                status=InferenceStatus.FAILED,
                detections=[],
                timing=error_timing,
                metadata={'error': str(e)}
            )

            self._update_statistics(error_result)

            # 🗑️ 錯誤時也要清理記憶體
            try:
                if original_image_array is not None:
                    del original_image_array
                if 'image_array' in locals():
                    del image_array
                import gc
                gc.collect()
            except:
                pass  # 忽略清理過程中的錯誤

            raise InferenceProcessError(f"推理執行失敗: {str(e)}")

    def predict_batch(self,
                      images: List[Union[str, np.ndarray]],
                      output_dir: Optional[str] = None,
                      gt_annotations: Optional[List[List[Dict]]] = None,
                      **kwargs) -> List[StandardResult]:
        """
        批次推理接口

        Args:
            images: 輸入圖像列表
            output_dir: 輸出目錄
            gt_annotations: GT標註列表
            **kwargs: 其他參數

        Returns:
            List[StandardResult]: 推理結果列表
        """
        results = []

        # 🧠 記憶體清理器 - 批次處理開始
        if self.memory_cleaner:
            try:
                self.memory_cleaner.on_batch_start()
            except Exception as memory_cleanup_error:
                self.logger.warning(f"批次開始記憶體清理失敗: {memory_cleanup_error}")

        for i, image in enumerate(images):
            try:
                # 獲取對應的GT標註
                gt = gt_annotations[i] if gt_annotations and i < len(
                    gt_annotations) else None

                # 單張推理
                result = self.predict(image, output_dir, gt, **kwargs)
                results.append(result)

                # 🗑️ 每處理完一張圖像就清理一次記憶體
                if i % 5 == 0:  # 每5張圖像強制清理一次
                    import gc
                    gc.collect()
                    self.logger.debug(f"🧹 批次處理記憶體清理: {i+1}/{len(images)}")

                # 進度報告
                if i % 10 == 0 or i == len(images) - 1:
                    success_count = sum(
                        1 for r in results if r.status == InferenceStatus.SUCCESS)
                    print(
                        f"📊 批次推理進度: {i+1}/{len(images)}, 成功率: {success_count}/{i+1}")

            except Exception as e:
                print(f"❌ 圖像 {i} 推理失敗: {str(e)}")

                # 創建失敗結果
                error_result = self._create_standard_result(
                    status=InferenceStatus.FAILED,
                    detections=[],
                    timing=TimingInfo(total_time=0.0, inference_time=0.0),
                    metadata={'error': str(e), 'image_index': i}
                )
                results.append(error_result)

                # 🗑️ 錯誤後也要清理記憶體
                import gc
                gc.collect()

        # 🧠 記憶體清理器 - 批次處理結束
        if self.memory_cleaner:
            try:
                self.memory_cleaner.on_batch_end()
            except Exception as memory_cleanup_error:
                self.logger.warning(f"批次結束記憶體清理失敗: {memory_cleanup_error}")

        # 🗑️ 批次處理完成後的最終清理
        import gc
        gc.collect()
        self.logger.info(f"🧹 批次推理完成，執行最終記憶體清理")

        return results

    def _execute_inference(self, image: np.ndarray) -> List[Detection]:
        """執行核心推理邏輯"""
        # 🎯 計算最低置信度閾值 - 使用類別特定的confidence
        min_confidence = self._get_min_confidence()

        # 基礎推理
        detections = self.model_adapter.inference(
            image,
            confidence=min_confidence,  # 使用類別特定的最低閾值
            iou_threshold=self.config.processing.fusion.iou_threshold,
            max_det=self.config.processing.max_det
        )

        # 切片推理（如果啟用）
        if self.slice_processor is not None:
            slice_detections = self.slice_processor.process(
                image, self.model_adapter)
            detections.extend(slice_detections)

        # ROI過濾（如果啟用）
        if self.config.processing.roi.enabled and self.image_processor:
            detections = self._apply_roi_filtering(image, detections)

        return detections

    def _apply_roi_filtering(self, image: np.ndarray, detections: List[Detection]) -> List[Detection]:
        """應用ROI過濾，只保留ROI區域內的檢測結果"""
        if not detections:
            return detections

        try:
            # 創建ROI遮罩
            roi_mask = self.image_processor.create_roi_mask(image.shape[:2])

            filtered_detections = []

            for detection in detections:
                # 檢查檢測框是否在ROI區域內
                if self._is_detection_in_roi(detection, roi_mask):
                    filtered_detections.append(detection)

            self.logger.debug(
                f"🎯 ROI過濾: {len(detections)} → {len(filtered_detections)} 檢測結果")

            return filtered_detections

        except Exception as e:
            self.logger.warning(f"ROI過濾失敗: {e}")
            return detections

    def _is_detection_in_roi(self, detection: Detection, roi_mask: np.ndarray) -> bool:
        """判斷檢測結果是否在ROI區域內"""
        try:
            if len(detection.bbox) < 4:
                return False

            x1, y1, x2, y2 = map(int, detection.bbox[:4])

            # 確保座標在圖像範圍內
            h, w = roi_mask.shape
            x1 = max(0, min(x1, w-1))
            y1 = max(0, min(y1, h-1))
            x2 = max(0, min(x2, w-1))
            y2 = max(0, min(y2, h-1))

            # 計算檢測框中心點
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2

            # 檢查中心點是否在ROI區域內
            return roi_mask[center_y, center_x]

        except Exception as e:
            self.logger.warning(f"ROI檢查失敗: {e}")
            return True  # 出錯時保留檢測結果

    def _get_min_confidence(self) -> float:
        """
        獲取最低的置信度閾值（用於模型推理）

        Returns:
            float: 最低的置信度閾值
        """
        if not self.config.classes:
            return 0.05  # 默認閾值

        # 只考慮啟用的類別
        enabled_configs = [
            config for config in self.config.classes.values() if config.enabled]

        if not enabled_configs:
            return 0.05

        min_conf = min(config.confidence for config in enabled_configs)

        # 記錄使用的閾值
        self.logger.debug(f"🎯 推理使用最低置信度閾值: {min_conf:.3f}")

        return min_conf

    def _execute_postprocessing(self, image: np.ndarray, detections: List[Detection]) -> List[Detection]:
        """執行後處理"""
        # 1. 物件融合（簡化版）
        fused_detections = self.fusion_engine.fuse(detections)

        # 2. 🎯 SAHI專用重疊合併（獨立處理步驟）
        sahi_processed_detections = self.sahi_processor.process(
            fused_detections)

        # 3. 進階後處理
        processed_detections = self.post_processor.process(
            image, sahi_processed_detections)

        return processed_detections

    def _execute_visualization(self,
                               image: np.ndarray,
                               detections: List[Detection],
                               output_dir: Optional[str],
                               gt_annotations: Optional[List[Dict]]) -> Optional[Dict[str, np.ndarray]]:
        """執行視覺化"""
        if not self.config.visualization.save_visualizations:
            return None

        # 🆕 空預測跳過視覺化檢查（考慮類別過濾）
        if not detections:
            skip_empty = getattr(self.config.output,
                                 'skip_empty_prediction_images', False)
            if skip_empty:
                self.logger.debug("🚫 跳過空預測圖像的視覺化生成")
                return None
        else:
            # 檢查是否所有檢測都被類別過濾掉了
            enabled_count = 0
            for detection in detections:
                class_config = self.config.get_class_config(detection.class_id)
                if class_config and class_config.enabled:
                    enabled_count += 1

            # 如果所有檢測都被過濾掉，且啟用空預測跳過，則跳過視覺化
            if enabled_count == 0:
                skip_empty = getattr(self.config.output,
                                     'skip_empty_prediction_images', False)
                if skip_empty:
                    self.logger.debug("🚫 跳過所有類別被禁用的圖像視覺化生成")
                    return None

        visualization_results = {}

        try:
            # 生成基礎預測視覺化（如果啟用）
            if self.config.visualization.enable_prediction_only:
                prediction_image = self._create_prediction_visualization(
                    image, detections)
                if prediction_image is not None:
                    visualization_results['prediction'] = prediction_image

            # 生成三視圖（如果啟用）
            if self.three_view_generator and self.config.visualization.enable_three_view:
                try:
                    three_view_image = self.three_view_generator.generate(
                        original_image=image,
                        detections=detections,
                        gt_annotations=gt_annotations,
                        output_dir=output_dir
                    )
                    if three_view_image is not None:
                        visualization_results['three_view'] = three_view_image
                except Exception as e:
                    self.logger.warning(f"三視圖生成失敗: {e}")

            return visualization_results if visualization_results else None

        except Exception as e:
            print(f"⚠️ 視覺化生成失敗: {str(e)}")
            return None

    def _create_prediction_visualization(self, image: np.ndarray, detections: List[Detection]) -> Optional[np.ndarray]:
        """創建單純預測結果視覺化"""
        try:
            vis_image = image.copy()

            for detection in detections:
                # 獲取類別配置
                class_config = self.config.get_class_config(detection.class_id)
                if not class_config or not class_config.enabled:
                    continue

                # 繪製檢測框
                bbox = detection.bbox
                if len(bbox) >= 4:
                    x1, y1, x2, y2 = map(int, bbox[:4])
                    color = tuple(map(int, class_config.color))

                    # 繪製邊框
                    cv2.rectangle(vis_image, (x1, y1), (x2, y2), color,
                                  self.config.visualization.line_thickness)

                    # 添加標籤文字
                    label = f"{class_config.display_name}: {detection.confidence:.2f}"
                    font = cv2.FONT_HERSHEY_SIMPLEX
                    font_scale = self.config.visualization.font_size
                    font_thickness = self.config.visualization.font_thickness

                    # 計算文字大小
                    (text_width, text_height), baseline = cv2.getTextSize(
                        label, font, font_scale, font_thickness)

                    # 繪製文字背景
                    cv2.rectangle(vis_image, (x1, y1 - text_height - baseline),
                                  (x1 + text_width, y1), color, -1)

                    # 繪製文字
                    cv2.putText(vis_image, label, (x1, y1 - baseline),
                                font, font_scale, (0, 0, 0), font_thickness)

                # 繪製mask（如果有）
                if detection.mask is not None:
                    try:
                        mask = detection.mask.astype(np.uint8)

                        # 確保mask尺寸與圖像匹配
                        if mask.shape[:2] != vis_image.shape[:2]:
                            mask = cv2.resize(mask, (vis_image.shape[1], vis_image.shape[0]),
                                              interpolation=cv2.INTER_NEAREST)

                        # 創建彩色遮罩
                        colored_mask = np.zeros_like(vis_image)
                        colored_mask[:] = class_config.color

                        # 應用透明度
                        alpha = self.config.visualization.transparency
                        mask_area = mask > 0

                        if mask_area.shape[:2] == vis_image.shape[:2]:
                            vis_image[mask_area] = cv2.addWeighted(
                                vis_image[mask_area], 1 - alpha,
                                colored_mask[mask_area], alpha, 0
                            )
                    except Exception as e:
                        self.logger.warning(
                            f"mask繪製失敗 (類別{detection.class_id}): {e}")
                        continue

            # 🖼️ 應用輸出圖像縮放
            vis_image = self._apply_output_scaling(vis_image)

            return vis_image

        except Exception as e:
            self.logger.error(f"預測視覺化創建失敗: {e}")
            return None
        finally:
            # 🗑️ 視覺化後清理臨時圖像變數
            try:
                if 'vis_image' in locals():
                    del vis_image
                if 'colored_mask' in locals():
                    del colored_mask
            except:
                pass

    def _apply_output_scaling(self, image: np.ndarray) -> np.ndarray:
        """
        🖼️ 應用輸出圖像縮放

        Args:
            image: 輸入圖像

        Returns:
            np.ndarray: 縮放後的圖像
        """
        try:
            scale = getattr(self.config.visualization,
                            'output_image_scale', 1.0)

            if scale == 1.0:
                return image

            h, w = image.shape[:2]
            new_w = int(w * scale)
            new_h = int(h * scale)

            # 確保尺寸至少為1
            new_w = max(1, new_w)
            new_h = max(1, new_h)

            scaled_image = cv2.resize(
                image, (new_w, new_h), interpolation=cv2.INTER_AREA)

            self.logger.debug(
                f"🖼️ 輸出圖像縮放: {w}x{h} → {new_w}x{new_h} (比例: {scale:.2f})")

            return scaled_image

        except Exception as e:
            self.logger.warning(f"⚠️ 輸出圖像縮放失敗: {str(e)}")
            return image

    def generate_visualizations(self,
                                image_path: str,
                                detections: List[Detection],
                                gt_annotations: Optional[List[Dict]] = None) -> Optional[Dict[str, str]]:
        """
        生成視覺化結果

        Args:
            image_path: 圖像路徑
            detections: 檢測結果
            gt_annotations: GT標註

        Returns:
            Dict[str, str]: 視覺化文件路徑字典
        """
        try:
            # 載入原始圖像
            import cv2
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"無法載入圖像: {image_path}")

            # 應用圖像縮放
            if self.image_processor:
                image, scale_factor = self.image_processor.resize_image(image)
                # 縮放檢測結果和GT標註以匹配縮小後的圖像
                if scale_factor != 1.0:
                    # 縮放檢測結果
                    scaled_detections = []
                    for detection in detections:
                        scaled_detection = detection.__dict__.copy()
                        if 'bbox' in scaled_detection and scaled_detection['bbox']:
                            bbox = scaled_detection['bbox']
                            scaled_detection['bbox'] = [
                                bbox[0] * scale_factor,  # x1
                                bbox[1] * scale_factor,  # y1
                                bbox[2] * scale_factor,  # x2
                                bbox[3] * scale_factor   # y2
                            ]
                        scaled_detections.append(
                            type(detection)(**scaled_detection))

                    # 縮放GT標註
                    if gt_annotations:
                        gt_annotations = self.image_processor.scale_gt_annotations(
                            gt_annotations, scale_factor
                        )
                else:
                    scaled_detections = detections
            else:
                scaled_detections = detections

            # 執行視覺化
            visualization_results = self._execute_visualization(
                image, scaled_detections, self.config.output_path, gt_annotations
            )

            if visualization_results:
                # 保存視覺化結果到 images/ 子目錄
                from pathlib import Path
                output_dir = Path(self.config.output_path) / "images"
                output_dir.mkdir(parents=True, exist_ok=True)

                image_name = Path(image_path).stem
                saved_paths = {}

                # 保存單純預測結果
                if 'prediction' in visualization_results:
                    prediction_path = output_dir / \
                        f"{image_name}_prediction.jpg"
                    cv2.imwrite(str(prediction_path),
                                visualization_results['prediction'])
                    saved_paths['prediction'] = str(prediction_path)

                # 保存三視圖結果
                if 'three_view' in visualization_results:
                    three_view_path = output_dir / \
                        f"{image_name}_three_view.jpg"
                    cv2.imwrite(str(three_view_path),
                                visualization_results['three_view'])
                    saved_paths['three_view'] = str(three_view_path)

                # 為了向前兼容，保留visualization鍵
                if saved_paths:
                    saved_paths['visualization'] = next(
                        iter(saved_paths.values()))

                return saved_paths

            return None

        except Exception as e:
            self.logger.error(f"視覺化生成失敗: {str(e)}")
            return None

    def _calculate_metrics(self, detections: List[Detection], gt_annotations: Optional[List[Dict]]) -> Optional[ConfusionMetrics]:
        """計算混淆矩陣指標"""
        if not gt_annotations:
            return None

        try:
            from ..utils import calculate_confusion_metrics
            return calculate_confusion_metrics(detections, gt_annotations, iou_threshold=0.5)
        except Exception as e:
            print(f"⚠️ 指標計算失敗: {str(e)}")
            return None

    def warmup(self, num_runs: int = 3):
        """推理引擎預熱"""
        print(f"🔥 推理引擎預熱中...")

        # 模型預熱
        self.model_adapter.warmup(num_runs)

        # 管道預熱
        dummy_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        for i in range(num_runs):
            try:
                start_time = time.time()
                result = self.predict(dummy_image)
                warmup_time = time.time() - start_time
                print(f"   管道預熱 {i+1}/{num_runs}: {warmup_time:.3f}秒")
            except Exception as e:
                print(f"   預熱 {i+1} 失敗: {str(e)}")

        print("✅ 推理引擎預熱完成")

    def get_system_info(self) -> Dict[str, Any]:
        """獲取系統信息"""
        info = {
            'engine_type': 'UnifiedInferenceEngine',
            'model_info': self.model_adapter.get_model_info(),
            'config_summary': {
                'slice_enabled': self.config.processing.slice.enabled,
                'fusion_strategy': self.config.processing.fusion.strategy.value,
                'visualization_enabled': self.config.visualization.save_visualizations,
                'enabled_classes': len(self.config.get_enabled_classes())
            },
            'statistics': self.get_statistics()
        }

        return info

    def cleanup(self):
        """清理所有資源"""
        print("🧹 清理推理引擎資源...")

        try:
            # 清理模型適配器
            if self.model_adapter:
                self.model_adapter.cleanup()

            # 清理其他組件
            components = [
                self.slice_processor,
                self.fusion_engine,
                self.post_processor,
                self.csv_manager,
                self.image_processor  # 🗑️ 添加圖像處理器清理
            ]

            for component in components:
                if component and hasattr(component, 'cleanup'):
                    component.cleanup()

            # 🗑️ 明確釋放組件引用
            self.model_adapter = None
            self.slice_processor = None
            self.fusion_engine = None
            self.post_processor = None
            self.csv_manager = None
            self.image_processor = None
            self.font_manager = None
            self.three_view_generator = None
            self.gt_loader = None

            # 強制垃圾回收
            import gc
            gc.collect()

            # 📊 最終記憶體狀態報告
            final_memory = self._get_memory_usage()
            memory_freed = self._initial_memory - final_memory
            if memory_freed > 0:
                print(f"✅ 推理引擎資源清理完成 (釋放 {memory_freed:.1f} MB)")
            else:
                print("✅ 推理引擎資源清理完成")

        except Exception as e:
            print(f"⚠️ 資源清理失敗: {str(e)}")

    def _get_memory_usage(self) -> float:
        """
        📊 獲取當前記憶體使用量 (MB)

        Returns:
            float: 記憶體使用量 (MB)
        """
        try:
            process = psutil.Process(os.getpid())
            memory_mb = process.memory_info().rss / 1024 / 1024
            return memory_mb
        except Exception:
            return 0.0

    def _check_memory_growth(self, operation_name: str = "") -> None:
        """
        📊 檢查記憶體增長情況

        Args:
            operation_name: 操作名稱
        """
        try:
            current_memory = self._get_memory_usage()
            memory_growth = current_memory - self._initial_memory

            if memory_growth > 100:  # 超過100MB成長時發出警告
                self.logger.warning(
                    f"⚠️ 記憶體使用量增長: {memory_growth:.1f} MB ({operation_name})")
            elif operation_name:  # 有操作名稱時記錄debug資訊
                self.logger.debug(
                    f"📊 記憶體: {current_memory:.1f} MB (+{memory_growth:.1f} MB) - {operation_name}")

        except Exception as e:
            self.logger.debug(f"記憶體監控失敗: {e}")


def create_inference_engine(config: Union[UnifiedConfig, str, Dict[str, Any]]) -> UnifiedInferenceEngine:
    """
    工廠函數：創建推理引擎

    Args:
        config: 統一配置（對象、YAML路徑或字典）

    Returns:
        UnifiedInferenceEngine: 推理引擎實例
    """
    if isinstance(config, str):
        # YAML路徑
        config = UnifiedConfig.from_yaml(config)
    elif isinstance(config, dict):
        # 配置字典
        config = UnifiedConfig.from_dict(config)
    elif not isinstance(config, UnifiedConfig):
        raise ValueError(f"不支援的配置類型: {type(config)}")

    # 驗證配置
    errors = config.validate()
    if errors:
        raise ValueError(f"配置驗證失敗: {errors}")

    return UnifiedInferenceEngine(config)
