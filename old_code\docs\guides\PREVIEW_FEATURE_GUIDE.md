# 🎯 ROI和切片預覽功能使用指南

## 📷 功能概述

新增的預覽功能可以：
- **只處理第一張圖像**：快速預覽配置效果
- **顯示ROI區域**：四個方向用不同顏色標示
- **顯示切片配置**：在ROI區域內顯示切片網格、重疊區域等
- **參數標註**：完整的配置參數說明

## 🚀 快速使用

### 1. 啟用預覽模式
在 `run_unified_yolo.py` 中設置：
```python
preview_mode = True                    # 啟用預覽模式
preview_only = False                   # False: 生成預覽後繼續推理, True: 只生成預覽
```

### 2. 運行腳本
```bash
cd road_ai_framework/
python run_unified_yolo.py
```

### 3. 查看結果
預覽圖將保存到：`{output_path}/preview/{第一張圖像名稱}_roi_slice_preview.jpg`

## 🎨 視覺化說明

### ROI邊界顏色
- 🔴 **紅色** - 上方邊界 (`roi_top_ratio`)
- 🟢 **綠色** - 下方邊界 (`roi_bottom_ratio`)  
- 🔵 **藍色** - 左方邊界 (`roi_left_ratio`)
- 🟡 **黃色** - 右方邊界 (`roi_right_ratio`)

### 切片網格顯示
- 🔲 **灰色網格線** - 切片分割線
- 📊 **X0, X1, X2...** - 水平切片索引
- 📊 **Y0, Y1, Y2...** - 垂直切片索引
- 🟨 **半透明黃色** - 重疊區域 (`overlap_ratio`)

### 參數標註
預覽圖會顯示：
- ROI配置（四個方向的倍數）
- 切片配置（大小、重疊比例）
- ROI區域尺寸
- 顏色說明

## ⚙️ 配置參數

### ROI配置
```python
# ROI中心區域配置 - 數值越大越靠近中心
roi_top_ratio = 3.0        # 從中心往上保留的倍數 (1.0=邊緣, 5.0=中心)
roi_bottom_ratio = 2.8     # 從中心往下保留的倍數
roi_left_ratio = 1.3       # 從中心往左保留的倍數  
roi_right_ratio = 1.7      # 從中心往右保留的倍數
```

### 切片配置
```python
# 高級切片推理配置
advanced_slice_height = 320      # 切片高度
advanced_slice_width = 320       # 切片寬度
advanced_overlap_ratio = 0.2     # 重疊比例 (0.0-1.0)
```

### 預覽配置
```python
# 預覽模式配置
preview_mode = True               # 是否啟用預覽模式
preview_only = False              # 只生成預覽圖，不執行推理
preview_save_original = True      # 預覽模式中是否保存原圖
enable_roi_preview = True         # 是否啟用ROI和切片預覽
```

## 🔧 實現細節

### 新增文件
- `models/inference/roi_preview_generator.py` - ROI預覽生成器
- `test_preview.py` - 預覽功能測試腳本
- `PREVIEW_FEATURE_GUIDE.md` - 使用指南（本文件）

### 修改文件
- `run_unified_yolo.py` - 添加預覽模式支持
  - 合併重複參數 `enable_roi_preview` 和 `force_roi_preview_display`
  - 新增預覽模式邏輯
  - 只處理第一張圖像的功能

### ROI座標計算邏輯
```python
# 中心點
center_x, center_y = w // 2, h // 2

# ROI邊界計算（ratio/10 作為百分比）
roi_top = max(0, center_y - int(h * roi_top_ratio / 10))
roi_bottom = min(h, center_y + int(h * roi_bottom_ratio / 10))
roi_left = max(0, center_x - int(w * roi_left_ratio / 10))
roi_right = min(w, center_x + int(w * roi_right_ratio / 10))
```

### 切片網格計算
```python
# 步長計算（考慮重疊）
step_h = int(slice_h * (1 - overlap_ratio))
step_w = int(slice_w * (1 - overlap_ratio))

# 重疊區域大小
overlap_h = int(slice_h * overlap_ratio)
overlap_w = int(slice_w * overlap_ratio)
```

## 💡 使用技巧

### 1. 配置測試
- 先啟用 `preview_only = True` 快速測試配置
- 確認ROI區域和切片配置合適後再進行推理

### 2. ROI調整
- `roi_*_ratio` 數值越大，ROI區域越靠近中心
- 建議範圍：1.0（邊緣）到 5.0（中心）
- 可根據實際圖像內容調整

### 3. 切片優化
- 確保切片大小適合目標物體尺寸
- 適當的重疊比例可以減少邊界效應
- 建議重疊比例：0.1-0.3

### 4. 性能考量
- 預覽模式處理速度很快（只處理一張圖像）
- 可以快速驗證多種配置組合
- 推薦在批次處理前先預覽

## 🐛 故障排除

### 常見問題
1. **預覽圖不生成**
   - 檢查輸入目錄是否包含圖像文件
   - 確認輸出目錄有寫入權限

2. **ROI區域不合適**
   - 調整 `roi_*_ratio` 參數
   - 檢查圖像尺寸和中心點計算

3. **切片網格顯示異常**
   - 檢查切片大小是否合理
   - 確認重疊比例設置正確

### 調試信息
預覽模式會輸出詳細信息：
- 選擇的第一張圖像路徑
- ROI座標計算結果
- 切片配置參數
- 預覽圖保存路徑

## 🎉 總結

預覽功能大大簡化了ROI和切片配置的調試過程：
- ✅ **合併重複參數**：統一了預覽相關設置
- ✅ **直觀可視化**：四色ROI邊界 + 切片網格
- ✅ **快速驗證**：只處理第一張圖像，節省時間
- ✅ **詳細標註**：完整的參數說明和顏色說明
- ✅ **靈活配置**：支援預覽模式和正常推理模式

現在可以輕鬆地：
1. 設置 `preview_mode = True`
2. 運行腳本查看預覽圖
3. 根據預覽結果調整參數
4. 進行正式的批次推理

預覽功能讓ROI和切片配置變得更加直觀和高效！