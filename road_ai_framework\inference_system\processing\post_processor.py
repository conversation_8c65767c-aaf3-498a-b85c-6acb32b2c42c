#!/usr/bin/env python3
"""
⚙️ 後處理器
執行智能過濾、相鄰合併、混淆矩陣計算等後處理邏輯
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import numpy as np

from ..core.base_inference import Detection, ConfusionMetrics
from ..config import ProcessingConfig
from .object_connection import ObjectConnectionProcessor


class PostProcessor:
    """
    後處理器

    功能：
    - 智能過濾 (linear_crack vs Alligator_crack)
    - 相鄰區域合併
    - 混淆矩陣計算
    - 類別過濾
    """

    def __init__(self, processing_config: ProcessingConfig, class_configs: Dict = None):
        """
        初始化後處理器

        Args:
            processing_config: 處理配置
            class_configs: 類別配置字典 {class_id: ClassConfig}
        """
        self.config = processing_config
        self.class_configs = class_configs or {}
        self.logger = logging.getLogger(__name__)

        # 🔗 初始化物件連接處理器
        connection_config = {
            'enable_object_connection': getattr(processing_config, 'enable_object_connection', False),
            'connection_distance_threshold': getattr(processing_config, 'connection_distance_threshold', 30.0),
            'connection_same_class_only': getattr(processing_config, 'connection_same_class_only', True),
            'connection_confidence_weight': getattr(processing_config, 'connection_confidence_weight', 0.3),
            'enable_crack_line_connection': getattr(processing_config, 'enable_crack_line_connection', False)
        }
        self.object_connection_processor = ObjectConnectionProcessor(
            connection_config)

        self.stats = {
            'total_processed': 0,
            'filtered_detections': 0,
            'merged_detections': 0,
            'connected_objects': 0,
            'processing_times': []
        }

    def process(self, image: np.ndarray, detections: List[Detection]) -> List[Detection]:
        """
        執行後處理

        Args:
            image: 原始圖像
            detections: 輸入檢測結果

        Returns:
            List[Detection]: 後處理結果
        """
        if not detections:
            return []

        import time
        start_time = time.time()

        # 1. 智能過濾
        if self.config.filtering.enabled:
            detections = self._intelligent_filtering(detections)

        # 1.5. 🤖 獨立的Joint優先過濾（即使智能過濾未啟用也可運行）
        elif getattr(self.config.filtering, 'enable_joint_priority_over_crack', False):
            detections = self._joint_priority_filtering(detections)

        # 2. 🔗 物件連接處理 (Stage 3 新功能)
        if self.object_connection_processor.enabled:
            connection_result = self.object_connection_processor.process(
                detections)
            detections = connection_result.connected_detections
            self.stats['connected_objects'] += connection_result.total_connections

            if connection_result.total_connections > 0:
                self.logger.debug(
                    f"🔗 物件連接完成：{connection_result.total_connections}次連接")

        # 3. 相鄰區域合併
        if self.config.enable_adjacent_merge:
            detections = self._merge_adjacent_regions(detections)

        # 4. 最終置信度過濾
        detections = self._apply_confidence_filtering(detections)

        processing_time = time.time() - start_time
        self.stats['total_processed'] += 1
        self.stats['processing_times'].append(processing_time)

        self.logger.debug(
            f"⚙️ 後處理完成：{len(detections)}個檢測，耗時{processing_time:.3f}秒")

        return detections

    def _intelligent_filtering(self, detections: List[Detection]) -> List[Detection]:
        """
        智能過濾邏輯

        處理以下衝突情況：
        1. linear_crack vs Alligator_crack: 基於長寬比和面積比的智能判斷
        2. linear_crack vs joint: 統一的Joint優先規則（合併原Step 2和Step 3）
           - 當啟用Joint優先規則時：joint與linear_crack重疊時優先保留joint
           - 當未啟用時：回退到原始Step 2邏輯
        """
        if not detections:
            return detections

        filtered = []
        removal_indices = set()

        for i, det1 in enumerate(detections):
            if i in removal_indices:
                continue

            for j, det2 in enumerate(detections[i+1:], i+1):
                if j in removal_indices:
                    continue

                # 計算IoU
                iou = self._calculate_iou(det1.bbox, det2.bbox)

                if iou > 0.0:
                    # 🐊 Step 1: linear_crack vs Alligator_crack 智能判斷
                    if ((det1.class_name == "linear_crack" and det2.class_name == "Alligator_crack") or
                            (det1.class_name == "Alligator_crack" and det2.class_name == "linear_crack")):

                        # 🔍 檢查是否有重疊
                        overlap_threshold = getattr(
                            self.config.filtering, 'alligator_contains_linear_threshold', 0.2)
                        
                        if iou > overlap_threshold:
                            # 💡 簡化邏輯：有重疊時，大box優先保留
                            det1_box_size = (det1.bbox[2] - det1.bbox[0]) * (det1.bbox[3] - det1.bbox[1])
                            det2_box_size = (det2.bbox[2] - det2.bbox[0]) * (det2.bbox[3] - det2.bbox[1])
                            
                            if det1_box_size > det2_box_size:
                                # det1的box較大，移除det2
                                removal_indices.add(j)
                                self.logger.debug(
                                    f"📏 重疊檢測: 保留大box {det1.class_name} (大小={det1_box_size:.0f} > {det2_box_size:.0f})")
                            else:
                                # det2的box較大或相等，移除det1
                                removal_indices.add(i)
                                self.logger.debug(
                                    f"📏 重疊檢測: 保留大box {det2.class_name} (大小={det2_box_size:.0f} >= {det1_box_size:.0f})")
                        
                        # 🔧 長寬比和面積比作為獨立功能（可選）
                        elif (getattr(self.config.filtering, 'enable_aspect_ratio_filter', False) or
                              getattr(self.config.filtering, 'enable_area_ratio_filter', False)):
                            
                            det1_width = det1.bbox[2] - det1.bbox[0]
                            det1_height = det1.bbox[3] - det1.bbox[1]
                            det2_width = det2.bbox[2] - det2.bbox[0]
                            det2_height = det2.bbox[3] - det2.bbox[1]

                            # 📏 長寬比過濾（獨立功能）
                            aspect_ratio_triggered = False
                            if getattr(self.config.filtering, 'enable_aspect_ratio_filter', False):
                                det1_aspect_ratio = min(det1_width, det1_height) / max(det1_width, det1_height)
                                det2_aspect_ratio = min(det2_width, det2_height) / max(det2_width, det2_height)
                                
                                if det1_aspect_ratio < self.config.filtering.linear_aspect_ratio_threshold:
                                    # det1符合線狀特徵，優先保留
                                    if det1.class_name == "linear_crack":
                                        removal_indices.add(j)
                                        self.logger.debug(f"📏 長寬比過濾: 保留Linear_crack (長寬比={det1_aspect_ratio:.3f})")
                                        aspect_ratio_triggered = True
                                elif det2_aspect_ratio < self.config.filtering.linear_aspect_ratio_threshold:
                                    # det2符合線狀特徵，優先保留
                                    if det2.class_name == "linear_crack":
                                        removal_indices.add(i)
                                        self.logger.debug(f"📏 長寬比過濾: 保留Linear_crack (長寬比={det2_aspect_ratio:.3f})")
                                        aspect_ratio_triggered = True
                            
                            # 📐 面積比過濾（獨立功能）
                            if (not aspect_ratio_triggered and 
                                getattr(self.config.filtering, 'enable_area_ratio_filter', False)):
                                det1_area = det1_width * det1_height
                                det2_area = det2_width * det2_height
                                area_ratio = min(det1_area, det2_area) / max(det1_area, det2_area)
                                
                                if area_ratio < self.config.filtering.area_ratio_threshold:
                                    # 面積差異大，保留面積較小的（通常是linear_crack）
                                    if det1_area < det2_area and det1.class_name == "linear_crack":
                                        removal_indices.add(j)
                                        self.logger.debug(f"📐 面積比過濾: 保留小面積Linear_crack (面積比={area_ratio:.3f})")
                                    elif det2_area < det1_area and det2.class_name == "linear_crack":
                                        removal_indices.add(i)
                                        self.logger.debug(f"📐 面積比過濾: 保留小面積Linear_crack (面積比={area_ratio:.3f})")

                    # 🤖 統一的Joint優先過濾規則 - 合併原Step 2和Step 3
                    elif ((det1.class_name == "linear_crack" and det2.class_name == "joint") or
                          (det1.class_name == "joint" and det2.class_name == "linear_crack")):

                        # 檢查是否啟用Joint優先規則
                        enable_joint_priority = getattr(
                            self.config.filtering, 'enable_joint_priority_over_crack', False)
                        joint_threshold = getattr(
                            self.config.filtering, 'joint_crack_overlap_threshold', 0.1)

                        # 🎯 增強判斷：結合IoU和中心點距離
                        center_distance = self._calculate_center_distance(
                            det1.bbox, det2.bbox)

                        if enable_joint_priority and iou > joint_threshold:
                            # 額外檢查：如果中心點距離太遠，可能是誤判
                            max_dimension = max(
                                det1.bbox[2] -
                                det1.bbox[0], det1.bbox[3] - det1.bbox[1],
                                det2.bbox[2] -
                                det2.bbox[0], det2.bbox[3] - det2.bbox[1]
                            )

                            # 使用配置的空間分離係數
                            separation_factor = getattr(
                                self.config.filtering, 'spatial_separation_factor', 1.5)
                            if center_distance > max_dimension * separation_factor:
                                self.logger.debug(
                                    f"🎯 空間分離檢測: 中心距離={center_distance:.1f} > {max_dimension * separation_factor:.1f}，保留兩者")
                            else:
                                # 優先保留 joint，移除 linear_crack
                                if det1.class_name == "linear_crack":
                                    removal_indices.add(i)
                                    self.logger.debug(
                                        f"🤖 Joint優先規則: 移除Linear_crack (IoU={iou:.3f}, 距離={center_distance:.1f})")
                                else:
                                    removal_indices.add(j)
                                    self.logger.debug(
                                        f"🤖 Joint優先規則: 移除Linear_crack (IoU={iou:.3f}, 距離={center_distance:.1f})")
                        # 如果Joint優先規則未啟用，則不進行joint vs linear_crack過濾
                        elif not enable_joint_priority:
                            self.logger.debug(
                                f"📋 Joint優先規則未啟用，跳過joint vs linear_crack過濾 (IoU={iou:.3f})")

        # 構建過濾後的結果
        for i, detection in enumerate(detections):
            if i not in removal_indices:
                filtered.append(detection)

        filtered_count = len(detections) - len(filtered)
        self.stats['filtered_detections'] += filtered_count

        if filtered_count > 0:
            self.logger.debug(f"🧠 智能過濾移除：{filtered_count}個檢測")

        return filtered

    def _joint_priority_filtering(self, detections: List[Detection]) -> List[Detection]:
        """
        🤖 獨立的Joint優先過濾邏輯

        專門處理joint vs linear_crack衝突，可獨立於智能過濾運行
        """
        if not detections:
            return detections

        filtered = []
        removal_indices = set()

        for i, det1 in enumerate(detections):
            if i in removal_indices:
                continue

            for j, det2 in enumerate(detections[i+1:], i+1):
                if j in removal_indices:
                    continue

                # 只處理joint vs linear_crack的情況
                if ((det1.class_name == "linear_crack" and det2.class_name == "joint") or
                        (det1.class_name == "joint" and det2.class_name == "linear_crack")):

                    # 計算IoU
                    iou = self._calculate_iou(det1.bbox, det2.bbox)

                    # 獲取配置參數
                    joint_threshold = getattr(
                        self.config.filtering, 'joint_crack_overlap_threshold', 0.1)

                    if iou > joint_threshold:
                        # 🎯 增強判斷：結合IoU和中心點距離
                        center_distance = self._calculate_center_distance(
                            det1.bbox, det2.bbox)

                        # 計算最大邊長
                        max_dimension = max(
                            det1.bbox[2] -
                            det1.bbox[0], det1.bbox[3] - det1.bbox[1],
                            det2.bbox[2] -
                            det2.bbox[0], det2.bbox[3] - det2.bbox[1]
                        )

                        # 使用配置的空間分離係數
                        separation_factor = getattr(
                            self.config.filtering, 'spatial_separation_factor', 1.5)

                        if center_distance > max_dimension * separation_factor:
                            self.logger.debug(
                                f"🎯 空間分離檢測: 中心距離={center_distance:.1f} > {max_dimension * separation_factor:.1f}，保留兩者")
                        else:
                            # 優先保留 joint，移除 linear_crack
                            if det1.class_name == "linear_crack":
                                removal_indices.add(i)
                                self.logger.debug(
                                    f"🤖 Joint優先過濾: 移除Linear_crack (IoU={iou:.3f}, 距離={center_distance:.1f})")
                            else:
                                removal_indices.add(j)
                                self.logger.debug(
                                    f"🤖 Joint優先過濾: 移除Linear_crack (IoU={iou:.3f}, 距離={center_distance:.1f})")

        # 構建過濾後的結果
        for i, detection in enumerate(detections):
            if i not in removal_indices:
                filtered.append(detection)

        filtered_count = len(detections) - len(filtered)
        self.stats['filtered_detections'] += filtered_count

        if filtered_count > 0:
            self.logger.debug(f"🤖 Joint優先過濾移除：{filtered_count}個檢測")

        return filtered

    def _merge_adjacent_regions(self, detections: List[Detection]) -> List[Detection]:
        """
        合併相鄰的同類別區域

        Args:
            detections: 輸入檢測結果

        Returns:
            List[Detection]: 合併後的檢測結果
        """
        if not detections or not self.config.enable_adjacent_merge:
            return detections

        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det.class_id
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        merged_detections = []
        merge_count = 0

        for class_id, class_dets in class_groups.items():
            if len(class_dets) <= 1:
                merged_detections.extend(class_dets)
                continue

            # 相鄰合併邏輯
            merged_group = []
            remaining = class_dets[:]  # 使用列表切片複製

            while remaining:
                current = remaining.pop(0)
                merged_candidates = [current]

                # 尋找相鄰的檢測
                i = 0
                while i < len(remaining):
                    candidate = remaining[i]

                    # 檢查與當前群組中任何檢測的距離
                    is_adjacent = False
                    for merged_det in merged_candidates:
                        distance = self._calculate_center_distance(
                            merged_det.bbox, candidate.bbox)
                        if distance < self.config.adjacent_distance_threshold:
                            is_adjacent = True
                            break

                    if is_adjacent:
                        merged_candidates.append(remaining.pop(i))
                        merge_count += 1
                    else:
                        i += 1

                # 如果有多個候選者，進行合併
                if len(merged_candidates) > 1:
                    merged_detection = self._merge_detections(
                        merged_candidates)
                    merged_group.append(merged_detection)
                else:
                    merged_group.extend(merged_candidates)

            merged_detections.extend(merged_group)

        self.stats['merged_detections'] += merge_count

        if merge_count > 0:
            self.logger.debug(f"🔗 相鄰合併：合併了{merge_count}個檢測")

        return merged_detections

    def _apply_confidence_filtering(self, detections: List[Detection]) -> List[Detection]:
        """應用置信度過濾 - 使用類別特定的置信度閾值"""
        if not self.class_configs:
            # 回退到全局閾值
            threshold = self.config.fusion.confidence_threshold
            filtered = [
                det for det in detections if det.confidence >= threshold]

            filtered_count = len(detections) - len(filtered)
            if filtered_count > 0:
                self.logger.debug(
                    f"🎯 置信度過濾移除：{filtered_count}個檢測（全局閾值：{threshold}）")

            return filtered

        # 使用類別特定的置信度閾值
        filtered = []
        filtered_count_by_class = {}

        for detection in detections:
            class_config = self.class_configs.get(detection.class_id)
            if class_config:
                threshold = class_config.confidence
                class_name = class_config.name
            else:
                # 如果沒找到類別配置，使用全局閾值
                threshold = self.config.fusion.confidence_threshold
                class_name = detection.class_name or f"class_{detection.class_id}"

            if detection.confidence >= threshold:
                filtered.append(detection)
            else:
                if class_name not in filtered_count_by_class:
                    filtered_count_by_class[class_name] = {
                        'count': 0, 'threshold': threshold}
                filtered_count_by_class[class_name]['count'] += 1

        # 輸出過濾統計
        total_filtered = len(detections) - len(filtered)
        if total_filtered > 0:
            self.logger.debug(f"🎯 置信度過濾移除：{total_filtered}個檢測")
            for class_name, info in filtered_count_by_class.items():
                self.logger.debug(
                    f"   {class_name}: {info['count']}個 (閾值: {info['threshold']:.3f})")

        return filtered

    def _calculate_center_distance(self, bbox1: List[float], bbox2: List[float]) -> float:
        """計算兩個bbox中心點的距離"""
        center1_x = (bbox1[0] + bbox1[2]) / 2
        center1_y = (bbox1[1] + bbox1[3]) / 2
        center2_x = (bbox2[0] + bbox2[2]) / 2
        center2_y = (bbox2[1] + bbox2[3]) / 2

        return np.sqrt((center1_x - center2_x)**2 + (center1_y - center2_y)**2)

    def _merge_detections(self, detections: List[Detection]) -> Detection:
        """
        合併多個檢測為一個

        Args:
            detections: 要合併的檢測列表

        Returns:
            Detection: 合併後的檢測
        """
        if len(detections) == 1:
            return detections[0]

        # 找到最高置信度的檢測作為基準
        base_detection = max(detections, key=lambda x: x.confidence)

        # 計算合併後的bbox（取包圍盒）
        all_bboxes = [det.bbox for det in detections]
        merged_bbox = [
            min(bbox[0] for bbox in all_bboxes),  # x1
            min(bbox[1] for bbox in all_bboxes),  # y1
            max(bbox[2] for bbox in all_bboxes),  # x2
            max(bbox[3] for bbox in all_bboxes),  # y2
        ]

        # 計算平均置信度
        avg_confidence = sum(
            det.confidence for det in detections) / len(detections)

        # 合併mask（如果存在）
        merged_mask = None
        if any(det.mask is not None for det in detections):
            # 簡化實現：使用最大的mask
            masks = [det.mask for det in detections if det.mask is not None]
            if masks:
                merged_mask = masks[0]  # 取第一個非空mask

        # 計算合併後面積
        merged_area = (merged_bbox[2] - merged_bbox[0]) * \
            (merged_bbox[3] - merged_bbox[1])

        return Detection(
            bbox=merged_bbox,
            confidence=avg_confidence,
            class_id=base_detection.class_id,
            class_name=base_detection.class_name,
            mask=merged_mask,
            area=merged_area
        )

    def calculate_confusion_metrics(self,
                                    predictions: List[Detection],
                                    gt_annotations: List[Dict[str, Any]],
                                    iou_threshold: float = 0.5) -> Optional[ConfusionMetrics]:
        """
        計算混淆矩陣指標

        Args:
            predictions: 預測結果
            gt_annotations: GT標註
            iou_threshold: IoU閾值

        Returns:
            ConfusionMetrics: 混淆矩陣指標
        """
        if not gt_annotations:
            return None

        try:
            tp = 0  # True Positive
            fp = 0  # False Positive
            fn = 0  # False Negative

            matched_gt = set()

            # 計算TP和FP
            for pred in predictions:
                best_iou = 0.0
                best_gt_idx = -1

                for gt_idx, gt in enumerate(gt_annotations):
                    if gt_idx in matched_gt:
                        continue

                    # 確保類別匹配
                    if pred.class_id != gt.get('class_id', -1):
                        continue

                    # 計算IoU
                    gt_bbox = gt.get('bbox', [])
                    if len(gt_bbox) >= 4:
                        iou = self._calculate_iou(pred.bbox, gt_bbox)
                        if iou > best_iou:
                            best_iou = iou
                            best_gt_idx = gt_idx

                # 判斷是否為TP
                if best_iou >= iou_threshold and best_gt_idx >= 0:
                    tp += 1
                    matched_gt.add(best_gt_idx)
                else:
                    fp += 1

            # 計算FN
            fn = len(gt_annotations) - len(matched_gt)

            # 計算指標
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            f1_score = 2 * (precision * recall) / (precision +
                                                   recall) if (precision + recall) > 0 else 0.0

            return ConfusionMetrics(
                true_positive=tp,
                false_positive=fp,
                false_negative=fn,
                precision=precision,
                recall=recall,
                f1_score=f1_score
            )

        except Exception as e:
            self.logger.error(f"❌ 混淆矩陣計算失敗: {str(e)}")
            return None

    def _calculate_iou(self, bbox1: List[float], bbox2: List[float]) -> float:
        """計算IoU"""
        x1_max = max(bbox1[0], bbox2[0])
        y1_max = max(bbox1[1], bbox2[1])
        x2_min = min(bbox1[2], bbox2[2])
        y2_min = min(bbox1[3], bbox2[3])

        if x2_min <= x1_max or y2_min <= y1_max:
            return 0.0

        intersection = (x2_min - x1_max) * (y2_min - y1_max)
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    def get_statistics(self) -> Dict[str, Any]:
        """獲取後處理統計信息"""
        avg_processing_time = (
            sum(self.stats['processing_times']) /
            len(self.stats['processing_times'])
            if self.stats['processing_times'] else 0.0
        )

        return {
            'total_processed': self.stats['total_processed'],
            'filtered_detections': self.stats['filtered_detections'],
            'merged_detections': self.stats['merged_detections'],
            'connected_objects': self.stats['connected_objects'],
            'average_processing_time': avg_processing_time,
            'config': {
                'filtering_enabled': self.config.filtering.enabled,
                'adjacent_merge_enabled': self.config.enable_adjacent_merge,
                'distance_threshold': self.config.adjacent_distance_threshold,
                'object_connection_enabled': self.object_connection_processor.enabled
            },
            'object_connection_stats': self.object_connection_processor.get_statistics() if self.object_connection_processor.enabled else {}
        }

    def reset_statistics(self):
        """重置統計信息"""
        self.stats = {
            'total_processed': 0,
            'filtered_detections': 0,
            'merged_detections': 0,
            'connected_objects': 0,
            'processing_times': []
        }

        # 重置物件連接處理器統計
        if hasattr(self, 'object_connection_processor'):
            self.object_connection_processor.reset_statistics()

    def cleanup(self):
        """清理後處理器資源"""
        self.logger.debug("🧹 清理後處理器資源")
        self.reset_statistics()
