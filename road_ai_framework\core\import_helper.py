#!/usr/bin/env python3
"""
統一導入管理助手
統一管理所有模組的導入路徑
"""

import sys
from pathlib import Path

# 設置matplotlib繪圖樣式（避免中文顯示問題）


def setup_matplotlib():
    """設置matplotlib繪圖樣式為英文，避免中文顯示問題"""
    try:
        import matplotlib.pyplot as plt
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei',
                                           'Times New Roman']
        plt.rcParams['axes.unicode_minus'] = False
        return True
    except ImportError:
        return False


def setup_project_paths():
    """設置項目路徑（同時支援 old_code 內的舊版目錄）"""
    current_dir = Path(__file__).parent
    project_root = current_dir

    old_code_dir = project_root.parent / "old_code"
    # 添加主要路徑（將 old_code 加入搜尋路徑，便於導入舊模組）
    paths_to_add = [
        str(project_root),
        str(old_code_dir),
    ]

    # 如存在，額外加入舊版目錄以相容舊導入方式
    legacy_dirs = [
        project_root.parent / "AI模型建構訓練驗證",
        project_root.parent / "資料前處理",
        old_code_dir / "AI模型建構訓練驗證",
        old_code_dir / "資料前處理",
    ]

    for p in paths_to_add + [str(p) for p in legacy_dirs if p.exists()]:
        if p not in sys.path:
            sys.path.insert(0, p)


# 自動設置路徑和matplotlib
setup_project_paths()
MATPLOTLIB_CONFIGURED = setup_matplotlib()

# 統一導入介面


def get_model_create_path():
    """獲取model_create路徑（優先新位置，回退 old_code 位置）"""
    root = Path(__file__).parent.parent  # road_ai_framework 根
    primary = root / "AI模型建構訓練驗證" / "model_create"
    if primary.exists():
        return primary
    fallback = root / "old_code" / "AI模型建構訓練驗證" / "model_create"
    return fallback


def get_data_processing_path():
    """獲取資料前處理路徑（優先新位置，回退 old_code 位置）"""
    root = Path(__file__).parent.parent  # road_ai_framework 根
    primary = root / "資料前處理"
    if primary.exists():
        return primary
    fallback = root / "old_code" / "資料前處理"
    return fallback


def get_project_root():
    """獲取項目根目錄"""
    return Path(__file__).parent

# 常用導入
# 檢查模組可用性（不實際導入，避免循環依賴）


def check_vision_mamba_available():
    try:
        vision_mamba_path = get_model_create_path() / "encoder" / "mamba" / \
            "vision_mamba_core.py"
        return vision_mamba_path.exists()
    except:
        return False


def check_csp_iformer_available():
    try:
        seg_path = get_model_create_path() / "encoder" / "VIT" / \
            "CSP_IFormer_final_SegMode.py"
        cls_path = get_model_create_path() / "encoder" / "VIT" / \
            "CSP_IFormer_final_ClsMode.py"
        return seg_path.exists() and cls_path.exists()
    except:
        return False


def check_training_available():
    try:
        training_path = get_model_create_path() / "training" / "__init__.py"
        return training_path.exists()
    except:
        return False


def check_data_processing_available():
    try:
        converter_path = get_data_processing_path() / "tools" / \
            "annotation_converter_v2.py"
        return converter_path.exists()
    except:
        return False


def check_enhanced_yolo_available():
    try:
        yolo_path = get_model_create_path() / "inference" / "enhanced_yolo_inference.py"
        return yolo_path.exists()
    except:
        return False


# 設置可用性標誌
VISION_MAMBA_AVAILABLE = check_vision_mamba_available()
CSP_IFORMER_AVAILABLE = check_csp_iformer_available()
TRAINING_AVAILABLE = check_training_available()
DATA_PROCESSING_AVAILABLE = check_data_processing_available()
ENHANCED_YOLO_AVAILABLE = check_enhanced_yolo_available()

# 導出可用性標誌
__all__ = [
    'setup_project_paths',
    'setup_matplotlib',
    'get_model_create_path',
    'get_data_processing_path',
    'get_project_root',
    'VISION_MAMBA_AVAILABLE',
    'CSP_IFORMER_AVAILABLE',
    'TRAINING_AVAILABLE',
    'DATA_PROCESSING_AVAILABLE',
    'ENHANCED_YOLO_AVAILABLE',
    'MATPLOTLIB_CONFIGURED'
]
