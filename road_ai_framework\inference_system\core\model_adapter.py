#!/usr/bin/env python3
"""
🔌 模型適配器
統一不同模型框架的接口，支援YOLO、MMDetection等
"""

import time
import logging
from pathlib import Path
from typing import Dict, List, Union, Optional, Any
import numpy as np

from .base_inference import Detection, ModelLoadError, InferenceProcessError
from ..config import ModelConfig


class ModelAdapter:
    """
    模型適配器 - 統一不同模型框架的接口
    
    支援:
    - YOLO (ultralytics)
    - MMDetection
    - Detectron2
    - 自定義模型
    """
    
    def __init__(self, model_config: ModelConfig):
        """
        初始化模型適配器
        
        Args:
            model_config: 模型配置
        """
        self.config = model_config
        self.model = None
        self.model_type = None
        self._load_model()
    
    def _load_model(self):
        """載入模型"""
        try:
            # 檢查模型路徑
            model_path = self.config.segmentation_model_path or self.config.detection_model_path
            if not model_path:
                raise ModelLoadError("未指定模型路徑")
            
            if not Path(model_path).exists():
                raise ModelLoadError(f"模型文件不存在: {model_path}")
            
            # 嘗試載入YOLO模型
            if self._is_yolo_model(model_path):
                self._load_yolo_model(model_path)
            else:
                raise ModelLoadError(f"不支援的模型格式: {model_path}")
                
        except Exception as e:
            raise ModelLoadError(f"模型載入失敗: {str(e)}")
    
    def _is_yolo_model(self, model_path: str) -> bool:
        """判斷是否為YOLO模型"""
        return model_path.endswith(('.pt', '.pth', '.onnx', '.engine'))
    
    def _load_yolo_model(self, model_path: str):
        """載入YOLO模型"""
        try:
            from ultralytics import YOLO
            
            self.model = YOLO(model_path)
            self.model_type = "yolo"
            
            # 設置設備
            if self.config.device:
                self.model.to(self.config.device)
            
            print(f"✅ YOLO模型載入成功: {Path(model_path).name}")
            print(f"   設備: {self.config.device}")
            print(f"   半精度: {self.config.half_precision}")
            
        except ImportError:
            raise ModelLoadError("ultralytics未安裝，無法載入YOLO模型")
        except Exception as e:
            raise ModelLoadError(f"YOLO模型載入失敗: {str(e)}")
    
    def inference(self, 
                 image: np.ndarray,
                 confidence: float = 0.25,
                 iou_threshold: float = 0.45,
                 max_det: int = 1000) -> List[Detection]:
        """
        執行模型推理
        
        Args:
            image: 輸入圖像 (numpy array)
            confidence: 置信度閾值
            iou_threshold: IoU閾值
            max_det: 最大檢測數量
            
        Returns:
            List[Detection]: 檢測結果列表
        """
        if self.model is None:
            raise InferenceProcessError("模型未載入")
        
        try:
            start_time = time.time()
            
            if self.model_type == "yolo":
                results = self._yolo_inference(image, confidence, iou_threshold, max_det)
            else:
                raise InferenceProcessError(f"不支援的模型類型: {self.model_type}")
            
            inference_time = time.time() - start_time
            
            # 記錄推理時間
            logging.debug(f"模型推理完成，耗時: {inference_time:.3f}秒，檢測數量: {len(results)}")
            
            return results
            
        except Exception as e:
            raise InferenceProcessError(f"推理執行失敗: {str(e)}")
    
    def _yolo_inference(self, 
                       image: np.ndarray, 
                       confidence: float,
                       iou_threshold: float,
                       max_det: int) -> List[Detection]:
        """YOLO模型推理"""
        try:
            # YOLO推理
            results = self.model(
                image,
                conf=confidence,
                iou=iou_threshold,
                max_det=max_det,
                imgsz=self.config.img_size,
                half=self.config.half_precision,
                device=self.config.device,
                verbose=False
            )
            
            detections = []
            
            for result in results:
                # 處理檢測框
                if hasattr(result, 'boxes') and result.boxes is not None:
                    boxes = result.boxes
                    for i in range(len(boxes)):
                        # 提取bbox信息
                        bbox = boxes.xyxy[i].cpu().numpy().tolist()  # [x1, y1, x2, y2]
                        conf = float(boxes.conf[i].cpu().numpy())
                        class_id = int(boxes.cls[i].cpu().numpy())
                        
                        # 獲取類別名稱
                        class_name = self.model.names.get(class_id, f"class_{class_id}")
                        
                        # 處理mask（如果有）
                        mask = None
                        if hasattr(result, 'masks') and result.masks is not None:
                            try:
                                mask_data = result.masks.data[i].cpu().numpy()
                                if len(mask_data.shape) == 2:
                                    mask = mask_data
                                else:
                                    mask = mask_data[0] if mask_data.shape[0] == 1 else mask_data
                            except (IndexError, AttributeError):
                                pass
                        
                        # 計算面積
                        area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1]) if len(bbox) >= 4 else None
                        
                        detection = Detection(
                            bbox=bbox,
                            confidence=conf,
                            class_id=class_id,
                            class_name=class_name,
                            mask=mask,
                            area=area
                        )
                        
                        detections.append(detection)
            
            return detections
            
        except Exception as e:
            raise InferenceProcessError(f"YOLO推理失敗: {str(e)}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """獲取模型信息"""
        info = {
            'model_type': self.model_type,
            'model_path': self.config.segmentation_model_path or self.config.detection_model_path,
            'device': self.config.device,
            'half_precision': self.config.half_precision,
            'img_size': self.config.img_size
        }
        
        if self.model and self.model_type == "yolo":
            try:
                info.update({
                    'model_task': getattr(self.model, 'task', 'unknown'),
                    'class_names': getattr(self.model, 'names', {}),
                    'num_classes': len(getattr(self.model, 'names', {}))
                })
            except:
                pass
        
        return info
    
    def warmup(self, num_runs: int = 3):
        """模型預熱"""
        if self.model is None:
            return
        
        try:
            # 創建虛擬圖像進行預熱
            dummy_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            
            print(f"🔥 模型預熱中... ({num_runs}次)")
            for i in range(num_runs):
                start_time = time.time()
                self.inference(dummy_image, confidence=0.5)
                warmup_time = time.time() - start_time
                print(f"   預熱 {i+1}/{num_runs}: {warmup_time:.3f}秒")
            
            print("✅ 模型預熱完成")
            
        except Exception as e:
            print(f"⚠️ 模型預熱失敗: {str(e)}")
    
    def cleanup(self):
        """清理模型資源"""
        if self.model is not None:
            try:
                # 如果是torch模型，清理GPU記憶體
                if hasattr(self.model, 'model') and hasattr(self.model.model, 'cpu'):
                    self.model.model.cpu()
                    
                # 垃圾回收
                import gc
                gc.collect()
                
                # 清理CUDA緩存
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                except ImportError:
                    pass
                    
                print("✅ 模型資源清理完成")
                
            except Exception as e:
                print(f"⚠️ 模型資源清理失敗: {str(e)}")
            finally:
                self.model = None