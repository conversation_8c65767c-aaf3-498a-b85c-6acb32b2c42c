#!/usr/bin/env python3
"""
🎯 SAHI專用處理器
從融合引擎中獨立出來的SAHI重疊合併功能
"""

import logging
from typing import List, Dict, Any, Optional
import numpy as np

from ..core.base_inference import Detection
from ..config import SAHIConfig


class SAHIProcessor:
    """
    SAHI專用處理器
    
    功能：
    - SAHI專用重疊合併
    - 支援mask與mask的IoU計算
    - 支援box與box的IoU計算  
    - 保持較高的召回率，適合SAHI場景
    """

    def __init__(self, sahi_config: SAHIConfig):
        """
        初始化SAHI處理器

        Args:
            sahi_config: SAHI配置
        """
        self.config = sahi_config
        self.logger = logging.getLogger(__name__)
        
        self.stats = {
            'total_processes': 0,
            'input_detections': 0,
            'output_detections': 0,
            'merge_operations': 0,
            'processing_times': []
        }

    def process(self, detections: List[Detection]) -> List[Detection]:
        """
        處理檢測結果，執行SAHI專用重疊合併
        
        Args:
            detections: 原始檢測結果列表
            
        Returns:
            List[Detection]: 處理後的檢測結果
        """
        if not detections:
            return []
            
        # 如果沒有啟用SAHI重疊合併，直接返回
        if not self.config.enable_sahi_overlap_merge:
            self.logger.debug("🎯 SAHI重疊合併未啟用，跳過處理")
            return detections
            
        import time
        start_time = time.time()
        
        # 轉換為內部格式
        internal_detections = self._detections_to_internal(detections)
        
        # 執行SAHI重疊合併
        merged_internal = self._sahi_overlap_merge(internal_detections)
        
        # 轉換回Detection格式
        merged_detections = self._internal_to_detections(merged_internal)
        
        # 🗑️ 清理中間變數
        del internal_detections
        del merged_internal
        
        # 更新統計
        processing_time = time.time() - start_time
        self.stats['total_processes'] += 1
        self.stats['input_detections'] += len(detections)
        self.stats['output_detections'] += len(merged_detections)
        self.stats['processing_times'].append(processing_time)
        
        self.logger.debug(
            f"🎯 SAHI重疊合併完成：{len(detections)} → {len(merged_detections)}，耗時：{processing_time:.3f}秒")
        
        # 🗑️ 最終記憶體清理
        import gc
        gc.collect()
        
        return merged_detections
    
    def _detections_to_internal(self, detections: List[Detection]) -> List[Dict[str, Any]]:
        """轉換Detection對象為內部字典格式"""
        internal = []
        for det in detections:
            internal.append({
                'bbox': det.bbox,
                'confidence': det.confidence,
                'class_id': det.class_id,
                'class_name': det.class_name,
                'mask': det.mask,
                'area': det.area
            })
        return internal

    def _internal_to_detections(self, internal_detections: List[Dict[str, Any]]) -> List[Detection]:
        """轉換內部字典格式為Detection對象"""
        detections = []
        for det in internal_detections:
            detection = Detection(
                bbox=det['bbox'],
                confidence=det['confidence'],
                class_id=det['class_id'],
                class_name=det['class_name'],
                mask=det.get('mask'),
                area=det.get('area')
            )
            detections.append(detection)
        return detections

    def _sahi_overlap_merge(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        SAHI專用重疊合併策略
        
        特點：
        - 檢測IoU > threshold的同類別物件進行合併
        - 支援mask與mask的IoU計算
        - 支援box與box的IoU計算  
        - 保持較高的召回率，適合SAHI場景
        
        Args:
            detections: 檢測結果列表
            
        Returns:
            List[Dict]: 合併後的檢測結果
        """
        if not detections:
            return []
        
        self.logger.debug(f"🎯 開始SAHI重疊合併: {len(detections)} 個檢測")
        
        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)
        
        final_detections = []
        merge_count = 0
        
        for class_id, class_dets in class_groups.items():
            if len(class_dets) <= 1:
                final_detections.extend(class_dets)
                continue
            
            # 為每個檢測分配唯一ID
            for i, det in enumerate(class_dets):
                det['temp_id'] = i
            
            # 建立IoU連接圖  
            connections = {}  # temp_id -> [connected_temp_ids]
            
            for i, det1 in enumerate(class_dets):
                temp_id1 = det1['temp_id']
                connections[temp_id1] = []
                
                for j, det2 in enumerate(class_dets[i+1:], i+1):
                    temp_id2 = det2['temp_id']
                    
                    # 計算IoU（支持mask和box）
                    iou = self._calculate_flexible_iou(
                        det1, det2, self.config.enable_mask_iou_calculation
                    )
                    
                    if iou > self.config.sahi_merge_iou_threshold:
                        connections[temp_id1].append(temp_id2)
                        if temp_id2 not in connections:
                            connections[temp_id2] = []
                        connections[temp_id2].append(temp_id1)
                        
                        self.logger.debug(
                            f"   類別{class_id}: 檢測{temp_id1}與{temp_id2} IoU={iou:.3f} > {self.config.sahi_merge_iou_threshold} → 連接")
            
            # 使用深度優先搜尋找到連通分量
            visited = set()
            merge_groups = []
            
            for temp_id in range(len(class_dets)):
                if temp_id not in visited:
                    group = []
                    self._dfs_merge_group(temp_id, connections, visited, group)
                    merge_groups.append(group)
            
            # 合併每個group中的檢測
            for group_ids in merge_groups:
                if len(group_ids) == 1:
                    # 單個檢測，直接添加
                    det_idx = group_ids[0]
                    final_detections.append(class_dets[det_idx])
                else:
                    # 多個檢測，需要合併
                    group_detections = [class_dets[idx] for idx in group_ids]
                    merged_detection = self._merge_sahi_detections(
                        group_detections, self.config.sahi_merge_confidence_strategy
                    )
                    final_detections.append(merged_detection)
                    merge_count += len(group_detections) - 1
                    self.stats['merge_operations'] += 1
                    
                    self.logger.debug(f"   類別{class_id}: 合併{len(group_detections)}個檢測為1個")
        
        self.logger.debug(f"🎯 SAHI重疊合併完成: {len(detections)} → {len(final_detections)} 個檢測 (合併{merge_count}次)")
        
        # 🗑️ 清理中間變數
        del class_groups
        del connections
        del visited
        del merge_groups
        import gc
        gc.collect()
        
        return final_detections
    
    def _calculate_flexible_iou(self, det1: Dict[str, Any], det2: Dict[str, Any], 
                               enable_mask_iou: bool = True) -> float:
        """
        靈活的IoU計算，支持mask和box
        
        Args:
            det1: 第一個檢測
            det2: 第二個檢測
            enable_mask_iou: 是否啟用mask IoU計算
            
        Returns:
            float: IoU值
        """
        # 如果兩者都有mask且啟用mask IoU，優先使用mask IoU
        if (enable_mask_iou and 
            det1.get('mask') is not None and 
            det2.get('mask') is not None):
            try:
                mask_iou = self._calculate_mask_iou(det1['mask'], det2['mask'])
                return mask_iou
            except Exception as e:
                self.logger.warning(f"Mask IoU計算失敗，回退到Box IoU: {e}")
        
        # 使用box IoU
        return self._calculate_iou(det1['bbox'], det2['bbox'])
    
    def _calculate_mask_iou(self, mask1: np.ndarray, mask2: np.ndarray) -> float:
        """
        計算兩個mask的IoU
        
        Args:
            mask1: 第一個mask
            mask2: 第二個mask
            
        Returns:
            float: IoU值
        """
        try:
            # 確保mask是布爾類型
            mask1 = mask1.astype(bool)
            mask2 = mask2.astype(bool)
            
            # 確保mask尺寸相同
            if mask1.shape != mask2.shape:
                # 如果尺寸不同，調整到相同尺寸
                import cv2
                if mask1.size > mask2.size:
                    mask2 = cv2.resize(mask2.astype(np.uint8), 
                                     (mask1.shape[1], mask1.shape[0]), 
                                     interpolation=cv2.INTER_NEAREST).astype(bool)
                else:
                    mask1 = cv2.resize(mask1.astype(np.uint8), 
                                     (mask2.shape[1], mask2.shape[0]), 
                                     interpolation=cv2.INTER_NEAREST).astype(bool)
            
            # 計算交集和並集
            intersection = np.logical_and(mask1, mask2)
            union = np.logical_or(mask1, mask2)
            
            intersection_area = np.sum(intersection)
            union_area = np.sum(union)
            
            if union_area == 0:
                return 0.0
            
            iou = intersection_area / union_area
            return float(iou)
            
        except Exception as e:
            self.logger.warning(f"Mask IoU計算異常: {e}")
            return 0.0
    
    def _calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """計算IoU"""
        x1_max = max(box1[0], box2[0])
        y1_max = max(box1[1], box2[1])
        x2_min = min(box1[2], box2[2])
        y2_min = min(box1[3], box2[3])

        if x2_min <= x1_max or y2_min <= y1_max:
            return 0.0

        intersection = (x2_min - x1_max) * (y2_min - y1_max)
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0
    
    def _dfs_merge_group(self, temp_id: int, connections: Dict[int, List[int]], 
                        visited: set, group: List[int]):
        """
        深度優先搜尋找到合併組
        
        Args:
            temp_id: 當前檢測的臨時ID
            connections: 連接關係字典
            visited: 已訪問的節點集合
            group: 當前合併組的檢測ID列表
        """
        visited.add(temp_id)
        group.append(temp_id)
        
        # 遍歷所有連接的檢測
        for connected_id in connections.get(temp_id, []):
            if connected_id not in visited:
                self._dfs_merge_group(connected_id, connections, visited, group)
    
    def _merge_sahi_detections(self, detections: List[Dict[str, Any]], 
                              confidence_strategy: str = "max") -> Dict[str, Any]:
        """
        合併SAHI檢測結果
        
        Args:
            detections: 需要合併的檢測列表
            confidence_strategy: 置信度合併策略 ("max", "avg", "weighted_avg")
            
        Returns:
            Dict: 合併後的檢測
        """
        if len(detections) == 1:
            return detections[0]
        
        # 計算合併後的邊界框（包圍所有檢測框）
        all_bboxes = [det['bbox'] for det in detections]
        x_min = min(bbox[0] for bbox in all_bboxes)
        y_min = min(bbox[1] for bbox in all_bboxes)
        x_max = max(bbox[2] for bbox in all_bboxes)
        y_max = max(bbox[3] for bbox in all_bboxes)
        
        merged_bbox = [x_min, y_min, x_max, y_max]
        
        # 根據策略計算合併置信度
        if confidence_strategy == "max":
            merged_confidence = max(det['confidence'] for det in detections)
        elif confidence_strategy == "avg":
            merged_confidence = sum(det['confidence'] for det in detections) / len(detections)
        elif confidence_strategy == "weighted_avg":
            # 按面積加權平均
            total_area = 0
            weighted_confidence_sum = 0
            
            for det in detections:
                bbox_area = self._calculate_bbox_area(det['bbox'])
                total_area += bbox_area
                weighted_confidence_sum += det['confidence'] * bbox_area
            
            merged_confidence = weighted_confidence_sum / max(total_area, 1e-6)
        else:
            merged_confidence = max(det['confidence'] for det in detections)
        
        # 確保置信度在合理範圍內
        merged_confidence = min(max(merged_confidence, 0.0), 1.0)
        
        # 使用最高置信度檢測的類別信息
        best_detection = max(detections, key=lambda x: x['confidence'])
        
        # 創建合併後的檢測
        merged_detection = {
            'bbox': merged_bbox,
            'confidence': merged_confidence,
            'class_id': best_detection['class_id'],
            'class_name': best_detection['class_name'],
            'area': self._calculate_bbox_area(merged_bbox),
            'merged_from_count': len(detections),
            'original_confidences': [det['confidence'] for det in detections],
            'merge_method': 'sahi_overlap_merge',
            'merge_strategy': confidence_strategy
        }
        
        # 處理mask合併（如果存在）
        masks = [det.get('mask') for det in detections if det.get('mask') is not None]
        if masks:
            try:
                merged_mask = self._merge_sahi_masks(masks)
                merged_detection['mask'] = merged_mask
            except Exception as e:
                self.logger.warning(f"SAHI mask合併失敗: {e}")
                merged_detection['mask'] = None
        else:
            merged_detection['mask'] = None
        
        return merged_detection
    
    def _merge_sahi_masks(self, masks: List[np.ndarray]) -> Optional[np.ndarray]:
        """
        合併多個SAHI masks
        
        Args:
            masks: mask列表
            
        Returns:
            Optional[np.ndarray]: 合併後的mask
        """
        if not masks:
            return None
        
        try:
            # 找到最大的mask尺寸
            max_h = max(mask.shape[0] for mask in masks)
            max_w = max(mask.shape[1] for mask in masks)
            
            # 將所有mask調整到相同尺寸並合併
            merged_mask = np.zeros((max_h, max_w), dtype=bool)
            
            for mask in masks:
                if mask.shape != (max_h, max_w):
                    import cv2
                    resized_mask = cv2.resize(
                        mask.astype(np.uint8), 
                        (max_w, max_h), 
                        interpolation=cv2.INTER_NEAREST
                    ).astype(bool)
                else:
                    resized_mask = mask.astype(bool)
                
                # 取並集
                merged_mask = np.logical_or(merged_mask, resized_mask)
                
                # 🗑️ 清理中間mask
                del resized_mask
            
            return merged_mask
            
        except Exception as e:
            self.logger.warning(f"SAHI masks合併失敗: {e}")
            return masks[0] if masks else None
    
    def _calculate_bbox_area(self, bbox: List[float]) -> float:
        """計算bbox面積"""
        return (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取SAHI處理統計信息"""
        avg_processing_time = (
            sum(self.stats['processing_times']) / len(self.stats['processing_times'])
            if self.stats['processing_times'] else 0.0
        )

        merge_ratio = (
            self.stats['merge_operations'] / max(self.stats['total_processes'], 1)
            if self.stats['total_processes'] > 0 else 0.0
        )

        return {
            'total_processes': self.stats['total_processes'],
            'input_detections': self.stats['input_detections'],
            'output_detections': self.stats['output_detections'],
            'merge_operations': self.stats['merge_operations'],
            'merge_ratio': merge_ratio,
            'average_processing_time': avg_processing_time,
            'config': {
                'enable_sahi_overlap_merge': self.config.enable_sahi_overlap_merge,
                'sahi_merge_iou_threshold': self.config.sahi_merge_iou_threshold,
                'enable_mask_iou_calculation': self.config.enable_mask_iou_calculation,
                'sahi_merge_confidence_strategy': self.config.sahi_merge_confidence_strategy
            }
        }

    def reset_statistics(self):
        """重置統計信息"""
        self.stats = {
            'total_processes': 0,
            'input_detections': 0,
            'output_detections': 0,
            'merge_operations': 0,
            'processing_times': []
        }

    def cleanup(self):
        """清理SAHI處理器資源"""
        self.logger.debug("🧹 清理SAHI處理器資源")
        
        self.reset_statistics()
        
        # 🗑️ 清理其他引用
        self.config = None
        
        # 強制垃圾回收
        import gc
        gc.collect()