#!/usr/bin/env python3
"""
🧪 LabelMe JSON功能簡化測試
測試LabelMe JSON生成的核心功能，不依賴實際的YOLO模型
"""

import json
import numpy as np
import cv2
from pathlib import Path
import tempfile
import sys

# 設置路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def create_mock_detection_result():
    """創建模擬的檢測結果"""
    # 創建簡單的測試mask
    mask = np.zeros((100, 100), dtype=np.uint8)
    # 創建一個矩形區域
    mask[30:70, 20:80] = 255
    
    detection = {
        'class_id': 2,
        'confidence': 0.85,
        'mask': mask,
        'bbox': [20, 30, 80, 70]  # [x1, y1, x2, y2]
    }
    
    return detection

def test_labelme_json_generator():
    """測試LabelMe JSON生成器核心功能"""
    print("🧪 測試LabelMe JSON生成器核心功能...")
    
    try:
        from models.inference.labelme_json_generator import create_labelme_json_generator
        
        # 創建生成器
        generator = create_labelme_json_generator(simplify_tolerance=2.0)
        
        # 創建測試檢測結果
        detection = create_mock_detection_result()
        detections = [detection]
        
        # 類別名稱映射
        class_names = {2: 'linear_crack'}
        
        # 創建臨時圖像
        with tempfile.TemporaryDirectory() as temp_dir:
            # 創建測試圖像
            test_image = np.zeros((100, 100, 3), dtype=np.uint8)
            test_image_path = Path(temp_dir) / "test_image.jpg"
            cv2.imwrite(str(test_image_path), test_image)
            
            print(f"✅ 創建測試圖像: {test_image_path}")
            
            # 生成LabelMe JSON
            json_path = generator.process_image_results(
                image_path=str(test_image_path),
                results=detections,
                output_dir=temp_dir,
                class_names=class_names
            )
            
            if json_path and Path(json_path).exists():
                print(f"✅ JSON檔案生成成功: {Path(json_path).name}")
                
                # 驗證JSON內容
                with open(json_path, 'r', encoding='utf-8') as f:
                    labelme_json = json.load(f)
                
                print(f"📋 JSON內容驗證:")
                print(f"   - 版本: {labelme_json.get('version', 'N/A')}")
                print(f"   - 圖像路徑: {labelme_json.get('imagePath', 'N/A')}")
                print(f"   - 圖像尺寸: {labelme_json.get('imageWidth', 'N/A')} x {labelme_json.get('imageHeight', 'N/A')}")
                print(f"   - 檢測數量: {len(labelme_json.get('shapes', []))}")
                
                if labelme_json.get('shapes'):
                    shape = labelme_json['shapes'][0]
                    print(f"   - 第一個檢測:")
                    print(f"     * 類別: {shape.get('label', 'N/A')}")
                    print(f"     * 形狀類型: {shape.get('shape_type', 'N/A')}")
                    print(f"     * 點數: {len(shape.get('points', []))}")
                
                return True
            else:
                print("❌ JSON檔案生成失敗")
                return False
                
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_labelme_integration():
    """測試LabelMe整合模組"""
    print("\n🧪 測試LabelMe整合模組...")
    
    try:
        # 創建模擬配置
        class MockConfig:
            def __init__(self):
                self.labelme_output = self.LabelMeConfig()
                self.paths = self.PathsConfig()
                self.classes = {2: self.ClassConfig('linear_crack')}
        
        class LabelMeConfig:
            def __init__(self):
                self.enable_labelme_output = True
                self.labelme_output_dir = 'test_labelme_json'
                self.labelme_simplify_tolerance = 2.0
                self.labelme_min_polygon_points = 3
                self.labelme_include_confidence = False
        
        class PathsConfig:
            def __init__(self):
                self.output_path = tempfile.mkdtemp()
        
        class ClassConfig:
            def __init__(self, name):
                self.name = name
        
        # 創建整合器
        from models.inference.labelme_integration import create_labelme_integration
        
        config = MockConfig()
        integration = create_labelme_integration(config)
        
        if integration.enabled:
            print("✅ 整合器初始化成功")
            print(f"📁 輸出目錄: {integration.output_dir}")
        else:
            print("❌ 整合器初始化失敗")
            return False
        
        # 創建測試檢測結果
        detection = create_mock_detection_result()
        
        # 創建測試圖像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        test_image_path = Path(config.paths.output_path) / "test_image.jpg"
        cv2.imwrite(str(test_image_path), test_image)
        
        # 測試單張處理
        json_path = integration.process_single_image_result(
            image_path=str(test_image_path),
            detections=[detection]
        )
        
        if json_path:
            print(f"✅ 單張處理成功: {Path(json_path).name}")
            return True
        else:
            print("❌ 單張處理失敗")
            return False
            
    except Exception as e:
        print(f"❌ 整合測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """檢查依賴項"""
    print("🔍 檢查依賴項...")
    
    dependencies = ['numpy', 'cv2', 'json', 'pathlib']
    missing = []
    
    for dep in dependencies:
        try:
            if dep == 'cv2':
                import cv2
            elif dep == 'numpy':
                import numpy
            elif dep == 'json':
                import json
            elif dep == 'pathlib':
                from pathlib import Path
            print(f"✅ {dep}")
        except ImportError:
            print(f"❌ {dep} (缺少)")
            missing.append(dep)
    
    if missing:
        print(f"\n⚠️ 缺少依賴項: {', '.join(missing)}")
        print("請安裝所需的依賴項")
        return False
    
    return True

def main():
    """主測試函數"""
    print("🧪 LabelMe JSON功能簡化測試")
    print("=" * 50)
    
    # 檢查依賴項
    if not check_dependencies():
        return
    
    # 測試核心功能
    tests = [
        ("LabelMe JSON生成器", test_labelme_json_generator),
        ("LabelMe整合模組", test_labelme_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: 通過\n")
            else:
                print(f"❌ {test_name}: 失敗\n")
        except Exception as e:
            print(f"❌ {test_name}: 異常 - {e}\n")
    
    print("=" * 50)
    print(f"🎯 測試結果: {passed}/{total} 個測試通過")
    
    if passed == total:
        print("🎉 核心功能正常！")
        print("\n💡 如果實際使用時沒有生成JSON，可能原因:")
        print("1. 模型檔案不存在或路徑錯誤")
        print("2. 使用的是檢測模型而非分割模型")
        print("3. 檢測結果中沒有有效的mask數據")
        print("4. 輸出目錄權限問題")
        
        print("\n🔍 下一步診斷:")
        print("1. 確認模型檔案存在且是分割模型")
        print("2. 運行實際推理並查看詳細日誌")
        print("3. 檢查'有效mask數量'的輸出")
    else:
        print("❌ 核心功能有問題，需要修復")

if __name__ == "__main__":
    main()