#!/usr/bin/env python3
"""
🔍 測試ClassConfig修復
"""

import sys
from pathlib import Path

# 添加路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_classconfig_creation():
    """測試ClassConfig創建"""
    print("🔍 測試ClassConfig創建...")
    
    try:
        from inference_system.config import ClassConfig
        print("✅ ClassConfig導入成功")
        
        # 測試創建ClassConfig - 使用正確的參數
        test_config = ClassConfig(
            name="linear_crack",
            display_name="縱向裂縫", 
            color=[0, 255, 0],
            confidence=0.3,
            sahi_confidence=0.1,
            enabled=True
        )
        
        print("✅ ClassConfig創建成功")
        print(f"   名稱: {test_config.name}")
        print(f"   顯示名: {test_config.display_name}")
        print(f"   顏色: {test_config.color}")
        print(f"   置信度: {test_config.confidence}")
        print(f"   SAHI置信度: {test_config.sahi_confidence}")
        print(f"   啟用: {test_config.enabled}")
        
        return True
        
    except Exception as e:
        print(f"❌ ClassConfig測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unified_config():
    """測試UnifiedConfig創建"""
    print("\n🔍 測試UnifiedConfig創建...")
    
    try:
        from inference_system.config import UnifiedConfig, ClassConfig
        
        # 創建類別配置字典
        classes = {}
        classes[6] = ClassConfig(
            name="linear_crack",
            display_name="縱向裂縫",
            color=[0, 255, 0],
            confidence=0.3,
            sahi_confidence=0.1,
            enabled=True
        )
        
        # 創建統一配置
        config = UnifiedConfig()
        config.classes = classes
        
        print("✅ UnifiedConfig創建成功")
        print(f"   類別數量: {len(config.classes)}")
        print(f"   啟用類別: {len(config.get_enabled_classes())}")
        
        return True
        
    except Exception as e:
        print(f"❌ UnifiedConfig測試失敗: {e}")
        import traceback  
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🚀 ClassConfig修復驗證")
    print("=" * 40)
    
    test1_success = test_classconfig_creation()
    test2_success = test_unified_config()
    
    print("\n" + "=" * 40)
    if test1_success and test2_success:
        print("✅ 所有測試通過，ClassConfig問題已修復！")
        print("💡 現在可以正常使用終極版本了")
    else:
        print("❌ 部分測試失敗，需要進一步檢查")

if __name__ == "__main__":
    main()