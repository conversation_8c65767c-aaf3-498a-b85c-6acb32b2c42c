# 🐍 統一YOLO推理系統 - Python依賴包

# 核心AI框架
torch>=2.0.0
torchvision>=0.15.0
ultralytics>=8.0.0
opencv-python>=4.8.0

# 圖像處理
Pillow>=9.5.0
numpy>=1.21.0
scikit-image>=0.19.0

# 數據增強和SAHI
albumentations>=1.3.0
sahi>=0.11.0

# 可視化和字體
matplotlib>=3.6.0

# 配置和序列化
pyyaml>=6.0
dataclasses-json>=0.5.7

# API服務
fastapi>=0.100.0
uvicorn[standard]>=0.22.0
pydantic>=2.0.0

# 數據庫和緩存
sqlite3  # Python內建
redis>=4.5.0  # 可選，用於分散式緩存

# 系統監控
psutil>=5.9.0

# 測試框架
pytest>=7.0.0
pytest-asyncio>=0.21.0

# 工具和實用程序
tqdm>=4.65.0
pathlib  # Python內建
logging  # Python內建
threading  # Python內建
multiprocessing  # Python內建

# HTTP客戶端（用於API測試）
requests>=2.28.0

# 圖像格式支援
imageio>=2.25.0

# 數學和統計
scipy>=1.9.0
pandas>=1.5.0  # 可選，用於數據分析

# 序列化
pickle  # Python內建
json  # Python內建

# 時間和日期
datetime  # Python內建

# 文件和路徑處理
pathlib  # Python內建

# 環境變量管理
python-dotenv>=1.0.0

# 生產級部署
gunicorn>=20.1.0  # WSGI服務器

# 可選：分散式計算
# ray[default]>=2.0.0  # 取消註釋以啟用Ray分散式計算

# 可選：深度學習框架擴展
# onnxruntime-gpu>=1.15.0  # ONNX運行時
# tensorrt>=8.6.0  # TensorRT加速（需要NVIDIA環境）

# 開發工具（開發環境可選）
# black>=23.0.0  # 代碼格式化
# flake8>=6.0.0  # 代碼檢查
# mypy>=1.0.0   # 類型檢查