#!/usr/bin/env python3
"""
🏆 最終工作版本 - 統一YOLO推理系統
解決所有導入問題的完全自包含版本

🌟 特色:
✅ 零導入依賴問題 - 完全自包含
✅ 智能降級機制 - 從Phase 4降級到基礎YOLO
✅ 完整錯誤處理 - 詳細錯誤信息和解決方案
✅ 參數化配置 - 所有參數集中管理

🎯 使用方法:
1. 修改下面的參數設定區域
2. python run_final_working.py
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union

# 確保路徑正確
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# ============================================================================
# 📋 參數設定區域 - 請在這裡修改您的設定
# ============================================================================

# 🤖 模型路徑
MODEL_PATH = r"D:\4_road_crack\best_0728.pt"

# 📁 輸入輸出路徑
INPUT_PATH = r"D:\image\road_crack\test_600_resize"
OUTPUT_PATH = r"D:\image\road_crack\test_600_out_final"

# 🎯 運行模式選擇
RUN_MODE = "AUTO"  # AUTO: 自動選擇最佳模式, BASIC: 純YOLO, ADVANCED: 嘗試高級功能

# 🧩 SAHI設定
ENABLE_SAHI = False
SAHI_SLICE_SIZE = 512
SAHI_OVERLAP_RATIO = 0.2

# 🧠 智能過濾設定
ENABLE_INTELLIGENT_FILTERING = True
LINEAR_ASPECT_RATIO_THRESHOLD = 0.8
AREA_RATIO_THRESHOLD = 0.4
STEP2_IOU_THRESHOLD = 0.3

# 🎨 視覺化設定
ENABLE_VISUALIZATION = True
ENABLE_THREE_VIEW = True
THREE_VIEW_LAYOUT = "horizontal"  # horizontal 或 vertical
FONT_SIZE = 1.0
FONT_THICKNESS = 2
LINE_THICKNESS = 2
OUTPUT_QUALITY = 95

# 🏷️ 類別配置
CLASS_CONFIGS = {
    0: ["Alligator_crack", "龜裂", [0, 0, 255], 0.4, 0.1, True],
    1: ["deformation", "變形", [100, 100, 100], 0.1, 0.1, True], 
    2: ["dirt", "汙垢", [110, 110, 110], 0.1, 0.08, True],
    3: ["expansion_joint", "伸縮縫", [120, 120, 120], 0.1, 0.08, True],
    4: ["joint", "路面接縫", [130, 130, 130], 0.1, 0.1, True],
    5: ["lane_line_linear", "白線裂縫", [140, 140, 140], 0.1, 0.05, True],
    6: ["linear_crack", "裂縫", [0, 255, 0], 0.3, 0.1, True],
    7: ["manhole", "孔蓋", [255, 0, 255], 0.1, 0.1, True],
    8: ["patch", "補綻", [255, 0, 0], 0.1, 0.1, True],
    9: ["patch_square", "補綻_方正", [160, 160, 160], 0.1, 0.1, True],
    10: ["potholes", "坑洞", [0, 255, 255], 0.1, 0.1, True],
    11: ["rutting", "車轍", [255, 255, 0], 0.1, 0.1, True]
}

# 📊 輸出設定
SAVE_CSV = True
SAVE_JSON = True
SAVE_IMAGES = True

# 🔧 系統設定
LOG_LEVEL = "INFO"
MAX_WORKERS = 1

# ============================================================================
# 🛠️ 系統功能實現
# ============================================================================

def setup_logging() -> logging.Logger:
    """設置日誌系統"""
    logging.basicConfig(
        level=getattr(logging, LOG_LEVEL),
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def check_dependencies() -> Dict[str, bool]:
    """檢查系統依賴"""
    deps = {}
    
    # 檢查基本依賴
    try:
        import torch
        deps['torch'] = True
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError:
        deps['torch'] = False
        print("❌ PyTorch 不可用")
    
    try:
        import ultralytics
        deps['ultralytics'] = True
        print(f"✅ Ultralytics: {ultralytics.__version__}")
    except ImportError:
        deps['ultralytics'] = False
        print("❌ Ultralytics 不可用")
    
    try:
        import cv2
        deps['cv2'] = True
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError:
        deps['cv2'] = False
        print("❌ OpenCV 不可用")
    
    try:
        import numpy as np
        deps['numpy'] = True
        print(f"✅ NumPy: {np.__version__}")
    except ImportError:
        deps['numpy'] = False
        print("❌ NumPy 不可用")
    
    # 檢查高級功能
    try:
        from inference_system import create_inference_system
        deps['advanced_system'] = True
        print("✅ 高級推理系統可用")
    except ImportError:
        deps['advanced_system'] = False
        print("⚠️ 高級推理系統不可用，將使用基礎模式")
    
    # 檢查Phase 4組件
    phase4_available = 0
    try:
        from intelligence.model_selector import IntelligentModelManager
        phase4_available += 1
        print("✅ 智能模型選擇器可用")
    except ImportError:
        print("⚠️ 智能模型選擇器不可用")
    
    try:
        from enterprise.multi_tenant import TenantManager
        phase4_available += 1
        print("✅ 多租戶管理器可用")
    except ImportError:
        print("⚠️ 多租戶管理器不可用")
    
    try:
        from load_balancing.intelligent_balancer import IntelligentLoadBalancer
        phase4_available += 1
        print("✅ 智能負載均衡器可用")
    except ImportError:
        print("⚠️ 智能負載均衡器不可用")
    
    deps['phase4_components'] = phase4_available
    if phase4_available > 0:
        print(f"🧠 Phase 4 功能部分可用 ({phase4_available}/3 組件)")
    else:
        print("ℹ️ Phase 4 功能完全不可用")
    
    return deps

def determine_run_mode(deps: Dict[str, bool]) -> str:
    """根據依賴情況決定運行模式"""
    if RUN_MODE != "AUTO":
        return RUN_MODE
    
    # 自動選擇模式
    if deps.get('advanced_system', False) and deps.get('phase4_components', 0) > 0:
        return "INTELLIGENT"
    elif deps.get('advanced_system', False):
        return "ADVANCED"
    elif deps.get('ultralytics', False):
        return "BASIC"
    else:
        return "MINIMAL"

class BasicYOLOInference:
    """基礎YOLO推理類"""
    
    def __init__(self, model_path: str, logger: logging.Logger):
        self.model_path = model_path
        self.logger = logger
        self.model = None
        
    def load_model(self):
        """載入YOLO模型"""
        try:
            from ultralytics import YOLO
            self.model = YOLO(self.model_path)
            self.logger.info(f"✅ 模型載入成功: {Path(self.model_path).name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 模型載入失敗: {e}")
            return False
    
    def inference_single(self, image_path: str) -> Dict[str, Any]:
        """單張圖像推理"""
        try:
            # 獲取有效類別的最低置信度
            enabled_classes = {k: v for k, v in CLASS_CONFIGS.items() if v[5]}
            if not enabled_classes:
                min_conf = 0.3
            else:
                min_conf = min(config[3] for config in enabled_classes.values())
            
            # YOLO推理
            results = self.model(image_path, conf=min_conf)
            
            # 解析結果
            detections = []
            if results and len(results) > 0:
                for result in results:
                    if hasattr(result, 'boxes') and result.boxes is not None:
                        for box in result.boxes:
                            class_id = int(box.cls.item())
                            confidence = float(box.conf.item())
                            
                            # 檢查類別是否啟用
                            if class_id in enabled_classes:
                                class_config = enabled_classes[class_id]
                                if confidence >= class_config[3]:  # 置信度閾值
                                    detection = {
                                        'class_id': class_id,
                                        'class_name': class_config[0],
                                        'display_name': class_config[1],
                                        'confidence': confidence,
                                        'bbox': box.xyxy[0].tolist(),
                                        'color': class_config[2]
                                    }
                                    detections.append(detection)
            
            # 智能過濾
            if ENABLE_INTELLIGENT_FILTERING:
                detections = self.apply_intelligent_filtering(detections)
            
            return {
                'success': True,
                'image_path': image_path,
                'detections': detections,
                'detection_count': len(detections)
            }
            
        except Exception as e:
            self.logger.error(f"❌ 推理失敗 {image_path}: {e}")
            return {
                'success': False,
                'image_path': image_path,
                'error': str(e)
            }
    
    def apply_intelligent_filtering(self, detections: List[Dict]) -> List[Dict]:
        """應用智能過濾"""
        if not detections:
            return detections
        
        filtered = []
        
        # Step 1: linear_crack vs Alligator_crack 智能判斷
        linear_cracks = [d for d in detections if d['class_name'] == 'linear_crack']
        alligator_cracks = [d for d in detections if d['class_name'] == 'Alligator_crack']
        
        for linear in linear_cracks:
            should_keep = True
            linear_bbox = linear['bbox']
            linear_area = (linear_bbox[2] - linear_bbox[0]) * (linear_bbox[3] - linear_bbox[1])
            
            for alligator in alligator_cracks:
                # 計算IoU
                iou = self.calculate_iou(linear_bbox, alligator['bbox'])
                if iou > 0.0:
                    # 計算長寬比和面積比
                    width = linear_bbox[2] - linear_bbox[0]
                    height = linear_bbox[3] - linear_bbox[1]
                    aspect_ratio = min(width, height) / max(width, height)
                    
                    alligator_area = (alligator['bbox'][2] - alligator['bbox'][0]) * (alligator['bbox'][3] - alligator['bbox'][1])
                    area_ratio = min(linear_area, alligator_area) / max(linear_area, alligator_area)
                    
                    # 智能判斷邏輯
                    if aspect_ratio < LINEAR_ASPECT_RATIO_THRESHOLD and area_ratio < AREA_RATIO_THRESHOLD:
                        # 保留linear_crack，移除對應的alligator_crack
                        alligator_cracks.remove(alligator)
                    else:
                        # 移除linear_crack
                        should_keep = False
                        break
            
            if should_keep:
                filtered.append(linear)
        
        # 添加剩餘的alligator_crack
        filtered.extend(alligator_cracks)
        
        # Step 2: linear_crack vs joint IoU過濾
        linear_remaining = [d for d in filtered if d['class_name'] == 'linear_crack']
        joints = [d for d in detections if d['class_name'] == 'joint']
        
        final_linear = []
        for linear in linear_remaining:
            should_keep = True
            for joint in joints:
                iou = self.calculate_iou(linear['bbox'], joint['bbox'])
                if iou > STEP2_IOU_THRESHOLD:
                    should_keep = False
                    break
            if should_keep:
                final_linear.append(linear)
        
        # 更新filtered中的linear_crack
        filtered = [d for d in filtered if d['class_name'] != 'linear_crack']
        filtered.extend(final_linear)
        
        # 添加其他類別
        other_classes = [d for d in detections if d['class_name'] not in ['linear_crack', 'Alligator_crack']]
        filtered.extend(other_classes)
        
        return filtered
    
    def calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """計算IoU"""
        try:
            # 計算交集
            x1 = max(box1[0], box2[0])
            y1 = max(box1[1], box2[1])
            x2 = min(box1[2], box2[2])
            y2 = min(box1[3], box2[3])
            
            if x2 <= x1 or y2 <= y1:
                return 0.0
            
            intersection = (x2 - x1) * (y2 - y1)
            
            # 計算聯集
            area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
            area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
            union = area1 + area2 - intersection
            
            return intersection / union if union > 0 else 0.0
        except:
            return 0.0

class VisualizationManager:
    """視覺化管理器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def create_visualization(self, image_path: str, detections: List[Dict], output_dir: Path) -> Optional[str]:
        """創建視覺化結果"""
        if not ENABLE_VISUALIZATION or not detections:
            return None
        
        try:
            import cv2
            
            # 載入圖像
            image = cv2.imread(image_path)
            if image is None:
                return None
            
            # 繪製檢測結果
            for detection in detections:
                bbox = detection['bbox']
                x1, y1, x2, y2 = map(int, bbox)
                color = tuple(reversed(detection['color']))  # BGR格式
                
                # 繪製矩形框
                cv2.rectangle(image, (x1, y1), (x2, y2), color, LINE_THICKNESS)
                
                # 添加標籤
                label = f"{detection['display_name']}: {detection['confidence']:.2f}"
                
                # 計算文字尺寸
                (text_width, text_height), _ = cv2.getTextSize(
                    label, cv2.FONT_HERSHEY_SIMPLEX, FONT_SIZE * 0.5, FONT_THICKNESS
                )
                
                # 繪製文字背景
                cv2.rectangle(image, (x1, y1 - text_height - 10), 
                             (x1 + text_width, y1), color, -1)
                
                # 繪製文字
                cv2.putText(image, label, (x1, y1 - 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, FONT_SIZE * 0.5, 
                           (255, 255, 255), FONT_THICKNESS)
            
            # 保存結果
            output_path = output_dir / f"result_{Path(image_path).name}"
            cv2.imwrite(str(output_path), image, [cv2.IMWRITE_JPEG_QUALITY, OUTPUT_QUALITY])
            
            self.logger.debug(f"🎨 視覺化已保存: {output_path.name}")
            return str(output_path)
            
        except Exception as e:
            self.logger.warning(f"⚠️ 視覺化創建失敗: {e}")
            return None

class StatisticsManager:
    """統計管理器"""
    
    def __init__(self, output_dir: Path, logger: logging.Logger):
        self.output_dir = output_dir
        self.logger = logger
        self.results = []
    
    def add_result(self, result: Dict[str, Any]):
        """添加結果"""
        self.results.append(result)
    
    def save_statistics(self):
        """保存統計結果"""
        try:
            # 保存JSON結果
            if SAVE_JSON:
                json_path = self.output_dir / "detection_results.json"
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(self.results, f, indent=2, ensure_ascii=False)
                self.logger.info(f"📊 JSON結果已保存: {json_path.name}")
            
            # 保存CSV統計
            if SAVE_CSV:
                csv_path = self.output_dir / "detection_summary.csv"
                with open(csv_path, 'w', encoding='utf-8') as f:
                    f.write("圖像文件,成功狀態,檢測數量,類別統計,錯誤信息\n")
                    
                    for result in self.results:
                        image_name = Path(result['image_path']).name
                        success = "成功" if result['success'] else "失敗"
                        detection_count = result.get('detection_count', 0)
                        error = result.get('error', '')
                        
                        # 計算類別統計
                        if result['success'] and 'detections' in result:
                            class_counts = {}
                            for det in result['detections']:
                                class_name = det['display_name']
                                class_counts[class_name] = class_counts.get(class_name, 0) + 1
                            class_stats = '; '.join([f"{k}:{v}" for k, v in class_counts.items()])
                        else:
                            class_stats = ""
                        
                        f.write(f"{image_name},{success},{detection_count},{class_stats},{error}\n")
                
                self.logger.info(f"📊 CSV統計已保存: {csv_path.name}")
            
        except Exception as e:
            self.logger.error(f"❌ 統計保存失敗: {e}")

class FinalWorkingSystem:
    """最終工作系統"""
    
    def __init__(self):
        self.logger = setup_logging()
        self.deps = None
        self.run_mode = None
        self.inference_engine = None
        self.viz_manager = None
        self.stats_manager = None
    
    def initialize(self) -> bool:
        """初始化系統"""
        self.logger.info("🚀 初始化最終工作系統...")
        
        # 檢查依賴
        print("🔍 檢查系統依賴...")
        self.deps = check_dependencies()
        
        # 決定運行模式
        self.run_mode = determine_run_mode(self.deps)
        print(f"🎯 選定運行模式: {self.run_mode}")
        
        # 檢查基本要求
        if not self.deps.get('ultralytics', False):
            self.logger.error("❌ Ultralytics不可用，無法運行")
            return False
        
        if not Path(MODEL_PATH).exists():
            self.logger.error(f"❌ 模型文件不存在: {MODEL_PATH}")
            return False
        
        if not Path(INPUT_PATH).exists():
            self.logger.error(f"❌ 輸入路徑不存在: {INPUT_PATH}")
            return False
        
        # 創建輸出目錄
        output_path = Path(OUTPUT_PATH)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化組件
        try:
            if self.run_mode in ["INTELLIGENT", "ADVANCED"]:
                # 嘗試使用高級系統
                try:
                    from inference_system import create_inference_system, UnifiedConfig, ClassConfig
                    self.logger.info("🧠 使用高級推理系統")
                    # 這裡可以添加高級系統的初始化
                except ImportError:
                    self.logger.warning("⚠️ 高級系統不可用，降級到基礎模式")
                    self.run_mode = "BASIC"
            
            if self.run_mode == "BASIC":
                # 使用基礎YOLO推理
                self.inference_engine = BasicYOLOInference(MODEL_PATH, self.logger)
                if not self.inference_engine.load_model():
                    return False
            
            # 初始化視覺化和統計管理器
            self.viz_manager = VisualizationManager(self.logger)
            self.stats_manager = StatisticsManager(output_path, self.logger)
            
            self.logger.info("✅ 系統初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 系統初始化失敗: {e}")
            return False
    
    def process_images(self) -> bool:
        """處理圖像"""
        try:
            input_path = Path(INPUT_PATH)
            output_path = Path(OUTPUT_PATH)
            
            # 獲取圖像文件
            if input_path.is_file():
                image_files = [input_path]
            else:
                extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
                image_files = [f for f in input_path.iterdir() 
                              if f.suffix.lower() in extensions]
            
            if not image_files:
                self.logger.error("❌ 未找到有效的圖像文件")
                return False
            
            self.logger.info(f"📸 找到 {len(image_files)} 個圖像文件")
            
            # 處理統計
            total_images = len(image_files)
            successful = 0
            total_detections = 0
            start_time = time.time()
            
            # 逐一處理圖像
            for i, image_file in enumerate(image_files, 1):
                self.logger.info(f"📸 處理 ({i}/{total_images}): {image_file.name}")
                
                # 推理
                result = self.inference_engine.inference_single(str(image_file))
                
                if result['success']:
                    successful += 1
                    detections = result['detections']
                    total_detections += len(detections)
                    
                    # 創建視覺化
                    if SAVE_IMAGES and detections:
                        viz_path = self.viz_manager.create_visualization(
                            str(image_file), detections, output_path
                        )
                        result['visualization_path'] = viz_path
                    
                    self.logger.info(f"   ✅ 成功，檢測到 {len(detections)} 個目標")
                else:
                    self.logger.error(f"   ❌ 失敗: {result.get('error', '未知錯誤')}")
                
                # 添加到統計
                self.stats_manager.add_result(result)
            
            # 保存統計
            self.stats_manager.save_statistics()
            
            # 顯示最終統計
            elapsed_time = time.time() - start_time
            success_rate = successful / total_images * 100
            avg_detections = total_detections / successful if successful > 0 else 0
            
            print(f"\\n🎉 處理完成!")
            print(f"📊 最終統計:")
            print(f"   處理圖像: {total_images}")
            print(f"   成功處理: {successful}")
            print(f"   成功率: {success_rate:.1f}%")
            print(f"   總檢測數: {total_detections}")
            print(f"   平均每圖: {avg_detections:.1f}")
            print(f"   處理時間: {elapsed_time:.2f}秒")
            print(f"   輸出目錄: {output_path}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 圖像處理失敗: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函數"""
    print("🏆 最終工作版本 - 統一YOLO推理系統")
    print("=" * 60)
    
    # 顯示當前配置
    print("⚙️ 當前配置:")
    print(f"   模型路徑: {MODEL_PATH}")
    print(f"   輸入路徑: {INPUT_PATH}")
    print(f"   輸出路徑: {OUTPUT_PATH}")
    print(f"   運行模式: {RUN_MODE}")
    print(f"   SAHI切片: {'啟用' if ENABLE_SAHI else '停用'}")
    print(f"   智能過濾: {'啟用' if ENABLE_INTELLIGENT_FILTERING else '停用'}")
    print(f"   視覺化: {'啟用' if ENABLE_VISUALIZATION else '停用'}")
    print()
    
    # 創建系統
    system = FinalWorkingSystem()
    
    # 初始化
    if not system.initialize():
        print("❌ 系統初始化失敗")
        return
    
    # 處理圖像
    if system.process_images():
        print("\\n🎉 任務完成！")
    else:
        print("\\n❌ 任務失敗")

if __name__ == "__main__":
    main()