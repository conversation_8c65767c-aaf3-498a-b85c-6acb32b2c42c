# YOLO推理系統 - 完整依賴包清單
# ============================================

# 🤖 核心AI框架 (必需)
torch>=2.0.0,<2.5.0
torchvision>=0.15.0,<0.20.0
ultralytics>=8.0.0
sahi>=0.11.0

# 🖼️ 圖像處理 (必需)
opencv-python>=4.8.0
Pillow>=9.5.0
albumentations>=1.3.0

# 🔢 科學計算 (必需)
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.11.0
matplotlib>=3.7.0
seaborn>=0.12.0

# 🧠 機器學習工具 (必需)
scikit-learn>=1.3.0

# 📊 性能監控 (必需)
psutil
tqdm>=4.65.0

# ⚙️ 配置管理 (必需)
PyYAML>=6.0

# 🧪 測試框架 (開發用)
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-asyncio>=0.21.0

# 📦 模型融合工具 (可選 - 高級融合功能需要)
ensemble-boxes>=1.0.9

# 🎨 可視化增強 (可選)
plotly>=5.15.0

# 🖥️ GUI工具 (可選 - 圖形界面需要)
# PyQt6>=6.5.0

# 🚀 深度學習加速 (GPU用戶推薦)
# nvidia-ml-py3>=7.352.0    # NVIDIA GPU監控

# 🔧 開發工具 (開發用)
black>=23.7.0              # 代碼格式化
flake8>=6.0.0              # 代碼檢查
mypy>=1.5.0                # 類型檢查

# 📝 文檔生成 (可選)
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# ============================================
# 📋 安裝說明:
# 1. 基礎安裝: pip install -r requirements.txt
# 2. 完整安裝: pip install -r requirements.txt[gpu]
# 3. 開發安裝: pip install -r requirements.txt[dev]
# ============================================

# 🔧 系統需求:
# - Python >= 3.8 (推薦 3.9+)
# - CUDA >= 11.7 (GPU用戶)
# - RAM >= 8GB (推薦 16GB+)
# - VRAM >= 4GB (GPU用戶, 推薦 8GB+)