# 🌐 分散式模型推理系統
# Phase 4 核心功能 - 跨地區、跨雲的分散式AI推理

import asyncio
import logging
import threading
import time
import hashlib
import pickle
import json
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
from enum import Enum
import concurrent.futures
import queue
import uuid

class NodeType(Enum):
    """節點類型枚舉"""
    COORDINATOR = "coordinator"       # 協調器節點
    WORKER = "worker"                # 工作節點
    GATEWAY = "gateway"              # 網關節點
    CACHE = "cache"                  # 緩存節點
    STORAGE = "storage"              # 存儲節點

class TaskStatus(Enum):
    """任務狀態枚舉"""
    PENDING = "pending"              # 等待中
    ASSIGNED = "assigned"            # 已分配
    RUNNING = "running"              # 運行中
    COMPLETED = "completed"          # 已完成
    FAILED = "failed"                # 失敗
    CANCELLED = "cancelled"          # 已取消
    TIMEOUT = "timeout"              # 超時

class TaskPriority(Enum):
    """任務優先級枚舉"""
    LOW = 1
    NORMAL = 5
    HIGH = 8
    CRITICAL = 10

@dataclass
class InferenceTask:
    """推理任務"""
    task_id: str
    input_data: Any                  # 輸入數據
    model_id: str                    # 模型ID
    task_type: str = "inference"     # 任務類型
    priority: TaskPriority = TaskPriority.NORMAL
    
    # 任務配置
    timeout_seconds: int = 300       # 超時時間
    retry_count: int = 3             # 重試次數
    requires_gpu: bool = True        # 是否需要GPU
    
    # 狀態信息
    status: TaskStatus = TaskStatus.PENDING
    assigned_node: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 結果數據
    result: Optional[Any] = None
    error_message: Optional[str] = None
    processing_time: float = 0.0
    
    # 分散式信息
    parent_task_id: Optional[str] = None  # 父任務ID（用於任務分解）
    subtasks: List[str] = field(default_factory=list)  # 子任務列表
    dependencies: List[str] = field(default_factory=list)  # 依賴任務

@dataclass 
class WorkerNode:
    """工作節點"""
    node_id: str
    node_type: NodeType
    hostname: str
    ip_address: str
    port: int
    
    # 能力信息
    max_concurrent_tasks: int = 4
    supported_models: List[str] = field(default_factory=list)
    has_gpu: bool = False
    gpu_memory_gb: float = 0.0
    cpu_cores: int = 4
    memory_gb: float = 8.0
    
    # 狀態信息
    is_online: bool = True
    current_load: int = 0           # 當前任務數
    last_heartbeat: datetime = field(default_factory=datetime.now)
    
    # 性能統計
    total_tasks_completed: int = 0
    total_processing_time: float = 0.0
    average_task_time: float = 0.0
    error_rate: float = 0.0
    
    # 地理信息
    region: str = "default"
    availability_zone: str = "default"
    
    # 網絡延遲（到其他節點）
    network_latency: Dict[str, float] = field(default_factory=dict)

@dataclass
class DistributedCluster:
    """分散式集群"""
    cluster_id: str
    cluster_name: str
    coordinator_node: str           # 協調器節點ID
    
    # 節點管理
    nodes: Dict[str, WorkerNode] = field(default_factory=dict)
    node_groups: Dict[str, List[str]] = field(default_factory=dict)
    
    # 負載均衡配置
    load_balancing_strategy: str = "least_loaded"
    enable_auto_scaling: bool = True
    min_nodes: int = 1
    max_nodes: int = 10
    
    # 容錯配置
    task_replication: int = 1       # 任務複製數
    failure_tolerance: int = 1      # 容錯節點數
    enable_checkpointing: bool = True
    
    # 性能配置
    batch_processing: bool = True
    max_batch_size: int = 8
    enable_caching: bool = True
    cache_ttl_hours: int = 24


class DistributedInferenceEngine:
    """分散式推理引擎"""
    
    def __init__(self, cluster_config: DistributedCluster):
        self.logger = logging.getLogger(__name__)
        self.cluster = cluster_config
        self.node_id = self._generate_node_id()
        
        # 任務管理
        self.task_queue = queue.PriorityQueue()
        self.active_tasks: Dict[str, InferenceTask] = {}
        self.completed_tasks: Dict[str, InferenceTask] = {}
        
        # 工作線程池
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=8)
        self.task_processor_thread = None
        
        # 節點通信
        self.message_handlers: Dict[str, Callable] = {}
        self.heartbeat_interval = 30  # 秒
        
        # 緩存系統
        self.result_cache: Dict[str, Any] = {}
        self.cache_timestamps: Dict[str, datetime] = {}
        
        # 統計信息
        self.cluster_stats = {
            'total_tasks_submitted': 0,
            'total_tasks_completed': 0,
            'total_tasks_failed': 0,
            'average_task_time': 0.0,
            'peak_concurrent_tasks': 0,
            'cache_hit_rate': 0.0
        }
        
        # 運行狀態
        self.running = True
        self._start_background_services()

    def submit_task(self, task: InferenceTask) -> str:
        """提交推理任務"""
        try:
            # 檢查緩存
            cache_key = self._generate_cache_key(task)
            if self.cluster.enable_caching and cache_key in self.result_cache:
                cached_result = self.result_cache[cache_key]
                cache_time = self.cache_timestamps[cache_key]
                
                # 檢查緩存是否過期
                if datetime.now() - cache_time < timedelta(hours=self.cluster.cache_ttl_hours):
                    self.logger.info(f"任務 {task.task_id} 命中緩存")
                    task.result = cached_result
                    task.status = TaskStatus.COMPLETED
                    task.completed_at = datetime.now()
                    self.completed_tasks[task.task_id] = task
                    self._update_cache_stats()
                    return task.task_id
            
            # 任務分解（如果需要）
            if self._should_decompose_task(task):
                subtasks = self._decompose_task(task)
                for subtask in subtasks:
                    self.task_queue.put((subtask.priority.value, subtask.created_at, subtask))
                self.logger.info(f"任務 {task.task_id} 分解為 {len(subtasks)} 個子任務")
            else:
                # 直接提交任務
                self.task_queue.put((task.priority.value, task.created_at, task))
            
            self.active_tasks[task.task_id] = task
            self.cluster_stats['total_tasks_submitted'] += 1
            
            self.logger.info(f"提交任務: {task.task_id}")
            return task.task_id
            
        except Exception as e:
            self.logger.error(f"提交任務失敗: {e}")
            raise

    def get_task_status(self, task_id: str) -> Optional[InferenceTask]:
        """獲取任務狀態"""
        if task_id in self.active_tasks:
            return self.active_tasks[task_id]
        elif task_id in self.completed_tasks:
            return self.completed_tasks[task_id]
        else:
            return None

    def cancel_task(self, task_id: str) -> bool:
        """取消任務"""
        task = self.get_task_status(task_id)
        if not task:
            return False
        
        if task.status in [TaskStatus.PENDING, TaskStatus.ASSIGNED]:
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now()
            
            # 移動到完成列表
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
            self.completed_tasks[task_id] = task
            
            self.logger.info(f"取消任務: {task_id}")
            return True
        
        return False

    def add_worker_node(self, node: WorkerNode) -> bool:
        """添加工作節點"""
        try:
            self.cluster.nodes[node.node_id] = node
            
            # 添加到默認組
            if "default" not in self.cluster.node_groups:
                self.cluster.node_groups["default"] = []
            self.cluster.node_groups["default"].append(node.node_id)
            
            self.logger.info(f"添加工作節點: {node.node_id} ({node.hostname})")
            return True
            
        except Exception as e:
            self.logger.error(f"添加工作節點失敗: {e}")
            return False

    def remove_worker_node(self, node_id: str) -> bool:
        """移除工作節點"""
        try:
            if node_id not in self.cluster.nodes:
                return False
            
            # 從所有組中移除
            for group in self.cluster.node_groups.values():
                if node_id in group:
                    group.remove(node_id)
            
            # 重新分配該節點的任務
            self._reassign_node_tasks(node_id)
            
            del self.cluster.nodes[node_id]
            self.logger.info(f"移除工作節點: {node_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"移除工作節點失敗: {e}")
            return False

    def _select_best_node(self, task: InferenceTask) -> Optional[WorkerNode]:
        """選擇最佳工作節點"""
        available_nodes = []
        
        for node in self.cluster.nodes.values():
            if (node.is_online and 
                node.current_load < node.max_concurrent_tasks and
                (not task.requires_gpu or node.has_gpu)):
                
                # 檢查模型支持
                if task.model_id in node.supported_models or not node.supported_models:
                    available_nodes.append(node)
        
        if not available_nodes:
            return None
        
        # 根據負載均衡策略選擇
        if self.cluster.load_balancing_strategy == "least_loaded":
            return min(available_nodes, key=lambda n: n.current_load)
        elif self.cluster.load_balancing_strategy == "fastest":
            return min(available_nodes, key=lambda n: n.average_task_time)
        elif self.cluster.load_balancing_strategy == "round_robin":
            # 簡化的輪詢實現
            return available_nodes[self.cluster_stats['total_tasks_submitted'] % len(available_nodes)]
        else:
            return available_nodes[0]

    def _process_task(self, task: InferenceTask) -> bool:
        """處理單個任務"""
        try:
            # 選擇工作節點
            selected_node = self._select_best_node(task)
            if not selected_node:
                self.logger.warning(f"沒有可用節點處理任務: {task.task_id}")
                return False
            
            # 分配任務
            task.assigned_node = selected_node.node_id
            task.status = TaskStatus.ASSIGNED
            task.started_at = datetime.now()
            
            # 更新節點負載
            selected_node.current_load += 1
            
            self.logger.info(f"任務 {task.task_id} 分配到節點 {selected_node.node_id}")
            
            # 執行任務（模擬）
            result = self._execute_task_on_node(task, selected_node)
            
            if result['success']:
                task.status = TaskStatus.COMPLETED
                task.result = result['data']
                task.completed_at = datetime.now()
                task.processing_time = (task.completed_at - task.started_at).total_seconds()
                
                # 更新緩存
                if self.cluster.enable_caching:
                    cache_key = self._generate_cache_key(task)
                    self.result_cache[cache_key] = task.result
                    self.cache_timestamps[cache_key] = datetime.now()
                
                # 更新統計
                self.cluster_stats['total_tasks_completed'] += 1
                self._update_node_stats(selected_node, task.processing_time, True)
                
                self.logger.info(f"任務 {task.task_id} 完成，用時 {task.processing_time:.2f}s")
                
            else:
                task.status = TaskStatus.FAILED
                task.error_message = result.get('error', 'Unknown error')
                task.completed_at = datetime.now()
                
                self.cluster_stats['total_tasks_failed'] += 1
                self._update_node_stats(selected_node, 0, False)
                
                self.logger.error(f"任務 {task.task_id} 失敗: {task.error_message}")
            
            # 更新節點負載
            selected_node.current_load = max(0, selected_node.current_load - 1)
            
            # 移動到完成列表
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            self.completed_tasks[task.task_id] = task
            
            return True
            
        except Exception as e:
            self.logger.error(f"處理任務失敗: {e}")
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
            return False

    def _execute_task_on_node(self, task: InferenceTask, node: WorkerNode) -> Dict[str, Any]:
        """在指定節點執行任務"""
        try:
            # 模擬任務執行
            task.status = TaskStatus.RUNNING
            
            # 模擬處理時間（基於節點性能）
            base_time = 1.0  # 基礎處理時間
            gpu_speedup = 0.3 if node.has_gpu and task.requires_gpu else 1.0
            cpu_factor = max(0.5, 4.0 / node.cpu_cores)  # CPU核心數影響
            
            processing_time = base_time * gpu_speedup * cpu_factor
            time.sleep(processing_time)  # 模擬處理延遲
            
            # 模擬結果
            result = {
                'success': True,
                'data': {
                    'task_id': task.task_id,
                    'model_id': task.model_id,
                    'predictions': [
                        {'class': 'road_crack', 'confidence': 0.89, 'bbox': [100, 100, 200, 200]},
                        {'class': 'pothole', 'confidence': 0.76, 'bbox': [300, 150, 400, 250]}
                    ],
                    'processing_node': node.node_id,
                    'processing_time': processing_time
                }
            }
            
            # 模擬失敗情況（5%機率）
            import random
            if random.random() < 0.05:
                result = {
                    'success': False,
                    'error': f'模擬處理失敗 on node {node.node_id}'
                }
            
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _should_decompose_task(self, task: InferenceTask) -> bool:
        """判斷是否需要分解任務"""
        # 簡化邏輯：批量任務可以分解
        if isinstance(task.input_data, list) and len(task.input_data) > self.cluster.max_batch_size:
            return True
        return False

    def _decompose_task(self, task: InferenceTask) -> List[InferenceTask]:
        """分解任務為子任務"""
        subtasks = []
        
        if isinstance(task.input_data, list):
            batch_size = self.cluster.max_batch_size
            for i in range(0, len(task.input_data), batch_size):
                batch_data = task.input_data[i:i + batch_size]
                
                subtask = InferenceTask(
                    task_id=f"{task.task_id}_batch_{i//batch_size}",
                    input_data=batch_data,
                    model_id=task.model_id,
                    task_type=task.task_type,
                    priority=task.priority,
                    timeout_seconds=task.timeout_seconds,
                    parent_task_id=task.task_id
                )
                
                subtasks.append(subtask)
                task.subtasks.append(subtask.task_id)
        
        return subtasks

    def _generate_cache_key(self, task: InferenceTask) -> str:
        """生成緩存鍵"""
        # 基於輸入數據和模型ID生成hash
        data_str = json.dumps(task.input_data, sort_keys=True, default=str)
        key_data = f"{task.model_id}:{data_str}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def _update_node_stats(self, node: WorkerNode, processing_time: float, success: bool):
        """更新節點統計"""
        if success:
            node.total_tasks_completed += 1
            node.total_processing_time += processing_time
            node.average_task_time = node.total_processing_time / node.total_tasks_completed
        else:
            # 更新錯誤率
            total_attempts = node.total_tasks_completed + 1
            node.error_rate = (1 - node.total_tasks_completed / total_attempts) if total_attempts > 0 else 0

    def _update_cache_stats(self):
        """更新緩存統計"""
        cache_hits = sum(1 for task in self.completed_tasks.values() 
                        if task.result and task.processing_time == 0)
        total_tasks = len(self.completed_tasks)
        self.cluster_stats['cache_hit_rate'] = cache_hits / max(total_tasks, 1)

    def _reassign_node_tasks(self, node_id: str):
        """重新分配節點任務"""
        tasks_to_reassign = []
        
        for task in self.active_tasks.values():
            if task.assigned_node == node_id and task.status != TaskStatus.COMPLETED:
                task.assigned_node = None
                task.status = TaskStatus.PENDING
                tasks_to_reassign.append(task)
        
        # 重新提交任務
        for task in tasks_to_reassign:
            self.task_queue.put((task.priority.value, task.created_at, task))
        
        self.logger.info(f"重新分配 {len(tasks_to_reassign)} 個任務")

    def _start_background_services(self):
        """啟動後台服務"""
        # 啟動任務處理線程
        self.task_processor_thread = threading.Thread(
            target=self._task_processor_worker, daemon=True
        )
        self.task_processor_thread.start()
        
        # 啟動心跳檢查線程
        heartbeat_thread = threading.Thread(
            target=self._heartbeat_worker, daemon=True
        )
        heartbeat_thread.start()

    def _task_processor_worker(self):
        """任務處理工作線程"""
        while self.running:
            try:
                # 從隊列獲取任務
                priority, created_at, task = self.task_queue.get(timeout=1.0)
                
                # 提交到線程池處理
                future = self.thread_pool.submit(self._process_task, task)
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"任務處理工作線程錯誤: {e}")

    def _heartbeat_worker(self):
        """心跳檢查工作線程"""
        while self.running:
            try:
                current_time = datetime.now()
                offline_nodes = []
                
                for node_id, node in self.cluster.nodes.items():
                    # 檢查心跳超時
                    if (current_time - node.last_heartbeat).total_seconds() > self.heartbeat_interval * 2:
                        if node.is_online:
                            node.is_online = False
                            offline_nodes.append(node_id)
                            self.logger.warning(f"節點 {node_id} 離線")
                
                # 重新分配離線節點的任務
                for node_id in offline_nodes:
                    self._reassign_node_tasks(node_id)
                
                time.sleep(self.heartbeat_interval)
                
            except Exception as e:
                self.logger.error(f"心跳檢查錯誤: {e}")

    def update_node_heartbeat(self, node_id: str, metrics: Optional[Dict[str, Any]] = None):
        """更新節點心跳"""
        node = self.cluster.nodes.get(node_id)
        if node:
            node.last_heartbeat = datetime.now()
            if not node.is_online:
                node.is_online = True
                self.logger.info(f"節點 {node_id} 恢復在線")
            
            # 更新節點指標
            if metrics:
                node.current_load = metrics.get('current_load', node.current_load)

    def get_cluster_status(self) -> Dict[str, Any]:
        """獲取集群狀態"""
        total_nodes = len(self.cluster.nodes)
        online_nodes = sum(1 for node in self.cluster.nodes.values() if node.is_online)
        total_capacity = sum(node.max_concurrent_tasks for node in self.cluster.nodes.values())
        current_load = sum(node.current_load for node in self.cluster.nodes.values())
        
        return {
            'cluster_id': self.cluster.cluster_id,
            'cluster_name': self.cluster.cluster_name,
            'total_nodes': total_nodes,
            'online_nodes': online_nodes,
            'offline_nodes': total_nodes - online_nodes,
            'total_capacity': total_capacity,
            'current_load': current_load,
            'utilization_rate': (current_load / max(total_capacity, 1)) * 100,
            'active_tasks': len(self.active_tasks),
            'completed_tasks': len(self.completed_tasks),
            'queue_size': self.task_queue.qsize(),
            'cluster_stats': self.cluster_stats.copy()
        }

    def _generate_node_id(self) -> str:
        """生成節點ID"""
        return f"node_{uuid.uuid4().hex[:8]}"

    def stop(self):
        """停止分散式引擎"""
        self.running = False
        self.thread_pool.shutdown(wait=True)
        
        if self.task_processor_thread:
            self.task_processor_thread.join(timeout=5)
        
        self.logger.info("分散式推理引擎已停止")


# 使用示例和測試
if __name__ == "__main__":
    # 配置日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建集群配置
    cluster_config = DistributedCluster(
        cluster_id="road_ai_cluster",
        cluster_name="道路AI檢測集群",
        coordinator_node="coordinator_001",
        load_balancing_strategy="least_loaded",
        enable_auto_scaling=True,
        max_batch_size=4,
        enable_caching=True
    )
    
    # 創建分散式推理引擎
    engine = DistributedInferenceEngine(cluster_config)
    
    # 添加工作節點
    workers = [
        WorkerNode(
            node_id="worker_001",
            node_type=NodeType.WORKER,
            hostname="gpu-worker-1",
            ip_address="************",
            port=8000,
            max_concurrent_tasks=4,
            has_gpu=True,
            gpu_memory_gb=8.0,
            cpu_cores=8,
            memory_gb=32.0,
            supported_models=["yolo11_seg", "yolo12_det"],
            region="asia-east"
        ),
        WorkerNode(
            node_id="worker_002",
            node_type=NodeType.WORKER,
            hostname="cpu-worker-1",
            ip_address="************",
            port=8000,
            max_concurrent_tasks=2,
            has_gpu=False,
            cpu_cores=16,
            memory_gb=64.0,
            supported_models=["yolo_lite"],
            region="asia-east"
        ),
        WorkerNode(
            node_id="worker_003",
            node_type=NodeType.WORKER,
            hostname="edge-worker-1",
            ip_address="************",
            port=8000,
            max_concurrent_tasks=1,
            has_gpu=False,
            cpu_cores=4,
            memory_gb=8.0,
            supported_models=["yolo_edge"],
            region="us-west"
        )
    ]
    
    for worker in workers:
        engine.add_worker_node(worker)
    
    print(f"✅ 創建分散式集群，包含 {len(workers)} 個工作節點")
    
    # 提交測試任務
    test_tasks = []
    for i in range(10):
        task = InferenceTask(
            task_id=f"task_{i:03d}",
            input_data={'image_path': f'/data/test_image_{i}.jpg'},
            model_id="yolo11_seg",
            priority=TaskPriority.NORMAL if i % 2 == 0 else TaskPriority.HIGH,
            requires_gpu=True
        )
        
        task_id = engine.submit_task(task)
        test_tasks.append(task_id)
    
    print(f"✅ 提交 {len(test_tasks)} 個測試任務")
    
    # 等待任務完成
    time.sleep(5)
    
    # 模擬節點心跳
    for worker in workers:
        engine.update_node_heartbeat(worker.node_id, {
            'current_load': worker.current_load
        })
    
    # 檢查任務狀態
    completed_count = 0
    failed_count = 0
    
    for task_id in test_tasks:
        task = engine.get_task_status(task_id)
        if task:
            if task.status == TaskStatus.COMPLETED:
                completed_count += 1
            elif task.status == TaskStatus.FAILED:
                failed_count += 1
    
    print(f"📊 任務狀態: {completed_count} 完成, {failed_count} 失敗")
    
    # 獲取集群狀態
    cluster_status = engine.get_cluster_status()
    print(f"\n🌐 集群狀態:")
    print(f"   在線節點: {cluster_status['online_nodes']}/{cluster_status['total_nodes']}")
    print(f"   集群利用率: {cluster_status['utilization_rate']:.1f}%")
    print(f"   活躍任務: {cluster_status['active_tasks']}")
    print(f"   完成任務: {cluster_status['completed_tasks']}")
    print(f"   緩存命中率: {cluster_status['cluster_stats']['cache_hit_rate']:.1%}")
    
    # 停止引擎
    engine.stop()