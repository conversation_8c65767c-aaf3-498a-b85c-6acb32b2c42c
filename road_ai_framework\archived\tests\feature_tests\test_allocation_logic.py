#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試分配邏輯：驗證用戶要求的場景
"""

def test_user_scenario():
    """
    測試用戶具體場景：
    multi_class中有30個person，單類別有20個person
    設定multi_class50%和30個的person
    期望：從multi_class取15，從單類別取15
    """
    
    print("🧪 測試用戶具體場景")
    print("="*60)
    print("📊 場景設定:")
    print("  - multi_class中有30個person")
    print("  - 單類別中有20個person") 
    print("  - 設定multi_class使用率50%")
    print("  - 總需求30個person")
    print("  - 期望結果: multi_class 15 + 單類別 15 = 30")
    print()
    
    # 模擬資料
    category_info = {
        'name': 'person',
        'multi_class_count': 30,
        'single_class_count': 20,
        'total_count': 50
    }
    
    # 用戶設定
    total_demand = 30
    multi_class_percent = 50.0
    
    print("🔧 計算過程:")
    
    # 新的分配邏輯（和代碼中一致）
    if category_info['multi_class_count'] > 0:
        multi_class_usable = int(category_info['multi_class_count'] * multi_class_percent / 100)
        from_multi_class = min(total_demand, multi_class_usable)
    else:
        from_multi_class = 0
    
    from_single_class = min(max(0, total_demand - from_multi_class), category_info['single_class_count'])
    
    print(f"  1. multi_class可用數量 = {category_info['multi_class_count']} × {multi_class_percent}% = {multi_class_usable}")
    print(f"  2. 從multi_class提取 = min({total_demand}, {multi_class_usable}) = {from_multi_class}")
    print(f"  3. 剩餘需求 = {total_demand} - {from_multi_class} = {total_demand - from_multi_class}")
    print(f"  4. 從單類別提取 = min({total_demand - from_multi_class}, {category_info['single_class_count']}) = {from_single_class}")
    print()
    
    print("📋 最終結果:")
    print(f"  - 從multi_class提取: {from_multi_class} 個")
    print(f"  - 從單類別提取: {from_single_class} 個")
    print(f"  - 總計: {from_multi_class + from_single_class} 個")
    print()
    
    # 驗證結果
    expected_multi = 15
    expected_single = 15
    expected_total = 30
    
    success = (from_multi_class == expected_multi and 
              from_single_class == expected_single and 
              from_multi_class + from_single_class == expected_total)
    
    if success:
        print("✅ 測試通過！分配邏輯正確。")
    else:
        print("❌ 測試失敗！")
        print(f"  期望: multi_class {expected_multi} + 單類別 {expected_single} = {expected_total}")
        print(f"  實際: multi_class {from_multi_class} + 單類別 {from_single_class} = {from_multi_class + from_single_class}")
    
    return success

def test_edge_cases():
    """測試邊界情況"""
    
    print("\n🔍 測試邊界情況")
    print("="*60)
    
    test_cases = [
        {
            'name': '需求超過總可用數量',
            'multi_class_count': 10,
            'single_class_count': 15,
            'total_demand': 50,
            'multi_class_percent': 100.0,
            'expected_multi': 10,
            'expected_single': 15
        },
        {
            'name': 'multi_class使用率0%',
            'multi_class_count': 30,
            'single_class_count': 20,
            'total_demand': 25,
            'multi_class_percent': 0.0,
            'expected_multi': 0,
            'expected_single': 20
        },
        {
            'name': '只有multi_class，沒有單類別',
            'multi_class_count': 30,
            'single_class_count': 0,
            'total_demand': 20,
            'multi_class_percent': 80.0,
            'expected_multi': 20,
            'expected_single': 0
        },
        {
            'name': '需求很小',
            'multi_class_count': 30,
            'single_class_count': 20,
            'total_demand': 5,
            'multi_class_percent': 50.0,
            'expected_multi': 5,
            'expected_single': 0
        }
    ]
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 測試案例 {i}: {case['name']}")
        
        # 計算
        if case['multi_class_count'] > 0:
            multi_class_usable = int(case['multi_class_count'] * case['multi_class_percent'] / 100)
            from_multi_class = min(case['total_demand'], multi_class_usable)
        else:
            from_multi_class = 0
        
        from_single_class = min(max(0, case['total_demand'] - from_multi_class), case['single_class_count'])
        
        # 驗證
        passed = (from_multi_class == case['expected_multi'] and 
                 from_single_class == case['expected_single'])
        
        if passed:
            print(f"  ✅ 通過: multi_class {from_multi_class} + 單類別 {from_single_class}")
        else:
            print(f"  ❌ 失敗:")
            print(f"    期望: multi_class {case['expected_multi']} + 單類別 {case['expected_single']}")
            print(f"    實際: multi_class {from_multi_class} + 單類別 {from_single_class}")
            all_passed = False
    
    return all_passed

if __name__ == "__main__":
    print("🚀 開始測試分配邏輯")
    print("="*60)
    
    # 測試主要場景
    main_test_passed = test_user_scenario()
    
    # 測試邊界情況
    edge_tests_passed = test_edge_cases()
    
    print("\n" + "="*60)
    print("📊 測試總結:")
    
    if main_test_passed and edge_tests_passed:
        print("🎉 所有測試通過！分配邏輯工作正常。")
        print("\n📋 確認的功能:")
        print("✅ 支援每類別獨立的multi_class使用率設定")
        print("✅ 智能分配：優先使用multi_class，不足時補充單類別")
        print("✅ 邊界情況處理正確")
        print("✅ 符合用戶需求的具體場景")
    else:
        print("❌ 部分測試失敗，需要檢查邏輯。")
        if not main_test_passed:
            print("  - 主要場景測試失敗")
        if not edge_tests_passed:
            print("  - 邊界情況測試失敗")