#!/usr/bin/env python3
"""
🔍 LabelMe JSON輸出問題診斷腳本
診斷為什麼沒有生成JSON檔案的具體原因
"""

import sys
from pathlib import Path

# 設置路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_parameters():
    """檢查參數設定"""
    print("🔍 檢查參數設定...")
    
    try:
        # 讀取run_unified_yolo.py中的參數
        script_path = current_dir / "run_unified_yolo.py"
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查關鍵參數
        if "enable_labelme_output = True" in content:
            print("✅ enable_labelme_output = True")
        elif "enable_labelme_output = False" in content:
            print("❌ enable_labelme_output = False (需要設為True)")
            return False
        else:
            print("⚠️ 找不到enable_labelme_output參數")
            return False
        
        # 檢查其他參數
        if "labelme_output_dir" in content:
            print("✅ labelme_output_dir參數存在")
        else:
            print("⚠️ 找不到labelme_output_dir參數")
        
        if "labelme_simplify_tolerance" in content:
            print("✅ labelme_simplify_tolerance參數存在")
        else:
            print("⚠️ 找不到labelme_simplify_tolerance參數")
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查參數失敗: {e}")
        return False

def check_model_type():
    """檢查模型類型"""
    print("\n🔍 檢查模型類型...")
    
    try:
        script_path = current_dir / "run_unified_yolo.py"
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找模型路徑
        import re
        model_pattern = r'segmentation_model_path\s*=\s*[r]?["\']([^"\']+)["\']'
        match = re.search(model_pattern, content)
        
        if match:
            model_path = match.group(1)
            print(f"📦 找到模型路徑: {model_path}")
            
            # 檢查模型檔案是否存在
            if Path(model_path).exists():
                print("✅ 模型檔案存在")
                
                # 檢查是否是分割模型
                if "seg" in model_path.lower() or "segment" in model_path.lower():
                    print("✅ 看起來是分割模型")
                else:
                    print("⚠️ 模型檔名中沒有'seg'或'segment'，請確認是否為分割模型")
                    print("💡 LabelMe JSON需要分割模型才能生成mask數據")
                
                return True
            else:
                print(f"❌ 模型檔案不存在: {model_path}")
                return False
        else:
            print("❌ 找不到segmentation_model_path設定")
            return False
            
    except Exception as e:
        print(f"❌ 檢查模型類型失敗: {e}")
        return False

def check_file_structure():
    """檢查檔案結構"""
    print("\n🔍 檢查檔案結構...")
    
    required_files = [
        "models/inference/labelme_json_generator.py",
        "models/inference/labelme_integration.py",
        "models/inference/advanced_inference_wrapper.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        full_path = current_dir / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (檔案不存在)")
            all_exist = False
    
    return all_exist

def check_output_directory():
    """檢查輸出目錄設定"""
    print("\n🔍 檢查輸出目錄...")
    
    try:
        script_path = current_dir / "run_unified_yolo.py"
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找輸出路徑
        import re
        output_pattern = r'output_path\s*=\s*[r]?["\']([^"\']+)["\']'
        match = re.search(output_pattern, content)
        
        if match:
            output_path = match.group(1)
            print(f"📁 輸出路徑: {output_path}")
            
            # 檢查路徑是否存在或可創建
            output_dir = Path(output_path)
            if output_dir.exists():
                print("✅ 輸出目錄存在")
                
                # 檢查權限
                try:
                    test_file = output_dir / "test_write_permission.tmp"
                    test_file.touch()
                    test_file.unlink()
                    print("✅ 輸出目錄可寫入")
                except:
                    print("❌ 輸出目錄無寫入權限")
                    return False
                
            else:
                print(f"⚠️ 輸出目錄不存在: {output_path}")
                print("💡 系統會嘗試自動創建")
            
            # 檢查LabelMe JSON目錄設定
            labelme_dir = output_dir / "labelme_json"
            print(f"📁 LabelMe JSON目錄將創建在: {labelme_dir}")
            
            return True
        else:
            print("❌ 找不到output_path設定")
            return False
            
    except Exception as e:
        print(f"❌ 檢查輸出目錄失敗: {e}")
        return False

def check_integration_points():
    """檢查整合點"""
    print("\n🔍 檢查LabelMe整合點...")
    
    try:
        script_path = current_dir / "run_unified_yolo.py"
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查整合器創建
        if "from models.inference.labelme_integration import create_labelme_integration" in content:
            print("✅ LabelMe整合器導入")
        else:
            print("❌ 缺少LabelMe整合器導入")
            return False
        
        if "labelme_integration = create_labelme_integration(config_manager)" in content:
            print("✅ LabelMe整合器創建")
        else:
            print("❌ 缺少LabelMe整合器創建")
            return False
        
        # 檢查批次處理整合
        if "labelme_integration.process_batch_results(results)" in content:
            print("✅ 批次處理LabelMe整合")
        else:
            print("❌ 缺少批次處理LabelMe整合")
            return False
        
        # 檢查單張處理整合
        if "labelme_integration.process_single_image_result" in content:
            print("✅ 單張處理LabelMe整合")
        else:
            print("❌ 缺少單張處理LabelMe整合")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 檢查整合點失敗: {e}")
        return False

def provide_troubleshooting_guide():
    """提供故障排除建議"""
    print("\n🛠️ 故障排除建議:")
    print("1. 確認使用分割模型(seg)而非檢測模型")
    print("2. 檢查enable_labelme_output是否設為True")
    print("3. 確認輸出目錄有寫入權限")
    print("4. 查看推理過程中的日誌輸出")
    print("5. 確認檢測結果中包含mask數據")
    
    print("\n🔍 調試步驟:")
    print("1. 運行推理時查看是否有'有效mask數量'的輸出")
    print("2. 如果mask數量為0，檢查模型是否為分割模型")
    print("3. 如果有mask但沒有JSON，檢查輸出目錄權限")
    print("4. 查看是否有錯誤或警告信息")
    
    print("\n📋 快速修復清單:")
    print("□ enable_labelme_output = True")
    print("□ 使用分割模型(.pt檔案)")
    print("□ 輸出目錄存在且可寫入")
    print("□ 檢測結果包含有效的mask數據")

def main():
    """主診斷函數"""
    print("🔍 LabelMe JSON輸出問題診斷")
    print("=" * 50)
    
    checks = [
        ("參數設定", check_parameters),
        ("模型類型", check_model_type),
        ("檔案結構", check_file_structure),
        ("輸出目錄", check_output_directory),
        ("整合點", check_integration_points)
    ]
    
    all_passed = True
    for check_name, check_func in checks:
        try:
            if not check_func():
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name}檢查異常: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("🎉 所有檢查通過！")
        print("如果仍然沒有生成JSON檔案，可能的原因:")
        print("1. 模型沒有檢測到任何物件")
        print("2. 檢測到的物件沒有有效的mask")
        print("3. mask數據在處理過程中丟失")
        print("\n建議運行推理並查看詳細日誌輸出")
    else:
        print("❌ 發現問題，請根據上述檢查結果進行修復")
    
    provide_troubleshooting_guide()

if __name__ == "__main__":
    main()