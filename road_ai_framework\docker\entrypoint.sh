#!/bin/bash
# 🐳 Docker入口點腳本

set -e

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 統一YOLO推理系統 v3.0 - Docker容器啟動${NC}"
echo "=" * 60

# 檢查CUDA可用性
if command -v nvidia-smi &> /dev/null; then
    echo -e "${GREEN}✅ NVIDIA GPU檢測成功${NC}"
    nvidia-smi --query-gpu=name,memory.total --format=csv,noheader
else
    echo -e "${YELLOW}⚠️ 未檢測到NVIDIA GPU，將使用CPU模式${NC}"
fi

# 檢查Python環境
echo -e "${BLUE}🐍 檢查Python環境...${NC}"
python3 --version
pip3 list | grep -E "(torch|ultralytics|fastapi)" || echo -e "${YELLOW}⚠️ 部分依賴包可能未正確安裝${NC}"

# 創建必要目錄
echo -e "${BLUE}📁 準備數據目錄...${NC}"
mkdir -p /app/data/{input,output,cache,logs,models}
chmod 755 /app/data/*

# 設置環境變量
export PYTHONPATH="/app:$PYTHONPATH"
export CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-0}

# 根據命令參數選擇運行模式
case "$1" in
    "api")
        echo -e "${GREEN}🌐 啟動API服務模式${NC}"
        echo "服務將在 http://0.0.0.0:8000 上運行"
        echo "API文檔: http://localhost:8000/docs"
        echo "健康檢查: http://localhost:8000/health"
        exec python3 -m inference_system.api.inference_api
        ;;
    
    "batch")
        echo -e "${GREEN}📦 啟動批量處理模式${NC}"
        if [ -z "$INPUT_PATH" ]; then
            echo -e "${RED}❌ 錯誤: 批量模式需要設置 INPUT_PATH 環境變量${NC}"
            exit 1
        fi
        
        export OUTPUT_PATH=${OUTPUT_PATH:-/app/data/output}
        echo "輸入目錄: $INPUT_PATH"
        echo "輸出目錄: $OUTPUT_PATH"
        
        exec python3 run_unified_yolo_v3.py
        ;;
    
    "benchmark")
        echo -e "${GREEN}📊 啟動基準測試模式${NC}"
        export TEST_IMAGE_DIR=${TEST_IMAGE_DIR:-/app/data/input}
        export BENCHMARK_OUTPUT_DIR=${BENCHMARK_OUTPUT_DIR:-/app/data/output/benchmark}
        
        echo "測試圖像目錄: $TEST_IMAGE_DIR"
        echo "測試結果目錄: $BENCHMARK_OUTPUT_DIR"
        
        exec python3 benchmark_system.py
        ;;
    
    "test")
        echo -e "${GREEN}🧪 啟動架構測試模式${NC}"
        exec python3 test_new_architecture.py
        ;;
    
    "shell")
        echo -e "${GREEN}🐚 啟動交互式Shell${NC}"
        exec /bin/bash
        ;;
    
    *)
        echo -e "${YELLOW}📋 可用運行模式:${NC}"
        echo "  api       - 啟動RESTful API服務"
        echo "  batch     - 批量處理模式（需要設置INPUT_PATH）"
        echo "  benchmark - 性能基準測試"
        echo "  test      - 架構功能測試"  
        echo "  shell     - 交互式Shell"
        echo ""
        echo -e "${YELLOW}示例:${NC}"
        echo "  docker run unified-yolo:v3 api"
        echo "  docker run -e INPUT_PATH=/data/images unified-yolo:v3 batch"
        echo "  docker run unified-yolo:v3 benchmark"
        echo ""
        echo -e "${YELLOW}環境變量:${NC}"
        echo "  MODEL_PATH          - 模型文件路徑"
        echo "  INPUT_PATH          - 輸入圖像目錄"
        echo "  OUTPUT_PATH         - 輸出結果目錄"
        echo "  CUDA_VISIBLE_DEVICES - GPU設備ID"
        echo "  MAX_WORKERS         - 並發工作數"
        echo "  ENABLE_CACHING      - 啟用緩存(true/false)"
        echo ""
        exit 1
        ;;
esac