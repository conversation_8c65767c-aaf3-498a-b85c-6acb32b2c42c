#!/usr/bin/env python3
"""
🚀 簡單分類器使用示例
直接修改配置參數，無需命令行
"""

# 導入分類器
from high_accuracy_classifier import ClassifierConfig, ClassificationTrainer
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_classifier_example():
    """運行分類器示例"""
    
    print("🏆 高精度分類器 - 簡單使用示例")
    print("=" * 50)
    
    # ==================== 📝 配置你的參數 ====================
    # 修改這些參數來匹配你的需求
    
    config = ClassifierConfig(
        # 📁 數據路徑 (必須修改)
        data_dir="./your_data",                    # 替換為你的數據目錄
        output_dir="./classifier_results",         # 輸出目錄
        
        # 🤖 模型配置
        model_name="efficientnet_v2_l",           # 最高精度模型
        image_size=480,                           # 圖像尺寸
        pretrained=True,                          # 使用預訓練權重
        
        # ⚡ 訓練配置
        batch_size=16,                            # 批次大小 (根據GPU調整)
        num_epochs=50,                            # 訓練輪數 (示例用較少)
        learning_rate=0.001,                      # 學習率
        
        # 🚀 優化功能
        mixed_precision=True,                     # 混合精度 (節省記憶體)
        use_advanced_augmentation=True,           # 高級數據增強
        early_stopping_patience=10,               # 早停耐心值
        
        # 💻 硬體配置
        device="auto",                            # 自動選擇GPU/CPU
        num_workers=4                             # 數據載入進程數
    )
    
    # ==================== 🔍 檢查配置 ====================
    print("\n📋 當前配置:")
    print(f"  📁 數據目錄: {config.data_dir}")
    print(f"  📂 輸出目錄: {config.output_dir}")
    print(f"  🤖 模型: {config.model_name}")
    print(f"  🖼️ 圖像尺寸: {config.image_size}")
    print(f"  📦 批次大小: {config.batch_size}")
    print(f"  🔄 訓練輪數: {config.num_epochs}")
    print(f"  ⚡ 設備: {config.device}")
    print(f"  🔧 混合精度: {'啟用' if config.mixed_precision else '關閉'}")
    print(f"  🎨 高級增強: {'啟用' if config.use_advanced_augmentation else '關閉'}")
    
    # ==================== 🚀 開始訓練 ====================
    try:
        # 創建訓練器
        trainer = ClassificationTrainer(config)
        
        # 設置數據和模型
        print("\n📊 設置數據載入器...")
        trainer.setup_data()
        
        print("🏗️ 設置模型...")
        trainer.setup_model()
        
        print("🎯 開始訓練...")
        trainer.train()
        
        print("🧪 開始測試...")
        test_results = trainer.test()
        
        print("\n🎉 訓練完成!")
        if test_results:
            print(f"🏆 最終測試準確率: {test_results['test_accuracy']:.2f}%")
            
    except Exception as e:
        print(f"\n❌ 發生錯誤: {e}")
        print("\n💡 可能的解決方案:")
        print("  1. 檢查數據目錄路徑是否正確")
        print("  2. 確保數據按照要求的目錄結構組織")
        print("  3. 檢查GPU記憶體是否足夠 (可以減少batch_size)")
        print("  4. 確保安裝了所有必要的依賴")
        return False
    
    return True

def create_sample_config():
    """創建示例配置文件"""
    print("\n📄 創建示例配置...")
    
    sample_config = {
        "data_dir": "./sample_data",
        "output_dir": "./sample_output",
        "model_name": "efficientnet_v2_l",
        "image_size": 224,  # 較小的尺寸用於快速測試
        "batch_size": 8,    # 較小的批次用於節省記憶體
        "num_epochs": 20,   # 較少的輪數用於快速測試
        "learning_rate": 0.001,
        "mixed_precision": True,
        "use_advanced_augmentation": False,  # 關閉高級增強以加速
        "device": "auto"
    }
    
    import json
    with open("sample_classifier_config.json", "w", encoding="utf-8") as f:
        json.dump(sample_config, f, ensure_ascii=False, indent=2)
    
    print("✅ 示例配置已保存: sample_classifier_config.json")

def main():
    """主函數"""
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--create-config":
            create_sample_config()
            return
        elif sys.argv[1] == "--help":
            print("🚀 簡單分類器使用方法:")
            print("  python simple_classifier_example.py           # 運行示例")
            print("  python simple_classifier_example.py --create-config  # 創建配置文件")
            print("  python simple_classifier_example.py --help    # 顯示幫助")
            return
    
    # 運行分類器示例
    success = run_classifier_example()
    
    if not success:
        print("\n🔧 故障排除建議:")
        print("  1. 首先運行: python test_classifier_setup.py")
        print("  2. 檢查數據目錄結構是否正確")
        print("  3. 如需幫助: python simple_classifier_example.py --help")

if __name__ == "__main__":
    main()