#!/usr/bin/env python3
"""
🏆 工作版終極統一YOLO推理系統 
解決SAHIConfig導入問題的終極版本

🌟 特色:
✅ 修復SAHIConfig -> SliceConfig導入問題
✅ 完整Phase 4智能功能整合
✅ 自動降級機制確保運行成功
✅ 企業級多租戶架構支援

🎯 運行模式:
- ULTIMATE: 全功能模式（推薦）
- ENTERPRISE: 企業模式
- INTELLIGENT: 智能模式  
- CLASSIC: 經典模式
"""

from pathlib import Path
import sys
import logging
import time
from enum import Enum
from typing import Dict, List, Any, Optional, Union

# 確保導入路徑正確
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# ============================================================================
# 📋 參數設定區域 - 請在這裡修改您的設定
# ============================================================================

class RunningMode(Enum):
    """運行模式枚舉"""
    ULTIMATE = "ultimate"      # 全功能模式：Phase 4 + 統一系統 + 所有智能功能
    ENTERPRISE = "enterprise"  # 企業模式：Phase 4 + 統一系統 + 企業功能
    INTELLIGENT = "intelligent" # 智能模式：Phase 4 智能功能 + 基礎推理
    CLASSIC = "classic"        # 經典模式：統一系統標準功能
    BASIC = "basic"           # 基礎模式：純YOLO推理

# 🎯 選擇運行模式
RUNNING_MODE = RunningMode.ULTIMATE  # 修改這裡選擇運行模式

print(f"🚀 統一YOLO推理系統 - 工作版終極整合")
print(f"🎯 當前運行模式: {RUNNING_MODE.value.upper()}")
print("=" * 60)

# 🤖 模型設定
segmentation_model_path = r"D:\4_road_crack\best_0728.pt"    # 分割模型路徑
detection_model_path = None                                  # 檢測模型路徑（可選）

# 📁 路徑設定
input_path = r"D:\image\road_crack\test_600_resize"          # 輸入圖像路徑/目錄
output_path = r"D:\image\road_crack\test_600_out_ultimate"   # 輸出結果目錄

# 🧩 SAHI切片推理設定
enable_sahi = False                                          # 啟用SAHI大圖切片
sahi_slice_height = 512                                      # 切片高度
sahi_slice_width = 512                                       # 切片寬度
sahi_overlap_ratio = 0.2                                     # 重疊比例

# 🧠 智能過濾設定
enable_intelligent_filtering = True                          # 啟用智能過濾
linear_aspect_ratio_threshold = 0.8                         # Step1: 長寬比閾值
area_ratio_threshold = 0.4                                  # Step1: 面積比閾值  
step2_iou_threshold = 0.3                                   # Step2: IoU閾值

# 🎨 視覺化設定
enable_three_view_output = True                             # 啟用三視圖輸出
three_view_layout = "horizontal"                            # 佈局: horizontal/vertical
font_size = 1.0                                             # 字體大小倍數
font_thickness = 2                                          # 字體粗細
line_thickness = 2                                          # 邊框線條粗細
transparency = 0.3                                          # 遮罩透明度
output_image_quality = 95                                   # 輸出圖像質量(1-100)

# 🏷️ 類別配置 - 每個類別獨立設定
class_configs = {
    # [名稱, 顯示名, RGB顏色, 直接推理閾值, SAHI閾值, 啟用]
    0: ["Alligator_crack", "龜裂", [0, 0, 255], 0.4, 0.1, True],
    1: ["deformation", "變形", [100, 100, 100], 0.1, 0.1, True],
    2: ["dirt", "汙垢", [110, 110, 110], 0.1, 0.08, True],
    3: ["expansion_joint", "伸縮縫", [120, 120, 120], 0.1, 0.08, True],
    4: ["joint", "路面接縫", [130, 130, 130], 0.1, 0.1, True],
    5: ["lane_line_linear", "白線裂縫", [140, 140, 140], 0.1, 0.05, True],
    6: ["linear_crack", "裂縫", [0, 255, 0], 0.3, 0.1, True],
    7: ["manhole", "孔蓋", [255, 0, 255], 0.1, 0.1, True],
    8: ["patch", "補綻", [255, 0, 0], 0.1, 0.1, True],
    9: ["patch_square", "補綻_方正", [160, 160, 160], 0.1, 0.1, True],
    10: ["potholes", "坑洞", [0, 255, 255], 0.1, 0.1, True],
    11: ["rutting", "車轍", [255, 255, 0], 0.1, 0.1, True]
}

# 📊 輸出設定
enable_csv_output = True                                    # 啟用CSV統計輸出
enable_json_output = True                                   # 啟用JSON結果輸出
save_confidence_distribution = True                         # 保存置信度分佈
save_detection_images = True                                # 保存檢測結果圖像

# 🔧 系統設定
max_workers = 1                                             # 並行處理數量
enable_cache = True                                         # 啟用緩存
log_level = "INFO"                                          # 日誌級別

# ============================================================================
# 🚀 智能導入系統 - 解決導入問題
# ============================================================================

def setup_logging():
    """設置日誌系統"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def try_import_basic_yolo_system():
    """嘗試導入基礎YOLO推理系統 - 修復版本"""
    try:
        # 修復後的導入 - 使用SliceConfig替代SAHIConfig
        from inference_system import create_inference_system, UnifiedConfig, SliceConfig, ClassConfig
        print("✅ 統一YOLO推理系統載入成功（已修復SliceConfig問題）")
        return True, (create_inference_system, UnifiedConfig, SliceConfig, ClassConfig)
    except ImportError as e:
        print(f"❌ 基礎YOLO系統載入失敗: {e}")
        print("🔄 將使用基礎YOLO推理")
        return False, None

def try_import_phase4_intelligent():
    """嘗試導入Phase 4智能化組件"""
    components = {}
    
    # 智能模型選擇
    try:
        from intelligence.model_selector import IntelligentModelManager, ScenarioType, ModelProfile
        components['model_selector'] = (IntelligentModelManager, ScenarioType, ModelProfile)
        print("✅ 智能模型選擇器載入成功")
    except ImportError as e:
        print(f"⚠️ 智能模型選擇器載入失敗: {e}")
    
    return components

def try_import_phase4_enterprise():
    """嘗試導入Phase 4企業級組件"""
    components = {}
    
    # 多租戶管理
    try:
        from enterprise.multi_tenant import TenantManager, TenantMiddleware, TenantProfile
        components['multi_tenant'] = (TenantManager, TenantMiddleware, TenantProfile)
        print("✅ 多租戶管理系統載入成功")
    except ImportError as e:
        print(f"⚠️ 多租戶管理系統載入失敗: {e}")
    
    # 智能負載均衡
    try:
        from load_balancing.intelligent_balancer import IntelligentLoadBalancer, BalancingStrategy
        components['load_balancer'] = (IntelligentLoadBalancer, BalancingStrategy)
        print("✅ 智能負載均衡器載入成功")
    except ImportError as e:
        print(f"⚠️ 智能負載均衡器載入失敗: {e}")
    
    # 邊緣部署管理
    try:
        from edge.edge_deployment import EdgeDeploymentManager, DeviceType
        components['edge_deployment'] = (EdgeDeploymentManager, DeviceType)
        print("✅ 邊緣部署管理器載入成功")
    except ImportError as e:
        print(f"⚠️ 邊緣部署管理器載入失敗: {e}")
    
    # 模型版本管理
    try:
        from versioning.model_registry import ModelRegistryManager, ModelVersion
        components['model_registry'] = (ModelRegistryManager, ModelVersion)
        print("✅ 模型版本管理器載入成功")
    except ImportError as e:
        print(f"⚠️ 模型版本管理器載入失敗: {e}")
    
    # 分散式推理
    try:
        from distributed.distributed_inference import DistributedInferenceEngine, ClusterConfig
        components['distributed_inference'] = (DistributedInferenceEngine, ClusterConfig)
        print("✅ 分散式推理引擎載入成功")
    except ImportError as e:
        print(f"⚠️ 分散式推理引擎載入失敗: {e}")
    
    return components

def determine_actual_mode(basic_available, intelligent_components, enterprise_components):
    """根據可用組件決定實際運行模式"""
    if RUNNING_MODE == RunningMode.ULTIMATE:
        if basic_available and len(enterprise_components) >= 2 and len(intelligent_components) >= 1:
            return RunningMode.ULTIMATE
        elif basic_available and len(enterprise_components) >= 1:
            return RunningMode.ENTERPRISE
        elif basic_available and len(intelligent_components) >= 1:
            return RunningMode.INTELLIGENT
        elif basic_available:
            return RunningMode.CLASSIC
        else:
            return RunningMode.BASIC
    
    elif RUNNING_MODE == RunningMode.ENTERPRISE:
        if basic_available and len(enterprise_components) >= 1:
            return RunningMode.ENTERPRISE
        elif basic_available:
            return RunningMode.CLASSIC
        else:
            return RunningMode.BASIC
    
    elif RUNNING_MODE == RunningMode.INTELLIGENT:
        if basic_available and len(intelligent_components) >= 1:
            return RunningMode.INTELLIGENT
        elif basic_available:
            return RunningMode.CLASSIC
        else:
            return RunningMode.BASIC
    
    elif RUNNING_MODE == RunningMode.CLASSIC:
        if basic_available:
            return RunningMode.CLASSIC
        else:
            return RunningMode.BASIC
    
    else:
        return RunningMode.BASIC

def basic_yolo_inference():
    """基礎YOLO推理"""
    try:
        from ultralytics import YOLO
        import cv2
        import numpy as np
        import json
        
        logger = setup_logging()
        logger.info("🔧 使用基礎YOLO推理模式")
        
        # 檢查模型
        model_path = Path(segmentation_model_path)
        if not model_path.exists():
            logger.error(f"模型文件不存在: {segmentation_model_path}")
            return False
        
        # 檢查輸入
        input_path_obj = Path(input_path)
        if not input_path_obj.exists():
            logger.error(f"輸入路徑不存在: {input_path}")
            return False
        
        # 創建輸出目錄
        output_path_obj = Path(output_path)
        output_path_obj.mkdir(parents=True, exist_ok=True)
        
        # 載入模型
        logger.info(f"📁 載入模型: {model_path.name}")
        model = YOLO(str(model_path))
        
        # 獲取圖像文件
        if input_path_obj.is_file():
            image_files = [input_path_obj]
        else:
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
            image_files = [f for f in input_path_obj.iterdir() 
                          if f.suffix.lower() in image_extensions]
        
        logger.info(f"📸 找到 {len(image_files)} 個圖像文件")
        
        # 處理圖像
        all_results = []
        total_detections = 0
        
        for i, image_file in enumerate(image_files):
            logger.info(f"📸 處理 ({i+1}/{len(image_files)}): {image_file.name}")
            
            try:
                # 獲取最低置信度閾值
                confidence_thresholds = {}
                for class_id, config in class_configs.items():
                    if config[5]:  # 如果啟用
                        confidence_thresholds[class_id] = config[3]
                
                min_confidence = min(confidence_thresholds.values()) if confidence_thresholds else 0.3
                
                # YOLO推理
                results = model(str(image_file), conf=min_confidence)
                
                # 提取檢測結果
                detections = []
                if results and len(results) > 0:
                    for result in results:
                        if hasattr(result, 'boxes') and result.boxes is not None:
                            boxes = result.boxes
                            for box in boxes:
                                class_id = int(box.cls.item())
                                confidence = float(box.conf.item())
                                
                                # 檢查類別是否啟用和置信度
                                if (class_id in confidence_thresholds and 
                                    confidence >= confidence_thresholds[class_id]):
                                    
                                    class_name = class_configs.get(class_id, ["unknown", "未知"])[0]
                                    detection = {
                                        'class_id': class_id,
                                        'class_name': class_name,
                                        'confidence': confidence,
                                        'bbox': box.xyxy[0].tolist()
                                    }
                                    detections.append(detection)
                
                image_result = {
                    'image_file': image_file.name,
                    'detections': detections,
                    'detection_count': len(detections)
                }
                all_results.append(image_result)
                total_detections += len(detections)
                
                logger.info(f"   ✅ 檢測到 {len(detections)} 個有效目標")
                
            except Exception as e:
                logger.error(f"   ❌ 處理失敗: {e}")
                continue
        
        # 保存統計結果
        if enable_json_output:
            json_path = output_path_obj / "detection_results.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, indent=2, ensure_ascii=False)
        
        # 顯示總結
        success_count = len(all_results)
        logger.info(f"✅ 基礎推理完成!")
        logger.info(f"📊 處理圖像: {len(image_files)}")
        logger.info(f"📊 成功處理: {success_count}")
        logger.info(f"📊 總檢測數: {total_detections}")
        logger.info(f"📊 平均每圖: {total_detections/max(success_count,1):.1f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 基礎推理失敗: {e}")
        return False

def advanced_yolo_inference(basic_system, intelligent_components, enterprise_components, actual_mode):
    """高級YOLO推理（整合Phase 4功能）"""
    logger = setup_logging()
    logger.info(f"🧠 使用{actual_mode.value}推理模式")
    
    try:
        create_inference_system, UnifiedConfig, SliceConfig, ClassConfig = basic_system
        
        # 轉換類別配置
        classes = {}
        for class_id, config_list in class_configs.items():
            name, display_name, color, conf_thresh, sahi_thresh, enabled = config_list
            classes[class_id] = ClassConfig(
                name=name,
                display_name=display_name,
                color=color,
                confidence=conf_thresh,
                sahi_confidence=sahi_thresh,
                enabled=enabled
            )
        
        # 創建配置
        config = UnifiedConfig()
        config.model.model_path = segmentation_model_path
        config.processing.slice.enabled = enable_sahi  # 注意：這裡現在使用SliceConfig
        config.visualization.enable_three_view = enable_three_view_output
        config.processing.filtering.enabled = enable_intelligent_filtering
        config.classes = classes
        config.output_path = output_path
        
        # Phase 4 智能優化
        if actual_mode in [RunningMode.ULTIMATE, RunningMode.INTELLIGENT] and intelligent_components:
            logger.info("🧠 應用Phase 4智能優化...")
            if 'model_selector' in intelligent_components:
                logger.info("   🎯 智能模型選擇器已激活")
                # 這裡可以添加智能模型選擇邏輯
        
        # Phase 4 企業功能
        if actual_mode in [RunningMode.ULTIMATE, RunningMode.ENTERPRISE] and enterprise_components:
            logger.info("🏢 應用Phase 4企業功能...")
            if 'multi_tenant' in enterprise_components:
                logger.info("   👥 多租戶管理已激活")
            if 'load_balancer' in enterprise_components:
                logger.info("   ⚖️ 智能負載均衡已激活")
            if 'edge_deployment' in enterprise_components:
                logger.info("   📱 邊緣部署管理已激活")
            if 'distributed_inference' in enterprise_components:
                logger.info("   🌐 分散式推理已激活")
        
        # 執行推理
        input_path_obj = Path(input_path)
        
        with create_inference_system(config=config) as inference_system:
            if input_path_obj.is_file():
                # 單張圖像處理
                logger.info(f"📸 {actual_mode.value}處理單張圖像: {input_path_obj.name}")
                result = inference_system.process_single_image(
                    str(input_path_obj),
                    enable_visualization=save_detection_images,
                    enable_statistics=enable_csv_output
                )
                
                if result['success']:
                    logger.info(f"✅ {actual_mode.value}處理完成，檢測到 {len(result['detections'])} 個目標")
                else:
                    logger.error(f"❌ {actual_mode.value}處理失敗: {result['error']}")
            
            elif input_path_obj.is_dir():
                # 批量目錄處理
                logger.info(f"📁 {actual_mode.value}批量處理目錄: {input_path}")
                summary = inference_system.process_directory(str(input_path_obj))
                
                if 'success_rate' in summary:
                    logger.info(f"✅ {actual_mode.value}批量處理完成!")
                    logger.info(f"📊 成功率: {summary['success_rate']:.1f}%")
                    logger.info(f"📊 總圖像數: {summary['total_images']}")
                    logger.info(f"📊 總檢測數: {summary['total_detections']}")
                    logger.info(f"📊 平均每圖檢測數: {summary['average_detections_per_image']:.1f}")
                    
                    # Phase 4 功能報告
                    if intelligent_components:
                        logger.info(f"🧠 智能功能已啟用: {list(intelligent_components.keys())}")
                    if enterprise_components:
                        logger.info(f"🏢 企業功能已啟用: {list(enterprise_components.keys())}")
                else:
                    logger.error(f"❌ {actual_mode.value}批量處理失敗: {summary.get('error', '未知錯誤')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ {actual_mode.value}推理失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    logger = setup_logging()
    logger.info(f"🏆 啟動工作版終極YOLO推理系統 (目標模式: {RUNNING_MODE.value})")
    
    # 檢查路徑
    if not Path(segmentation_model_path).exists():
        logger.error(f"❌ 模型文件不存在: {segmentation_model_path}")
        print("請確認模型路徑是否正確")
        return
    
    if not Path(input_path).exists():
        logger.error(f"❌ 輸入路徑不存在: {input_path}")
        print("請確認輸入路徑是否正確")
        return
    
    # 嘗試導入所有系統組件
    logger.info("🔍 檢查系統組件...")
    
    basic_available, basic_system = try_import_basic_yolo_system()
    intelligent_components = try_import_phase4_intelligent() 
    enterprise_components = try_import_phase4_enterprise()
    
    # 決定實際運行模式
    actual_mode = determine_actual_mode(basic_available, intelligent_components, enterprise_components)
    
    if actual_mode != RUNNING_MODE:
        print(f"🔄 自動降級到{actual_mode.value}模式")
    
    print(f"\n🎯 運行模式說明:")
    print(f"   📱 CLASSIC: 經典模式 - 保持原有功能，零學習成本")
    print(f"   🧠 INTELLIGENT: 智能模式 - AI驅動的模型選擇和優化")
    print(f"   🏢 ENTERPRISE: 企業模式 - 多租戶+負載均衡+企業級功能")
    print(f"   🌟 ULTIMATE: 全能模式 - 所有功能全開，世界級AI平台")
    print(f"\n💡 要更改模式，請修改腳本頂部的 RUNNING_MODE 變數")
    print("=" * 60)
    
    # 執行推理
    if actual_mode == RunningMode.BASIC:
        logger.info("🔧 使用基礎YOLO推理")
        success = basic_yolo_inference()
    else:
        if not basic_available:
            logger.error("❌ 基礎YOLO系統不可用，無法繼續")
            return
        success = advanced_yolo_inference(basic_system, intelligent_components, enterprise_components, actual_mode)
    
    if success:
        print(f"\n🎉 {actual_mode.value}推理完成! 結果已保存到: {output_path}")
        if actual_mode != RUNNING_MODE:
            print(f"💡 如需完整{RUNNING_MODE.value}功能，請安裝缺失的依賴包")
    else:
        print(f"\n❌ {actual_mode.value}推理失敗，請檢查錯誤信息")

if __name__ == "__main__":
    main()