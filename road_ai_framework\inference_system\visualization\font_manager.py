#!/usr/bin/env python3
"""
🖋️ 字體管理器
支持中文字體和自定義字體，從原始 unified_yolo_inference.py 中提取
"""

import logging
from pathlib import Path
from typing import Optional, Tuple
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

from ..config import VisualizationConfig


class FontManager:
    """
    字體管理器
    
    功能：
    - 中文字體自動配置
    - 自定義字體支援
    - OpenCV和matplotlib字體統一管理
    """
    
    def __init__(self, 
                 font_path: str = "", 
                 font_size: float = 1.0,
                 font_thickness: int = 2, 
                 font_scale: float = 1.0):
        """
        初始化字體管理器
        
        Args:
            font_path: 自定義字體路徑
            font_size: 字體大小倍數
            font_thickness: 字體粗細
            font_scale: 字體縮放比例
        """
        self.font_path = font_path
        self.font_size = font_size
        self.font_thickness = font_thickness
        self.font_scale = font_scale
        self.matplotlib_font = None
        self.logger = logging.getLogger(__name__)
        
        self._setup_fonts()
    
    @classmethod
    def from_config(cls, config: VisualizationConfig) -> 'FontManager':
        """從配置創建字體管理器"""
        return cls(
            font_path=config.font_path,
            font_size=config.font_size,
            font_thickness=config.font_thickness,
            font_scale=config.font_scale
        )
    
    def _setup_fonts(self):
        """設置字體"""
        try:
            # 設置matplotlib中文字體
            if self.font_path and Path(self.font_path).exists():
                # 使用自定義字體
                font_prop = fm.FontProperties(fname=self.font_path)
                plt.rcParams['font.family'] = font_prop.get_name()
                self.matplotlib_font = font_prop
                self.logger.debug(f"✅ 使用自定義字體: {self.font_path}")
            else:
                # 使用系統默認中文字體
                chinese_fonts = ['Microsoft YaHei', 'SimHei', 'KaiTi', 'SimSun']
                font_found = False
                
                for font_name in chinese_fonts:
                    try:
                        plt.rcParams['font.sans-serif'] = [font_name]
                        font_found = True
                        self.logger.debug(f"✅ 使用系統字體: {font_name}")
                        break
                    except:
                        continue
                
                if not font_found:
                    self.logger.warning("⚠️ 未找到中文字體，可能出現亂碼")
                
                plt.rcParams['axes.unicode_minus'] = False
                
        except Exception as e:
            self.logger.error(f"❌ 字體設置失敗: {str(e)}")
    
    def get_cv2_font_scale(self, base_scale: float = 0.6) -> float:
        """
        獲取OpenCV字體縮放比例
        
        Args:
            base_scale: 基礎縮放比例
            
        Returns:
            float: 計算後的字體縮放比例
        """
        return base_scale * self.font_size * self.font_scale
    
    def get_cv2_font_thickness(self) -> int:
        """
        獲取OpenCV字體粗細
        
        Returns:
            int: 字體粗細值
        """
        return max(1, int(self.font_thickness * self.font_scale))
    
    def get_matplotlib_fontsize(self, base_size: int = 12) -> int:
        """
        獲取matplotlib字體大小
        
        Args:
            base_size: 基礎字體大小
            
        Returns:
            int: 計算後的字體大小
        """
        return int(base_size * self.font_size * self.font_scale)
    
    def get_text_size(self, text: str, font_scale: Optional[float] = None) -> Tuple[int, int]:
        """
        計算文字尺寸
        
        Args:
            text: 文字內容
            font_scale: 字體縮放（可選）
            
        Returns:
            Tuple[int, int]: (寬度, 高度)
        """
        import cv2
        
        scale = font_scale or self.get_cv2_font_scale()
        thickness = self.get_cv2_font_thickness()
        
        (text_width, text_height), baseline = cv2.getTextSize(
            text, cv2.FONT_HERSHEY_SIMPLEX, scale, thickness
        )
        
        return text_width, text_height + baseline
    
    def apply_text_style(self, 
                        image: np.ndarray,
                        text: str,
                        position: Tuple[int, int],
                        color: Tuple[int, int, int] = (255, 255, 255),
                        background_color: Optional[Tuple[int, int, int]] = None) -> np.ndarray:
        """
        在圖像上繪製帶樣式的文字
        
        Args:
            image: 目標圖像
            text: 文字內容
            position: 文字位置 (x, y)
            color: 文字顏色 (B, G, R)
            background_color: 背景顏色（可選）
            
        Returns:
            np.ndarray: 繪製後的圖像
        """
        import cv2
        
        result_image = image.copy()
        font_scale = self.get_cv2_font_scale()
        thickness = self.get_cv2_font_thickness()
        
        # 如果需要背景色
        if background_color:
            text_width, text_height = self.get_text_size(text, font_scale)
            x, y = position
            
            # 繪製背景矩形
            cv2.rectangle(
                result_image,
                (x - 2, y - text_height - 2),
                (x + text_width + 2, y + 2),
                background_color,
                -1
            )
        
        # 繪製文字
        cv2.putText(
            result_image,
            text,
            position,
            cv2.FONT_HERSHEY_SIMPLEX,
            font_scale,
            color,
            thickness,
            cv2.LINE_AA
        )
        
        return result_image
    
    def get_optimal_font_size(self, image_shape: Tuple[int, int, int]) -> float:
        """
        根據圖像大小計算最佳字體大小
        
        Args:
            image_shape: 圖像形狀 (H, W, C)
            
        Returns:
            float: 最佳字體縮放比例
        """
        h, w = image_shape[:2]
        image_size = max(h, w)
        
        # 根據圖像大小調整字體
        if image_size < 640:
            return self.font_size * 0.8
        elif image_size > 1920:
            return self.font_size * 1.5
        else:
            return self.font_size
    
    def cleanup(self):
        """清理字體管理器資源"""
        self.logger.debug("🧹 清理字體管理器資源")
        # 重置matplotlib設置（如果需要）
        pass