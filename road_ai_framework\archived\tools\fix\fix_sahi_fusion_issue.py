#!/usr/bin/env python3
"""
🔧 SAHI融合策略修复脚本
修复 sahi_overlap_merge 策略不生效的问题
"""

import re
from pathlib import Path

def fix_adaptive_fusion_issue():
    """修复自适应融合覆盖配置的问题"""
    print("🔧 开始修复SAHI融合策略问题...")
    
    # 1. 修复 run_unified_yolo_ultimate.py 中的自适应融合设置
    ultimate_file = Path("run_unified_yolo_ultimate.py")
    if ultimate_file.exists():
        print(f"📝 修复文件: {ultimate_file}")
        
        with open(ultimate_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 禁用自适应融合，确保使用用户配置的策略
        original_line = "enable_adaptive_fusion = True"
        fixed_line = "enable_adaptive_fusion = False  # 🔧 禁用自适应融合，确保使用配置的SAHI策略"
        
        if original_line in content:
            content = content.replace(original_line, fixed_line)
            print(f"   ✅ 禁用自适应融合: {original_line} → {fixed_line}")
        else:
            print(f"   ⚠️ 未找到目标行: {original_line}")
        
        # 添加调试日志确认策略使用
        debug_insertion_point = 'logger.info(f"🔀 融合策略設定為: {fusion_strategy}'
        debug_code = '''        
        # 🔧 添加SAHI融合策略调试信息
        if fusion_strategy == "sahi_overlap_merge":
            logger.info("🎯 SAHI专用融合策略已启用 - 将合并IoU > 0.1的同类检测")
            logger.info(f"   IoU阈值: {sahi_merge_iou_threshold}")
            logger.info(f"   Mask IoU优先: {enable_mask_iou_calculation}")
            logger.info(f"   置信度策略: {sahi_merge_confidence_strategy}")
            logger.info("   自适应融合: 已禁用，将严格使用SAHI策略")'''
        
        if debug_insertion_point in content and debug_code not in content:
            content = content.replace(
                debug_insertion_point,
                debug_insertion_point + debug_code
            )
            print("   ✅ 添加SAHI融合策略调试日志")
        
        # 写入修复后的文件
        with open(ultimate_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"   💾 文件已保存: {ultimate_file}")
    else:
        print(f"   ❌ 文件不存在: {ultimate_file}")

def enhance_fusion_engine_logging():
    """增强融合引擎的日志输出"""
    print("\n🔍 增强融合引擎日志输出...")
    
    fusion_engine_file = Path("inference_system/processing/fusion_engine.py")
    if fusion_engine_file.exists():
        print(f"📝 修复文件: {fusion_engine_file}")
        
        with open(fusion_engine_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在 fuse 方法中添加策略确认日志
        target_section = """        # 🔀 自適應融合策略選擇 (如果啟用)
        if hasattr(self.config, 'enable_adaptive_fusion') and getattr(self.config, 'enable_adaptive_fusion', False):
            selected_strategy = self._select_adaptive_strategy(
                internal_detections)
            self.stats['adaptive_selections'] += 1"""
        
        enhanced_section = """        # 🔀 自適應融合策略選擇 (如果啟用)
        if hasattr(self.config, 'enable_adaptive_fusion') and getattr(self.config, 'enable_adaptive_fusion', False):
            original_strategy = selected_strategy
            selected_strategy = self._select_adaptive_strategy(internal_detections)
            self.stats['adaptive_selections'] += 1
            # 🔧 添加策略选择日志
            if original_strategy != selected_strategy:
                self.logger.warning(f"🔄 自适应融合已覆盖配置策略: {original_strategy} → {selected_strategy}")
            else:
                self.logger.info(f"🎯 自适应融合确认使用策略: {selected_strategy}")"""
        
        if target_section in content:
            content = content.replace(target_section, enhanced_section)
            print("   ✅ 增强自适应融合策略选择日志")
        
        # 在 _sahi_overlap_merge 方法开头添加详细日志
        sahi_method_start = """        self.logger.debug(f"🎯 開始SAHI重疊合併: {len(detections)} 個檢測")"""
        sahi_enhanced_start = """        self.logger.debug(f"🎯 開始SAHI重疊合併: {len(detections)} 個檢測")
        self.logger.info(f"🔧 SAHI合并参数: IoU阈值={merge_threshold}, Mask IoU优先={enable_mask_iou}, 置信度策略={confidence_strategy}")"""
        
        if sahi_method_start in content and sahi_enhanced_start not in content:
            content = content.replace(sahi_method_start, sahi_enhanced_start)
            print("   ✅ 增强SAHI合并方法日志")
        
        # 写入修复后的文件
        with open(fusion_engine_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"   💾 文件已保存: {fusion_engine_file}")
    else:
        print(f"   ❌ 文件不存在: {fusion_engine_file}")

def create_verification_script():
    """创建验证修复效果的脚本"""
    print("\n🧪 创建验证脚本...")
    
    verify_script = """#!/usr/bin/env python3
\"\"\"
🧪 SAHI融合策略修复验证脚本
验证修复是否生效
\"\"\"

import logging
from pathlib import Path

def main():
    print("🧪 SAHI融合策略修复验证")
    print("=" * 50)
    
    # 1. 检查配置文件修复状态
    ultimate_file = Path("run_unified_yolo_ultimate.py")
    if ultimate_file.exists():
        with open(ultimate_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if "enable_adaptive_fusion = False" in content:
            print("✅ 自适应融合已禁用")
        else:
            print("❌ 自适应融合仍然启用")
            
        if "fusion_strategy = \\"sahi_overlap_merge\\"" in content:
            print("✅ SAHI融合策略已配置")
        else:
            print("❌ SAHI融合策略未配置")
    
    # 2. 检查融合引擎日志增强
    fusion_file = Path("inference_system/processing/fusion_engine.py")
    if fusion_file.exists():
        with open(fusion_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "自适应融合已覆盖配置策略" in content:
            print("✅ 融合引擎日志已增强")
        else:
            print("❌ 融合引擎日志未增强")
    
    print("\\n📋 验证完成！")
    print("\\n🚀 运行测试:")
    print("1. cd D:\\\\99_AI_model\\\\road_ai_framework")
    print("2. python run_unified_yolo_ultimate.py")
    print("3. 检查输出日志中是否有 '🎯 SAHI专用融合策略已启用' 信息")
    print("4. 查看结果图像中重叠框是否减少")

if __name__ == "__main__":
    main()
"""
    
    verify_file = Path("verify_sahi_fusion_fix.py")
    with open(verify_file, 'w', encoding='utf-8') as f:
        f.write(verify_script)
    
    print(f"   📝 验证脚本已创建: {verify_file}")

def main():
    """主修复流程"""
    print("🚀 SAHI融合策略修复工具")
    print("=" * 60)
    
    try:
        # 1. 修复自适应融合问题
        fix_adaptive_fusion_issue()
        
        # 2. 增强日志输出
        enhance_fusion_engine_logging()
        
        # 3. 创建验证脚本
        create_verification_script()
        
        print("\n" + "=" * 60)
        print("🎉 修复完成！")
        print("\n📋 修复内容:")
        print("1. ✅ 禁用自适应融合，确保使用SAHI策略")
        print("2. ✅ 增强融合引擎日志输出")
        print("3. ✅ 添加策略确认和参数输出")
        print("4. ✅ 创建验证脚本")
        
        print("\n🚀 测试步骤:")
        print("1. 运行: python verify_sahi_fusion_fix.py  # 验证修复状态")
        print("2. 运行: python run_unified_yolo_ultimate.py  # 执行推理")
        print("3. 检查日志中是否出现 '🎯 SAHI专用融合策略已启用'")
        print("4. 检查结果图像中重叠框是否显著减少")
        
        print("\n💡 期望结果:")
        print("- 日志明确显示使用SAHI策略")
        print("- Linear_crack重叠框数量显著减少")
        print("- 融合前后检测数量变化统计可见")
        
    except Exception as e:
        print(f"❌ 修复过程出现错误: {str(e)}")

if __name__ == "__main__":
    main()
"""