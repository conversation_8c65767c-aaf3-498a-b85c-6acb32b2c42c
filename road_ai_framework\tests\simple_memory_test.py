#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple memory cleanup test without Unicode characters
"""

import sys
import logging
from pathlib import Path

# Setup path
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

def test_memory_cleanup():
    """Test basic memory cleanup functionality"""
    print("=== Testing Memory Cleanup System ===")
    
    try:
        # Import memory cleanup modules
        from inference_system.performance import (
            MemoryCleaner, MemoryCleanupConfig, MemoryType, CleanupTrigger, create_memory_cleaner
        )
        print("[OK] Memory cleanup modules imported successfully")
        
    except ImportError as e:
        print(f"[ERROR] Failed to import memory cleanup modules: {e}")
        return False

    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Create test config
    test_config = MemoryCleanupConfig(
        enabled=True,
        trigger_mode=CleanupTrigger.MANUAL,
        cleanup_interval_seconds=10.0,
        cleanup_after_n_images=5,
        cpu_memory_threshold=80.0,
        gpu_memory_threshold=80.0,
        enable_torch_cleanup=True,
        enable_opencv_cleanup=True,
        enable_python_gc=True,
        enable_cuda_cleanup=True,
        enable_memory_monitoring=False,
        log_memory_usage=True
    )
    
    print("[OK] Test configuration created")
    
    # Create memory cleaner
    try:
        memory_cleaner = create_memory_cleaner(test_config)
        print("[OK] Memory cleaner created successfully")
    except Exception as e:
        print(f"[ERROR] Failed to create memory cleaner: {e}")
        return False
    
    # Test 1: Get memory usage
    print("\n--- Test 1: Get Memory Usage ---")
    try:
        memory_info = memory_cleaner.get_memory_usage()
        print(f"CPU memory usage: {memory_info['cpu_memory']['usage_percent']:.1f}%")
        print(f"Process memory usage: {memory_info['process_memory']['rss_mb']:.1f} MB")
        
        if 'gpu_memory' in memory_info and memory_info['gpu_memory']:
            for gpu_id, gpu_info in memory_info['gpu_memory'].items():
                print(f"{gpu_id} memory usage: {gpu_info['usage_percent']:.1f}%")
        else:
            print("No GPU memory info available")
            
        print("[OK] Test 1 passed")
    except Exception as e:
        print(f"[ERROR] Test 1 failed: {e}")
        return False
    
    # Test 2: Manual memory cleanup
    print("\n--- Test 2: Manual Memory Cleanup ---")
    try:
        cleanup_result = memory_cleaner.cleanup_memory(MemoryType.ALL, "manual_test")
        
        if cleanup_result.get("success"):
            effect = cleanup_result.get("effect", {})
            cpu_freed = effect.get("cpu_memory_freed_mb", 0)
            gpu_freed = effect.get("gpu_memory_freed_mb", 0)
            total_freed = effect.get("total_memory_freed_mb", 0)
            cleanup_time = cleanup_result.get("cleanup_time", 0)
            
            print(f"CPU memory freed: {cpu_freed:.1f} MB")
            print(f"GPU memory freed: {gpu_freed:.1f} MB")
            print(f"Total memory freed: {total_freed:.1f} MB")
            print(f"Cleanup time: {cleanup_time:.3f} seconds")
            print(f"Actions performed: {len(cleanup_result.get('actions_performed', []))}")
            
            for action in cleanup_result.get('actions_performed', []):
                print(f"  - {action}")
                
            print("[OK] Test 2 passed")
        else:
            print(f"[ERROR] Test 2 failed: {cleanup_result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Test 2 failed: {e}")
        return False
    
    # Test 3: Cleanup statistics
    print("\n--- Test 3: Cleanup Statistics ---")
    try:
        stats = memory_cleaner.get_cleanup_stats()
        print(f"Total cleanups: {stats['total_cleanups']}")
        print(f"Memory freed: {stats['memory_freed_mb']:.1f} MB")
        print(f"GPU memory freed: {stats['gpu_memory_freed_mb']:.1f} MB")
        print(f"Manual cleanups: {stats['manual_cleanups']}")
        print(f"Automatic cleanups: {stats['automatic_cleanups']}")
        
        print("[OK] Test 3 passed")
    except Exception as e:
        print(f"[ERROR] Test 3 failed: {e}")
        return False
    
    # Cleanup resources
    try:
        memory_cleaner.cleanup()
        print("\n[OK] Memory cleaner resources cleaned up")
    except Exception as e:
        print(f"[WARNING] Resource cleanup warning: {e}")
    
    print("\n=== All Tests Passed! Memory cleanup system is working ===")
    return True

if __name__ == "__main__":
    success = test_memory_cleanup()
    sys.exit(0 if success else 1)