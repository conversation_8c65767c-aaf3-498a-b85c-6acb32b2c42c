import sys
import os
import json
import random
import shutil
from pathlib import Path
from typing import Dict, List, Tuple
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
    QWidget, QPushButton, QLabel, QLineEdit, QSpinBox, 
    QDoubleSpinBox, QCheckBox, QProgressBar, QTextEdit,
    QFileDialog, QGroupBox, QScrollArea, QMessageBox,
    QComboBox, QTableWidget, QTableWidgetItem, QHeaderView
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont


class SampleExtractor(QThread):
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)
    
    def __init__(self, source_folder: str, output_folder: str, 
                 category_settings: Dict, multi_class_settings: Dict, all_categories: List):
        super().__init__()
        self.source_folder = source_folder
        self.output_folder = output_folder
        self.category_settings = category_settings
        self.multi_class_settings = multi_class_settings
        self.all_categories = all_categories  # 包含完整的類別資訊
    
    def run(self):
        try:
            # 創建輸出資料夾
            os.makedirs(self.output_folder, exist_ok=True)
            
            # 計算每個類別的提取分配（每個標註都計算為一個樣本）
            extraction_plan = self._calculate_extraction_plan()
            
            # 計算總樣本數用於進度顯示
            total_samples = sum(plan['from_multi_class'] + plan['from_single_class'] 
                            for plan in extraction_plan.values())
            processed_samples = 0
            
            self.status_updated.emit(f"提取計畫：")
            for category, plan in extraction_plan.items():
                self.status_updated.emit(f"  {category}: multi_class {plan['from_multi_class']} + 單類別 {plan['from_single_class']} = 總計 {plan['total']}")
            
            # 處理multi_class (按類別分別提取)
            if self.multi_class_settings['enabled'] and any(plan['from_multi_class'] > 0 for plan in extraction_plan.values()):
                self.status_updated.emit(f"處理 multi_class...")
                success, extracted_counts = self._process_multi_class_by_categories(extraction_plan)
                if not success:
                    self.finished_signal.emit(False, "multi_class 處理失敗")
                    return
                
                # 更新進度
                for category, count in extracted_counts.items():
                    processed_samples += count
                self.progress_updated.emit(int(processed_samples / total_samples * 100))
            
            # 處理各個類別
            for category, plan in extraction_plan.items():
                if plan['from_single_class'] > 0:
                    self.status_updated.emit(f"處理類別: {category} (需要 {plan['from_single_class']} 個樣本)")
                    success = self._process_category(category, plan['from_single_class'])
                    if not success:
                        self.finished_signal.emit(False, f"類別 {category} 處理失敗")
                        return
                    processed_samples += plan['from_single_class']
                    self.progress_updated.emit(int(processed_samples / total_samples * 100))
            
            # 在完成前，分析輸出並生成報告
            self._analyze_output_and_generate_report()
            
            self.finished_signal.emit(True, "樣本提取完成！報告已生成。")
            
        except Exception as e:
            self.finished_signal.emit(False, f"錯誤: {str(e)}")
    
    def _calculate_extraction_plan(self) -> Dict[str, Dict]:
        """計算每個類別要從multi_class和單類別各提取多少"""
        extraction_plan = {}
        
        # 建立類別資訊映射
        category_info_map = {cat['name']: cat for cat in self.all_categories}
        
        for category_name, settings in self.category_settings.items():
            if not settings['enabled']:
                continue
                
            category_info = category_info_map.get(category_name)
            if not category_info:
                continue
            
            total_demand = settings['count']
            multi_class_available = category_info['multi_class_count']
            single_class_available = category_info['single_class_count']
            
            # 新的分配邏輯：按百分比從multi_class中直接提取，再從單類別補足
            if 'multi_class_percent' in settings and multi_class_available > 0:
                # 🔧 使用總需求的百分比來計算multi_class需求量
                multi_class_percent = settings['multi_class_percent']
                # 🔧 新邏輯：基於總需求的百分比來計算
                desired_multi_class = int(total_demand * multi_class_percent / 100)
                multi_class_usable = min(desired_multi_class, multi_class_available)
            else:
                multi_class_usable = 0
            
            # 新的分配方式：先從multi_class提取，再從單類別補足
            from_multi_class = multi_class_usable
            remaining_needed = max(0, total_demand - from_multi_class)
            from_single_class = min(remaining_needed, single_class_available)
            
            extraction_plan[category_name] = {
                'from_multi_class': from_multi_class,
                'from_single_class': from_single_class,
                'total': from_multi_class + from_single_class,
                'multi_class_usable': multi_class_usable,  # 新增：記錄可用數量
                'category_info': category_info
            }
        
        return extraction_plan
    
    def _process_multi_class_by_categories(self, extraction_plan: Dict) -> Tuple[bool, Dict[str, int]]:
        """按類別從multi_class/overlap中提取指定數量（使用標準計算方式）"""
        multi_class_path = Path(self.source_folder) / "multi_class"
        multi_class_overlap_path = multi_class_path / "overlap"
        
        if not multi_class_overlap_path.exists():
            self.status_updated.emit(f"找不到路徑: {multi_class_overlap_path}")
            return False, {}
        
        # 獲取overlap資料夾中的所有圖像文件
        image_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            image_files.extend(list(multi_class_overlap_path.glob(f"*{ext}")))
        
        if len(image_files) == 0:
            self.status_updated.emit("在multi_class/overlap中找不到樣本文件")
            return False, {}
        
        # 使用標準方式分析每個圖像的類別資訊
        image_categories = {}
        json_parse_success = 0
        json_parse_failed = 0
        
        self.status_updated.emit(f"正在使用標準方式分析 {len(image_files)} 個圖像的類別資訊...")
        
        for img_file in image_files:
            # 🔧 修復：移除overlap_前綴來尋找對應的JSON檔案
            img_stem = img_file.stem  # 取得檔名（不含副檔名）
            
            # 如果檔名以overlap_開頭，移除這個前綴
            if img_stem.startswith('overlap_'):
                json_stem = img_stem[8:]  # 移除'overlap_'前綴（8個字符）
            else:
                json_stem = img_stem
            
            json_file_name = json_stem + '.json'
            json_file = multi_class_path / json_file_name  # 在multi_class根目錄中尋找
            
            # self.status_updated.emit(f"查找JSON: {img_file.name} -> {json_file_name}")
            
            if json_file.exists():
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 使用標準方式提取類別資訊
                    current_file_labels = []
                    
                    # 檢查 'shapes' 鍵是否存在且為列表
                    if "shapes" in data and isinstance(data["shapes"], list):
                        for shape in data["shapes"]:
                            category = shape.get("label")
                            if category:
                                current_file_labels.append(category)
                    
                    if current_file_labels:  # 只有成功提取到類別時才記錄
                        image_categories[img_file] = current_file_labels
                        json_parse_success += 1
                    else:
                        json_parse_failed += 1
                        self.status_updated.emit(f"❌ JSON無類別資訊 {json_file.name}")
                        
                        # 輸出JSON結構調試信息
                        keys = list(data.keys()) if isinstance(data, dict) else type(data).__name__
                        self.status_updated.emit(f"    JSON結構: {keys}")
                        
                except Exception as e:
                    json_parse_failed += 1
                    self.status_updated.emit(f"❌ JSON解析失敗 {json_file.name}: {str(e)}")
            else:
                json_parse_failed += 1
                self.status_updated.emit(f"❌ 找不到JSON文件: {json_file}")
        
        self.status_updated.emit(f"📊 JSON解析統計: 成功 {json_parse_success}, 失敗 {json_parse_failed}")
        
        # 如果所有JSON都解析失敗，給出建議
        if json_parse_success == 0 and len(image_files) > 0:
            self.status_updated.emit("⚠️ 所有JSON檔案都沒有找到類別資訊!")
            self.status_updated.emit("💡 請檢查：")
            self.status_updated.emit(f"   1. JSON檔案是否在 {multi_class_path}")
            self.status_updated.emit(f"   2. 檔名是否對應（如 image1.jpg -> image1.json）")
            self.status_updated.emit("   3. JSON格式是否正確")
        
        # 按類別組織圖像（考慮每個標註為一個樣本）
        category_images = {}  # {category: [img_file1, img_file2, ...]} - 每個類別中可能有重複的圖像檔案
        category_sample_counts = {}  # {category: total_sample_count} - 每個類別的總樣本數量
        
        for img_file, categories in image_categories.items():
            # 統計每個類別在這個圖像中的樣本數量
            category_counts_in_image = {}
            for category in categories:
                category_counts_in_image[category] = category_counts_in_image.get(category, 0) + 1
            
            # 為每個類別添加圖像（根據該類別在圖像中的樣本數量）
            for category, count_in_image in category_counts_in_image.items():
                if category not in category_images:
                    category_images[category] = []
                    category_sample_counts[category] = 0
                
                # 添加圖像檔案（每個樣本都添加一次）
                for _ in range(count_in_image):
                    category_images[category].append(img_file)
                    category_sample_counts[category] += 1
        
        # 按提取計畫選擇圖像
        selected_images = []
        extracted_counts = {}
        
        self.status_updated.emit(f"Multi_class overlap中找到 {len(image_files)} 個圖像文件")
        self.status_updated.emit(f"成功解析 {len(image_categories)} 個圖像的類別資訊")
        
        # 輸出類別統計
        for category, imgs in category_images.items():
            sample_count = category_sample_counts.get(category, len(imgs))
            unique_images = len(set(imgs))  # 獨特圖像數量
            self.status_updated.emit(f"類別 '{category}': {sample_count} 個樣本（來自 {unique_images} 個圖像檔案）")
        
        for category_name, plan in extraction_plan.items():
            need_count = plan['from_multi_class']
            self.status_updated.emit(f"類別 '{category_name}' 需要從multi_class提取: {need_count} 個")
            
            if need_count <= 0:
                extracted_counts[category_name] = 0
                continue
                
            available_images = category_images.get(category_name, [])
            available_samples = len(available_images)  # 現在這裡的每個元素都代表一個樣本
            unique_images = len(set(available_images)) if available_images else 0
            self.status_updated.emit(f"類別 '{category_name}' 在multi_class中可用: {available_samples} 個樣本（來自 {unique_images} 個圖像檔案）")
            
            actual_count = min(need_count, len(available_images))  # available_images現在正確代表樣本數量
            
            if actual_count > 0:
                selected = random.sample(available_images, actual_count)
                selected_images.extend(selected)
                extracted_counts[category_name] = actual_count
                self.status_updated.emit(f"類別 '{category_name}' 實際選中: {actual_count} 個")
            else:
                extracted_counts[category_name] = 0
                self.status_updated.emit(f"類別 '{category_name}' 沒有可用圖像")
        
        self.status_updated.emit(f"總共選中 {len(selected_images)} 個multi_class圖像待複製")
        
        # 創建multi_class輸出資料夾結構
        output_multi_class = Path(self.output_folder) / "multi_class"
        output_overlap = output_multi_class / "overlap"
        os.makedirs(output_overlap, exist_ok=True)
        
        # 📋 複製選中的multi_class圖像（只複製overlap圖像）
        self.status_updated.emit(f"準備複製 {len(selected_images)} 個multi_class overlap圖像")
        
        copied_count = 0
        for img_file in selected_images:
            try:
                # 🔧 直接複製overlap圖像（已經是overlap_格式）
                dest_img = output_overlap / img_file.name
                shutil.copy2(img_file, dest_img)
                copied_count += 1
                
                # 📋 Multi-class不複製JSON（標註檔案包含多個類別）
                    
            except Exception as e:
                self.status_updated.emit(f"❌ 複製multi_class失敗 {img_file.name}: {str(e)}")
                continue
        
        self.status_updated.emit(f"🎉 Multi_class複製完成: {copied_count}/{len(selected_images)} 個overlap圖像")
        return True, extracted_counts
    
    def _extract_categories_from_json(self, data) -> List[str]:
        """從json數據中提取類別列表（保留重複，因為每個標註都是一個樣本）"""
        categories = []
        
        # 嘗試不同的json結構
        if 'categories' in data:
            categories = data['categories']
        elif 'labels' in data:
            categories = data['labels']
        elif 'annotations' in data:
            # COCO格式
            for ann in data['annotations']:
                if 'category_id' in ann:
                    categories.append(str(ann['category_id']))
                elif 'category_name' in ann:
                    categories.append(ann['category_name'])
        elif 'shapes' in data:
            # LabelMe格式
            for shape in data['shapes']:
                if 'label' in shape:
                    categories.append(shape['label'])
        elif 'objects' in data:
            # 可能的格式：objects列表
            for obj in data['objects']:
                if isinstance(obj, dict):
                    if 'label' in obj:
                        categories.append(obj['label'])
                    elif 'category' in obj:
                        categories.append(obj['category'])
                    elif 'class' in obj:
                        categories.append(obj['class'])
        elif 'detections' in data:
            # 可能的格式：detections列表
            for det in data['detections']:
                if isinstance(det, dict):
                    if 'label' in det:
                        categories.append(det['label'])
                    elif 'class' in det:
                        categories.append(det['class'])
        
        # 如果以上都沒找到，嘗試直接查找常見的鍵
        if not categories:
            common_keys = ['class_names', 'class_labels', 'label_names', 'category_names']
            for key in common_keys:
                if key in data and isinstance(data[key], list):
                    categories = data[key]
                    break
        
        # 標準化類別名稱
        result = []
        for category in categories:
            if isinstance(category, dict) and 'name' in category:
                result.append(category['name'])
            elif isinstance(category, dict) and 'label' in category:
                result.append(category['label'])
            else:
                result.append(str(category))
        
        # 去除空字符串，但保留重複項（因為每個標註都是一個樣本）
        result = [cat.strip() for cat in result if cat and str(cat).strip()]
        
        return result
    
    def _process_category(self, category: str, count: int) -> bool:
        """處理單個類別的樣本提取（使用標準計算方式）"""
        # 不再依賴資料夾名稱，直接處理所有資料夾
        source_path = Path(self.source_folder)
        
        # 搜集所有包含該類別的JSON檔案和對應圖像
        all_samples = []  # 儲存每個標註對應的圖像檔案
        
        # 遍歷所有子資料夾來找到該類別的標註
        for item in source_path.iterdir():
            if not item.is_dir() or item.name == "multi_class":
                continue
                
            # 處理 train/val/test 的結構
            subfolders = ["train", "val", "test"]
            paths_to_check = []
            
            # 檢查是否為直接的train/val/test資料夾
            if item.name in subfolders:
                paths_to_check.append(item)
            else:
                # 檢查是否包含train/val/test子資料夾
                for subfolder in subfolders:
                    subfolder_path = item / subfolder
                    if subfolder_path.exists():
                        paths_to_check.append(subfolder_path)
                        
                # 如果沒有子資料夾，直接處理當前資料夾
                if not paths_to_check:
                    paths_to_check.append(item)
            
            # 處理所有路徑
            for path_to_check in paths_to_check:
                self._collect_category_samples_from_path(path_to_check, category, all_samples)
        
        available_samples_count = len(all_samples)
        self.status_updated.emit(f"📈 類別 '{category}' 總共有 {available_samples_count} 個可用標註樣本")

        
        actual_count = min(count, available_samples_count)
        
        if actual_count < count:
            self.status_updated.emit(f"⚠️ 類別 '{category}' 標註樣本不足。需要 {count}，實際提取 {actual_count}。")
        
        if actual_count == 0:
            self.status_updated.emit(f"類別 '{category}' 無需提取樣本。")
            return True

        # 隨機選擇標註樣本
        selected_image_files = random.sample(all_samples, actual_count)
        
        # 創建類別輸出資料夾
        output_category = Path(self.output_folder) / category
        output_overlap = output_category / "overlap"
        os.makedirs(output_overlap, exist_ok=True)
        
        # 📋 複製文件（只複製overlap圖像，不複製JSON）
        copied_files = set()
        for img_file in selected_image_files:
            # 產生對應的overlap檔名
            overlap_filename = f"overlap_{img_file.name}"
            
            if overlap_filename in copied_files:
                continue
            
            try:
                # 🔧 只複製overlap圖像文件（不複製原圖和JSON）
                dest_img = output_overlap / overlap_filename
                shutil.copy2(img_file, dest_img)
                
                copied_files.add(overlap_filename)
            except Exception as e:
                self.status_updated.emit(f"❌ 複製文件失敗 {img_file.name}: {str(e)}")
                continue

        self.status_updated.emit(f"🎉 類別 '{category}' 成功複製 {len(copied_files)} 個overlap圖像（對應 {actual_count} 個標註樣本）")
        return True
    
    def _collect_category_samples_from_path(self, path: Path, target_category: str, all_samples: list):
        """從指定路徑搜集特定類別的標註樣本"""
        if not path.exists():
            return
            
        for json_file in path.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                
                # 使用標準方式處理JSON
                if "shapes" in json_data and isinstance(json_data["shapes"], list):
                    for shape in json_data["shapes"]:
                        category = shape.get("label")
                        if category == target_category:
                            # 🔍 找到匹配的標註，尋找對應的overlap圖像
                            # 先尋找直接對應的圖像檔案
                            base_name = json_file.stem
                            
                            # 在overlap資料夾中尋承overlap_前綴的圖像
                            overlap_folder = path / "overlap" if path.name != "overlap" else path
                            if overlap_folder.exists():
                                overlap_img_name = f"overlap_{base_name}.jpg"
                                overlap_img_file = overlap_folder / overlap_img_name
                                
                                if overlap_img_file.exists():
                                    all_samples.append(overlap_img_file)
                                else:
                                    # 嘗試其他副檔名
                                    for ext in ['.jpeg', '.png', '.bmp']:
                                        overlap_img_name = f"overlap_{base_name}{ext}"
                                        overlap_img_file = overlap_folder / overlap_img_name
                                        if overlap_img_file.exists():
                                            all_samples.append(overlap_img_file)
                                            break
                                
            except Exception:
                # 静默失敗，繼續處理下一個檔案
                pass

    def _analyze_output_and_generate_report(self):
        """深度分析輸出資料夾並生成詳細報告（使用與提取時一致的算法）"""
        self.status_updated.emit("\n正在深度分析輸出結果並生成報告...")
        
        output_path = Path(self.output_folder)
        report_path = output_path / "extraction_analysis_report.txt"
        
        try:
            # 深度分析結果
            analysis_results = self._deep_analyze_output_folder()
            
            # 生成詳細報告內容
            report_content = self._generate_detailed_report(analysis_results)
            
            # 寫入報告檔案
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
                
            self.status_updated.emit(f"✅ 詳細分析報告已成功生成於: {report_path}")
            
            # 在控制台輸出簡要統計
            self._print_summary_statistics(analysis_results)

        except Exception as e:
            self.status_updated.emit(f"❌ 生成分析報告時發生錯誤: {str(e)}")
    
    def _deep_analyze_output_folder(self) -> Dict:
        """深度分析輸出資料夾，使用與提取時一致的算法"""
        output_path = Path(self.output_folder)
        analysis_results = {
            'categories': {},
            'multi_class_analysis': {},
            'total_files': 0,
            'total_samples': 0,
            'file_distribution': {},
            'json_analysis': {}
        }
        
        # 分析各個類別資料夾
        for category_path in output_path.iterdir():
            if not category_path.is_dir():
                continue
                
            category_name = category_path.name
            
            if category_name == "multi_class":
                # 分析multi_class資料夾
                multi_class_result = self._analyze_multi_class_output(category_path)
                analysis_results['multi_class_analysis'] = multi_class_result
                analysis_results['total_files'] += multi_class_result['total_files']
                analysis_results['total_samples'] += multi_class_result['total_samples']
            else:
                # 分析單類別資料夾
                category_result = self._analyze_single_category_output(category_path, category_name)
                analysis_results['categories'][category_name] = category_result
                analysis_results['total_files'] += category_result['file_count']
                analysis_results['total_samples'] += category_result['sample_count']
        
        return analysis_results
    
    def _analyze_multi_class_output(self, multi_class_path: Path) -> Dict:
        """分析multi_class輸出資料夾（使用標準算法）"""
        result = {
            'total_files': 0,
            'total_samples': 0,
            'category_samples': {},
            'unique_images': 0,
            'json_found': 0,
            'json_missing': 0
        }
        
        overlap_path = multi_class_path / "overlap"
        if not overlap_path.exists():
            return result
        
        # 獲取所有圖像文件
        image_files = []
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            image_files.extend(list(overlap_path.glob(f"*{ext}")))
        
        result['total_files'] = len(image_files)
        result['unique_images'] = len(image_files)
        
        # 分析每個圖像對應的JSON（使用與提取時相同的邏輯）
        source_multi_class_path = Path(self.source_folder) / "multi_class"
        
        for img_file in image_files:
            # 移除overlap_前綴來尋找對應的JSON檔案
            img_stem = img_file.stem
            if img_stem.startswith('overlap_'):
                json_stem = img_stem[8:]  # 移除'overlap_'前綴
            else:
                json_stem = img_stem
            
            json_file_name = json_stem + '.json'
            json_file = source_multi_class_path / json_file_name
            
            if json_file.exists():
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 使用標準方式提取類別資訊
                    if "shapes" in data and isinstance(data["shapes"], list):
                        for shape in data["shapes"]:
                            category = shape.get("label")
                            if category:
                                result['category_samples'][category] = result['category_samples'].get(category, 0) + 1
                                result['total_samples'] += 1
                        result['json_found'] += 1
                    else:
                        result['json_missing'] += 1
                        
                except Exception:
                    result['json_missing'] += 1
            else:
                result['json_missing'] += 1
        
        return result
    
    def _analyze_single_category_output(self, category_path: Path, category_name: str) -> Dict:
        """分析單類別輸出資料夾"""
        result = {
            'file_count': 0,
            'sample_count': 0,
            'overlap_files': []
        }
        
        overlap_path = category_path / "overlap"
        if overlap_path.exists():
            overlap_files = [f for f in overlap_path.iterdir() if f.is_file() and f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']]
            result['file_count'] = len(overlap_files)
            result['sample_count'] = len(overlap_files)  # 對於單類別，每個檔案代表一個樣本
            result['overlap_files'] = [f.name for f in overlap_files]
        
        return result
    
    def _generate_detailed_report(self, analysis_results: Dict) -> str:
        """生成詳細的分析報告"""
        import datetime
        
        report_content = "圖像樣本提取深度分析報告\n"
        report_content += "=" * 50 + "\n"
        report_content += f"報告生成時間: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        report_content += f"源資料夾: {self.source_folder}\n"
        report_content += f"輸出資料夾: {self.output_folder}\n\n"
        
        # 總體統計
        report_content += "📊 總體統計\n"
        report_content += "-" * 20 + "\n"
        report_content += f"總檔案數: {analysis_results['total_files']} 個\n"
        report_content += f"總樣本數: {analysis_results['total_samples']} 個\n\n"
        
        # 🎯 按類別統計（單類別 + 多類別）
        report_content += "🎯 各類別提取統計\n"
        report_content += "-" * 30 + "\n"
        
        # 收集所有類別名稱
        all_categories = set()
        if analysis_results['categories']:
            all_categories.update(analysis_results['categories'].keys())
        if analysis_results['multi_class_analysis'] and 'category_samples' in analysis_results['multi_class_analysis']:
            all_categories.update(analysis_results['multi_class_analysis']['category_samples'].keys())
        
        # 按類別顯示統計
        for category_name in sorted(all_categories):
            single_class_count = 0
            multi_class_count = 0
            
            # 獲取單類別數量
            if category_name in analysis_results['categories']:
                single_class_count = analysis_results['categories'][category_name]['sample_count']
            
            # 獲取多類別數量
            if (analysis_results['multi_class_analysis'] and 
                'category_samples' in analysis_results['multi_class_analysis']):
                multi_class_count = analysis_results['multi_class_analysis']['category_samples'].get(category_name, 0)
            
            total_count = single_class_count + multi_class_count
            
            report_content += f"\n類別: {category_name}\n"
            report_content += f"  單類別: {single_class_count} 個樣本\n"
            report_content += f"  多類別: {multi_class_count} 個樣本\n"
            report_content += f"  總計: {total_count} 個樣本\n"
        
        report_content += "\n"
        
        # # Multi-class 詳細分析
        # if analysis_results['multi_class_analysis']:
        #     multi_result = analysis_results['multi_class_analysis']
        #     report_content += "🔄 Multi-class 詳細分析\n"
        #     report_content += "-" * 25 + "\n"
        #     report_content += f"檔案數量: {multi_result['total_files']} 個\n"
        #     report_content += f"樣本數量: {multi_result['total_samples']} 個\n"
        #     report_content += f"JSON解析成功: {multi_result['json_found']} 個\n"
        #     report_content += f"JSON解析失敗: {multi_result['json_missing']} 個\n\n"
        
        # # 單類別詳細分析
        # if analysis_results['categories']:
        #     report_content += "📁 單類別詳細分析\n"
        #     report_content += "-" * 25 + "\n"
            
        #     sorted_categories = sorted(analysis_results['categories'].items(), key=lambda x: x[1]['sample_count'], reverse=True)
            
        #     for category_name, category_result in sorted_categories:
        #         report_content += f"\n類別: {category_name}\n"
        #         report_content += f"  檔案數量: {category_result['file_count']} 個\n"
        #         report_content += f"  樣本數量: {category_result['sample_count']} 個\n"
                
        #         if len(category_result['overlap_files']) <= 10:
        #             report_content += f"  檔案列表: {', '.join(category_result['overlap_files'])}\n"
        #         else:
        #             report_content += f"  檔案列表: {', '.join(category_result['overlap_files'][:5])} ... (共{len(category_result['overlap_files'])}個)\n"
        
        # 提取設定回顧
        report_content += "\n⚙️ 提取設定回顧\n"
        report_content += "-" * 20 + "\n"
        
        if hasattr(self, 'category_settings'):
            for category_name, settings in self.category_settings.items():
                if settings['enabled']:
                    report_content += f"\n{category_name}:\n"
                    report_content += f"  需求數量: {settings['count']} 個\n"
                    if 'multi_class_percent' in settings:
                        report_content += f"  Multi-class百分比: {settings['multi_class_percent']}%\n"
        
        # # 提取效果評估
        # report_content += "\n📈 提取效果評估\n"
        # report_content += "-" * 20 + "\n"
        
        # if hasattr(self, 'category_settings'):
        #     for category_name, settings in self.category_settings.items():
        #         if not settings['enabled']:
        #             continue
                    
        #         target_count = settings['count']
        #         actual_count = 0
                
        #         # 計算實際提取數量
        #         if category_name in analysis_results['categories']:
        #             actual_count += analysis_results['categories'][category_name]['sample_count']
                
        #         if analysis_results['multi_class_analysis'] and 'category_samples' in analysis_results['multi_class_analysis']:
        #             actual_count += analysis_results['multi_class_analysis']['category_samples'].get(category_name, 0)
                
        #         completion_rate = (actual_count / target_count * 100) if target_count > 0 else 0
        #         report_content += f"{category_name}: {actual_count}/{target_count} ({completion_rate:.1f}%)\n"
        
        report_content += f"\n報告結束 - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        return report_content
    
    def _print_summary_statistics(self, analysis_results: Dict):
        """在控制台輸出簡要統計"""
        self.status_updated.emit("\n📊 提取結果統計:")
        self.status_updated.emit(f"總檔案數: {analysis_results['total_files']} 個")
        self.status_updated.emit(f"總樣本數: {analysis_results['total_samples']} 個")
        
        # 🎯 按類別顯示單類別和多類別統計
        self.status_updated.emit("\n🎯 各類別提取統計:")
        
        # 收集所有類別名稱
        all_categories = set()
        if analysis_results['categories']:
            all_categories.update(analysis_results['categories'].keys())
        if analysis_results['multi_class_analysis'] and 'category_samples' in analysis_results['multi_class_analysis']:
            all_categories.update(analysis_results['multi_class_analysis']['category_samples'].keys())
        
        # 按類別顯示統計
        for category_name in sorted(all_categories):
            single_class_count = 0
            multi_class_count = 0
            
            # 獲取單類別數量
            if category_name in analysis_results['categories']:
                single_class_count = analysis_results['categories'][category_name]['sample_count']
            
            # 獲取多類別數量
            if (analysis_results['multi_class_analysis'] and 
                'category_samples' in analysis_results['multi_class_analysis']):
                multi_class_count = analysis_results['multi_class_analysis']['category_samples'].get(category_name, 0)
            
            total_count = single_class_count + multi_class_count
            
            self.status_updated.emit(f"  {category_name}: 單類別 {single_class_count} + 多類別 {multi_class_count} = 總計 {total_count}")


class ImageSampleExtractorGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.source_folder = ""
        self.output_folder = ""
        self.categories = []
        self.category_widgets = {}
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle("圖像樣本抽取工具 - 支援多標註樣本計算")
        self.setGeometry(100, 100, 800, 600)
        
        # 主widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 主布局
        main_layout = QVBoxLayout(main_widget)
        
        # 資料夾選擇區域
        folder_group = QGroupBox("資料夾設定")
        folder_layout = QVBoxLayout(folder_group)
        
        # 源資料夾
        source_layout = QHBoxLayout()
        source_layout.addWidget(QLabel("源資料夾:"))
        self.source_label = QLabel("未選擇")
        self.source_label.setStyleSheet("QLabel { border: 1px solid gray; padding: 5px; }")
        source_layout.addWidget(self.source_label, 1)
        self.source_btn = QPushButton("選擇")
        self.source_btn.clicked.connect(self.select_source_folder)
        source_layout.addWidget(self.source_btn)
        folder_layout.addLayout(source_layout)
        
        # 輸出資料夾
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("輸出資料夾:"))
        self.output_label = QLabel("未選擇")
        self.output_label.setStyleSheet("QLabel { border: 1px solid gray; padding: 5px; }")
        output_layout.addWidget(self.output_label, 1)
        self.output_btn = QPushButton("選擇")
        self.output_btn.clicked.connect(self.select_output_folder)
        output_layout.addWidget(self.output_btn)
        folder_layout.addLayout(output_layout)
        
        main_layout.addWidget(folder_group)
        
        # Multi-class資訊顯示區域
        self.multi_class_group = QGroupBox("Multi-Class 資訊 (一定會提取)")
        multi_class_layout = QHBoxLayout(self.multi_class_group)
        
        self.multi_class_info_label = QLabel("未掃描")
        self.multi_class_info_label.setStyleSheet("QLabel { font-weight: bold; color: blue; }")
        multi_class_layout.addWidget(self.multi_class_info_label)
        
        multi_class_layout.addStretch()
        main_layout.addWidget(self.multi_class_group)
        
        # 類別設定區域
        categories_group = QGroupBox("類別設定")
        categories_layout = QVBoxLayout(categories_group)
        
        # 掃描按鈕
        self.scan_btn = QPushButton("掃描類別")
        self.scan_btn.clicked.connect(self.scan_categories)
        categories_layout.addWidget(self.scan_btn)
        
        # 類別列表（滾動區域）
        self.scroll_area = QScrollArea()
        self.scroll_widget = QWidget()
        self.categories_layout = QVBoxLayout(self.scroll_widget)
        self.scroll_area.setWidget(self.scroll_widget)
        self.scroll_area.setWidgetResizable(True)
        categories_layout.addWidget(self.scroll_area)
        
        main_layout.addWidget(categories_group)
        
        # 執行區域
        execute_group = QGroupBox("執行")
        execute_layout = QVBoxLayout(execute_group)
        
        self.execute_btn = QPushButton("開始提取樣本")
        self.execute_btn.clicked.connect(self.execute_extraction)
        self.execute_btn.setEnabled(False)
        execute_layout.addWidget(self.execute_btn)
        
        # 進度條
        self.progress_bar = QProgressBar()
        execute_layout.addWidget(self.progress_bar)
        
        # 狀態文字
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(100)
        self.status_text.setReadOnly(True)
        execute_layout.addWidget(self.status_text)
        
        main_layout.addWidget(execute_group)
    
    def select_source_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "選擇源資料夾")
        if folder:
            self.source_folder = folder
            self.source_label.setText(folder)
            self.categories = []
            self.update_categories_display()
    
    def select_output_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "選擇輸出資料夾")
        if folder:
            self.output_folder = folder
            self.output_label.setText(folder)
            self.update_execute_button()
    
    def scan_categories(self):
        if not self.source_folder:
            QMessageBox.warning(self, "警告", "請先選擇源資料夾")
            return
        
        source_path = Path(self.source_folder)
        
        # 第一步：使用標準方式分析整個資料集
        self.status_text.append("正在使用標準方式分析資料集...")
        all_class_stats, multi_object_stats = self._analyze_dataset_like_standard(source_path)
        
        # 顯示分析結果
        self.status_text.append("\n=== 標準類別統計結果 ===")
        total_all_samples = 0
        for category, counts in all_class_stats.items():
            total_count = counts['total']
            total_all_samples += total_count
            self.status_text.append(f"{category}: 總計={total_count} (訓練={counts.get('train', 0)}, 驗證={counts.get('val', 0)}, 測試={counts.get('test', 0)})")
        
        self.status_text.append(f"\n所有類別總標註數: {total_all_samples}")
        
        self.status_text.append("\n=== 多物件影像中的類別統計 ===")
        total_multi_samples = 0
        for category, counts in multi_object_stats.items():
            total_count = counts['total']
            total_multi_samples += total_count
            self.status_text.append(f"{category}: 總計={total_count} (訓練={counts.get('train', 0)}, 驗證={counts.get('val', 0)}, 測試={counts.get('test', 0)})")
        
        self.status_text.append(f"多物件影像總標註數: {total_multi_samples}")
        
        # 計算multi_class資料夾的相關資訊
        multi_class_path = source_path / "multi_class"
        multi_class_overlap_path = multi_class_path / "overlap"
        multi_class_overlap_count = 0
        
        if multi_class_overlap_path.exists():
            for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
                multi_class_overlap_count += len(list(multi_class_overlap_path.glob(f"*{ext}")))
            self.status_text.append(f"\nmulti_class/overlap: {multi_class_overlap_count} 個圖像檔案可供提取")
        
        # 第二步：整理類別資訊供界面使用
        self.status_text.append("\n正在整理類別資訊...")
        self.categories = []
        
        # 使用all_class_stats作為主要的類別統計基礎
        for category_name, counts in all_class_stats.items():
            # 計算該類別在多物件影像中的數量（這是multi_class的概念）
            multi_class_count = multi_object_stats.get(category_name, {}).get('total', 0)
            
            # 計算單類別數量 = 總數 - 多物件數量
            single_class_count = counts['total'] - multi_class_count
            
            self.categories.append({
                'name': category_name,
                'path': '',  # 不依賴資料夾路徑
                'single_class_count': single_class_count,  # 單標註影像中的數量
                'multi_class_count': multi_class_count,    # 多標註影像中的數量
                'total_count': counts['total']  # 總計標註數
            })
            
            self.status_text.append(f"  ✓ {category_name}: multi_class {multi_class_count} + 單類別 {single_class_count} = 總計 {counts['total']}")
        
        # 第三步：更新界面顯示
        # 更新multi_class資訊顯示
        if multi_class_overlap_count > 0:
            multi_class_info = f"可提取: {multi_class_overlap_count} 個圖像檔案 (多標註樣本: {total_multi_samples})"
            self.multi_class_info_label.setText(multi_class_info)
        else:
            self.multi_class_info_label.setText("沒有找到可提取的圖像")
        
        # 更新multi_class group box標題
        self.multi_class_group.setTitle(f"Multi-Class 資訊 (一定會提取: {multi_class_overlap_count} 個樣本)")
        
        # 更新類別顯示
        self.update_categories_display()
        
        # 統計資訊
        total_categories = len(self.categories)
        total_single_class = sum(cat['single_class_count'] for cat in self.categories)
        total_multi_class_samples = sum(cat['multi_class_count'] for cat in self.categories)
        
        self.status_text.append(f"\n=== 掃描完成結果 ===")
        self.status_text.append(f"找到 {total_categories} 個類別")
        self.status_text.append(f"Multi-class overlap可提取: {multi_class_overlap_count} 個圖像檔案")
        self.status_text.append(f"Multi-class標註總計: {total_multi_class_samples} 個")
        self.status_text.append(f"單類別標註總計: {total_single_class} 個")
        self.status_text.append(f"整體標註總計: {total_all_samples} 個（每個標註都計算為一個樣本）")
    
    def _analyze_dataset_like_standard(self, dataset_folder: Path) -> tuple:
        """
        使用與計算訓練驗證測試類別數量.py相同的方式分析資料集
        
        Returns:
            tuple: (all_class_stats, multi_object_stats)
        """
        from collections import defaultdict
        
        # 儲存所有影像中的類別統計
        all_class_stats = defaultdict(lambda: {"total": 0, "train": 0, "val": 0, "test": 0, "unknown": 0})
        
        # 儲存包含多個物件的影像中的類別統計
        multi_object_stats = defaultdict(lambda: {"total": 0, "train": 0, "val": 0, "test": 0, "unknown": 0})
        
        subfolders = ["train", "val", "test"]
        
        # 遍歷所有子資料夾
        for item in dataset_folder.iterdir():
            if not item.is_dir():
                continue
            
            # 🔧 特殊處理multi_class資料夾
            if item.name == "multi_class":
                self._process_multi_class_for_standard_analysis(
                    item, all_class_stats, multi_object_stats
                )
                continue
                
            # 檢查是否為 train/val/test 的子資料夾
            if item.name in subfolders:
                subfolder = item.name
                current_path = item
                self._process_folder_for_standard_analysis(
                    current_path, subfolder, all_class_stats, multi_object_stats
                )
            else:
                # 檢查是否包含 train/val/test 子資料夾
                has_subfolders = False
                for subfolder in subfolders:
                    subfolder_path = item / subfolder
                    if subfolder_path.exists():
                        has_subfolders = True
                        self._process_folder_for_standard_analysis(
                            subfolder_path, subfolder, all_class_stats, multi_object_stats
                        )
                
                if not has_subfolders:
                    # 沒有子資料夾，直接處理當前資料夾
                    self._process_folder_for_standard_analysis(
                        item, "unknown", all_class_stats, multi_object_stats
                    )
        
        return dict(all_class_stats), dict(multi_object_stats)
    
    def _process_multi_class_for_standard_analysis(self, multi_class_path: Path, 
                                                  all_class_stats: dict, multi_object_stats: dict):
        """特殊處理multi_class資料夾的標準分析"""
        if not multi_class_path.exists():
            return
        
        # 🔍 掃描multi_class根目錄下的JSON檔案
        json_files = list(multi_class_path.glob("*.json"))
        print(f"🔍 Multi_class路徑: {multi_class_path}")
        print(f"🔍 找到JSON檔案數量: {len(json_files)}")
        if json_files:
            print(f"🔍 前3個JSON檔案: {[f.name for f in json_files[:3]]}")
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                    
                # 用於追蹤當前JSON檔案中所有物件的類別
                current_file_labels = [] 
                
                # 檢查 'shapes' 鍵是否存在且為列表
                if "shapes" in json_data and isinstance(json_data["shapes"], list):
                    for shape in json_data["shapes"]:
                        category = shape.get("label")
                        if category:
                            # 執行所有類別的標準統計
                            all_class_stats[category]["total"] += 1
                            all_class_stats[category]["unknown"] += 1  # multi_class不分train/val/test
                            current_file_labels.append(category)
                    
                    # 🎯 重點：multi_class中所有標註都可能是多物件
                    # 判斷是否為多物件（多個標註）影像
                    if len(current_file_labels) > 1:
                        for category in current_file_labels:
                            multi_object_stats[category]["total"] += 1
                            multi_object_stats[category]["unknown"] += 1
                            
            except Exception as e:
                print(f"❌ 處理JSON失敗: {json_file.name} - {str(e)}")
                pass
        
        # 📈 統計multi_class處理結果
        total_multi_annotations = sum(stats.get('unknown', 0) for stats in all_class_stats.values())
        total_multi_objects = sum(stats.get('unknown', 0) for stats in multi_object_stats.values())
        
        print(f"\n📈 Multi_class處理結果:")
        print(f"   總標註數: {total_multi_annotations}")
        print(f"   多物件標註數: {total_multi_objects}")
        print(f"   處理檔案數: {len(json_files)}")
    
    def _process_folder_for_standard_analysis(self, folder_path: Path, subfolder: str, 
                                            all_class_stats: dict, multi_object_stats: dict):
        """處理單個資料夾的標準分析"""
        if not folder_path.exists():
            return
            
        for json_file in folder_path.glob("*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                    
                # 用於追蹤當前JSON檔案中所有物件的類別
                current_file_labels = [] 
                
                # 檢查 'shapes' 鍵是否存在且為列表
                if "shapes" in json_data and isinstance(json_data["shapes"], list):
                    for shape in json_data["shapes"]:
                        category = shape.get("label")
                        if category:
                            # 執行所有類別的標準統計
                            all_class_stats[category]["total"] += 1
                            all_class_stats[category][subfolder] += 1
                            current_file_labels.append(category)
                    
                    # 判斷是否為多物件（多個標註）影像
                    if len(current_file_labels) > 1:
                        for category in current_file_labels:
                            multi_object_stats[category]["total"] += 1
                            multi_object_stats[category][subfolder] += 1
                            
            except Exception as e:
                # 静默失敗，不影響結果
                pass
    
    def update_categories_display(self):
        # 清除現有widget
        for widget in self.category_widgets.values():
            widget.setParent(None)
        self.category_widgets.clear()
        
        # 添加新的類別widget
        for category in self.categories:
            category_widget = self.create_category_widget(category)
            self.categories_layout.addWidget(category_widget)
            self.category_widgets[category['name']] = category_widget
        
        self.categories_layout.addStretch()
        self.update_execute_button()
    
    def create_category_widget(self, category):
        title_text = f"{category['name']} (multi_class: {category['multi_class_count']} + 單類別: {category['single_class_count']} = 總計: {category['total_count']} 個樣本)"
        
        group = QGroupBox(title_text)
        main_layout = QVBoxLayout(group)
        
        # 第一行：基本控制
        first_row = QHBoxLayout()
        
        # 啟用checkbox
        enabled_cb = QCheckBox("啟用")
        enabled_cb.setChecked(True)
        first_row.addWidget(enabled_cb)
        
        # 數量設定 (基於總計數量 = multi_class + 單類別)
        first_row.addWidget(QLabel("總需求數量:"))
        count_spin = QSpinBox()
        count_spin.setRange(0, category['total_count'])
        count_spin.setValue(min(50, category['total_count']))
        first_row.addWidget(count_spin)
        
        # 百分比設定 (基於總計數量)
        first_row.addWidget(QLabel("或百分比:"))
        percent_spin = QDoubleSpinBox()
        percent_spin.setRange(0, 100)
        percent_spin.setValue(10.0)
        percent_spin.setSuffix("%")
        first_row.addWidget(percent_spin)
        
        apply_percent_btn = QPushButton("套用百分比")
        first_row.addWidget(apply_percent_btn)
        
        first_row.addStretch()
        main_layout.addLayout(first_row)
        
        # 第二行：Multi-class 設定（當有multi_class數據時）
        if category['multi_class_count'] > 0:
            second_row = QHBoxLayout()
            
            second_row.addWidget(QLabel("Multi-class使用率:"))
            multi_class_percent_spin = QDoubleSpinBox()
            multi_class_percent_spin.setRange(0, 100)
            multi_class_percent_spin.setValue(100.0)  # 預設100%
            multi_class_percent_spin.setSuffix("%")
            multi_class_percent_spin.setToolTip(f"總需求數量中multi-class所占的百分比（可用: {category['multi_class_count']}個）")
            second_row.addWidget(multi_class_percent_spin)
            
            second_row.addStretch()
            main_layout.addLayout(second_row)
        else:
            # 沒有multi_class數據時，創建隱藏的控件
            multi_class_percent_spin = QDoubleSpinBox()
            multi_class_percent_spin.setValue(0.0)
            multi_class_percent_spin.setVisible(False)
        
        # 第三行：預覽資訊
        third_row = QHBoxLayout()
        preview_label = QLabel("預覽: multi_class 0 + 單類別 0 = 總計 0")
        preview_label.setStyleSheet("QLabel { color: blue; font-weight: bold; background-color: #f0f8ff; padding: 5px; border: 1px solid #87ceeb; border-radius: 3px; }")
        third_row.addWidget(preview_label)
        third_row.addStretch()
        main_layout.addLayout(third_row)
        
        # 更新預覽的函數（修改為基於總需求的百分比）
        def update_preview():
            total_demand = count_spin.value()
            
            if category['multi_class_count'] > 0 and total_demand > 0:
                multi_class_percent = multi_class_percent_spin.value()
                # 🔧 新邏輯：從總需求中計算multi_class所占百分比
                desired_multi_class = int(total_demand * multi_class_percent / 100)
                # 但不能超過可用的multi_class數量
                from_multi_class = min(desired_multi_class, category['multi_class_count'])
            else:
                from_multi_class = 0
            
            # 剩餘的從單類別提取
            remaining_needed = max(0, total_demand - from_multi_class)
            from_single_class = min(remaining_needed, category['single_class_count'])
            
            total_actual = from_multi_class + from_single_class
            
            # 更新預覽文字
            if category['multi_class_count'] > 0:
                preview_text = f"預覽: multi_class {from_multi_class}/{category['multi_class_count']} + 單類別 {from_single_class}/{category['single_class_count']} = 總計 {total_actual}"
            else:
                preview_text = f"預覽: 單類別 {from_single_class}/{category['single_class_count']} = 總計 {total_actual}"
            
            # 如果總計不足，用紅色顯示
            if total_actual < total_demand:
                preview_label.setStyleSheet("QLabel { color: red; font-weight: bold; background-color: #ffe4e1; padding: 5px; border: 1px solid #ff6b6b; border-radius: 3px; }")
                preview_text += f" (不足 {total_demand - total_actual})"
            else:
                preview_label.setStyleSheet("QLabel { color: blue; font-weight: bold; background-color: #f0f8ff; padding: 5px; border: 1px solid #87ceeb; border-radius: 3px; }")
            
            preview_label.setText(preview_text)
        
        def apply_percentage():
            percentage = percent_spin.value() / 100
            new_count = int(category['total_count'] * percentage)
            count_spin.setValue(min(new_count, category['total_count']))
            update_preview()
        
        # 連接信號
        apply_percent_btn.clicked.connect(apply_percentage)
        count_spin.valueChanged.connect(update_preview)
        multi_class_percent_spin.valueChanged.connect(update_preview)
        update_preview()  # 初始更新
        
        # 儲存控件引用
        group.enabled_cb = enabled_cb
        group.count_spin = count_spin
        group.percent_spin = percent_spin
        group.multi_class_percent_spin = multi_class_percent_spin
        group.preview_label = preview_label
        group.category_info = category
        
        return group
    
    def update_execute_button(self):
        can_execute = (
            bool(self.source_folder) and 
            bool(self.output_folder) and 
            bool(self.categories)
        )
        self.execute_btn.setEnabled(can_execute)
    
    def execute_extraction(self):
        # 收集設定
        category_settings = {}
        for name, widget in self.category_widgets.items():
            category_settings[name] = {
                'enabled': widget.enabled_cb.isChecked(),
                'count': widget.count_spin.value(),
                'multi_class_percent': widget.multi_class_percent_spin.value()  # 新增：每類別的multi_class百分比
            }
        
        # multi_class一定會提取，不需要全域設定
        multi_class_settings = {
            'enabled': True,  # 一定啟用
            'percent': 100.0  # 不再使用全域百分比
        }
        
        # 檢查是否有啟用的類別
        has_enabled = any(settings['enabled'] for settings in category_settings.values())
        if not has_enabled:
            QMessageBox.warning(self, "警告", "請至少啟用一個類別")
            return
        
        # 顯示提取預覽
        preview_text = "提取預覽：\n"
        total_multi_class_samples = 0
        total_single_class_samples = 0
        
        for name, widget in self.category_widgets.items():
            if widget.enabled_cb.isChecked():
                category_info = widget.category_info
                total_demand = widget.count_spin.value()
                multi_class_percent = widget.multi_class_percent_spin.value()
                
                # 🔧 計算各類別的multi_class和單類別揔取數量（新邏輯）
                if category_info['multi_class_count'] > 0 and total_demand > 0:
                    # 基於總需求的百分比來計算
                    desired_multi_class = int(total_demand * multi_class_percent / 100)
                    from_multi_class = min(desired_multi_class, category_info['multi_class_count'])
                else:
                    from_multi_class = 0
                
                remaining_needed = max(0, total_demand - from_multi_class)
                from_single_class = min(remaining_needed, category_info['single_class_count'])
                total_actual = from_multi_class + from_single_class
                
                total_multi_class_samples += from_multi_class
                total_single_class_samples += from_single_class
                
                status_indicator = ""
                if total_actual < total_demand:
                    status_indicator = f" ⚠️不足{total_demand - total_actual}個"
                
                if category_info['multi_class_count'] > 0:
                    preview_text += f"  {name}: multi_class {from_multi_class}/{category_info['multi_class_count']} + 單類別 {from_single_class}/{category_info['single_class_count']} = 總計 {total_actual}/{total_demand}{status_indicator}\n"
                else:
                    preview_text += f"  {name}: 單類別 {from_single_class}/{category_info['single_class_count']} = 總計 {total_actual}/{total_demand}{status_indicator}\n"
        
        preview_text += f"\n📊 總計提取: multi_class {total_multi_class_samples} + 單類別 {total_single_class_samples} = {total_multi_class_samples + total_single_class_samples} 個樣本\n"
        
        self.status_text.clear()
        self.status_text.append(preview_text)
        
        # 開始提取
        self.execute_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.status_text.append("開始提取樣本...")
        self.status_text.append("所有multi_class樣本一定會提取（按各類別設定的百分比）")
        
        # 創建工作線程
        self.worker = SampleExtractor(
            self.source_folder, 
            self.output_folder,
            category_settings,
            multi_class_settings,
            self.categories  # 傳遞完整的類別資訊
        )
        
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.status_text.append)
        self.worker.finished_signal.connect(self.extraction_finished)
        
        self.worker.start()
    
    def extraction_finished(self, success: bool, message: str):
        self.execute_btn.setEnabled(True)
        self.status_text.append(message)
        
        if success:
            QMessageBox.information(self, "完成", "樣本提取完成！\n分析報告已生成。")
        else:
            QMessageBox.critical(self, "錯誤", f"提取失敗: {message}")
    
    def _extract_categories_from_json(self, data) -> List[str]:
        """從json數據中提取類別列表（保留重複，因為每個標註都是一個樣本）"""
        categories = []
        
        # 嘗試不同的json結構
        if 'categories' in data:
            categories = data['categories']
        elif 'labels' in data:
            categories = data['labels']
        elif 'annotations' in data:
            # COCO格式
            for ann in data['annotations']:
                if 'category_id' in ann:
                    categories.append(str(ann['category_id']))
                elif 'category_name' in ann:
                    categories.append(ann['category_name'])
        elif 'shapes' in data:
            # LabelMe格式
            for shape in data['shapes']:
                if 'label' in shape:
                    categories.append(shape['label'])
        elif 'objects' in data:
            # 可能的格式：objects列表
            for obj in data['objects']:
                if isinstance(obj, dict):
                    if 'label' in obj:
                        categories.append(obj['label'])
                    elif 'category' in obj:
                        categories.append(obj['category'])
                    elif 'class' in obj:
                        categories.append(obj['class'])
        elif 'detections' in data:
            # 可能的格式：detections列表
            for det in data['detections']:
                if isinstance(det, dict):
                    if 'label' in det:
                        categories.append(det['label'])
                    elif 'class' in det:
                        categories.append(det['class'])
        
        # 如果以上都沒找到，嘗試直接查找常見的鍵
        if not categories:
            common_keys = ['class_names', 'class_labels', 'label_names', 'category_names']
            for key in common_keys:
                if key in data and isinstance(data[key], list):
                    categories = data[key]
                    break
        
        # 標準化類別名稱
        result = []
        for category in categories:
            if isinstance(category, dict) and 'name' in category:
                result.append(category['name'])
            elif isinstance(category, dict) and 'label' in category:
                result.append(category['label'])
            else:
                result.append(str(category))
        
        # 去除空字符串，但保留重複項（因為每個標註都是一個樣本）
        result = [cat.strip() for cat in result if cat and str(cat).strip()]
        
        return result


def main():
    app = QApplication(sys.argv)
    
    # 設定應用程式樣式 - 白色主題
    app.setStyleSheet("""
        QMainWindow {
            background-color: white;
        }
        QWidget {
            background-color: white;
            color: black;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #d0d0d0;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
            background-color: #fafafa;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            background-color: white;
        }
        QPushButton {
            background-color: #e0e0e0;
            border: 1px solid #c0c0c0;
            color: black;
            padding: 8px 16px;
            border-radius: 4px;
        }
        QPushButton:hover {
            background-color: #d0d0d0;
            border: 1px solid #a0a0a0;
        }
        QPushButton:pressed {
            background-color: #c0c0c0;
        }
        QPushButton:disabled {
            background-color: #f0f0f0;
            color: #808080;
            border: 1px solid #e0e0e0;
        }
        QLabel {
            background-color: transparent;
            color: black;
        }
        QLineEdit, QSpinBox, QDoubleSpinBox {
            background-color: white;
            border: 1px solid #c0c0c0;
            border-radius: 3px;
            padding: 4px;
            color: black;
        }
        QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {
            border: 2px solid #4CAF50;
        }
        QCheckBox {
            background-color: transparent;
            color: black;
        }
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #c0c0c0;
            background-color: white;
            border-radius: 2px;
        }
        QCheckBox::indicator:checked {
            background-color: #4CAF50;
            border: 1px solid #4CAF50;
        }
        QTextEdit {
            background-color: white;
            border: 1px solid #c0c0c0;
            border-radius: 3px;
            color: black;
        }
        QProgressBar {
            border: 1px solid #c0c0c0;
            border-radius: 5px;
            background-color: #f0f0f0;
            text-align: center;
        }
        QProgressBar::chunk {
            background-color: #4CAF50;
            border-radius: 4px;
        }
        QScrollArea {
            background-color: white;
            border: 1px solid #d0d0d0;
        }
        QComboBox {
            background-color: white;
            border: 1px solid #c0c0c0;
            border-radius: 3px;
            padding: 4px;
            color: black;
        }
        QComboBox:focus {
            border: 2px solid #4CAF50;
        }
    """)
    
    window = ImageSampleExtractorGUI()
    window.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()