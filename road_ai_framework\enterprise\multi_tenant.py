# 🏢 多租戶架構支援系統
# Phase 4 核心功能 - 企業級多用戶隔離

import asyncio
import logging
import hashlib
import jwt
import redis
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from enum import Enum
import json
import threading
from pathlib import Path
import uuid

class TenantType(Enum):
    """租戶類型枚舉"""
    INDIVIDUAL = "individual"      # 個人用戶
    SMALL_BUSINESS = "small_business"  # 小型企業
    ENTERPRISE = "enterprise"      # 大型企業  
    GOVERNMENT = "government"      # 政府機構

class SubscriptionTier(Enum):
    """訂閱層級枚舉"""
    FREE = "free"
    BASIC = "basic" 
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    UNLIMITED = "unlimited"

@dataclass
class ResourceQuota:
    """資源配額配置"""
    # API調用限制
    api_calls_per_hour: int = 100
    api_calls_per_day: int = 1000
    
    # 並發限制
    max_concurrent_requests: int = 5
    max_batch_size: int = 10
    
    # 存儲限制
    storage_limit_mb: int = 1000
    max_models: int = 3
    
    # 處理限制
    max_image_size_mb: int = 10
    max_processing_time: int = 300  # 秒
    
    # 功能限制
    enable_custom_models: bool = False
    enable_api_access: bool = True
    enable_batch_processing: bool = False
    enable_priority_processing: bool = False

@dataclass
class TenantProfile:
    """租戶檔案"""
    tenant_id: str
    tenant_name: str
    tenant_type: TenantType
    subscription_tier: SubscriptionTier
    
    # 聯繫信息
    contact_email: str
    contact_phone: Optional[str] = None
    organization: Optional[str] = None
    
    # 訂閱信息
    subscription_start: datetime = field(default_factory=datetime.now)
    subscription_end: Optional[datetime] = None
    is_active: bool = True
    
    # 資源配額
    quota: ResourceQuota = field(default_factory=ResourceQuota)
    
    # 使用統計
    current_api_calls: int = 0
    total_api_calls: int = 0
    last_activity: datetime = field(default_factory=datetime.now)
    
    # 自定義配置
    custom_settings: Dict[str, Any] = field(default_factory=dict)
    
    # 安全設置
    api_keys: List[str] = field(default_factory=list)
    allowed_ips: List[str] = field(default_factory=list)
    webhook_urls: List[str] = field(default_factory=list)

@dataclass
class TenantUsage:
    """租戶使用統計"""
    tenant_id: str
    date: datetime
    api_calls: int = 0
    processing_time: float = 0.0
    data_processed_mb: float = 0.0
    errors: int = 0
    cache_hits: int = 0
    concurrent_peak: int = 0

class TenantManager:
    """租戶管理器"""
    
    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379):
        self.logger = logging.getLogger(__name__)
        self.tenants: Dict[str, TenantProfile] = {}
        self.usage_stats: Dict[str, List[TenantUsage]] = {}
        self._lock = threading.Lock()
        
        # Redis連接用於分散式管理
        try:
            self.redis_client = redis.Redis(host=redis_host, port=redis_port, 
                                          decode_responses=True)
            self.redis_available = True
        except Exception as e:
            self.logger.warning(f"Redis連接失敗，使用本地緩存: {e}")
            self.redis_client = None
            self.redis_available = False
        
        # JWT密鑰
        self.jwt_secret = "your-secret-key-change-in-production"
        
        # 預設配額配置
        self.default_quotas = {
            SubscriptionTier.FREE: ResourceQuota(
                api_calls_per_hour=50,
                api_calls_per_day=200,
                max_concurrent_requests=2,
                storage_limit_mb=100,
                max_models=1
            ),
            SubscriptionTier.BASIC: ResourceQuota(
                api_calls_per_hour=500,
                api_calls_per_day=5000,
                max_concurrent_requests=5,
                storage_limit_mb=1000,
                max_models=3,
                enable_batch_processing=True
            ),
            SubscriptionTier.PROFESSIONAL: ResourceQuota(
                api_calls_per_hour=2000,
                api_calls_per_day=20000,
                max_concurrent_requests=10,
                storage_limit_mb=5000,
                max_models=10,
                enable_custom_models=True,
                enable_batch_processing=True,
                enable_priority_processing=True
            ),
            SubscriptionTier.ENTERPRISE: ResourceQuota(
                api_calls_per_hour=10000,
                api_calls_per_day=100000,
                max_concurrent_requests=50,
                storage_limit_mb=50000,
                max_models=50,
                enable_custom_models=True,
                enable_batch_processing=True,
                enable_priority_processing=True
            )
        }

    def create_tenant(self, tenant_name: str, tenant_type: TenantType,
                     subscription_tier: SubscriptionTier, 
                     contact_email: str, **kwargs) -> TenantProfile:
        """創建新租戶"""
        try:
            tenant_id = self._generate_tenant_id(tenant_name, contact_email)
            
            # 檢查租戶是否已存在
            if self._tenant_exists(tenant_id):
                raise ValueError(f"租戶已存在: {tenant_id}")
            
            # 創建租戶檔案
            tenant = TenantProfile(
                tenant_id=tenant_id,
                tenant_name=tenant_name,
                tenant_type=tenant_type,
                subscription_tier=subscription_tier,
                contact_email=contact_email,
                quota=self.default_quotas.get(subscription_tier, ResourceQuota()),
                **kwargs
            )
            
            # 生成API密鑰
            api_key = self._generate_api_key(tenant_id)
            tenant.api_keys = [api_key]
            
            # 存儲租戶
            with self._lock:
                self.tenants[tenant_id] = tenant
                self.usage_stats[tenant_id] = []
            
            # 持久化到Redis
            if self.redis_available:
                self._save_tenant_to_redis(tenant)
            
            self.logger.info(f"成功創建租戶: {tenant_id}")
            return tenant
            
        except Exception as e:
            self.logger.error(f"創建租戶失敗: {e}")
            raise

    def get_tenant(self, tenant_id: str) -> Optional[TenantProfile]:
        """獲取租戶信息"""
        if tenant_id in self.tenants:
            return self.tenants[tenant_id]
        
        # 嘗試從Redis加載
        if self.redis_available:
            tenant = self._load_tenant_from_redis(tenant_id)
            if tenant:
                self.tenants[tenant_id] = tenant
                return tenant
        
        return None

    def authenticate_tenant(self, api_key: str) -> Optional[TenantProfile]:
        """通過API密鑰驗證租戶"""
        try:
            # 解析JWT token
            payload = jwt.decode(api_key, self.jwt_secret, algorithms=['HS256'])
            tenant_id = payload.get('tenant_id')
            
            if not tenant_id:
                return None
            
            tenant = self.get_tenant(tenant_id)
            if not tenant or not tenant.is_active:
                return None
            
            # 檢查API密鑰是否有效
            if api_key not in tenant.api_keys:
                return None
            
            # 更新最後活動時間
            tenant.last_activity = datetime.now()
            
            return tenant
            
        except jwt.InvalidTokenError:
            self.logger.warning("無效的API密鑰")
            return None

    def check_quota(self, tenant_id: str, resource_type: str, 
                   amount: int = 1) -> bool:
        """檢查資源配額"""
        tenant = self.get_tenant(tenant_id)
        if not tenant or not tenant.is_active:
            return False
        
        quota = tenant.quota
        
        if resource_type == "api_calls_per_hour":
            current_hour_calls = self._get_current_hour_calls(tenant_id)
            return current_hour_calls + amount <= quota.api_calls_per_hour
            
        elif resource_type == "api_calls_per_day":
            current_day_calls = self._get_current_day_calls(tenant_id)
            return current_day_calls + amount <= quota.api_calls_per_day
            
        elif resource_type == "concurrent_requests":
            current_concurrent = self._get_current_concurrent_requests(tenant_id)
            return current_concurrent + amount <= quota.max_concurrent_requests
            
        elif resource_type == "storage":
            current_storage = self._get_current_storage_usage(tenant_id)
            return current_storage + amount <= quota.storage_limit_mb
        
        return True

    def consume_quota(self, tenant_id: str, resource_type: str, 
                     amount: int = 1):
        """消耗資源配額"""
        tenant = self.get_tenant(tenant_id)
        if not tenant:
            return
        
        # 更新使用統計
        self._update_usage_stats(tenant_id, resource_type, amount)
        
        # 更新租戶統計
        if resource_type == "api_calls":
            tenant.current_api_calls += amount
            tenant.total_api_calls += amount

    def _generate_tenant_id(self, tenant_name: str, email: str) -> str:
        """生成租戶ID"""
        data = f"{tenant_name}_{email}_{datetime.now().timestamp()}"
        return hashlib.md5(data.encode()).hexdigest()[:16]

    def _generate_api_key(self, tenant_id: str) -> str:
        """生成API密鑰"""
        payload = {
            'tenant_id': tenant_id,
            'created_at': datetime.now().timestamp(),
            'exp': datetime.now() + timedelta(days=365)  # 1年有效期
        }
        return jwt.encode(payload, self.jwt_secret, algorithm='HS256')

    def _tenant_exists(self, tenant_id: str) -> bool:
        """檢查租戶是否存在"""
        if tenant_id in self.tenants:
            return True
        
        if self.redis_available:
            return self.redis_client.exists(f"tenant:{tenant_id}")
        
        return False

    def _get_current_hour_calls(self, tenant_id: str) -> int:
        """獲取當前小時API調用數"""
        current_hour = datetime.now().replace(minute=0, second=0, microsecond=0)
        
        if self.redis_available:
            key = f"quota:{tenant_id}:hour:{current_hour.timestamp()}"
            calls = self.redis_client.get(key)
            return int(calls) if calls else 0
        
        # 本地統計
        today_usage = self._get_today_usage(tenant_id)
        if today_usage:
            return sum(1 for usage in today_usage 
                      if usage.date.hour == current_hour.hour)
        return 0

    def _get_current_day_calls(self, tenant_id: str) -> int:
        """獲取當天API調用數"""
        if self.redis_available:
            today = datetime.now().date()
            key = f"quota:{tenant_id}:day:{today}"
            calls = self.redis_client.get(key)
            return int(calls) if calls else 0
        
        # 本地統計
        today_usage = self._get_today_usage(tenant_id)
        return sum(usage.api_calls for usage in today_usage) if today_usage else 0

    def _get_current_concurrent_requests(self, tenant_id: str) -> int:
        """獲取當前並發請求數"""
        if self.redis_available:
            key = f"concurrent:{tenant_id}"
            concurrent = self.redis_client.get(key)
            return int(concurrent) if concurrent else 0
        
        return 0  # 簡化本地實現

    def _get_current_storage_usage(self, tenant_id: str) -> int:
        """獲取當前存儲使用量"""
        if self.redis_available:
            key = f"storage:{tenant_id}"
            storage = self.redis_client.get(key)
            return int(storage) if storage else 0
        
        return 0  # 簡化本地實現

    def _get_today_usage(self, tenant_id: str) -> List[TenantUsage]:
        """獲取今天的使用統計"""
        if tenant_id not in self.usage_stats:
            return []
        
        today = datetime.now().date()
        return [usage for usage in self.usage_stats[tenant_id] 
                if usage.date.date() == today]

    def _update_usage_stats(self, tenant_id: str, resource_type: str, amount: int):
        """更新使用統計"""
        now = datetime.now()
        
        # Redis統計
        if self.redis_available:
            if resource_type == "api_calls":
                # 小時統計
                hour_key = f"quota:{tenant_id}:hour:{now.replace(minute=0, second=0, microsecond=0).timestamp()}"
                self.redis_client.incr(hour_key, amount)
                self.redis_client.expire(hour_key, 3600)  # 1小時過期
                
                # 日統計
                day_key = f"quota:{tenant_id}:day:{now.date()}"
                self.redis_client.incr(day_key, amount)
                self.redis_client.expire(day_key, 86400)  # 1天過期
        
        # 本地統計
        if tenant_id not in self.usage_stats:
            self.usage_stats[tenant_id] = []
        
        # 查找今天的統計記錄
        today_usage = None
        for usage in self.usage_stats[tenant_id]:
            if usage.date.date() == now.date():
                today_usage = usage
                break
        
        if not today_usage:
            today_usage = TenantUsage(tenant_id=tenant_id, date=now)
            self.usage_stats[tenant_id].append(today_usage)
        
        # 更新統計
        if resource_type == "api_calls":
            today_usage.api_calls += amount

    def _save_tenant_to_redis(self, tenant: TenantProfile):
        """保存租戶信息到Redis"""
        if not self.redis_available:
            return
        
        try:
            tenant_data = {
                'tenant_name': tenant.tenant_name,
                'tenant_type': tenant.tenant_type.value,
                'subscription_tier': tenant.subscription_tier.value,
                'contact_email': tenant.contact_email,
                'is_active': tenant.is_active,
                'api_keys': json.dumps(tenant.api_keys),
                'quota': json.dumps(tenant.quota.__dict__),
                'custom_settings': json.dumps(tenant.custom_settings)
            }
            
            key = f"tenant:{tenant.tenant_id}"
            self.redis_client.hmset(key, tenant_data)
            
        except Exception as e:
            self.logger.error(f"保存租戶到Redis失敗: {e}")

    def _load_tenant_from_redis(self, tenant_id: str) -> Optional[TenantProfile]:
        """從Redis加載租戶信息"""
        if not self.redis_available:
            return None
        
        try:
            key = f"tenant:{tenant_id}"
            tenant_data = self.redis_client.hgetall(key)
            
            if not tenant_data:
                return None
            
            # 重構租戶對象
            tenant = TenantProfile(
                tenant_id=tenant_id,
                tenant_name=tenant_data['tenant_name'],
                tenant_type=TenantType(tenant_data['tenant_type']),
                subscription_tier=SubscriptionTier(tenant_data['subscription_tier']),
                contact_email=tenant_data['contact_email'],
                is_active=tenant_data['is_active'] == 'True',
                api_keys=json.loads(tenant_data.get('api_keys', '[]')),
                custom_settings=json.loads(tenant_data.get('custom_settings', '{}'))
            )
            
            # 重構配額
            quota_data = json.loads(tenant_data.get('quota', '{}'))
            if quota_data:
                tenant.quota = ResourceQuota(**quota_data)
            
            return tenant
            
        except Exception as e:
            self.logger.error(f"從Redis加載租戶失敗: {e}")
            return None

    def update_tenant_subscription(self, tenant_id: str, 
                                 new_tier: SubscriptionTier) -> bool:
        """更新租戶訂閱層級"""
        tenant = self.get_tenant(tenant_id)
        if not tenant:
            return False
        
        old_tier = tenant.subscription_tier
        tenant.subscription_tier = new_tier
        tenant.quota = self.default_quotas.get(new_tier, ResourceQuota())
        
        # 持久化更新
        if self.redis_available:
            self._save_tenant_to_redis(tenant)
        
        self.logger.info(f"租戶 {tenant_id} 訂閱從 {old_tier.value} 升級到 {new_tier.value}")
        return True

    def get_tenant_analytics(self, tenant_id: str, 
                           days: int = 30) -> Dict[str, Any]:
        """獲取租戶分析數據"""
        tenant = self.get_tenant(tenant_id)
        if not tenant:
            return {}
        
        # 獲取使用統計
        usage_history = self.usage_stats.get(tenant_id, [])
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_usage = [u for u in usage_history if u.date >= cutoff_date]
        
        total_calls = sum(u.api_calls for u in recent_usage)
        total_processing_time = sum(u.processing_time for u in recent_usage)
        total_errors = sum(u.errors for u in recent_usage)
        
        analytics = {
            'tenant_info': {
                'tenant_id': tenant_id,
                'tenant_name': tenant.tenant_name,
                'subscription_tier': tenant.subscription_tier.value,
                'account_age_days': (datetime.now() - tenant.subscription_start).days
            },
            'usage_summary': {
                'total_api_calls': total_calls,
                'total_processing_time': total_processing_time,
                'error_rate': total_errors / max(total_calls, 1),
                'avg_processing_time': total_processing_time / max(total_calls, 1)
            },
            'quota_utilization': {
                'api_calls_daily': self._get_current_day_calls(tenant_id) / tenant.quota.api_calls_per_day,
                'api_calls_hourly': self._get_current_hour_calls(tenant_id) / tenant.quota.api_calls_per_hour,
                'storage': self._get_current_storage_usage(tenant_id) / tenant.quota.storage_limit_mb
            },
            'daily_usage': [
                {
                    'date': u.date.date().isoformat(),
                    'api_calls': u.api_calls,
                    'processing_time': u.processing_time,
                    'errors': u.errors
                }
                for u in recent_usage
            ]
        }
        
        return analytics

    def list_tenants(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """列出所有租戶"""
        tenants_list = []
        
        for tenant in self.tenants.values():
            if active_only and not tenant.is_active:
                continue
            
            tenants_list.append({
                'tenant_id': tenant.tenant_id,
                'tenant_name': tenant.tenant_name,
                'tenant_type': tenant.tenant_type.value,
                'subscription_tier': tenant.subscription_tier.value,
                'contact_email': tenant.contact_email,
                'is_active': tenant.is_active,
                'last_activity': tenant.last_activity.isoformat(),
                'total_api_calls': tenant.total_api_calls
            })
        
        return sorted(tenants_list, key=lambda x: x['last_activity'], reverse=True)


class TenantMiddleware:
    """租戶中間件 - 用於API請求處理"""
    
    def __init__(self, tenant_manager: TenantManager):
        self.tenant_manager = tenant_manager
        self.logger = logging.getLogger(__name__)

    async def authenticate_request(self, api_key: str, 
                                 request_type: str = "api_calls") -> Optional[TenantProfile]:
        """驗證請求並檢查配額"""
        # 驗證租戶
        tenant = self.tenant_manager.authenticate_tenant(api_key)
        if not tenant:
            self.logger.warning("租戶驗證失敗")
            return None
        
        # 檢查配額
        if not self.tenant_manager.check_quota(tenant.tenant_id, f"{request_type}_per_hour"):
            self.logger.warning(f"租戶 {tenant.tenant_id} 超出小時配額")
            return None
        
        if not self.tenant_manager.check_quota(tenant.tenant_id, f"{request_type}_per_day"):
            self.logger.warning(f"租戶 {tenant.tenant_id} 超出日配額")
            return None
        
        # 消耗配額
        self.tenant_manager.consume_quota(tenant.tenant_id, request_type)
        
        return tenant

    def get_tenant_context(self, tenant: TenantProfile) -> Dict[str, Any]:
        """獲取租戶上下文信息"""
        return {
            'tenant_id': tenant.tenant_id,
            'subscription_tier': tenant.subscription_tier.value,
            'quota': tenant.quota.__dict__,
            'custom_settings': tenant.custom_settings
        }


# 使用示例和測試
if __name__ == "__main__":
    # 配置日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建租戶管理器
    manager = TenantManager()
    
    # 創建測試租戶
    tenant = manager.create_tenant(
        tenant_name="測試企業",
        tenant_type=TenantType.ENTERPRISE,
        subscription_tier=SubscriptionTier.PROFESSIONAL,
        contact_email="<EMAIL>",
        organization="Test Company Ltd."
    )
    
    print(f"✅ 創建租戶: {tenant.tenant_id}")
    print(f"   API密鑰: {tenant.api_keys[0][:20]}...")
    print(f"   訂閱層級: {tenant.subscription_tier.value}")
    print(f"   小時配額: {tenant.quota.api_calls_per_hour}")
    
    # 測試配額檢查
    api_key = tenant.api_keys[0]
    middleware = TenantMiddleware(manager)
    
    async def test_requests():
        # 模擬API請求
        for i in range(5):
            authenticated_tenant = await middleware.authenticate_request(api_key)
            if authenticated_tenant:
                print(f"✅ 請求 {i+1} 驗證成功")
            else:
                print(f"❌ 請求 {i+1} 驗證失敗")
    
    # 運行測試
    asyncio.run(test_requests())
    
    # 顯示分析數據
    analytics = manager.get_tenant_analytics(tenant.tenant_id)
    print(f"\n📊 租戶分析:")
    print(f"   總API調用: {analytics['usage_summary']['total_api_calls']}")
    print(f"   日配額使用率: {analytics['quota_utilization']['api_calls_daily']:.1%}")
    
    # 列出所有租戶
    tenants_list = manager.list_tenants()
    print(f"\n📋 租戶列表: {len(tenants_list)} 個活躍租戶")