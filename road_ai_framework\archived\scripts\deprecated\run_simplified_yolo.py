#!/usr/bin/env python3
"""
簡化YOLO推理系統 - 可直接運行的示例腳本
類似於 enhanced_yolo_usage.py 但使用重構後的簡化API
"""

import sys
import os
from pathlib import Path

# 添加模組路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """主函數 - 直接運行YOLO推理"""
    
    # ==================== 參數設定區域 ====================
    # 🔧 在這裡修改您的參數設定
    
    # 模型路徑設定
    detection_model_path = "path/to/your/yolo_detection.pt"      # YOLO檢測模型
    segmentation_model_path = "path/to/your/yolo_segmentation.pt" # YOLO分割模型
    
    # 輸入輸出路徑
    input_path = "./test_image"          # 輸入圖片路徑或資料夾
    output_path = "./output"             # 輸出資料夾
    
    # 推理參數
    confidence = 0.25                    # 信心度閾值
    task_type = "auto"                   # 任務類型: "detection", "segmentation", "auto"
    
    # SAHI 設定 (大圖像切片推理)
    enable_sahi = False                  # 是否啟用SAHI
    slice_size = 640                     # 切片大小
    overlap_ratio = 0.2                  # 重疊比例
    
    # 設備設定
    device = "cuda"                      # "auto", "cpu", "cuda", "mps"
    
    # ==================== 開始執行 ====================
    
    print("🚀 簡化YOLO推理系統")
    print("=" * 60)
    
    try:
        # 檢查並導入模組
        print("📦 檢查模組依賴...")
        try:
            from models.inference.simplified import SimplifiedYOLO, quick_predict, print_dependency_report
            print("✅ 模組導入成功")
        except ImportError as e:
            print(f"❌ 模組導入失敗: {e}")
            print("請確保您在 road_ai_framework 目錄中運行此腳本")
            return
        
        # 檢查系統依賴
        print("\n🔍 檢查系統依賴...")
        try:
            print_dependency_report()
        except Exception as e:
            print(f"⚠️ 依賴檢查失敗: {e}")
        
        # 選擇模型路徑
        model_path = segmentation_model_path if segmentation_model_path and os.path.exists(segmentation_model_path) else detection_model_path
        
        print(f"\n📋 執行參數:")
        print(f"  模型路徑: {model_path}")
        print(f"  輸入路徑: {input_path}")
        print(f"  輸出路徑: {output_path}")
        print(f"  信心度: {confidence}")
        print(f"  任務類型: {task_type}")
        print(f"  SAHI: {'啟用' if enable_sahi else '停用'}")
        print(f"  設備: {device}")
        
        # 檢查輸入路徑
        if not os.path.exists(input_path):
            print(f"\n❌ 輸入路徑不存在: {input_path}")
            print("請修改 input_path 參數指向正確的圖片路徑或資料夾")
            return
        
        # 檢查模型路徑
        if not os.path.exists(model_path):
            print(f"\n❌ 模型路徑不存在: {model_path}")
            print("請修改 detection_model_path 或 segmentation_model_path 參數")
            print("或者下載相應的YOLO模型檔案")
            return
        
        # 創建輸出目錄
        os.makedirs(output_path, exist_ok=True)
        
        print(f"\n🎯 開始推理...")
        print("-" * 40)
        
        # 方法1: 使用快速推理函數
        if os.path.isfile(input_path):
            print(f"📸 處理單張圖片: {input_path}")
            try:
                result = quick_predict(
                    model_path=model_path,
                    image_path=input_path,
                    confidence=confidence,
                    output_dir=output_path,
                    enable_sahi=enable_sahi,
                    device=device
                )
                print(f"✅ 推理完成，結果保存至: {output_path}")
                if result:
                    print(f"📊 檢測結果: {len(result.get('boxes', []))} 個物件")
                
            except Exception as e:
                print(f"❌ 推理失敗: {e}")
        
        # 方法2: 使用類別導向API處理資料夾
        else:
            print(f"📁 處理資料夾: {input_path}")
            try:
                # 創建YOLO實例
                if enable_sahi:
                    yolo = SimplifiedYOLO.with_sahi(
                        model_path=model_path,
                        slice_size=slice_size,
                        overlap_ratio=overlap_ratio,
                        device=device
                    )
                else:
                    yolo = SimplifiedYOLO.from_model(
                        model_path=model_path,
                        confidence=confidence,
                        device=device
                    )
                
                # 批次處理
                results = yolo.predict_batch(input_path, output_path)
                
                print(f"✅ 批次推理完成")
                print(f"📊 處理了 {len(results)} 張圖片")
                print(f"💾 結果保存至: {output_path}")
                
            except Exception as e:
                print(f"❌ 批次推理失敗: {e}")
        
        print("\n🎉 推理完成!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⏹️ 用戶中斷執行")
    except Exception as e:
        print(f"\n💥 執行失敗: {e}")
        import traceback
        traceback.print_exc()


def quick_demo():
    """快速演示（不需要實際模型）"""
    print("🎯 簡化YOLO推理系統演示")
    print("=" * 50)
    
    try:
        from models.inference.simplified import SimplifiedYOLO, SimplifiedYOLOConfig
        
        # 演示配置創建
        print("1️⃣ 配置創建演示:")
        config = SimplifiedYOLOConfig(
            segmentation_model_path="demo_model.pt",
            global_conf=0.3,
            enable_sahi=True,
            slice_size=512
        )
        print(f"✅ 配置創建成功，參數數量: {len(config.__dict__)}")
        
        # 演示API使用
        print("\n2️⃣ API使用演示:")
        print("   SimplifiedYOLO.from_model('model.pt', confidence=0.3)")
        print("   SimplifiedYOLO.with_sahi('model.pt', slice_size=640)")
        print("   SimplifiedYOLO.detection_only('detection_model.pt')")
        
        # 演示配置總結
        print("\n3️⃣ 配置總結:")
        summary = config.summary()
        print(summary[:200] + "..." if len(summary) > 200 else summary)
        
        print("\n✅ 演示完成！")
        
    except ImportError as e:
        print(f"❌ 演示失敗: {e}")


def print_usage_guide():
    """顯示使用指南"""
    print("""
🚀 簡化YOLO推理系統使用指南
========================================

📋 快速開始:
1. 修改本腳本頂部的參數設定區域
2. 設定正確的模型路徑和輸入路徑
3. 直接運行: python run_simplified_yolo.py

📋 主要參數說明:
• detection_model_path: YOLO檢測模型路徑
• segmentation_model_path: YOLO分割模型路徑  
• input_path: 輸入圖片路徑或資料夾
• output_path: 輸出結果資料夾
• confidence: 信心度閾值 (0.0-1.0)
• enable_sahi: 是否啟用大圖像切片推理
• device: 運算設備 ("auto", "cpu", "cuda")

📋 支援的任務類型:
• detection: 物件檢測
• segmentation: 語義分割
• auto: 自動檢測 (根據模型類型)

📋 API使用範例:
```python
from models.inference.simplified import SimplifiedYOLO

# 快速推理
yolo = SimplifiedYOLO.from_model("model.pt")
result = yolo.predict("image.jpg")

# 啟用SAHI
yolo = SimplifiedYOLO.with_sahi("model.pt", slice_size=640)
result = yolo.predict("large_image.jpg")

# 批次處理
results = yolo.predict_batch("input_dir", "output_dir")
```

📋 相比原版改進:
• 參數減少 79% (70個 → 15個)
• 代碼簡化 97% (~100行 → ~3行)
• 自動類別檢測
• 統一API設計
• 向後兼容支援

📞 如需幫助，請查看:
• examples/simplified_yolo_usage.py
• tests/test_simplified_yolo.py
• models/inference/simplified/README.md
""")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "demo":
            quick_demo()
        elif sys.argv[1] == "help":
            print_usage_guide()
        else:
            print("❌ 未知參數，支援的參數: demo, help")
    else:
        main()