#!/usr/bin/env python3
"""
🚀 高級切片推理系統 (Advanced Slice Inference System)
通用檢測與分割解決方案

🌟 核心功能:
- 支援任意模型框架 (YOLO, MMDetection, Detectron2等)
- 智能切片策略和ROI後處理
- 7種先進的物件融合算法 (NMS, Soft-NMS, WBF, Cluster-NMS, None等)
- 相鄰同類別區域智能合併
- 3級詳細日誌系統
- 類別過濾和排除功能
- 整體圖像二次檢測
- 完整的檢測+分割輸出

📊 支援的融合策略:
1. Standard NMS - 經典非極大值抑制
2. Soft-NMS - 軟抑制，避免過度移除
3. Weighted Boxes Fusion (WBF) - 加權框融合
4. DIoU-NMS - 距離IoU增強NMS
5. Cluster-NMS - 聚類NMS，適合擁擠場景
6. Largest Object - 保留最大物件策略
7. None - 不進行融合，直接返回原始檢測結果

🎯 日誌級別:
- detailed: 切片數量、類別預測統計、處理時間
- lightweight: 切片數量、總預測數量 (默認)
- none: 無輸出
"""

from ensemble_boxes import nms, soft_nms, weighted_boxes_fusion
import cv2
import numpy as np
import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass, field
from enum import Enum
import importlib

# 嘗試導入可選依賴
import torch
TORCH_AVAILABLE = True
ENSEMBLE_BOXES_AVAILABLE = True


class FusionStrategy(Enum):
    """物件融合策略枚舉"""
    STANDARD_NMS = "standard_nms"
    SOFT_NMS = "soft_nms"
    WEIGHTED_BOXES_FUSION = "wbf"
    DIOU_NMS = "diou_nms"
    CLUSTER_NMS = "cluster_nms"
    LARGEST_OBJECT = "largest_object"
    NO_FUSION = "no_fusion" # 新增: 不進行融合


class LogLevel(Enum):
    """日誌級別枚舉"""
    DETAILED = "detailed"
    LIGHTWEIGHT = "lightweight"
    NONE = "none"


@dataclass
class SliceConfig:
    """切片配置類"""
    slice_height: int = 640
    slice_width: int = 640
    overlap_height_ratio: float = 0.3
    overlap_width_ratio: float = 0.3

    # 高級切片參數
    auto_slice_resolution: bool = True  # 自動調整切片解析度
    min_slice_size: int = 320          # 最小切片尺寸
    adaptive_overlap: bool = True       # 自適應重疊比例


@dataclass
class FusionConfig:
    """融合配置類"""
    strategy: FusionStrategy = FusionStrategy.WEIGHTED_BOXES_FUSION
    iou_threshold: float = 0.5
    confidence_threshold: float = 0.05

    # Soft-NMS特定參數
    soft_nms_sigma: float = 0.5
    soft_nms_min_score: float = 0.001

    # WBF特定參數
    wbf_skip_box_threshold: float = 0.0001
    # "avg", "max", "box_and_model_avg", "absent_model_aware_avg"
    wbf_conf_type: str = "avg"

    # Cluster-NMS特定參數
    cluster_nms_iou_threshold: float = 0.5
    cluster_beta: float = 0.6


@dataclass
class ProcessingConfig:
    """處理配置類"""
    enable_overall_inference: bool = True    # 整體圖像二次檢測
    enable_adjacent_merge: bool = True       # 相鄰區域合併
    adjacent_distance_threshold: float = 50.0  # 相鄰距離閾值

    enable_roi_processing: bool = False    # 新增: 啟用ROI處理
    roi_top_ratio: float = 2.0             # 新增: ROI頂部比例
    roi_bottom_ratio: float = 2.0          # 新增: ROI底部比例
    roi_left_ratio: float = 2.0            # 新增: ROI左側比例
    roi_right_ratio: float = 2.0           # 新增: ROI右側比例

    # 類別過濾與置信度
    excluded_class_ids: List[int] = field(default_factory=list)
    excluded_class_names: List[str] = field(default_factory=list)
    included_class_ids: Optional[List[int]] = None  # 如果設定，只保留這些類別
    # 類別特定的置信度閾值
    class_confidence_thresholds: Optional[Dict[int, float]] = None

    # 類別顯示信息 (從 run_unified_yolo.py 傳入)
    # {class_id: {'display_name': str, 'color': List[int]}}
    class_display_info: Optional[Dict[int, Dict[str, Any]]] = None
    label_aliases: Optional[Dict[str, str]] = None  # 標籤別名映射
    # {canonical_name: {'class_id': int, 'display_name': str, 'color': List[int]}}
    class_info_by_name: Optional[Dict[str, Dict[str, Any]]] = None

    # 輸出控制
    return_masks: bool = True
    return_boxes: bool = True
    mask_threshold: float = 0.5
    fill_alpha: float = 0.4  # mask填充透明度

    # 高級參數
    enable_mask_refinement: bool = True      # mask精煉
    enable_geometric_filtering: bool = True   # 幾何過濾
    min_object_area: int = 100              # 最小物件面積

    # 字體配置
    font_size: float = 0.7
    font_thickness: int = 2
    enable_roi_preview: bool = False


@dataclass
class LogConfig:
    """日誌配置類"""
    level: LogLevel = LogLevel.LIGHTWEIGHT
    save_debug_images: bool = False
    debug_output_dir: Optional[str] = None
    enable_timing_breakdown: bool = False
    # 新增: "unified", "intelligent", "simple", "outline_only"
    mask_render_mode: str = "unified"


class ModelWrapper:
    """通用模型包裝器"""

    def __init__(self, model: Any, model_type: str = "auto"):
        """
        初始化模型包裝器

        Args:
            model: 模型對象
            model_type: 模型類型 ("yolo", "detectron2", "mmdet", "huggingface", "auto")
        """
        self.model = model
        self.model_type = self._detect_model_type(
            model) if model_type == "auto" else model_type
        self.logger = logging.getLogger(__name__)

    def _detect_model_type(self, model) -> str:
        """自動檢測模型類型"""
        model_class = str(type(model))

        if "ultralytics" in model_class.lower() or "yolo" in model_class.lower():
            return "yolo"
        elif "detectron2" in model_class.lower():
            return "detectron2"
        elif "mmdet" in model_class.lower():
            return "mmdet"
        elif "transformers" in model_class.lower():
            return "huggingface"
        else:
            return "custom"

    def predict(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """
        統一預測接口

        Returns:
            List of detections: [{'bbox': [x1,y1,x2,y2], 'confidence': float, 
                                 'class_id': int, 'class_name': str, 'mask': np.ndarray}, ...]
        """
        if self.model_type == "yolo":
            return self._predict_yolo(image)
        # elif self.model_type == "detectron2":
            # return self._predict_detectron2(image)
        # elif self.model_type == "mmdet":
            # return self._predict_mmdet(image)
        # elif self.model_type == "huggingface":
            # return self._predict_huggingface(image)
        else:
            return self._predict_custom(image)

    def _predict_yolo(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """YOLO模型預測"""
        try:
            results = self.model(image)
            detections = []

            for result in results:
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()
                    class_ids = result.boxes.cls.cpu().numpy().astype(int)

                    # 處理masks - 支援檢測和分割模型
                    masks = None
                    if hasattr(result, 'masks') and result.masks is not None:
                        # 分割模型的mask處理
                        masks = result.masks.data.cpu().numpy()
                        self.logger.debug(f"檢測到分割mask: {masks.shape}")

                    for i, (box, conf, cls_id) in enumerate(zip(boxes, confidences, class_ids)):
                        # 處理mask數據
                        mask = None
                        if masks is not None and i < len(masks):
                            mask = masks[i]
                            # 確保mask是正確的格式和尺寸
                            if mask.ndim == 3:
                                mask = mask[0]  # 取第一個通道

                            # 將mask轉換為uint8格式
                            if mask.dtype != np.uint8:
                                mask = (mask * 255).astype(np.uint8)

                            # 確保mask尺寸與圖像匹配
                            if mask.shape != image.shape[:2]:
                                mask = cv2.resize(mask, (image.shape[1], image.shape[0]),
                                                  interpolation=cv2.INTER_NEAREST)

                        detection = {
                            'bbox': box.tolist(),
                            'confidence': float(conf),
                            'class_id': int(cls_id),
                            'class_name': self.model.names.get(cls_id, f"class_{cls_id}"),
                            'mask': mask
                        }
                        detections.append(detection)

            return detections

        except Exception as e:
            self.logger.error(f"YOLO prediction error: {e}")
            return []

    def _predict_custom(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """自定義模型預測"""
        try:
            # 假設自定義模型有predict方法
            if hasattr(self.model, 'predict'):
                return self.model.predict(image)
            else:
                raise AttributeError(
                    "Custom model must implement 'predict' method")
        except Exception as e:
            self.logger.error(f"Custom model prediction error: {e}")
            return []


class AdvancedFusionEngine:
    """高級融合引擎"""

    def __init__(self, fusion_config: FusionConfig):
        self.config = fusion_config
        self.logger = logging.getLogger(__name__)

    def fuse_detections(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """融合檢測結果"""
        if not detections:
            return []

        strategy = self.config.strategy

        if strategy == FusionStrategy.STANDARD_NMS:
            return self._standard_nms(detections)
        elif strategy == FusionStrategy.SOFT_NMS:
            return self._soft_nms(detections)
        elif strategy == FusionStrategy.WEIGHTED_BOXES_FUSION:
            return self._weighted_boxes_fusion(detections)
        elif strategy == FusionStrategy.DIOU_NMS:
            return self._diou_nms(detections)
        elif strategy == FusionStrategy.CLUSTER_NMS:
            return self._cluster_nms(detections)
        elif strategy == FusionStrategy.LARGEST_OBJECT:
            return self._largest_object_strategy(detections)
        elif strategy == FusionStrategy.NO_FUSION:
            self.logger.info("不進行融合，直接返回原始檢測結果。")
            return detections
        else:
            self.logger.warning(
                f"未知融合策略: {strategy}, 使用標準NMS。")
            return self._standard_nms(detections)

    def _standard_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """標準NMS實現"""
        if not detections:
            return []

        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        final_detections = []
        for class_id, class_dets in class_groups.items():
            # 按置信度排序
            class_dets.sort(key=lambda x: x['confidence'], reverse=True)

            keep = []
            while class_dets:
                current = class_dets.pop(0)
                keep.append(current)

                # 移除與當前框重疊度高的框
                remaining = []
                for det in class_dets:
                    iou = self._calculate_iou(current['bbox'], det['bbox'])
                    if iou < self.config.iou_threshold:
                        remaining.append(det)
                class_dets = remaining

            final_detections.extend(keep)

        return final_detections

    def _soft_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Soft-NMS實現 - 軟化置信度而非完全移除重疊框"""
        if not detections:
            return []

        # 如果有ensemble-boxes，使用其實現
        if ENSEMBLE_BOXES_AVAILABLE:
            try:
                # 轉換為ensemble-boxes格式
                boxes_list = []
                scores_list = []
                labels_list = []

                for det in detections:
                    bbox = det['bbox']
                    # 歸一化座標 (假設圖像尺寸)
                    boxes_list.append(
                        [bbox[0]/1000, bbox[1]/1000, bbox[2]/1000, bbox[3]/1000])
                    scores_list.append(det['confidence'])
                    labels_list.append(det['class_id'])

                # 執行soft-NMS
                boxes, scores, labels = soft_nms([boxes_list], [scores_list], [labels_list],
                                                 iou_thr=self.config.iou_threshold,
                                                 sigma=self.config.soft_nms_sigma,
                                                 thresh=self.config.soft_nms_min_score)

                # 轉換回標準格式
                result = []
                for i, (box, score, label) in enumerate(zip(boxes, scores, labels)):
                    if i < len(detections):
                        det = detections[i].copy()
                        det['bbox'] = [box[0]*1000, box[1]
                                       * 1000, box[2]*1000, box[3]*1000]
                        det['confidence'] = score
                        result.append(det)

                return result
            except Exception as e:
                self.logger.warning(
                    f"Ensemble-boxes soft-NMS failed: {e}, using manual implementation")

        # 手動實現Soft-NMS
        result = []
        remaining = detections.copy()

        # 按類別分組處理
        class_groups = {}
        for det in remaining:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        for class_id, class_dets in class_groups.items():
            # 按置信度排序
            class_dets.sort(key=lambda x: x['confidence'], reverse=True)

            # Soft-NMS處理
            i = 0
            while i < len(class_dets):
                current = class_dets[i]

                # 對後續所有框應用軟化
                j = i + 1
                while j < len(class_dets):
                    other = class_dets[j]
                    iou = self._calculate_iou(current['bbox'], other['bbox'])

                    if iou > 0:
                        # 使用高斯軟化函數
                        decay = np.exp(-(iou * iou) /
                                       self.config.soft_nms_sigma)
                        other['confidence'] *= decay

                        # 移除置信度過低的框
                        if other['confidence'] < self.config.soft_nms_min_score:
                            class_dets.pop(j)
                            continue

                    j += 1

                i += 1

            result.extend(class_dets)

        return result

    def _weighted_boxes_fusion(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """加權框融合實現 - 使用加權平均融合重疊框"""
        if not detections:
            return []

        # 如果有ensemble-boxes，使用其實現
        if ENSEMBLE_BOXES_AVAILABLE:
            try:
                # 轉換為ensemble-boxes格式
                boxes_list = []
                scores_list = []
                labels_list = []

                for det in detections:
                    bbox = det['bbox']
                    # 歸一化座標 (假設圖像尺寸1000x1000)
                    boxes_list.append(
                        [bbox[0]/1000, bbox[1]/1000, bbox[2]/1000, bbox[3]/1000])
                    scores_list.append(det['confidence'])
                    labels_list.append(det['class_id'])

                # 執行WBF
                boxes, scores, labels = weighted_boxes_fusion([boxes_list], [scores_list], [labels_list],
                                                              weights=None,
                                                              iou_thr=self.config.iou_threshold,
                                                              skip_box_thr=self.config.wbf_skip_box_threshold,
                                                              conf_type=self.config.wbf_conf_type)

                # 轉換回標準格式
                result = []
                for box, score, label in zip(boxes, scores, labels):
                    # 尋找原始檢測以獲取mask
                    original_det = None
                    for det in detections:
                        if det['class_id'] == int(label):
                            original_det = det
                            break

                    new_det = {
                        'bbox': [box[0]*1000, box[1]*1000, box[2]*1000, box[3]*1000],
                        'confidence': score,
                        'class_id': int(label),
                        'class_name': original_det['class_name'] if original_det else f"class_{int(label)}",
                        'mask': original_det['mask'] if original_det else None
                    }
                    result.append(new_det)

                return result
            except Exception as e:
                self.logger.warning(
                    f"Ensemble-boxes WBF failed: {e}, using manual implementation")

        # 手動實現簡化版WBF
        return self._manual_wbf(detections)

    def _manual_wbf(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """手動實現的簡化版WBF"""
        if not detections:
            return []

        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        result = []
        for class_id, class_dets in class_groups.items():
            if not class_dets:
                continue

            # 找到所有重疊的框組
            groups = []
            used = set()

            for i, det1 in enumerate(class_dets):
                if i in used:
                    continue

                group = [det1]
                used.add(i)

                for j, det2 in enumerate(class_dets[i+1:], i+1):
                    if j in used:
                        continue

                    iou = self._calculate_iou(det1['bbox'], det2['bbox'])
                    if iou > self.config.iou_threshold:
                        group.append(det2)
                        used.add(j)

                groups.append(group)

            # 對每個組進行加權融合
            for group in groups:
                if len(group) == 1:
                    result.append(group[0])
                else:
                    fused_det = self._fuse_detection_group(group)
                    result.append(fused_det)

        return result

    def _fuse_detection_group(self, group: List[Dict[str, Any]]) -> Dict[str, Any]:
        """融合檢測組"""
        if len(group) == 1:
            return group[0]

        # 計算加權平均座標
        total_confidence = sum(det['confidence'] for det in group)
        weights = [det['confidence'] / total_confidence for det in group]

        # 加權平均邊界框
        fused_bbox = [0, 0, 0, 0]
        for i, det in enumerate(group):
            bbox = det['bbox']
            weight = weights[i]
            fused_bbox[0] += bbox[0] * weight
            fused_bbox[1] += bbox[1] * weight
            fused_bbox[2] += bbox[2] * weight
            fused_bbox[3] += bbox[3] * weight

        # 選擇最高置信度的檢測作為基礎
        best_det = max(group, key=lambda x: x['confidence'])

        # 融合masks（如果有）
        fused_mask = None
        if any(det['mask'] is not None for det in group):
            masks = [det['mask'] for det in group if det['mask'] is not None]
            if masks:
                fused_mask = masks[0].copy()
                for mask in masks[1:]:
                    if mask.shape == fused_mask.shape:
                        fused_mask = cv2.bitwise_or(fused_mask, mask)

        return {
            'bbox': fused_bbox,
            'confidence': max(det['confidence'] for det in group),  # 使用最高置信度
            'class_id': best_det['class_id'],
            'class_name': best_det['class_name'],
            'mask': fused_mask
        }

    def _diou_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """DIoU-NMS實現"""
        if not detections:
            return []

        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        final_detections = []
        for class_id, class_dets in class_groups.items():
            # 按置信度排序
            class_dets.sort(key=lambda x: x['confidence'], reverse=True)

            keep = []
            while class_dets:
                current = class_dets.pop(0)
                keep.append(current)

                # 使用DIoU計算重疊度
                remaining = []
                for det in class_dets:
                    diou = self._calculate_diou(current['bbox'], det['bbox'])
                    if diou < self.config.iou_threshold:
                        remaining.append(det)
                class_dets = remaining

            final_detections.extend(keep)

        return final_detections

    def _cluster_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Cluster-NMS實現 - 適合密集場景的聚類NMS"""
        if not detections:
            return []

        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        result = []
        for class_id, class_dets in class_groups.items():
            if not class_dets:
                continue

            # 按置信度排序
            class_dets.sort(key=lambda x: x['confidence'], reverse=True)

            # 建立鄰接矩陣
            n = len(class_dets)
            adjacency_matrix = np.zeros((n, n), dtype=bool)

            for i in range(n):
                for j in range(i+1, n):
                    iou = self._calculate_iou(
                        class_dets[i]['bbox'], class_dets[j]['bbox'])
                    if iou > self.config.cluster_nms_iou_threshold:
                        adjacency_matrix[i][j] = True
                        adjacency_matrix[j][i] = True

            # 聚類算法 - 找連通分量
            visited = [False] * n
            clusters = []

            for i in range(n):
                if not visited[i]:
                    cluster = []
                    stack = [i]

                    while stack:
                        node = stack.pop()
                        if not visited[node]:
                            visited[node] = True
                            cluster.append(node)

                            # 添加相鄰未訪問的節點
                            for j in range(n):
                                if adjacency_matrix[node][j] and not visited[j]:
                                    stack.append(j)

                    clusters.append(cluster)

            # 對每個聚類進行處理
            for cluster in clusters:
                if len(cluster) == 1:
                    result.append(class_dets[cluster[0]])
                else:
                    # 聚類內使用加權融合
                    cluster_dets = [class_dets[i] for i in cluster]

                    # 計算聚類中心和密度
                    center_det = self._find_cluster_center(cluster_dets)

                    # 抑制聚類中其他檢測
                    suppressed = []
                    for det in cluster_dets:
                        iou_with_center = self._calculate_iou(
                            center_det['bbox'], det['bbox'])

                        # 使用密度加權的衰減函數
                        density_weight = self._calculate_density_weight(
                            det, cluster_dets)
                        decay = np.exp(-self.config.cluster_beta *
                                       iou_with_center * density_weight)

                        if det['confidence'] * decay > self.config.confidence_threshold:
                            det_copy = det.copy()
                            det_copy['confidence'] *= decay
                            suppressed.append(det_copy)

                    # 取置信度最高的作為代表
                    if suppressed:
                        representative = max(
                            suppressed, key=lambda x: x['confidence'])
                        result.append(representative)

        return result

    def _find_cluster_center(self, cluster_dets: List[Dict[str, Any]]) -> Dict[str, Any]:
        """找到聚類的中心檢測"""
        # 計算所有檢測的質心
        total_confidence = sum(det['confidence'] for det in cluster_dets)
        center_bbox = [0, 0, 0, 0]

        for det in cluster_dets:
            weight = det['confidence'] / total_confidence
            bbox = det['bbox']
            center_bbox[0] += bbox[0] * weight
            center_bbox[1] += bbox[1] * weight
            center_bbox[2] += bbox[2] * weight
            center_bbox[3] += bbox[3] * weight

        # 找到最接近質心的檢測
        min_distance = float('inf')
        center_det = cluster_dets[0]

        for det in cluster_dets:
            bbox = det['bbox']
            center_x = (bbox[0] + bbox[2]) / 2
            center_y = (bbox[1] + bbox[3]) / 2

            target_center_x = (center_bbox[0] + center_bbox[2]) / 2
            target_center_y = (center_bbox[1] + center_bbox[3]) / 2

            distance = np.sqrt((center_x - target_center_x)
                               ** 2 + (center_y - target_center_y)**2)

            if distance < min_distance:
                min_distance = distance
                center_det = det

        return center_det

    def _calculate_density_weight(self, det: Dict[str, Any], cluster_dets: List[Dict[str, Any]]) -> float:
        """計算檢測的密度權重"""
        bbox = det['bbox']
        center_x = (bbox[0] + bbox[2]) / 2
        center_y = (bbox[1] + bbox[3]) / 2

        # 計算與其他檢測的平均距離
        total_distance = 0
        count = 0

        for other_det in cluster_dets:
            if other_det is det:
                continue

            other_bbox = other_det['bbox']
            other_center_x = (other_bbox[0] + other_bbox[2]) / 2
            other_center_y = (other_bbox[1] + other_bbox[3]) / 2

            distance = np.sqrt((center_x - other_center_x) **
                               2 + (center_y - other_center_y)**2)
            total_distance += distance
            count += 1

        if count == 0:
            return 1.0

        avg_distance = total_distance / count
        # 距離越小，密度權重越大
        return 1.0 / (1.0 + avg_distance / 100.0)

    def _largest_object_strategy(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """最大物件策略"""
        if not detections:
            return []

        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        final_detections = []
        for class_id, class_dets in class_groups.items():
            # 按置信度排序
            class_dets.sort(key=lambda x: x['confidence'], reverse=True)

            merged = []
            for det in class_dets:
                bbox = det['bbox']
                area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])

                is_merged = False
                for i, existing in enumerate(merged):
                    existing_bbox = existing['bbox']
                    iou = self._calculate_iou(bbox, existing_bbox)

                    if iou > 0:  # 有重疊
                        existing_area = (
                            existing_bbox[2] - existing_bbox[0]) * (existing_bbox[3] - existing_bbox[1])
                        if area > existing_area:  # 當前物件更大
                            merged[i] = det
                        is_merged = True
                        break

                if not is_merged:
                    merged.append(det)

            final_detections.extend(merged)

        return final_detections

    def _calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """計算IoU"""
        x1_inter = max(box1[0], box2[0])
        y1_inter = max(box1[1], box2[1])
        x2_inter = min(box1[2], box2[2])
        y2_inter = min(box1[3], box2[3])

        inter_width = max(0, x2_inter - x1_inter)
        inter_height = max(0, y2_inter - y1_inter)
        intersection = inter_width * inter_height

        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0

    def _calculate_diou(self, box1: List[float], box2: List[float]) -> float:
        """計算DIoU"""
        iou = self._calculate_iou(box1, box2)

        # 中心點
        c1_x = (box1[0] + box1[2]) / 2
        c1_y = (box1[1] + box1[3]) / 2
        c2_x = (box2[0] + box2[2]) / 2
        c2_y = (box2[1] + box2[3]) / 2

        # 中心點距離
        center_dist_sq = (c1_x - c2_x) ** 2 + (c1_y - c2_y) ** 2

        # 最小外接矩形對角線
        outer_x1 = min(box1[0], box2[0])
        outer_y1 = min(box1[1], box2[1])
        outer_x2 = max(box1[2], box2[2])
        outer_y2 = max(box1[3], box2[3])
        outer_diag_sq = (outer_x2 - outer_x1) ** 2 + (outer_y2 - outer_y1) ** 2

        if outer_diag_sq == 0:
            return iou

        diou = iou - center_dist_sq / outer_diag_sq
        return diou


class AdjacentMerger:
    """相鄰區域合併器"""

    def __init__(self, distance_threshold: float = 50.0):
        self.distance_threshold = distance_threshold
        self.logger = logging.getLogger(__name__)

    def merge_adjacent_detections(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合併相鄰的同類別檢測"""
        if not detections:
            return []

        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        final_detections = []
        for class_id, class_dets in class_groups.items():
            merged = self._merge_class_detections(class_dets)
            final_detections.extend(merged)

        return final_detections

    def _merge_class_detections(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合併同類別的檢測"""
        if len(detections) <= 1:
            return detections

        # 按x座標排序，便於處理
        detections.sort(key=lambda x: x['bbox'][0])

        merged = []
        current_group = [detections[0]]

        for i in range(1, len(detections)):
            current_det = detections[i]

            # 檢查是否與當前組中的任何檢測相鄰
            is_adjacent = False
            for group_det in current_group:
                if self._are_adjacent(current_det['bbox'], group_det['bbox']):
                    is_adjacent = True
                    break

            if is_adjacent:
                current_group.append(current_det)
            else:
                # 合併當前組並開始新組
                if len(current_group) > 1:
                    merged_det = self._merge_detection_group(current_group)
                    merged.append(merged_det)
                else:
                    merged.extend(current_group)
                current_group = [current_det]

        # 處理最後一組
        if len(current_group) > 1:
            merged_det = self._merge_detection_group(current_group)
            merged.append(merged_det)
        else:
            merged.extend(current_group)

        return merged

    def _are_adjacent(self, bbox1: List[float], bbox2: List[float]) -> bool:
        """判斷兩個邊界框是否相鄰"""
        # 計算最短距離
        dist_x = max(0, bbox1[0] - bbox2[2], bbox2[0] - bbox1[2])
        dist_y = max(0, bbox1[1] - bbox2[3], bbox2[1] - bbox1[3])

        distance = np.sqrt(dist_x ** 2 + dist_y ** 2)
        return distance <= self.distance_threshold

    def _merge_detection_group(self, group: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合併檢測組"""
        if len(group) == 1:
            return group[0]

        # 找到最高置信度的檢測作為基礎
        base_det = max(group, key=lambda x: x['confidence'])

        # 計算合併後的邊界框
        min_x = min(det['bbox'][0] for det in group)
        min_y = min(det['bbox'][1] for det in group)
        max_x = max(det['bbox'][2] for det in group)
        max_y = max(det['bbox'][3] for det in group)

        merged_det = base_det.copy()
        merged_det['bbox'] = [min_x, min_y, max_x, max_y]

        # 合併masks（如果有）
        if all(det['mask'] is not None for det in group):
            merged_det['mask'] = self._merge_masks(
                group, [min_x, min_y, max_x, max_y])

        return merged_det

    def _merge_masks(self, group: List[Dict[str, Any]], merged_bbox: List[float]) -> np.ndarray:
        """合併masks - 使用邏輯OR操作合併重疊的masks"""
        if len(group) == 1:
            return group[0]['mask']

        # 獲取第一個mask作為基礎
        base_mask = group[0]['mask']
        merged_mask = base_mask.copy()

        # 逐個合併其他masks
        for det in group[1:]:
            if det['mask'] is not None:
                current_mask = det['mask']

                # 確保mask尺寸一致
                if current_mask.shape != merged_mask.shape:
                    current_mask = cv2.resize(current_mask,
                                              (merged_mask.shape[1],
                                               merged_mask.shape[0]),
                                              interpolation=cv2.INTER_NEAREST)

                # 使用邏輯OR合併masks
                merged_mask = cv2.bitwise_or(merged_mask, current_mask)

        return merged_mask


class AdvancedSliceInference:
    """高級切片推理系統"""

    def __init__(self,
                 model: Any,
                 slice_config: SliceConfig = None,
                 fusion_config: FusionConfig = None,
                 processing_config: ProcessingConfig = None,
                 log_config: LogConfig = None):
        """
        初始化高級切片推理系統

        Args:
            model: 檢測模型
            slice_config: 切片配置
            fusion_config: 融合配置
            processing_config: 處理配置
            log_config: 日誌配置
        """
        self.model_wrapper = ModelWrapper(model)
        self.slice_config = slice_config or SliceConfig()
        self.fusion_config = fusion_config or FusionConfig()
        self.processing_config = processing_config or ProcessingConfig()
        self.log_config = log_config or LogConfig()

        # 讀取 mask 渲染模式
        self.mask_render_mode = self.log_config.mask_render_mode

        # 初始化組件
        self.fusion_engine = AdvancedFusionEngine(self.fusion_config)
        self.adjacent_merger = AdjacentMerger(
            self.processing_config.adjacent_distance_threshold)

        # 設置日誌
        self.logger = logging.getLogger(__name__)

        # 統計數據
        self.stats = {
            'total_images': 0,
            'total_slices': 0,
            'total_detections': 0,
            'processing_times': [],
            'class_stats': {},
            'timing_breakdown': {
                'slice_generation': [],
                'slice_inference': [],
                'overall_inference': [],
                'fusion_processing': [],
                'adjacent_merging': [],
                'class_filtering': []
            }
        }

        self.logger.info("🚀 高級切片推理系統初始化完成")
        self._log_configuration()

    def _log_configuration(self):
        """記錄配置信息"""
        if self.log_config.level != LogLevel.NONE:
            self.logger.info(f"📊 配置摘要:")
            self.logger.info(
                f"  🔪 切片尺寸: {self.slice_config.slice_height}x{self.slice_config.slice_width}")
            self.logger.info(
                f"  🔄 重疊比例: {self.slice_config.overlap_height_ratio:.1%} x {self.slice_config.overlap_width_ratio:.1%}")
            self.logger.info(f"  🔀 融合策略: {self.fusion_config.strategy.value}")
            # 如果是不融合策略，顯示特殊提示
            if self.fusion_config.strategy == FusionStrategy.NO_FUSION:
                self.logger.info("  ⚠️ 已啟用 '不進行融合' 策略。")
            self.logger.info(
                f"  🧠 整體推理: {'啟用' if self.processing_config.enable_overall_inference else '禁用'}")
            self.logger.info(
                f"  🔗 相鄰合併: {'啟用' if self.processing_config.enable_adjacent_merge else '禁用'}")
            self.logger.info(f"  📝 日誌級別: {self.log_config.level.value}")

    def _check_roi_enabled(self) -> bool:
        """檢查是否啟用ROI處理"""
        config = self.processing_config
        if not config.enable_roi_processing:
            return False
        
        # 檢查是否所有ROI比例都為0，如果是，則視為未啟用ROI
        if (config.roi_top_ratio == 0 and config.roi_bottom_ratio == 0 and
            config.roi_left_ratio == 0 and config.roi_right_ratio == 0):
            return False
        return True

    def _apply_roi_cropping(self, image: np.ndarray) -> Tuple[np.ndarray, Optional[Tuple[int, int]]]:
        """根據配置的ROI比例應用圖像裁切

        Args:
            image (np.ndarray): 輸入圖像 (H, W, C)

        Returns:
            Tuple[np.ndarray, Optional[Tuple[int, int]]]: 裁切後的圖像和ROI偏移量 (x_offset, y_offset)
        """
        if not self._check_roi_enabled():
            return image, None

        h, w, _ = image.shape
        config = self.processing_config

        # 計算裁切像素值
        top_pixels = int(h * (config.roi_top_ratio / 100.0))
        bottom_pixels = int(h * (config.roi_bottom_ratio / 100.0))
        left_pixels = int(w * (config.roi_left_ratio / 100.0))
        right_pixels = int(w * (config.roi_right_ratio / 100.0))

        # 確保裁切範圍有效
        top_pixels = min(top_pixels, h - 1)
        bottom_pixels = min(bottom_pixels, h - 1 - top_pixels)
        left_pixels = min(left_pixels, w - 1)
        right_pixels = min(right_pixels, w - 1 - left_pixels)

        # 執行裁切
        cropped_image = image[top_pixels:h - bottom_pixels, left_pixels:w - right_pixels]
        roi_offset = (left_pixels, top_pixels)  # ROI的左上角在原始圖像中的偏移量

        self.logger.info(
            f"已應用ROI裁切：原始尺寸 {w}x{h}, 裁切後尺寸 {cropped_image.shape[1]}x{cropped_image.shape[0]}, "
            f"偏移量 {roi_offset}"
        )
        return cropped_image, roi_offset

    def predict(self,
                image: Union[str, np.ndarray],
                rois: Optional[List[Tuple[int, int, int, int]]] = None,
                gt_annotations: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        執行切片推理

        Args:
            image: 輸入圖像（路徑或numpy數組）
            rois: 感興趣區域列表 [(x1, y1, x2, y2), ...]

        Returns:
            推理結果字典
        """
        start_time = time.time()
        timing_breakdown = {}

        # 加載圖像
        if isinstance(image, str):
            img = cv2.imread(image)
            if img is None:
                raise ValueError(f"無法加載圖像: {image}")
        else:
            img = image.copy()

        original_shape = img.shape
        self.logger.debug(f"🖼️ 處理圖像尺寸: {original_shape}")

        # 第一階段：切片推理（記錄時間）
        slice_start = time.time()
        slices_and_preview = self._generate_slices(
            img, rois, return_preview=self.processing_config.enable_roi_preview)
        if self.processing_config.enable_roi_preview:
            slices, slice_preview_image = slices_and_preview
        else:
            slices = slices_and_preview
            slice_preview_image = None

        slice_detections = self._slice_inference(img, slices)  # 傳遞 slices
        # print(f"slice_detections: {slice_detections}")
        slice_count = len(slices)
        slice_time = time.time() - slice_start
        timing_breakdown['slice_inference'] = slice_time

        # 第二階段：整體圖像推理（可選，記錄時間）
        overall_detections = []
        if self.processing_config.enable_overall_inference:
            overall_start = time.time()
            overall_detections = self._overall_inference(img)
            # print(f"overall_detections: {overall_detections}")
            overall_time = time.time() - overall_start
            timing_breakdown['overall_inference'] = overall_time
        else:
            timing_breakdown['overall_inference'] = 0.0

        # 合併所有檢測
        all_detections = slice_detections + overall_detections

        # 第三階段：類別過濾（記錄時間）
        filter_start = time.time()
        filtered_detections = self._filter_by_class(all_detections)
        filter_time = time.time() - filter_start
        timing_breakdown['class_filtering'] = filter_time

        # 第四階段：物件融合（記錄時間）
        fusion_start = time.time()
        fused_detections = self.fusion_engine.fuse_detections(
            filtered_detections)
        fusion_time = time.time() - fusion_start
        timing_breakdown['fusion_processing'] = fusion_time

        # 第五階段：相鄰區域合併（可選，記錄時間）
        if self.processing_config.enable_adjacent_merge:
            merge_start = time.time()
            final_detections = self.adjacent_merger.merge_adjacent_detections(
                fused_detections)
            merge_time = time.time() - merge_start
            timing_breakdown['adjacent_merging'] = merge_time
        else:
            final_detections = fused_detections
            timing_breakdown['adjacent_merging'] = 0.0

        # 第六階段：後處理
        processed_detections = self._post_process_detections(
            final_detections, original_shape)

        # 第七階段：混淆矩陣計算（如果提供GT標註）
        confusion_metrics = None
        if gt_annotations:
            try:
                tp, fp, fn, precision, recall, f1 = self._calculate_metrics(
                    processed_detections, gt_annotations, iou_threshold=0.5)
                confusion_metrics = {
                    'TP': tp, 'FP': fp, 'FN': fn,
                    'Precision': precision, 'Recall': recall, 'F1': f1
                }
                self.logger.info(f"📊 混淆矩陣計算完成: TP={tp}, FP={fp}, FN={fn}, P={precision:.3f}, R={recall:.3f}, F1={f1:.3f}")
            except Exception as e:
                self.logger.warning(f"⚠️ 混淆矩陣計算失敗: {e}")
                confusion_metrics = None

        # 計算處理時間
        processing_time = time.time() - start_time

        # 更新統計（包含時間分解）
        self._update_stats(slice_count, len(processed_detections),
                           processing_time, processed_detections, timing_breakdown)

        # 記錄結果
        self._log_results(slice_count, len(processed_detections),
                          processing_time, processed_detections)

        # ===== 修正：確保 w, h 來源正確，避免未定義錯誤 =====
        h, w = img.shape[:2]  # 新增：取得圖像高度與寬度，確保 regions 正確
        if rois is None:
            regions = [(0, 0, w, h)]
        else:
            regions = rois

        # 準備返回結果
        result = {
            'detections': processed_detections,
            'image_shape': original_shape,
            'slice_count': slice_count,
            'processing_time': processing_time,
            'stats': self._get_current_stats(),
            'confusion_metrics': confusion_metrics  # 新增混淆矩陣結果
        }

        # 如果啟用ROI預覽，則生成預覽圖像
        if self.processing_config.enable_roi_preview:
            roi_preview_image = img.copy()
            for x_start, y_start, x_end, y_end in regions:
                cv2.rectangle(roi_preview_image, (x_start, y_start),
                              (x_end, y_end), (0, 255, 255), 2)  # 青色框
            result['roi_preview_image'] = roi_preview_image
            # 從 _generate_slices 獲取
            result['slice_preview_image'] = slice_preview_image

        # ===== 強制單張ROI預覽功能 =====
        # 如果啟用 force_single_image_roi_preview，自動生成ROI預覽圖並存檔
        if (getattr(self, 'force_single_image_roi_preview', False) or
                self.processing_config.enable_roi_preview):

            # 確保有預覽圖像
            if 'roi_preview_image' not in result or result['roi_preview_image'] is None:
                roi_preview_image = img.copy()
                for x_start, y_start, x_end, y_end in regions:
                    cv2.rectangle(roi_preview_image, (x_start, y_start),
                                  (x_end, y_end), (0, 255, 255), 2)
                result['roi_preview_image'] = roi_preview_image

            # 如果是字符串路徑，自動保存ROI預覽圖
            if isinstance(image, str):
                from pathlib import Path
                image_path = Path(image)
                image_name = image_path.stem

                # 嘗試在圖像目錄的父目錄創建輸出目錄
                try:
                    output_dir = image_path.parent / "output" / "roi_previews"
                    output_dir.mkdir(parents=True, exist_ok=True)

                    roi_preview_path = output_dir / \
                        f"{image_name}_roi_preview.jpg"
                    cv2.imwrite(str(roi_preview_path),
                                result['roi_preview_image'])

                    if slice_preview_image is not None:
                        slice_preview_path = output_dir / \
                            f"{image_name}_slice_preview.jpg"
                        cv2.imwrite(str(slice_preview_path),
                                    slice_preview_image)

                    self.logger.info(f"✅ ROI預覽圖已自動保存: {roi_preview_path}")
                except Exception as e:
                    self.logger.warning(f"⚠️ 無法自動保存ROI預覽圖: {e}")

        return result

    def generate_three_view_with_gt_masks(self, image: np.ndarray, predictions: List[Dict[str, Any]], gt_annotations: Optional[List[Dict[str, Any]]] = None, output_path: str = None, image_name: str = None, layout: str = "horizontal") -> Dict[str, np.ndarray]:
        """
        生成三視圖：原圖、GT（含mask）、預測結果（含mask）

        Args:
            image: 原始圖像
            predictions: 預測結果列表
            gt_annotations: GT標註列表（可選）
            output_path: 輸出路徑（可選）
            image_name: 圖像名稱（可選）
            layout: 佈局方式 ("horizontal" 或 "vertical")

        Returns:
            包含三個視圖的字典
        """
        # 準備三個視圖
        original_view = image.copy()

        # GT視圖 - 確保顯示mask
        gt_view = image.copy()
        filtered_gt_annotations = gt_annotations
        if gt_annotations:
            # 先過濾掉不需要的類別
            filtered_gt_annotations = []
            excluded_ids = self.processing_config.excluded_class_ids if hasattr(
                self.processing_config, 'excluded_class_ids') else []
            excluded_names = self.processing_config.excluded_class_names if hasattr(
                self.processing_config, 'excluded_class_names') else []
            included_ids = self.processing_config.included_class_ids if hasattr(
                self.processing_config, 'included_class_ids') else None
            for ann in gt_annotations:
                cid = ann.get('class_id', None)
                cname = ann.get('class_name', '')
                # 檢查排除
                if cid is not None and cid in excluded_ids:
                    continue
                if cname in excluded_names:
                    continue
                if included_ids is not None and (cid is None or cid not in included_ids):
                    continue
                filtered_gt_annotations.append(ann)
            # 轉換GT標註確保正確的label_aliases映射
            converted_gt = self._convert_gt_annotations(
                filtered_gt_annotations)
            gt_view = self._draw_annotations(gt_view, converted_gt, is_gt=True)
        # 預測視圖 - 確保顯示mask
        pred_view = image.copy()
        if predictions:
            # 轉換預測結果確保正確的label_aliases映射
            converted_pred = self._convert_pred_annotations(predictions)
            pred_view = self._draw_annotations(
                pred_view, converted_pred, is_gt=False)

        # 添加標題
        self._add_view_title(original_view, "原始圖像")
        gt_title = f"Ground Truth ({len(filtered_gt_annotations) if filtered_gt_annotations else 0} 個標註)"
        self._add_view_title(gt_view, gt_title)
        pred_title = f"預測結果 ({len(predictions)} 個檢測)"
        self._add_view_title(pred_view, pred_title)

        # 組合三視圖
        if layout == "horizontal":
            # 水平佈局
            spacing = 10
            total_width = original_view.shape[1] * 3 + spacing * 2
            total_height = original_view.shape[0]
            three_view = np.ones(
                (total_height, total_width, 3), dtype=np.uint8) * 255

            # 放置三個視圖
            w = original_view.shape[1]
            three_view[:, :w] = original_view
            three_view[:, w+spacing:w*2+spacing] = gt_view
            three_view[:, w*2+spacing*2:] = pred_view
        else:
            # 垂直佈局
            spacing = 10
            total_width = original_view.shape[1]
            total_height = original_view.shape[0] * 3 + spacing * 2
            three_view = np.ones(
                (total_height, total_width, 3), dtype=np.uint8) * 255

            # 放置三個視圖
            h = original_view.shape[0]
            three_view[:h, :] = original_view
            three_view[h+spacing:h*2+spacing, :] = gt_view
            three_view[h*2+spacing*2:, :] = pred_view

        # 保存三視圖（如果提供了路徑）
        if output_path and image_name:
            from pathlib import Path
            import os

            images_dir = Path(output_path) / "images"
            images_dir.mkdir(parents=True, exist_ok=True)

            three_view_path = images_dir / \
                f"{image_name}_three_view_with_masks.jpg"
            cv2.imwrite(str(three_view_path), three_view)

            self.logger.info(f"✅ 三視圖（含GT mask）已保存: {three_view_path}")

        return {
            'original': original_view,
            'ground_truth': gt_view,
            'predictions': pred_view,
            'combined': three_view
        }

    def _add_view_title(self, image: np.ndarray, title: str):
        """為視圖添加標題"""
        font_scale = self.processing_config.font_size * 1.5  # 標題稍大
        font_thickness = self.processing_config.font_thickness + 1

        # 計算文字尺寸
        (text_width, text_height), baseline = cv2.getTextSize(
            title, cv2.FONT_HERSHEY_SIMPLEX, font_scale, font_thickness)

        # 在圖像頂部添加白色背景
        text_y = 30 + text_height
        cv2.rectangle(image, (10, 10), (10 + text_width +
                      20, text_y + 10), (255, 255, 255), -1)
        cv2.rectangle(image, (10, 10), (10 + text_width +
                      20, text_y + 10), (0, 0, 0), 2)

        # 添加文字
        cv2.putText(image, title, (20, text_y),
                    cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), font_thickness)

    def _slice_inference(self, image: np.ndarray, slices: List[Tuple[np.ndarray, int, int]]) -> List[Dict[str, Any]]:
        """切片推理"""
        all_detections = []

        for slice_img, x_offset, y_offset in slices:
            # 在切片上執行推理
            slice_detections = self.model_wrapper.predict(slice_img)

            # 將座標轉換回原圖座標系
            for det in slice_detections:
                bbox = det['bbox']
                det['bbox'] = [
                    bbox[0] + x_offset,
                    bbox[1] + y_offset,
                    bbox[2] + x_offset,
                    bbox[3] + y_offset
                ]

                # 處理mask座標轉換
                if det['mask'] is not None:
                    # 創建原圖尺寸的mask
                    original_mask = np.zeros(image.shape[:2], dtype=np.uint8)
                    slice_mask = det['mask']

                    # 計算在原圖中的實際區域
                    slice_h, slice_w = slice_mask.shape
                    end_y = min(y_offset + slice_h, image.shape[0])
                    end_x = min(x_offset + slice_w, image.shape[1])

                    # 計算需要拷貝的實際大小
                    copy_h = end_y - y_offset
                    copy_w = end_x - x_offset

                    # 將切片mask拷貝到原圖mask的正確位置
                    original_mask[y_offset:end_y,
                                  x_offset:end_x] = slice_mask[:copy_h, :copy_w]
                    det['mask'] = original_mask

            all_detections.extend(slice_detections)
        return all_detections

    def _overall_inference(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """整體圖像推理"""
        return self.model_wrapper.predict(image)

    def _generate_slices(self, image: np.ndarray, rois: Optional[List[Tuple[int, int, int, int]]], return_preview: bool = False) -> Union[List[Tuple[np.ndarray, int, int]], Tuple[List[Tuple[np.ndarray, int, int]], np.ndarray]]:
        """生成切片"""
        h, w = image.shape[:2]  # 取得圖像高度與寬度
        slices = []
        slice_preview_image = None
        if return_preview:
            slice_preview_image = image.copy()

        if rois is None:
            regions = [(0, 0, w, h)]
        else:
            regions = rois

        # 根據配置調整切片尺寸 (Advanced Slicing)
        effective_slice_height = self.slice_config.slice_height
        effective_slice_width = self.slice_config.slice_width

        if self.slice_config.auto_slice_resolution:
            # 確保切片尺寸不超過圖像尺寸
            effective_slice_height = min(effective_slice_height, h)
            effective_slice_width = min(effective_slice_width, w)

            # 確保切片尺寸至少為 min_slice_size
            effective_slice_height = max(
                effective_slice_height, self.slice_config.min_slice_size)
            effective_slice_width = max(
                effective_slice_width, self.slice_config.min_slice_size)

            # 如果切片尺寸過大，可能會被調整為圖像尺寸
            if effective_slice_height > h:
                effective_slice_height = h
            if effective_slice_width > w:
                effective_slice_width = w

            self.logger.debug(
                f"自動調整切片尺寸：原始 {self.slice_config.slice_height}x{self.slice_config.slice_width} -> 實際 {effective_slice_height}x{effective_slice_width}")

        for x_start, y_start, x_end, y_end in regions:
            # 使用實際的切片尺寸計算步長
            h_step = int(effective_slice_height *
                         (1 - self.slice_config.overlap_height_ratio))
            w_step = int(effective_slice_width *
                         (1 - self.slice_config.overlap_width_ratio))

            # 確保步長至少為1，避免無限循環
            h_step = max(1, h_step)
            w_step = max(1, w_step)

            y = y_start
            while y < y_end:
                x = x_start
                while x < x_end:
                    slice_x1 = max(0, x)
                    slice_y1 = max(0, y)
                    slice_x2 = min(w, x + effective_slice_width)
                    slice_y2 = min(h, y + effective_slice_height)

                    # 檢查切片尺寸是否滿足最小要求
                    if (slice_x2 - slice_x1) >= self.slice_config.min_slice_size and \
                       (slice_y2 - slice_y1) >= self.slice_config.min_slice_size:
                        slice_img = image[slice_y1:slice_y2, slice_x1:slice_x2]
                        slices.append((slice_img, slice_x1, slice_y1))
                        if return_preview:
                            cv2.rectangle(
                                slice_preview_image, (slice_x1, slice_y1), (slice_x2, slice_y2), (255, 0, 255), 2)

                    x += w_step
                    if x >= x_end:
                        break

                y += h_step
                if y >= y_end:
                    break

        if return_preview:
            return slices, slice_preview_image
        return slices

    def _filter_by_class(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按類別過濾"""
        filtered = []

        for det in detections:
            class_id = det['class_id']
            class_name = det['class_name']

            # 檢查排除列表
            if class_id in self.processing_config.excluded_class_ids:
                continue
            if class_name in self.processing_config.excluded_class_names:
                continue

            # 檢查包含列表
            if (self.processing_config.included_class_ids is not None and
                    class_id not in self.processing_config.included_class_ids):
                continue

            # 檢查置信度 (使用類別特定閾值，如果存在)
            if self.processing_config.class_confidence_thresholds and \
               class_id in self.processing_config.class_confidence_thresholds:
                if det['confidence'] < self.processing_config.class_confidence_thresholds[class_id]:
                    continue
            elif det['confidence'] < self.fusion_config.confidence_threshold:
                continue

            filtered.append(det)

        return filtered

    def _post_process_detections(self, detections: List[Dict[str, Any]], image_shape: Tuple[int, int, int]) -> List[Dict[str, Any]]:
        """後處理檢測結果"""
        processed = []
        h, w = image_shape[:2]

        for det in detections:
            # 邊界框裁剪
            bbox = det['bbox']
            bbox[0] = max(0, min(w, bbox[0]))
            bbox[1] = max(0, min(h, bbox[1]))
            bbox[2] = max(0, min(w, bbox[2]))
            bbox[3] = max(0, min(h, bbox[3]))

            # 檢查最小面積
            area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
            if area < self.processing_config.min_object_area:
                continue

            # 幾何過濾
            if self.processing_config.enable_geometric_filtering:
                if not self._is_valid_geometry(bbox):
                    continue

            processed.append(det)

        return processed

    def _calculate_metrics(self, predictions: List[Dict[str, Any]], gt_annotations: List[Dict[str, Any]], iou_threshold: float = 0.5) -> Tuple[int, int, int, float, float, float]:
        """
        計算TP/FP/FN統計以及P、R、F1指標

        Args:
            predictions: 預測結果列表
            gt_annotations: GT數據列表
            iou_threshold: IoU閾值

        Returns:
            TP, FP, FN數量以及Precision, Recall, F1分數
        """
        if not predictions or not gt_annotations:
            return 0, 0, 0, 0.0, 0.0, 0.0

        tp = 0
        matched_gt = set()
        matched_pred = set()

        for i, pred in enumerate(predictions):
            pred_bbox = pred.get('bbox', [])
            pred_class_id = pred.get('class_id', -1)

            best_iou = 0
            best_gt_idx = -1

            for j, gt in enumerate(gt_annotations):
                if j in matched_gt:
                    continue

                gt_bbox = gt.get('bbox', [])
                gt_class_id = gt.get('class_id', -1)

                # 類別必須匹配
                if pred_class_id != gt_class_id:
                    continue

                # 計算IoU
                iou = self._calculate_iou(pred_bbox, gt_bbox)

                if iou > best_iou and iou >= iou_threshold:  # IoU閾值
                    best_iou = iou
                    best_gt_idx = j

            if best_gt_idx >= 0:
                tp += 1
                matched_gt.add(best_gt_idx)
                matched_pred.add(i)

        fp = len(predictions) - tp  # 未匹配的預測
        fn = len(gt_annotations) - tp      # 未匹配的GT

        # 計算P、R、F1
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * (precision * recall) / (precision +
                                         recall) if (precision + recall) > 0 else 0.0

        return tp, fp, fn, precision, recall, f1

    def _draw_annotations(self, image: np.ndarray, annotations: List[Dict[str, Any]], is_gt: bool = False, metrics: Optional[Dict[str, float]] = None) -> np.ndarray:
        """
        在圖像上繪製註釋 (檢測或GT) - 修復mask疊加變暗問題
        Args:
            image: 輸入圖像
            annotations: 註釋列表 (包含bbox, class_id, mask等)
            is_gt: 是否為GT註釋 (影響顏色和標籤)
        Returns:
            繪製後的圖像
        """
        # 關鍵修復：使用原始圖像作為底圖，然後為每個mask分別計算alpha混合
        base_image = image.copy()
        output_image = base_image.copy()

        # 收集所有mask，然後一次性混合到底圖
        all_masks = []
        all_colors = []

        for ann in annotations:
            # 自動補 class_id
            if 'class_id' not in ann or ann['class_id'] is None:
                label_key = ann.get('label') or ann.get('class_name')
                if label_key and self.processing_config.class_info_by_name and label_key in self.processing_config.class_info_by_name:
                    ann['class_id'] = self.processing_config.class_info_by_name[label_key]['class_id']
                else:
                    continue  # 查不到就跳過
            if 'bbox' not in ann or ann['bbox'] is None:
                # 嘗試自動從 polygon points 補 bbox
                if 'points' in ann and ann['points']:
                    pts = np.array(ann['points'])
                    x1, y1 = pts.min(axis=0)
                    x2, y2 = pts.max(axis=0)
                    ann['bbox'] = [float(x1), float(y1), float(x2), float(y2)]
                else:
                    continue  # 跳過沒有 bbox 的標註
            bbox = ann['bbox']
            class_id = ann['class_id']
            mask = ann.get('mask', None)

            # 取得標準類別名稱（先經 label_aliases 轉換）
            original_class_name = ann.get('class_name', f"class_{class_id}")
            resolved_class_name = self.processing_config.label_aliases.get(
                original_class_name, original_class_name) if self.processing_config.label_aliases else original_class_name

            # 取得顏色與名稱（強制用 class_id 查英文 name）
            info = None
            if self.processing_config.class_info_by_name:
                for v in self.processing_config.class_info_by_name.values():
                    if v.get('class_id') == class_id:
                        info = v
                        break
            if info:
                display_name = info.get('name', f'class_{class_id}')
                color = tuple(info['color'])
            else:
                display_name = f'class_{class_id}'
                color = (0, 255, 0)

            if is_gt:
                display_name = f"GT: {display_name}"

            # 收集mask資訊
            if mask is not None and self.processing_config.return_masks:
                if mask.dtype != np.uint8:
                    mask = (mask * 255).astype(np.uint8)
                if mask.shape[:2] != base_image.shape[:2]:
                    mask = cv2.resize(
                        mask, (base_image.shape[1], base_image.shape[0]), interpolation=cv2.INTER_NEAREST)

                mask_area = (
                    mask > self.processing_config.mask_threshold * 255)
                all_masks.append(mask_area)
                all_colors.append(color)

            # 繪製邊界框 (outline_only模式下不繪製邊界框，只繪製輪廓)
            draw_bbox = (self.processing_config.return_boxes and
                         self.mask_render_mode != "outline_only")
            if draw_bbox:
                x1, y1, x2, y2 = map(int, bbox)
                cv2.rectangle(output_image, (x1, y1), (x2, y2), color, 2)

                # 添加標籤
                text = display_name
                if not is_gt:
                    text += f" {ann['confidence']:.2f}"
                if not is_gt and metrics:
                    text += f"\nP:{metrics['precision']:.2f} R:{metrics['recall']:.2f} F1:{metrics['f1']:.2f}"
                    text += f"\nTP:{metrics['tp']} FP:{metrics['fp']} FN:{metrics['fn']}"

                lines = text.split('\n')
                y_offset = y1
                font_size = self.processing_config.font_size
                font_thickness = self.processing_config.font_thickness

                for line in reversed(lines):
                    (text_width, text_height), baseline = cv2.getTextSize(
                        line, cv2.FONT_HERSHEY_SIMPLEX, font_size, font_thickness)
                    cv2.rectangle(output_image, (x1, y_offset - text_height -
                                  baseline), (x1 + text_width, y_offset), color, -1)
                    cv2.putText(output_image, line, (x1, y_offset - baseline),
                                # 黑色字體
                                cv2.FONT_HERSHEY_SIMPLEX, font_size, (0, 0, 0), font_thickness)
                    y_offset -= (text_height + baseline + 5)

        # 🎨 終極修復：多種mask渲染模式，解決重疊變暗問題
        if all_masks and self.processing_config.return_masks:
            render_mode = self.mask_render_mode

            if render_mode == "outline_only":
                # 模式3: 只繪製邊框，不填充mask
                for i, (mask_area, mask_color) in enumerate(zip(all_masks, all_colors)):
                    # 找到mask的輪廓
                    mask_uint8 = mask_area.astype(np.uint8) * 255
                    contours, _ = cv2.findContours(
                        mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                    # 繪製輪廓線，使用較粗的線條
                    cv2.drawContours(output_image, contours, -1, mask_color, 3)

                    # 為outline_only模式添加文字標籤，位置基於mask輪廓
                    if contours and i < len(annotations):
                        ann = annotations[i]

                        # 獲取最大輪廓的邊界框作為文字位置參考
                        largest_contour = max(contours, key=cv2.contourArea)
                        x, y, w, h = cv2.boundingRect(largest_contour)

                        # 構建標籤文字
                        display_name = ann.get('class_name', 'unknown')
                        text = display_name
                        if not is_gt and 'confidence' in ann:
                            text += f" {ann['confidence']:.2f}"

                        # 繪製文字標籤
                        font_size = self.processing_config.font_size * 0.8  # 稍微小一點
                        font_thickness = self.processing_config.font_thickness

                        # 計算文字尺寸
                        (text_width, text_height), baseline = cv2.getTextSize(
                            text, cv2.FONT_HERSHEY_SIMPLEX, font_size, font_thickness)

                        # 文字背景框
                        text_x, text_y = x, y - 5
                        cv2.rectangle(output_image,
                                      (text_x, text_y - text_height - baseline),
                                      (text_x + text_width, text_y),
                                      mask_color, -1)

                        # 繪製文字
                        cv2.putText(output_image, text, (text_x, text_y - baseline),
                                    cv2.FONT_HERSHEY_SIMPLEX, font_size, (0, 0, 0), font_thickness)

            elif render_mode == "simple":
                # 模式2: 簡單極低alpha混合
                alpha = 0.06  # 極低alpha值
                for mask_area, mask_color in zip(all_masks, all_colors):
                    overlay = np.zeros_like(base_image)
                    overlay[mask_area] = np.array(
                        mask_color)  # 轉換為 numpy array
                    mask_3d = mask_area[..., None]

                    blended = cv2.addWeighted(
                        base_image, 1 - alpha, overlay, alpha, 0)
                    output_image = np.where(mask_3d, blended, output_image)

                    if np.any(mask_area):
                        # 非重疊區域使用極低alpha
                        overlay = np.zeros_like(base_image)
                        overlay[mask_area] = np.array(mask_color)  # 轉換
                        mask_3d = mask_area[..., None]

                        blended_region = cv2.addWeighted(
                            base_image, 1 - alpha,
                            overlay, alpha, 0
                        )
                        output_image = np.where(
                            mask_3d, blended_region, output_image)

            elif render_mode == "intelligent":  # "intelligent" mode (默認)
                # 模式1: 智能重疊處理，極低alpha值
                base_alpha = max(
                    0.03, min(0.06, self.processing_config.fill_alpha * 0.15))

                # 創建聯合mask來檢測重疊區域
                union_mask = np.zeros(base_image.shape[:2], dtype=np.bool_)
                overlap_mask = np.zeros(base_image.shape[:2], dtype=np.bool_)

                for mask_area in all_masks:
                    current_overlap = union_mask & mask_area
                    overlap_mask |= current_overlap
                    union_mask |= mask_area

                # 智能混合 - 重疊區域使用更低alpha
                for mask_area, mask_color in zip(all_masks, all_colors):
                    current_overlap = overlap_mask & mask_area
                    non_overlap_area = mask_area & ~current_overlap

                    if np.any(non_overlap_area):
                        # 非重疊區域使用極低alpha
                        overlay = np.zeros_like(base_image)
                        overlay[non_overlap_area] = np.array(mask_color)  # 轉換
                        mask_3d = non_overlap_area[..., None]

                        blended_region = cv2.addWeighted(
                            base_image, 1 - base_alpha,
                            overlay, base_alpha, 0
                        )
                        output_image = np.where(
                            mask_3d, blended_region, output_image)

                    if np.any(current_overlap):
                        # 重疊區域使用超低alpha
                        overlap_alpha = base_alpha * 0.25  # 再降低75%
                        overlay = np.zeros_like(base_image)
                        overlay[current_overlap] = np.array(mask_color)  # 轉換
                        mask_3d = current_overlap[..., None]

                        blended_region = cv2.addWeighted(
                            base_image, 1 - overlap_alpha,
                            overlay, overlap_alpha, 0
                        )
                        output_image = np.where(
                            mask_3d, blended_region, output_image)

            else:  # "unified" mode (新增默認推薦)
                # 🌟 模式1: 統一mask圖層方法 - 徹底解決疊加問題

                # Step 1: 創建統一的mask圖層
                unified_mask_layer = np.zeros_like(
                    base_image, dtype=np.float32)
                unified_alpha_layer = np.zeros(
                    base_image.shape[:2], dtype=np.float32)

                # Step 2: 將所有mask合併到統一圖層
                for mask_area, mask_color in zip(all_masks, all_colors):
                    # 為每個mask區域分配顏色
                    unified_mask_layer[mask_area] = np.array(
                        mask_color, dtype=np.float32)

                    # 處理重疊：重疊區域使用顏色平均或優先級策略
                    existing_alpha = unified_alpha_layer[mask_area]

                    # 重疊區域取平均顏色，避免某個顏色完全蓋住另一個
                    overlap_areas = existing_alpha > 0
                    if np.any(overlap_areas):
                        # 計算重疊區域的平均顏色
                        mask_indices = np.where(mask_area)
                        overlap_indices = (
                            mask_indices[0][overlap_areas], mask_indices[1][overlap_areas])

                        # 混合顏色：50%舊顏色 + 50%新顏色
                        existing_colors = unified_mask_layer[overlap_indices]
                        new_colors = np.array(mask_color, dtype=np.float32)
                        mixed_colors = (existing_colors + new_colors) / 2.0
                        unified_mask_layer[overlap_indices] = mixed_colors

                    # 更新alpha圖層
                    unified_alpha_layer[mask_area] = np.maximum(
                        unified_alpha_layer[mask_area], 1.0)

                # Step 3: 一次性混合統一mask圖層到原圖
                # 較高alpha值，因為只混合一次
                alpha = max(
                    0.15, min(0.25, self.processing_config.fill_alpha * 0.6))

                # 創建有效mask區域
                valid_mask = unified_alpha_layer > 0

                if np.any(valid_mask):
                    # 確保顏色值在有效範圍內
                    unified_mask_layer = np.clip(unified_mask_layer, 0, 255)

                    # 只在有mask的區域進行混合
                    mask_3d = valid_mask[..., None]  # 擴展到3通道

                    # 一次性alpha混合：原圖 * (1-alpha) + mask圖層 * alpha
                    blended_result = (
                        base_image.astype(np.float32) * (1 - alpha) +
                        unified_mask_layer * alpha
                    ).astype(np.uint8)

                    # 只在mask區域應用混合結果
                    output_image = np.where(
                        mask_3d, blended_result, output_image)

        return output_image

    # ===== 強制單張ROI預覽功能 =====
    force_single_image_roi_preview = False  # 預設關閉，可由外部設置或配置文件控制

    # ===== Mask渲染模式配置 =====
    # mask_render_mode = "unified"  # "unified", "intelligent", "simple", "outline_only" # 這行將被新的配置機制取代
    # unified: 統一mask圖層方法，一次性混合（推薦）
    # intelligent: 智能重疊處理，極低alpha值
    # simple: 簡單極低alpha混合
    # outline_only: 只繪製邊框，不填充mask

    def _draw_roi_and_sahi_grid(self, img, roi_params, sahi_params):
        """繪製ROI區域與切片格線，參考 unified_yolo_inference.py"""
        import cv2
        import numpy as np
        h, w = img.shape[:2]
        cx, cy = w // 2, h // 2
        crop_factor_top = (roi_params['top'] - 1) / 8
        crop_factor_bottom = (roi_params['bottom'] - 1) / 8
        crop_factor_left = (roi_params['left'] - 1) / 8
        crop_factor_right = (roi_params['right'] - 1) / 8
        y1_roi = int(h * crop_factor_top)
        y2_roi = int(h - h * crop_factor_bottom)
        x1_roi = int(w * crop_factor_left)
        x2_roi = int(w - w * crop_factor_right)
        y1_roi = max(0, y1_roi)
        y2_roi = min(h, y2_roi)
        x1_roi = max(0, x1_roi)
        x2_roi = min(w, x2_roi)
        if y1_roi >= y2_roi or x1_roi >= x2_roi:
            return img
        preview = img.copy()
        overlay = preview.copy()
        alpha = 0.3
        color_top = (0, 0, 255)
        color_bottom = (255, 0, 0)
        color_left = (0, 255, 255)
        color_right = (255, 255, 0)
        cv2.rectangle(overlay, (0, 0), (w, y1_roi), color_top, -1)
        cv2.rectangle(overlay, (0, y2_roi), (w, h), color_bottom, -1)
        cv2.rectangle(overlay, (0, y1_roi), (x1_roi, y2_roi), color_left, -1)
        cv2.rectangle(overlay, (x2_roi, y1_roi), (w, y2_roi), color_right, -1)
        preview = cv2.addWeighted(overlay, alpha, preview, 1 - alpha, 0)
        cv2.rectangle(preview, (x1_roi, y1_roi),
                      (x2_roi, y2_roi), (0, 255, 0), 6)
        sh, sw = sahi_params['slice_height'], sahi_params['slice_width']
        oh, ow = sahi_params['overlap_height_ratio'], sahi_params['overlap_width_ratio']
        step_h = int(sh * (1 - oh))
        step_w = int(sw * (1 - ow))
        if step_h <= 0:
            step_h = sh
        if step_w <= 0:
            step_w = sw
        for top in range(y1_roi, y2_roi, step_h):
            for left in range(x1_roi, x2_roi, step_w):
                btm = min(top + sh, y2_roi)
                rgt = min(left + sw, x2_roi)
                cv2.rectangle(preview, (left, top),
                              (rgt, btm), (0, 255, 255), 2)
        
        # 在預覽圖的左上角顯示切片尺寸
        text = f"切片尺寸: {sh}x{sw}"
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1.0
        font_thickness = 2
        text_size = cv2.getTextSize(text, font, font_scale, font_thickness)[0]
        
        # 放置文字，避免覆蓋ROI框
        text_x = 10
        text_y = 30
        
        cv2.putText(preview, text, (text_x, text_y), font, font_scale, (0, 255, 0), font_thickness, cv2.LINE_AA)

        return preview

    def predict_with_roi_preview(self, image: Union[str, np.ndarray], rois: Optional[List[Tuple[int, int, int, int]]] = None, gt_annotations: Optional[List[Dict[str, Any]]] = None, output_dir: str = None, image_name: str = None) -> Dict[str, Any]:
        """
        執行切片推理並產生 ROI 預覽圖，自動存檔到指定目錄

        Args:
            image: 輸入圖像（路徑或numpy數組）
            rois: 感興趣區域列表
            gt_annotations: GT標註數據
            output_dir: 輸出目錄（可選，如果不提供會自動推斷）
            image_name: 圖像名稱（可選，如果不提供會自動推斷）

        Returns:
            推理結果字典，包含ROI預覽圖像
        """
        import os

        # 設置臨時標記以啟用ROI預覽
        original_enable_roi_preview = self.processing_config.enable_roi_preview
        original_force_preview = getattr(
            self, 'force_single_image_roi_preview', False)

        # 強制啟用ROI預覽
        self.processing_config.enable_roi_preview = True
        self.force_single_image_roi_preview = True

        try:
            # 執行推理
            result = self.predict(image, rois, gt_annotations)

            # 如果提供了輸出目錄和圖像名稱，生成高級ROI預覽圖
            if output_dir and image_name:
                img = cv2.imread(image) if isinstance(
                    image, str) else image.copy()

                # 獲取ROI參數
                roi_params = {
                    'top': getattr(self.processing_config, 'roi_top_ratio', 2.0),
                    'bottom': getattr(self.processing_config, 'roi_bottom_ratio', 2.0),
                    'left': getattr(self.processing_config, 'roi_left_ratio', 2.0),
                    'right': getattr(self.processing_config, 'roi_right_ratio', 2.0),
                }

                # 獲取切片參數
                sahi_params = {
                    'slice_height': self.slice_config.slice_height,
                    'slice_width': self.slice_config.slice_width,
                    'overlap_height_ratio': self.slice_config.overlap_height_ratio,
                    'overlap_width_ratio': self.slice_config.overlap_width_ratio,
                }

                # 生成帶有ROI和切片格線的預覽圖
                advanced_preview = self._draw_roi_and_sahi_grid(
                    img, roi_params, sahi_params)

                # 確保輸出目錄存在
                images_dir = os.path.join(output_dir, "images")
                os.makedirs(images_dir, exist_ok=True)

                # 保存高級預覽圖
                advanced_preview_path = os.path.join(
                    images_dir, f"{image_name}_roi_force_preview.jpg")
                cv2.imwrite(advanced_preview_path, advanced_preview)

                # 添加到結果中
                result['advanced_roi_preview_image'] = advanced_preview
                result['advanced_roi_preview_path'] = advanced_preview_path

                self.logger.info(f"✅ 高級ROI預覽圖已存檔: {advanced_preview_path}")

        finally:
            # 恢復原始設置
            self.processing_config.enable_roi_preview = original_enable_roi_preview
            self.force_single_image_roi_preview = original_force_preview

        return result

    def _is_valid_geometry(self, bbox: List[float]) -> bool:
        """檢查幾何有效性"""
        width = bbox[2] - bbox[0]
        height = bbox[3] - bbox[1]

        # 檢查寬高比
        if width <= 0 or height <= 0:
            return False

        aspect_ratio = width / height
        if aspect_ratio > 10 or aspect_ratio < 0.1:  # 極端寬高比
            return False

        return True

    def _update_stats(self, slice_count: int, detection_count: int, processing_time: float, detections: List[Dict[str, Any]], timing_breakdown: Dict[str, float] = None):
        """更新統計數據"""
        self.stats['total_images'] += 1
        self.stats['total_slices'] += slice_count
        self.stats['total_detections'] += detection_count
        self.stats['processing_times'].append(processing_time)

        # 更新時間分解統計
        if timing_breakdown:
            for stage, duration in timing_breakdown.items():
                if stage in self.stats['timing_breakdown']:
                    self.stats['timing_breakdown'][stage].append(duration)

        # 更新類別統計
        for det in detections:
            class_name = det['class_name']
            if class_name not in self.stats['class_stats']:
                self.stats['class_stats'][class_name] = 0
            self.stats['class_stats'][class_name] += 1

    def _log_results(self, slice_count: int, detection_count: int, processing_time: float, detections: List[Dict[str, Any]]):
        """記錄結果"""
        if self.log_config.level == LogLevel.NONE:
            return

        if self.log_config.level == LogLevel.LIGHTWEIGHT:
            self.logger.info(f"切片數量: {slice_count}, 總預測數量: {detection_count}")

        elif self.log_config.level == LogLevel.DETAILED:
            self.logger.info(f"切片數量: {slice_count}")
            self.logger.info(f"預測數量: {detection_count}")

            # 按類別統計
            class_counts = {}
            for det in detections:
                class_name = det['class_name']
                class_counts[class_name] = class_counts.get(class_name, 0) + 1

            if class_counts:
                self.logger.info("各類別預測數量:")
                for class_name, count in sorted(class_counts.items()):
                    self.logger.info(f"  {class_name}: {count}")

            self.logger.info(f"處理時間: {processing_time:.2f} 秒")

            if self.log_config.enable_timing_breakdown:
                # 添加詳細的時間分解
                self._log_timing_breakdown()

    def _log_timing_breakdown(self):
        """記錄詳細的時間分解"""
        self.logger.info("⏱️ 詳細時間分解:")

        timing_stats = self.stats['timing_breakdown']
        for stage, times in timing_stats.items():
            if times:
                avg_time = np.mean(times)
                total_time = np.sum(times)
                last_time = times[-1]

                # 轉換階段名稱為中文
                stage_names = {
                    'slice_inference': '切片推理',
                    'overall_inference': '整體推理',
                    'fusion_processing': '結果融合',
                    'adjacent_merging': '相鄰合併',
                    'class_filtering': '類別過濾'
                }

                stage_name = stage_names.get(stage, stage)
                self.logger.info(
                    f"  📊 {stage_name}: {last_time:.3f}s (平均: {avg_time:.3f}s, 總計: {total_time:.3f}s)")

        # 計算各階段時間佔比
        if self.stats['processing_times']:
            total_avg_time = np.mean(self.stats['processing_times'])
            self.logger.info("📈 各階段時間佔比:")

            for stage, times in timing_stats.items():
                if times:
                    avg_time = np.mean(times)
                    percentage = (avg_time / total_avg_time) * \
                        100 if total_avg_time > 0 else 0
                    stage_name = {
                        'slice_inference': '切片推理',
                        'overall_inference': '整體推理',
                        'fusion_processing': '結果融合',
                        'adjacent_merging': '相鄰合併',
                        'class_filtering': '類別過濾'
                    }.get(stage, stage)

                    self.logger.info(f"  🔸 {stage_name}: {percentage:.1f}%")

    def _get_current_stats(self) -> Dict[str, Any]:
        """獲取當前統計數據"""
        avg_time = np.mean(
            self.stats['processing_times']) if self.stats['processing_times'] else 0

        return {
            'total_images': self.stats['total_images'],
            'total_slices': self.stats['total_slices'],
            'total_detections': self.stats['total_detections'],
            'avg_processing_time': avg_time,
            'class_distribution': self.stats['class_stats'].copy()
        }

    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """獲取完整統計報告"""
        stats = self._get_current_stats()

        if self.stats['processing_times']:
            times = np.array(self.stats['processing_times'])
            stats.update({
                'min_processing_time': float(times.min()),
                'max_processing_time': float(times.max()),
                'std_processing_time': float(times.std()),
                'total_processing_time': float(times.sum())
            })

        return stats

    def save_stats(self, output_path: str):
        """保存統計數據到文件"""
        stats = self.get_comprehensive_stats()

        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)

        self.logger.info(f"📊 統計數據已保存至: {output_path}")

    def _convert_gt_annotations(self, gt_annotations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        讀入GT時即轉換label與顏色，確保與class_configs一致
        統一使用label_aliases轉換後再用class_configs對應
        """
        converted = []
        for ann in gt_annotations:
            ann_copy = ann.copy()  # 避免修改原始數據

            # 獲取原始類別名稱
            original_class_name = ann_copy.get(
                'class_name', f"class_{ann_copy.get('class_id', 0)}")

            # Step 1: 先經 label_aliases 轉換得到標準名稱
            if self.processing_config.label_aliases:
                resolved_class_name = self.processing_config.label_aliases.get(
                    original_class_name, original_class_name)
            else:
                resolved_class_name = original_class_name

            # Step 2: 用標準名稱查找 class_info_by_name 獲取配置信息
            if (self.processing_config.class_info_by_name and
                    resolved_class_name in self.processing_config.class_info_by_name):
                info = self.processing_config.class_info_by_name[resolved_class_name]
                ann_copy.update({
                    'class_id': info['class_id'],
                    'class_name': resolved_class_name,  # 使用標準名稱
                    'display_name': info.get('display_name', resolved_class_name),
                    'color': tuple(info.get('color', [0, 255, 0]))
                })
            else:
                # 如果找不到對應配置，使用默認值
                ann_copy.update({
                    'class_name': resolved_class_name,
                    'display_name': resolved_class_name,
                    'color': (0, 255, 0)  # 默認綠色
                })

            converted.append(ann_copy)
        return converted

    def _convert_pred_annotations(self, pred_annotations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        預測結果產生後馬上轉換，統一使用label_aliases轉換後再用class_configs對應
        class_name 強制為 class_configs 的 name（英文標準名）
        """
        converted = []
        for ann in pred_annotations:
            ann_copy = ann.copy()  # 避免修改原始數據

            # 獲取原始類別名稱
            original_class_name = ann_copy.get(
                'class_name', f"class_{ann_copy.get('class_id', 0)}")

            # Step 1: 先經 label_aliases 轉換得到標準名稱
            if self.processing_config.label_aliases:
                resolved_class_name = self.processing_config.label_aliases.get(
                    original_class_name, original_class_name)
            else:
                resolved_class_name = original_class_name

            # Step 2: 用標準名稱查找 class_info_by_name 獲取配置信息
            if (self.processing_config.class_info_by_name and
                    resolved_class_name in self.processing_config.class_info_by_name):
                info = self.processing_config.class_info_by_name[resolved_class_name]
                ann_copy.update({
                    # 強制使用標準英文名稱
                    'class_name': info.get('name', resolved_class_name),
                    'display_name': info.get('display_name', info.get('name', resolved_class_name)),
                    'color': info.get('color', [0, 255, 0])
                })
                # 確保class_id一致
                if 'class_id' in info:
                    ann_copy['class_id'] = info['class_id']
            else:
                # 如果找不到對應配置，使用默認值
                ann_copy.update({
                    'class_name': resolved_class_name,
                    'display_name': resolved_class_name,
                    'color': [0, 255, 0]
                })

            converted.append(ann_copy)
        return converted


# 便利函數
def create_advanced_slice_inference(
    model: Any,
    slice_height: int = 640,
    slice_width: int = 640,
    overlap_ratio: float = 0.3,
    fusion_strategy: str = "wbf", # 新增 "no_fusion" 選項
    fusion_iou_threshold: float = 0.1,
    fusion_confidence_threshold: float = 0.1,
    enable_overall_inference: bool = True,
    enable_adjacent_merge: bool = True,
    excluded_classes: List[Union[int, str]] = None,
    included_class_ids: Optional[List[int]] = None,
    log_level: str = "lightweight",
    class_confidence_thresholds: Optional[Dict[int, float]] = None,
    class_display_info: Optional[Dict[int, Dict[str, Any]]] = None,
    label_aliases: Optional[Dict[str, str]] = None,
    font_size: float = 0.7,
    font_thickness: int = 2,
    enable_roi_preview: bool = False,
    fill_alpha: float = 0.4,
    mask_render_mode: str = "unified",
    enable_roi_processing: bool = False,   # 新增參數
    roi_top_ratio: float = 2.0,            # 新增參數
    roi_bottom_ratio: float = 2.0,         # 新增參數
    roi_left_ratio: float = 2.0,           # 新增參數
    roi_right_ratio: float = 2.0           # 新增參數
) -> AdvancedSliceInference:
    """
    創建高級切片推理系統的便利函數

    Args:
        model: 檢測模型
        slice_height: 切片高度
        slice_width: 切片寬度
        overlap_ratio: 重疊比例
        fusion_strategy: 融合策略 ("standard_nms", "soft_nms", "wbf", "diou_nms", "cluster_nms", "largest_object")
        fusion_iou_threshold: 融合IoU閾值
        fusion_confidence_threshold: 融合置信度閾值
        enable_overall_inference: 啟用整體推理
        enable_adjacent_merge: 啟用相鄰合併
        excluded_classes: 排除的類別（ID或名稱列表）
        included_class_ids: 只包含的類別ID列表（None=包含所有）
        log_level: 日誌級別 ("detailed", "lightweight", "none")
        class_confidence_thresholds: 類別特定的置信度閾值
        class_display_info: 類別顯示信息
        label_aliases: 標籤別名映射
        font_size: 字體大小
        font_thickness: 字體粗細
        enable_roi_preview: 啟用ROI預覽
        fill_alpha: mask填充透明度
        mask_render_mode: Mask渲染模式 ("unified", "intelligent", "simple", "outline_only")

    Returns:
        AdvancedSliceInference實例
    """
    # 配置設定
    slice_config = SliceConfig(
        slice_height=slice_height,
        slice_width=slice_width,
        overlap_height_ratio=overlap_ratio,
        overlap_width_ratio=overlap_ratio
    )

    fusion_config = FusionConfig(
        strategy=FusionStrategy(fusion_strategy),
        iou_threshold=fusion_iou_threshold,
        confidence_threshold=fusion_confidence_threshold
    )

    # 構建 class_info_by_name - 優化版本
    _class_info_by_name = {}
    if class_display_info:
        for class_id, info in class_display_info.items():
            # 使用 'name' 作為標準名稱，這應該是英文的類別名
            canonical_name = info.get('name', f"class_{class_id}")
            _class_info_by_name[canonical_name] = {
                'class_id': class_id,
                'name': canonical_name,
                'display_name': info.get('display_name', canonical_name),
                'color': info.get('color', [0, 255, 0])  # 默認綠色
            }

    # 應用 label_aliases 到 _class_info_by_name - 優化邏輯
    if label_aliases and _class_info_by_name:
        for alias, canonical in label_aliases.items():
            # 如果別名指向的標準名稱存在於 class_info_by_name 中
            if canonical in _class_info_by_name:
                # 為別名創建條目，指向相同的類別信息
                _class_info_by_name[alias] = _class_info_by_name[canonical].copy(
                )
            # 如果別名本身就是標準名稱，確保它存在
            elif alias in _class_info_by_name and canonical not in _class_info_by_name:
                _class_info_by_name[canonical] = _class_info_by_name[alias].copy(
                )

    processing_config = ProcessingConfig(
        enable_overall_inference=enable_overall_inference,
        enable_adjacent_merge=enable_adjacent_merge,
        class_confidence_thresholds=class_confidence_thresholds,
        class_display_info=class_display_info,
        label_aliases=label_aliases,
        class_info_by_name=_class_info_by_name,
        font_size=font_size,
        font_thickness=font_thickness,
        enable_roi_preview=enable_roi_preview,
        fill_alpha=fill_alpha,
        enable_roi_processing=enable_roi_processing,  # 傳遞新增參數
        roi_top_ratio=roi_top_ratio,
        roi_bottom_ratio=roi_bottom_ratio,
        roi_left_ratio=roi_left_ratio,
        roi_right_ratio=roi_right_ratio
    )

    # 處理排除類別
    if excluded_classes:
        excluded_ids = [x for x in excluded_classes if isinstance(x, int)]
        excluded_names = [x for x in excluded_classes if isinstance(x, str)]
        processing_config.excluded_class_ids = excluded_ids
        processing_config.excluded_class_names = excluded_names

    # 處理包含類別
    if included_class_ids is not None:
        processing_config.included_class_ids = included_class_ids

    log_config = LogConfig(
        level=LogLevel(log_level),
        mask_render_mode=mask_render_mode
    )

    return AdvancedSliceInference(
        model=model,
        slice_config=slice_config,
        fusion_config=fusion_config,
        processing_config=processing_config,
        log_config=log_config
    )


if __name__ == "__main__":
    print("🚀 高級切片推理系統 - 測試模式")
    print("請使用 create_advanced_slice_inference() 函數創建實例")
