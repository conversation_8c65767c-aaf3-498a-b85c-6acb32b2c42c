#!/usr/bin/env python3
"""
🔧 快速修復導入問題
為所有缺少 typing 導入的文件添加必要的類型導入
"""

import os
import re
from pathlib import Path

def fix_typing_imports(file_path):
    """修復單個文件的 typing 導入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查是否使用了類型註解
        type_patterns = [
            r'\bOptional\[',
            r'\bTuple\[', 
            r'\bList\[',
            r'\bDict\[',
            r'\bUnion\[',
            r'\bAny\b',
            r'\bCallable\['
        ]
        
        uses_types = any(re.search(pattern, content) for pattern in type_patterns)
        
        if not uses_types:
            return False
        
        # 檢查是否已經有 typing 導入
        has_typing_import = re.search(r'from typing import', content)
        
        if has_typing_import:
            return False
        
        # 確定需要導入的類型
        needed_types = []
        if re.search(r'\bOptional\[', content):
            needed_types.append('Optional')
        if re.search(r'\bTuple\[', content):
            needed_types.append('Tuple')
        if re.search(r'\bList\[', content):
            needed_types.append('List')
        if re.search(r'\bDict\[', content):
            needed_types.append('Dict')
        if re.search(r'\bUnion\[', content):
            needed_types.append('Union')
        if re.search(r'\bAny\b', content):
            needed_types.append('Any')
        if re.search(r'\bCallable\[', content):
            needed_types.append('Callable')
        
        if not needed_types:
            return False
        
        # 找到合適的插入位置
        lines = content.split('\n')
        insert_pos = 0
        
        # 跳過文檔字符串和注釋
        in_docstring = False
        for i, line in enumerate(lines):
            stripped = line.strip()
            
            # 跳過空行和註釋
            if not stripped or stripped.startswith('#'):
                continue
            
            # 檢查文檔字符串
            if stripped.startswith('"""') or stripped.startswith("'''"):
                if in_docstring:
                    in_docstring = False
                    continue
                else:
                    in_docstring = True
                    continue
            
            if in_docstring:
                continue
            
            # 找到第一個 import 語句
            if stripped.startswith('import ') or stripped.startswith('from '):
                insert_pos = i
                break
        
        # 插入 typing 導入
        typing_import = f"from typing import {', '.join(needed_types)}"
        lines.insert(insert_pos, typing_import)
        
        # 寫回文件
        new_content = '\n'.join(lines)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 修復: {file_path} - 添加: {', '.join(needed_types)}")
        return True
        
    except Exception as e:
        print(f"❌ 錯誤: {file_path} - {e}")
        return False

def main():
    """主函數"""
    print("🔧 開始修復 typing 導入問題...")
    
    # 要檢查的目錄
    inference_system_dir = Path("D:/99_AI_model/road_ai_framework/inference_system")
    
    if not inference_system_dir.exists():
        print(f"❌ 目錄不存在: {inference_system_dir}")
        return
    
    fixed_count = 0
    total_count = 0
    
    # 遞歸檢查所有 .py 文件
    for py_file in inference_system_dir.rglob("*.py"):
        if py_file.name.startswith('__'):
            continue
        
        total_count += 1
        if fix_typing_imports(py_file):
            fixed_count += 1
    
    print(f"\n📊 修復完成:")
    print(f"   總文件數: {total_count}")
    print(f"   修復文件數: {fixed_count}")
    print(f"   成功率: {fixed_count/max(total_count,1)*100:.1f}%")

if __name__ == "__main__":
    main()