# 📦 模型版本管理系統
# Phase 4 核心功能 - 企業級模型版本控制和註冊中心

import os
import json
import logging
import hashlib
import shutil
import sqlite3
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
import zipfile
import tempfile
import semver

class ModelStatus(Enum):
    """模型狀態枚舉"""
    DRAFT = "draft"                    # 草稿
    TRAINING = "training"              # 訓練中
    VALIDATING = "validating"          # 驗證中
    APPROVED = "approved"              # 已批准
    PRODUCTION = "production"          # 生產中
    DEPRECATED = "deprecated"          # 已廢棄
    ARCHIVED = "archived"              # 已歸檔

class ModelType(Enum):
    """模型類型枚舉"""
    DETECTION = "detection"
    SEGMENTATION = "segmentation"
    CLASSIFICATION = "classification"
    VISION_MAMBA = "vision_mamba"
    CSP_IFORMER = "csp_iformer"
    CUSTOM = "custom"

@dataclass
class ModelMetrics:
    """模型性能指標"""
    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    mIoU: float = 0.0                  # 分割任務
    mAP: float = 0.0                   # 檢測任務
    inference_time_ms: float = 0.0     # 推理時間
    model_size_mb: float = 0.0         # 模型大小
    memory_usage_mb: float = 0.0       # 記憶體使用量
    
    # 自定義指標
    custom_metrics: Dict[str, float] = field(default_factory=dict)

@dataclass
class ModelVersion:
    """模型版本"""
    model_id: str
    version: str
    model_type: ModelType
    status: ModelStatus
    
    # 文件信息
    model_path: str
    config_path: Optional[str] = None
    metadata_path: Optional[str] = None
    
    # 版本信息
    created_by: str = "system"
    created_at: datetime = field(default_factory=datetime.now)
    parent_version: Optional[str] = None
    
    # 性能指標
    metrics: ModelMetrics = field(default_factory=ModelMetrics)
    
    # 訓練信息
    training_config: Dict[str, Any] = field(default_factory=dict)
    training_dataset: Optional[str] = None
    training_epochs: int = 0
    
    # 部署信息
    deployment_targets: List[str] = field(default_factory=list)
    compatibility: Dict[str, Any] = field(default_factory=dict)
    
    # 文檔和標籤
    description: str = ""
    tags: List[str] = field(default_factory=list)
    changelog: str = ""
    
    # 檔案校驗
    file_hash: str = ""
    file_size: int = 0

@dataclass
class ModelRegistry:
    """模型註冊表"""
    registry_id: str
    name: str
    description: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    
    # 版本管理
    versions: Dict[str, ModelVersion] = field(default_factory=dict)
    latest_version: Optional[str] = None
    production_version: Optional[str] = None
    
    # 訪問控制
    owners: List[str] = field(default_factory=list)
    collaborators: List[str] = field(default_factory=list)
    public: bool = False
    
    # 統計信息
    download_count: int = 0
    last_accessed: datetime = field(default_factory=datetime.now)


class ModelRegistryManager:
    """模型註冊中心管理器"""
    
    def __init__(self, storage_path: str = "./model_registry"):
        self.logger = logging.getLogger(__name__)
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        
        # 數據庫
        self.db_path = self.storage_path / "registry.db"
        self._init_database()
        
        # 內存緩存
        self.registries: Dict[str, ModelRegistry] = {}
        self._load_registries()
    
    def _init_database(self):
        """初始化數據庫"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS registries (
                    registry_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_at TEXT,
                    latest_version TEXT,
                    production_version TEXT,
                    owners TEXT,
                    public BOOLEAN,
                    download_count INTEGER DEFAULT 0,
                    last_accessed TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS model_versions (
                    version_id TEXT PRIMARY KEY,
                    registry_id TEXT,
                    version TEXT,
                    model_type TEXT,
                    status TEXT,
                    model_path TEXT,
                    config_path TEXT,
                    created_by TEXT,
                    created_at TEXT,
                    parent_version TEXT,
                    metrics TEXT,
                    training_config TEXT,
                    description TEXT,
                    tags TEXT,
                    file_hash TEXT,
                    file_size INTEGER,
                    FOREIGN KEY (registry_id) REFERENCES registries (registry_id)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS deployment_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    registry_id TEXT,
                    version TEXT,
                    target_environment TEXT,
                    deployed_by TEXT,
                    deployed_at TEXT,
                    status TEXT,
                    rollback_version TEXT,
                    FOREIGN KEY (registry_id) REFERENCES registries (registry_id)
                )
            """)
    
    def create_registry(self, name: str, description: str = "",
                       owners: List[str] = None, public: bool = False) -> str:
        """創建模型註冊表"""
        try:
            registry_id = self._generate_registry_id(name)
            
            if registry_id in self.registries:
                raise ValueError(f"註冊表已存在: {name}")
            
            registry = ModelRegistry(
                registry_id=registry_id,
                name=name,
                description=description,
                owners=owners or ["system"],
                public=public
            )
            
            # 創建存儲目錄
            registry_dir = self.storage_path / registry_id
            registry_dir.mkdir(exist_ok=True)
            
            # 存儲到數據庫和內存
            self._save_registry(registry)
            self.registries[registry_id] = registry
            
            self.logger.info(f"創建模型註冊表: {name} ({registry_id})")
            return registry_id
            
        except Exception as e:
            self.logger.error(f"創建註冊表失敗: {e}")
            raise

    def register_model_version(self, registry_id: str, model_path: str,
                             version: str, model_type: ModelType,
                             **kwargs) -> bool:
        """註冊模型版本"""
        try:
            registry = self.registries.get(registry_id)
            if not registry:
                raise ValueError(f"註冊表不存在: {registry_id}")
            
            # 驗證版本格式
            if not self._is_valid_version(version):
                raise ValueError(f"無效版本格式: {version}")
            
            # 檢查版本是否已存在
            if version in registry.versions:
                raise ValueError(f"版本已存在: {version}")
            
            # 計算文件hash和大小
            file_hash, file_size = self._calculate_file_info(model_path)
            
            # 複製模型文件到註冊中心
            registry_dir = self.storage_path / registry_id
            model_filename = f"{version}_{Path(model_path).name}"
            stored_model_path = registry_dir / "models" / model_filename
            stored_model_path.parent.mkdir(exist_ok=True)
            shutil.copy2(model_path, stored_model_path)
            
            # 創建模型版本
            model_version = ModelVersion(
                model_id=f"{registry_id}:{version}",
                version=version,
                model_type=model_type,
                status=ModelStatus.DRAFT,
                model_path=str(stored_model_path),
                file_hash=file_hash,
                file_size=file_size,
                **kwargs
            )
            
            # 添加到註冊表
            registry.versions[version] = model_version
            
            # 更新最新版本
            if not registry.latest_version or self._compare_versions(version, registry.latest_version) > 0:
                registry.latest_version = version
            
            # 持久化
            self._save_model_version(model_version)
            self._update_registry(registry)
            
            self.logger.info(f"註冊模型版本: {registry_id}:{version}")
            return True
            
        except Exception as e:
            self.logger.error(f"註冊模型版本失敗: {e}")
            return False

    def get_model_version(self, registry_id: str, version: str = None) -> Optional[ModelVersion]:
        """獲取模型版本"""
        registry = self.registries.get(registry_id)
        if not registry:
            return None
        
        # 如果未指定版本，返回最新版本
        if version is None:
            version = registry.latest_version
        
        if version is None:
            return None
        
        return registry.versions.get(version)

    def list_model_versions(self, registry_id: str, 
                          status_filter: Optional[ModelStatus] = None) -> List[ModelVersion]:
        """列出模型版本"""
        registry = self.registries.get(registry_id)
        if not registry:
            return []
        
        versions = list(registry.versions.values())
        
        if status_filter:
            versions = [v for v in versions if v.status == status_filter]
        
        # 按版本號排序
        versions.sort(key=lambda v: v.version, reverse=True)
        return versions

    def promote_version(self, registry_id: str, version: str, 
                       target_status: ModelStatus) -> bool:
        """提升版本狀態"""
        try:
            model_version = self.get_model_version(registry_id, version)
            if not model_version:
                return False
            
            old_status = model_version.status
            model_version.status = target_status
            
            # 如果提升到生產環境，更新註冊表
            registry = self.registries[registry_id]
            if target_status == ModelStatus.PRODUCTION:
                registry.production_version = version
                self._update_registry(registry)
            
            # 持久化更新
            self._save_model_version(model_version)
            
            self.logger.info(f"版本狀態更新: {registry_id}:{version} {old_status.value} -> {target_status.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"提升版本狀態失敗: {e}")
            return False

    def update_model_metrics(self, registry_id: str, version: str, 
                           metrics: ModelMetrics) -> bool:
        """更新模型性能指標"""
        model_version = self.get_model_version(registry_id, version)
        if not model_version:
            return False
        
        model_version.metrics = metrics
        self._save_model_version(model_version)
        
        self.logger.info(f"更新模型指標: {registry_id}:{version}")
        return True

    def compare_versions(self, registry_id: str, version1: str, 
                        version2: str) -> Dict[str, Any]:
        """比較兩個版本"""
        v1 = self.get_model_version(registry_id, version1)
        v2 = self.get_model_version(registry_id, version2)
        
        if not v1 or not v2:
            return {}
        
        comparison = {
            'version1': version1,
            'version2': version2,
            'metrics_comparison': {
                'accuracy': {
                    'v1': v1.metrics.accuracy,
                    'v2': v2.metrics.accuracy,
                    'diff': v2.metrics.accuracy - v1.metrics.accuracy
                },
                'inference_time': {
                    'v1': v1.metrics.inference_time_ms,
                    'v2': v2.metrics.inference_time_ms,
                    'diff': v2.metrics.inference_time_ms - v1.metrics.inference_time_ms
                },
                'model_size': {
                    'v1': v1.metrics.model_size_mb,
                    'v2': v2.metrics.model_size_mb,
                    'diff': v2.metrics.model_size_mb - v1.metrics.model_size_mb
                }
            },
            'status_comparison': {
                'v1': v1.status.value,
                'v2': v2.status.value
            },
            'created_at': {
                'v1': v1.created_at.isoformat(),
                'v2': v2.created_at.isoformat()
            }
        }
        
        return comparison

    def deploy_version(self, registry_id: str, version: str, 
                      target_environment: str, deployed_by: str = "system") -> bool:
        """部署版本"""
        try:
            model_version = self.get_model_version(registry_id, version)
            if not model_version:
                return False
            
            # 記錄部署歷史
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO deployment_history 
                    (registry_id, version, target_environment, deployed_by, deployed_at, status)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    registry_id, version, target_environment, deployed_by,
                    datetime.now().isoformat(), "deployed"
                ))
            
            # 更新部署目標
            if target_environment not in model_version.deployment_targets:
                model_version.deployment_targets.append(target_environment)
                self._save_model_version(model_version)
            
            self.logger.info(f"部署版本: {registry_id}:{version} -> {target_environment}")
            return True
            
        except Exception as e:
            self.logger.error(f"部署版本失敗: {e}")
            return False

    def rollback_deployment(self, registry_id: str, target_environment: str,
                          rollback_version: str, deployed_by: str = "system") -> bool:
        """回滾部署"""
        try:
            # 記錄回滾歷史
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO deployment_history 
                    (registry_id, version, target_environment, deployed_by, deployed_at, status, rollback_version)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    registry_id, rollback_version, target_environment, deployed_by,
                    datetime.now().isoformat(), "rollback", rollback_version
                ))
            
            self.logger.info(f"回滾部署: {registry_id} -> {rollback_version} 在 {target_environment}")
            return True
            
        except Exception as e:
            self.logger.error(f"回滾部署失敗: {e}")
            return False

    def get_deployment_history(self, registry_id: str, 
                             target_environment: str = None) -> List[Dict[str, Any]]:
        """獲取部署歷史"""
        history = []
        
        with sqlite3.connect(self.db_path) as conn:
            query = """
                SELECT version, target_environment, deployed_by, deployed_at, status, rollback_version
                FROM deployment_history
                WHERE registry_id = ?
            """
            params = [registry_id]
            
            if target_environment:
                query += " AND target_environment = ?"
                params.append(target_environment)
            
            query += " ORDER BY deployed_at DESC"
            
            cursor = conn.execute(query, params)
            for row in cursor.fetchall():
                version, env, deployed_by, deployed_at, status, rollback = row
                history.append({
                    'version': version,
                    'target_environment': env,
                    'deployed_by': deployed_by,
                    'deployed_at': deployed_at,
                    'status': status,
                    'rollback_version': rollback
                })
        
        return history

    def search_models(self, query: str, model_type: Optional[ModelType] = None,
                     tags: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """搜索模型"""
        results = []
        
        for registry in self.registries.values():
            # 名稱匹配
            if query.lower() in registry.name.lower() or query.lower() in registry.description.lower():
                for version in registry.versions.values():
                    # 類型過濾
                    if model_type and version.model_type != model_type:
                        continue
                    
                    # 標籤過濾
                    if tags and not any(tag in version.tags for tag in tags):
                        continue
                    
                    results.append({
                        'registry_id': registry.registry_id,
                        'registry_name': registry.name,
                        'version': version.version,
                        'model_type': version.model_type.value,
                        'status': version.status.value,
                        'created_at': version.created_at.isoformat(),
                        'accuracy': version.metrics.accuracy,
                        'tags': version.tags
                    })
        
        return sorted(results, key=lambda x: x['created_at'], reverse=True)

    def export_model(self, registry_id: str, version: str, 
                    export_path: str, include_metadata: bool = True) -> bool:
        """導出模型"""
        try:
            model_version = self.get_model_version(registry_id, version)
            if not model_version:
                return False
            
            export_path = Path(export_path)
            export_path.mkdir(parents=True, exist_ok=True)
            
            # 導出模型文件
            model_file = Path(model_version.model_path)
            if model_file.exists():
                shutil.copy2(model_file, export_path / model_file.name)
            
            # 導出元數據
            if include_metadata:
                metadata = {
                    'registry_id': registry_id,
                    'version': version,
                    'model_type': model_version.model_type.value,
                    'created_at': model_version.created_at.isoformat(),
                    'metrics': model_version.metrics.__dict__,
                    'training_config': model_version.training_config,
                    'description': model_version.description,
                    'tags': model_version.tags
                }
                
                with open(export_path / "metadata.json", 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"導出模型: {registry_id}:{version} -> {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"導出模型失敗: {e}")
            return False

    def _generate_registry_id(self, name: str) -> str:
        """生成註冊表ID"""
        timestamp = str(int(datetime.now().timestamp()))
        data = f"{name}_{timestamp}"
        return hashlib.md5(data.encode()).hexdigest()[:16]

    def _is_valid_version(self, version: str) -> bool:
        """驗證版本格式"""
        try:
            semver.VersionInfo.parse(version)
            return True
        except ValueError:
            return False

    def _compare_versions(self, version1: str, version2: str) -> int:
        """比較版本號"""
        try:
            v1 = semver.VersionInfo.parse(version1)
            v2 = semver.VersionInfo.parse(version2)
            return v1.compare(v2)
        except ValueError:
            return 0

    def _calculate_file_info(self, file_path: str) -> tuple:
        """計算文件hash和大小"""
        file_path = Path(file_path)
        if not file_path.exists():
            return "", 0
        
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        
        return hash_md5.hexdigest(), file_path.stat().st_size

    def _save_registry(self, registry: ModelRegistry):
        """保存註冊表到數據庫"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO registries 
                (registry_id, name, description, created_at, latest_version, 
                 production_version, owners, public, download_count, last_accessed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                registry.registry_id, registry.name, registry.description,
                registry.created_at.isoformat(), registry.latest_version,
                registry.production_version, json.dumps(registry.owners),
                registry.public, registry.download_count, registry.last_accessed.isoformat()
            ))

    def _save_model_version(self, version: ModelVersion):
        """保存模型版本到數據庫"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO model_versions
                (version_id, registry_id, version, model_type, status, model_path,
                 config_path, created_by, created_at, parent_version, metrics,
                 training_config, description, tags, file_hash, file_size)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                version.model_id, version.model_id.split(':')[0], version.version,
                version.model_type.value, version.status.value, version.model_path,
                version.config_path, version.created_by, version.created_at.isoformat(),
                version.parent_version, json.dumps(version.metrics.__dict__),
                json.dumps(version.training_config), version.description,
                json.dumps(version.tags), version.file_hash, version.file_size
            ))

    def _update_registry(self, registry: ModelRegistry):
        """更新註冊表"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                UPDATE registries SET latest_version = ?, production_version = ?
                WHERE registry_id = ?
            """, (registry.latest_version, registry.production_version, registry.registry_id))

    def _load_registries(self):
        """從數據庫加載註冊表"""
        # 實現從數據庫加載的邏輯
        pass

    def get_registry_statistics(self) -> Dict[str, Any]:
        """獲取註冊中心統計信息"""
        total_registries = len(self.registries)
        total_versions = sum(len(r.versions) for r in self.registries.values())
        
        status_counts = {}
        type_counts = {}
        
        for registry in self.registries.values():
            for version in registry.versions.values():
                status = version.status.value
                model_type = version.model_type.value
                
                status_counts[status] = status_counts.get(status, 0) + 1
                type_counts[model_type] = type_counts.get(model_type, 0) + 1
        
        return {
            'total_registries': total_registries,
            'total_versions': total_versions,
            'status_distribution': status_counts,
            'type_distribution': type_counts,
            'storage_size_gb': self._calculate_storage_size() / (1024**3)
        }

    def _calculate_storage_size(self) -> int:
        """計算存儲使用量"""
        total_size = 0
        for root, dirs, files in os.walk(self.storage_path):
            for file in files:
                file_path = Path(root) / file
                if file_path.exists():
                    total_size += file_path.stat().st_size
        return total_size


# 使用示例和測試
if __name__ == "__main__":
    # 配置日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建模型註冊中心管理器
    manager = ModelRegistryManager()
    
    # 創建註冊表
    registry_id = manager.create_registry(
        name="道路檢測模型",
        description="用於道路基礎設施檢測的AI模型集合",
        owners=["AI_Team"],
        public=True
    )
    
    print(f"✅ 創建註冊表: {registry_id}")
    
    # 註冊模型版本
    success = manager.register_model_version(
        registry_id=registry_id,
        model_path="./models/road_detection_v1.pt",  # 假設模型文件
        version="1.0.0",
        model_type=ModelType.DETECTION,
        description="第一個生產版本",
        created_by="AI_Engineer",
        tags=["production", "road_detection", "yolo"]
    )
    
    if success:
        print("✅ 註冊模型版本成功")
        
        # 更新性能指標
        metrics = ModelMetrics(
            accuracy=0.89,
            precision=0.87,
            recall=0.91,
            f1_score=0.89,
            mAP=0.85,
            inference_time_ms=45.2,
            model_size_mb=128.5,
            memory_usage_mb=512.0
        )
        
        manager.update_model_metrics(registry_id, "1.0.0", metrics)
        print("✅ 更新性能指標")
        
        # 提升到生產狀態
        manager.promote_version(registry_id, "1.0.0", ModelStatus.PRODUCTION)
        print("✅ 提升到生產狀態")
        
        # 部署版本
        manager.deploy_version(registry_id, "1.0.0", "production", "DevOps_Team")
        print("✅ 部署版本")
    
    # 獲取統計信息
    stats = manager.get_registry_statistics()
    print(f"\n📊 註冊中心統計:")
    print(f"   總註冊表數: {stats['total_registries']}")
    print(f"   總版本數: {stats['total_versions']}")
    print(f"   狀態分佈: {stats['status_distribution']}")
    print(f"   類型分佈: {stats['type_distribution']}")
    print(f"   存儲使用量: {stats['storage_size_gb']:.2f} GB")