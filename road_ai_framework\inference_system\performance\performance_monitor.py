#!/usr/bin/env python3
"""
📊 性能監控器
提供系統性能監控、指標收集和報告功能
"""

import time
import psutil
import logging
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque
import json
from pathlib import Path

from ..config import UnifiedConfig


@dataclass
class PerformanceMetrics:
    """性能指標數據結構"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    gpu_memory_mb: Optional[float] = None
    gpu_utilization: Optional[float] = None
    processing_time: Optional[float] = None
    throughput: Optional[float] = None
    active_threads: int = 0
    queue_size: int = 0


@dataclass
class SystemResources:
    """系統資源信息"""
    cpu_count: int
    total_memory_gb: float
    available_memory_gb: float
    gpu_count: int
    gpu_total_memory_gb: List[float] = field(default_factory=list)
    gpu_available_memory_gb: List[float] = field(default_factory=list)


class PerformanceMonitor:
    """
    性能監控器
    
    特色功能:
    - 實時系統監控
    - GPU監控支援
    - 性能指標收集
    - 自動報告生成
    - 閾值告警
    """
    
    def __init__(self, 
                 config: UnifiedConfig,
                 monitoring_interval: float = 1.0,
                 max_history_size: int = 1000):
        """
        初始化性能監控器
        
        Args:
            config: 統一配置
            monitoring_interval: 監控間隔(秒)
            max_history_size: 最大歷史記錄數量
        """
        self.config = config
        self.monitoring_interval = monitoring_interval
        self.max_history_size = max_history_size
        
        # 性能數據存儲
        self.metrics_history: deque = deque(maxlen=max_history_size)
        self.current_metrics: Optional[PerformanceMetrics] = None
        
        # 監控控制
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._lock = threading.RLock()
        
        # 性能統計
        self._start_time = time.time()
        self._processed_images = 0
        self._total_processing_time = 0.0
        
        # 告警閾值
        self.cpu_threshold = 90.0  # CPU使用率告警閾值
        self.memory_threshold = 90.0  # 內存使用率告警閾值
        self.gpu_memory_threshold = 90.0  # GPU內存使用率告警閾值
        
        # 告警回調
        self.alert_callbacks: List[Callable] = []
        
        self.logger = logging.getLogger(__name__)
        
        # GPU監控支援
        self._gpu_available = self._check_gpu_availability()
        
        # 獲取系統信息
        self.system_resources = self._get_system_resources()
        
        self.logger.info("📊 性能監控器初始化完成")
        self.logger.info(f"   監控間隔: {monitoring_interval}秒")
        self.logger.info(f"   歷史記錄: 最多{max_history_size}條")
        self.logger.info(f"   GPU支援: {'是' if self._gpu_available else '否'}")
    
    def _check_gpu_availability(self) -> bool:
        """檢查GPU監控可用性"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            try:
                import pynvml
                pynvml.nvmlInit()
                return True
            except ImportError:
                return False
    
    def _get_system_resources(self) -> SystemResources:
        """獲取系統資源信息"""
        # 基本系統信息
        cpu_count = psutil.cpu_count()
        memory = psutil.virtual_memory()
        total_memory_gb = memory.total / (1024**3)
        available_memory_gb = memory.available / (1024**3)
        
        # GPU信息
        gpu_count = 0
        gpu_total_memory = []
        gpu_available_memory = []
        
        if self._gpu_available:
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_count = torch.cuda.device_count()
                    for i in range(gpu_count):
                        props = torch.cuda.get_device_properties(i)
                        total_mem = props.total_memory / (1024**3)
                        
                        # 獲取可用內存
                        torch.cuda.set_device(i)
                        available_mem = torch.cuda.memory_reserved(i) / (1024**3)
                        
                        gpu_total_memory.append(total_mem)
                        gpu_available_memory.append(total_mem - available_mem)
            except Exception as e:
                self.logger.debug(f"GPU信息獲取失敗: {str(e)}")
        
        return SystemResources(
            cpu_count=cpu_count,
            total_memory_gb=total_memory_gb,
            available_memory_gb=available_memory_gb,
            gpu_count=gpu_count,
            gpu_total_memory_gb=gpu_total_memory,
            gpu_available_memory_gb=gpu_available_memory
        )
    
    def start_monitoring(self):
        """開始性能監控"""
        with self._lock:
            if not self._monitoring:
                self._monitoring = True
                self._monitor_thread = threading.Thread(
                    target=self._monitoring_loop,
                    daemon=True
                )
                self._monitor_thread.start()
                self.logger.info("📊 開始性能監控")
    
    def stop_monitoring(self):
        """停止性能監控"""
        with self._lock:
            if self._monitoring:
                self._monitoring = False
                if self._monitor_thread and self._monitor_thread.is_alive():
                    self._monitor_thread.join(timeout=2.0)
                self.logger.info("📊 停止性能監控")
    
    def _monitoring_loop(self):
        """監控循環"""
        while self._monitoring:
            try:
                metrics = self._collect_metrics()
                
                with self._lock:
                    self.current_metrics = metrics
                    self.metrics_history.append(metrics)
                
                # 檢查告警條件
                self._check_alerts(metrics)
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                self.logger.warning(f"⚠️ 性能監控錯誤: {str(e)}")
                time.sleep(self.monitoring_interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指標"""
        # 基本系統指標
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / (1024**2)
        
        # GPU指標
        gpu_memory_mb = None
        gpu_utilization = None
        
        if self._gpu_available:
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_memory_mb = torch.cuda.memory_allocated() / (1024**2)
                    # GPU利用率需要更複雜的實現，這裡暫時設為None
            except Exception:
                pass
        
        # 處理指標
        processing_time = None
        throughput = None
        
        if self._processed_images > 0:
            elapsed_time = time.time() - self._start_time
            throughput = self._processed_images / elapsed_time if elapsed_time > 0 else 0
            processing_time = self._total_processing_time / self._processed_images
        
        # 線程信息
        active_threads = threading.active_count()
        
        return PerformanceMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_used_mb,
            gpu_memory_mb=gpu_memory_mb,
            gpu_utilization=gpu_utilization,
            processing_time=processing_time,
            throughput=throughput,
            active_threads=active_threads,
            queue_size=0  # 需要外部設置
        )
    
    def _check_alerts(self, metrics: PerformanceMetrics):
        """檢查告警條件"""
        alerts = []
        
        # CPU告警
        if metrics.cpu_percent > self.cpu_threshold:
            alerts.append({
                'type': 'cpu_high',
                'message': f'CPU使用率過高: {metrics.cpu_percent:.1f}%',
                'value': metrics.cpu_percent,
                'threshold': self.cpu_threshold
            })
        
        # 內存告警
        if metrics.memory_percent > self.memory_threshold:
            alerts.append({
                'type': 'memory_high',
                'message': f'內存使用率過高: {metrics.memory_percent:.1f}%',
                'value': metrics.memory_percent,
                'threshold': self.memory_threshold
            })
        
        # GPU內存告警
        if (metrics.gpu_memory_mb is not None and 
            self.system_resources.gpu_total_memory_gb):
            gpu_memory_percent = (metrics.gpu_memory_mb / 1024) / self.system_resources.gpu_total_memory_gb[0] * 100
            if gpu_memory_percent > self.gpu_memory_threshold:
                alerts.append({
                    'type': 'gpu_memory_high',
                    'message': f'GPU內存使用率過高: {gpu_memory_percent:.1f}%',
                    'value': gpu_memory_percent,
                    'threshold': self.gpu_memory_threshold
                })
        
        # 觸發告警回調
        for alert in alerts:
            self.logger.warning(f"⚠️ {alert['message']}")
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"告警回調執行失敗: {str(e)}")
    
    def record_processing(self, processing_time: float):
        """記錄處理時間"""
        with self._lock:
            self._processed_images += 1
            self._total_processing_time += processing_time
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """獲取當前性能指標"""
        with self._lock:
            return self.current_metrics
    
    def get_metrics_history(self, 
                           last_n: Optional[int] = None,
                           time_range: Optional[timedelta] = None) -> List[PerformanceMetrics]:
        """
        獲取歷史性能指標
        
        Args:
            last_n: 最近N條記錄
            time_range: 時間範圍
            
        Returns:
            List[PerformanceMetrics]: 歷史指標列表
        """
        with self._lock:
            history = list(self.metrics_history)
            
            if time_range:
                cutoff_time = datetime.now() - time_range
                history = [m for m in history if m.timestamp >= cutoff_time]
            
            if last_n:
                history = history[-last_n:]
            
            return history
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """獲取性能摘要統計"""
        with self._lock:
            if not self.metrics_history:
                return {'error': '無性能數據'}
            
            recent_metrics = list(self.metrics_history)[-min(100, len(self.metrics_history)):]
            
            # 計算統計指標
            cpu_values = [m.cpu_percent for m in recent_metrics]
            memory_values = [m.memory_percent for m in recent_metrics]
            
            summary = {
                'monitoring_duration_minutes': (time.time() - self._start_time) / 60,
                'processed_images': self._processed_images,
                'system_resources': {
                    'cpu_count': self.system_resources.cpu_count,
                    'total_memory_gb': self.system_resources.total_memory_gb,
                    'gpu_count': self.system_resources.gpu_count
                },
                'cpu_stats': {
                    'current': recent_metrics[-1].cpu_percent,
                    'average': sum(cpu_values) / len(cpu_values),
                    'max': max(cpu_values),
                    'min': min(cpu_values)
                },
                'memory_stats': {
                    'current': recent_metrics[-1].memory_percent,
                    'average': sum(memory_values) / len(memory_values),
                    'max': max(memory_values),
                    'min': min(memory_values),
                    'current_used_mb': recent_metrics[-1].memory_used_mb
                }
            }
            
            # GPU統計
            gpu_memory_values = [m.gpu_memory_mb for m in recent_metrics if m.gpu_memory_mb is not None]
            if gpu_memory_values:
                summary['gpu_stats'] = {
                    'current_memory_mb': gpu_memory_values[-1],
                    'average_memory_mb': sum(gpu_memory_values) / len(gpu_memory_values),
                    'max_memory_mb': max(gpu_memory_values),
                    'min_memory_mb': min(gpu_memory_values)
                }
            
            # 處理性能統計
            if self._processed_images > 0:
                elapsed_time = time.time() - self._start_time
                summary['processing_stats'] = {
                    'total_processed': self._processed_images,
                    'total_processing_time': self._total_processing_time,
                    'average_processing_time': self._total_processing_time / self._processed_images,
                    'throughput_per_second': self._processed_images / elapsed_time,
                    'throughput_per_minute': self._processed_images / (elapsed_time / 60)
                }
            
            return summary
    
    def export_metrics(self, 
                      output_path: str,
                      format: str = 'json',
                      time_range: Optional[timedelta] = None) -> str:
        """
        導出性能指標
        
        Args:
            output_path: 輸出路徑
            format: 導出格式 ('json' 或 'csv')
            time_range: 時間範圍
            
        Returns:
            str: 導出文件路徑
        """
        metrics_data = self.get_metrics_history(time_range=time_range)
        
        output_file = Path(output_path) / f"performance_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{format}"
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        if format.lower() == 'json':
            # JSON格式導出
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'system_resources': {
                    'cpu_count': self.system_resources.cpu_count,
                    'total_memory_gb': self.system_resources.total_memory_gb,
                    'gpu_count': self.system_resources.gpu_count,
                    'gpu_total_memory_gb': self.system_resources.gpu_total_memory_gb
                },
                'performance_summary': self.get_performance_summary(),
                'metrics_data': [
                    {
                        'timestamp': m.timestamp.isoformat(),
                        'cpu_percent': m.cpu_percent,
                        'memory_percent': m.memory_percent,
                        'memory_used_mb': m.memory_used_mb,
                        'gpu_memory_mb': m.gpu_memory_mb,
                        'gpu_utilization': m.gpu_utilization,
                        'processing_time': m.processing_time,
                        'throughput': m.throughput,
                        'active_threads': m.active_threads,
                        'queue_size': m.queue_size
                    }
                    for m in metrics_data
                ]
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        elif format.lower() == 'csv':
            # CSV格式導出
            import csv
            
            with open(output_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 寫入標題
                headers = [
                    'timestamp', 'cpu_percent', 'memory_percent', 'memory_used_mb',
                    'gpu_memory_mb', 'gpu_utilization', 'processing_time', 
                    'throughput', 'active_threads', 'queue_size'
                ]
                writer.writerow(headers)
                
                # 寫入數據
                for m in metrics_data:
                    writer.writerow([
                        m.timestamp.isoformat(),
                        m.cpu_percent,
                        m.memory_percent,
                        m.memory_used_mb,
                        m.gpu_memory_mb,
                        m.gpu_utilization,
                        m.processing_time,
                        m.throughput,
                        m.active_threads,
                        m.queue_size
                    ])
        
        self.logger.info(f"📊 性能指標已導出: {output_file}")
        return str(output_file)
    
    def add_alert_callback(self, callback: Callable[[Dict], None]):
        """添加告警回調函數"""
        self.alert_callbacks.append(callback)
    
    def set_thresholds(self, 
                      cpu_threshold: Optional[float] = None,
                      memory_threshold: Optional[float] = None,
                      gpu_memory_threshold: Optional[float] = None):
        """設置告警閾值"""
        if cpu_threshold is not None:
            self.cpu_threshold = cpu_threshold
        if memory_threshold is not None:
            self.memory_threshold = memory_threshold
        if gpu_memory_threshold is not None:
            self.gpu_memory_threshold = gpu_memory_threshold
        
        self.logger.info(f"📊 告警閾值已更新: CPU={self.cpu_threshold}%, Memory={self.memory_threshold}%, GPU Memory={self.gpu_memory_threshold}%")
    
    def cleanup(self):
        """清理性能監控器資源"""
        self.logger.debug("🧹 清理性能監控器資源")
        self.stop_monitoring()
        
        with self._lock:
            self.metrics_history.clear()
            self.alert_callbacks.clear()