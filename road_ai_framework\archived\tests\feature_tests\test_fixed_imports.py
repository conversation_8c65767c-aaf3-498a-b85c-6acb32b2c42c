#!/usr/bin/env python3
"""
🔍 測試修復後的導入功能
"""

import sys
from pathlib import Path

# 添加路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_basic_inference_system():
    """測試基礎推理系統導入"""
    print("🔍 測試基礎推理系統...")
    
    try:
        from inference_system import create_inference_system, UnifiedConfig, SliceConfig, ClassConfig
        print("✅ 基礎推理系統導入成功")
        print(f"   - create_inference_system: {create_inference_system}")
        print(f"   - UnifiedConfig: {UnifiedConfig}")
        print(f"   - SliceConfig: {SliceConfig}")
        print(f"   - ClassConfig: {ClassConfig}")
        return True
    except Exception as e:
        print(f"❌ 基礎推理系統導入失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_phase4_components():
    """測試Phase 4組件導入"""
    print("\n🧠 測試Phase 4組件...")
    
    # 測試智能模型選擇器
    try:
        from intelligence.model_selector import IntelligentModelManager, ScenarioType
        print("✅ 智能模型選擇器導入成功")
    except Exception as e:
        print(f"⚠️ 智能模型選擇器導入失敗: {e}")
    
    # 測試多租戶管理
    try:
        from enterprise.multi_tenant import TenantManager, TenantMiddleware
        print("✅ 多租戶管理器導入成功")
    except Exception as e:
        print(f"⚠️ 多租戶管理器導入失敗: {e}")
    
    # 測試負載均衡
    try:
        from load_balancing.intelligent_balancer import IntelligentLoadBalancer
        print("✅ 智能負載均衡器導入成功")
    except Exception as e:
        print(f"⚠️ 智能負載均衡器導入失敗: {e}")

def test_dependencies():
    """測試關鍵依賴"""
    print("\n📦 測試關鍵依賴...")
    
    deps = ['torch', 'ultralytics', 'cv2', 'numpy', 'jwt', 'redis', 'fastapi']
    
    for dep in deps:
        try:
            if dep == 'jwt':
                import jwt
            elif dep == 'cv2':
                import cv2
            else:
                __import__(dep)
            print(f"✅ {dep}: 可用")
        except ImportError:
            print(f"❌ {dep}: 不可用")

def main():
    """主函數"""
    print("🚀 測試修復後的導入功能")
    print("=" * 50)
    
    # 測試基礎系統
    basic_success = test_basic_inference_system()
    
    # 測試Phase 4組件
    test_phase4_components()
    
    # 測試依賴
    test_dependencies()
    
    print("\n" + "=" * 50)
    if basic_success:
        print("✅ 基礎系統修復成功，可以繼續使用終極版本！")
    else:
        print("❌ 基礎系統仍有問題，建議使用 run_final_working.py")

if __name__ == "__main__":
    main()