#!/usr/bin/env python3
"""
🧠 智能YOLO推理系統 - Phase 4 整合版
整合了Phase 4所有智能化功能的終極YOLO推理系統

🌟 Phase 4 新功能:
- 🧠 智能模型選擇: 根據場景自動選擇最優模型
- 🏢 多租戶支援: 企業級用戶隔離和配額管理
- ⚖️ 智能負載均衡: AI驅動的處理資源分配
- 🌐 分散式處理: 跨節點並行推理能力
- 📦 模型版本管理: 自動選擇和切換模型版本
- 🌐 邊緣計算: 邊緣設備智能部署

🎯 使用場景:
- 企業級道路檢測服務
- 多租戶SaaS AI平台  
- 邊緣計算推理部署
- 大規模分散式處理
"""

from pathlib import Path
import sys
import logging
import asyncio
import time
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

# 確保導入路徑正確
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 原有YOLO系統導入
try:
    from inference_system import create_inference_system, UnifiedConfig, ClassConfig
    YOLO_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 原有YOLO系統不可用: {e}")
    YOLO_AVAILABLE = False

# Phase 4 智能化模組導入
try:
    from intelligence.model_selector import (
        IntelligentModelManager, ScenarioType, ModelProfile, ModelType
    )
    from enterprise.multi_tenant import (
        TenantManager, TenantType, SubscriptionTier, TenantMiddleware
    )
    from load_balancing.intelligent_balancer import (
        IntelligentLoadBalancer, LoadBalancingStrategy, ServerNode, ServerMetrics
    )
    from distributed.distributed_inference import (
        DistributedInferenceEngine, DistributedCluster, InferenceTask, TaskPriority
    )
    from versioning.model_registry import (
        ModelRegistryManager, ModelVersion, ModelStatus
    )
    PHASE4_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Phase 4 模組不可用: {e}")
    PHASE4_AVAILABLE = False

# ============================================================================
# 📋 智能化參數設定區域
# ============================================================================

# 🧠 智能模型選擇設定
enable_intelligent_model_selection = True              # 啟用智能模型選擇
default_scenario = ScenarioType.HIGH_ACCURACY          # 默認推理場景
model_switching_enabled = True                         # 啟用動態模型切換

# 🏢 多租戶設定
enable_multi_tenant = False                            # 啟用多租戶模式
default_tenant_tier = SubscriptionTier.PROFESSIONAL   # 默認租戶層級
tenant_api_key = None                                  # 租戶API密鑰

# ⚖️ 負載均衡設定
enable_load_balancing = False                          # 啟用負載均衡
load_balancing_strategy = LoadBalancingStrategy.INTELLIGENT  # 負載均衡策略

# 🌐 分散式處理設定
enable_distributed_processing = False                  # 啟用分散式處理
max_distributed_tasks = 8                             # 最大分散式任務數
task_priority = TaskPriority.NORMAL                   # 任務優先級

# 📦 模型版本管理設定
enable_model_registry = True                          # 啟用模型註冊中心
auto_model_update = True                              # 自動模型更新

# 🤖 模型設定 (支援多模型配置)
model_configs = {
    "primary": {
        "path": r"D:\4_road_crack\best_0728.pt",
        "type": ModelType.SEGMENTATION,
        "scenario": [ScenarioType.HIGH_ACCURACY, ScenarioType.BATCH_PROCESSING],
        "weight": 100
    },
    "secondary": {
        "path": r"D:\4_road_crack\yolo_fast.pt",  # 快速模型
        "type": ModelType.DETECTION,
        "scenario": [ScenarioType.REAL_TIME, ScenarioType.EDGE_COMPUTING],
        "weight": 80
    }
}

# 📁 路徑設定
input_path = r"D:\image\road_crack\test_600_resize"
output_path = r"D:\image\road_crack\test_600_out_intelligent"

# 🏷️ 智能類別配置 - 根據場景自動調整閾值
intelligent_class_configs = {
    0: {
        "name": "Alligator_crack", "display_name": "龜裂", "color": [0, 0, 255],
        "thresholds": {
            ScenarioType.HIGH_ACCURACY: 0.3,
            ScenarioType.REAL_TIME: 0.5,
            ScenarioType.EDGE_COMPUTING: 0.6
        },
        "enabled": True
    },
    6: {
        "name": "linear_crack", "display_name": "裂縫", "color": [0, 255, 0],
        "thresholds": {
            ScenarioType.HIGH_ACCURACY: 0.2,
            ScenarioType.REAL_TIME: 0.4,
            ScenarioType.EDGE_COMPUTING: 0.5
        },
        "enabled": True
    },
    10: {
        "name": "potholes", "display_name": "坑洞", "color": [0, 255, 255],
        "thresholds": {
            ScenarioType.HIGH_ACCURACY: 0.25,
            ScenarioType.REAL_TIME: 0.45,
            ScenarioType.EDGE_COMPUTING: 0.55
        },
        "enabled": True
    }
}

# 其他原有設定保持不變
enable_sahi = False
enable_intelligent_filtering = True
enable_three_view_output = True
three_view_layout = "horizontal"
font_size = 1.0
output_image_quality = 95

# ============================================================================
# 🧠 智能化YOLO推理系統類
# ============================================================================

class IntelligentYOLOSystem:
    """智能化YOLO推理系統 - 整合Phase 4所有功能"""
    
    def __init__(self):
        self.logger = self._setup_logging()
        
        # 核心組件初始化
        self.model_manager = None
        self.tenant_manager = None
        self.load_balancer = None
        self.distributed_engine = None
        self.model_registry = None
        self.yolo_inference_system = None
        
        # 當前狀態
        self.current_scenario = default_scenario
        self.current_tenant = None
        self.processing_stats = {
            'total_processed': 0,
            'successful_processed': 0,
            'failed_processed': 0,
            'model_switches': 0,
            'average_processing_time': 0.0
        }
        
        self.logger.info("🧠 初始化智能化YOLO推理系統")
        self._initialize_components()

    def _setup_logging(self) -> logging.Logger:
        """設置日誌系統"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('intelligent_yolo.log', encoding='utf-8')
            ]
        )
        return logging.getLogger(__name__)

    def _initialize_components(self):
        """初始化所有智能化組件"""
        try:
            # 1. 智能模型管理器
            if enable_intelligent_model_selection and PHASE4_AVAILABLE:
                self.logger.info("🧠 初始化智能模型管理器")
                self.model_manager = IntelligentModelManager()
                self._register_models()
            
            # 2. 多租戶管理器
            if enable_multi_tenant and PHASE4_AVAILABLE:
                self.logger.info("🏢 初始化多租戶管理器")
                self.tenant_manager = TenantManager()
                self.tenant_middleware = TenantMiddleware(self.tenant_manager)
            
            # 3. 負載均衡器
            if enable_load_balancing and PHASE4_AVAILABLE:
                self.logger.info("⚖️ 初始化智能負載均衡器")
                self.load_balancer = IntelligentLoadBalancer(load_balancing_strategy)
            
            # 4. 分散式推理引擎
            if enable_distributed_processing and PHASE4_AVAILABLE:
                self.logger.info("🌐 初始化分散式推理引擎")
                cluster_config = DistributedCluster(
                    cluster_id="intelligent_yolo_cluster",
                    cluster_name="智能YOLO推理集群",
                    coordinator_node="coordinator_001"
                )
                self.distributed_engine = DistributedInferenceEngine(cluster_config)
            
            # 5. 模型註冊中心
            if enable_model_registry and PHASE4_AVAILABLE:
                self.logger.info("📦 初始化模型註冊中心")
                self.model_registry = ModelRegistryManager()
            
            self.logger.info("✅ 所有智能化組件初始化完成")
            
        except Exception as e:
            self.logger.error(f"組件初始化失敗: {e}")

    def _register_models(self):
        """註冊模型到智能管理器"""
        if not self.model_manager:
            return
        
        for model_id, config in model_configs.items():
            try:
                profile = ModelProfile(
                    model_id=model_id,
                    model_type=config["type"],
                    model_path=config["path"],
                    accuracy_score=0.85,  # 可以從實際測試中獲得
                    speed_score=45.0,     # FPS
                    memory_usage=1200,    # MB
                    gpu_requirement=True,
                    supported_scenarios=config["scenario"],
                    confidence_threshold=0.3
                )
                
                self.model_manager.selector.register_model(profile)
                self.logger.info(f"註冊模型: {model_id}")
                
            except Exception as e:
                self.logger.error(f"模型註冊失敗 {model_id}: {e}")

    async def authenticate_tenant(self, api_key: str = None) -> bool:
        """租戶認證"""
        if not enable_multi_tenant or not self.tenant_middleware:
            return True
        
        if not api_key:
            api_key = tenant_api_key
        
        if not api_key:
            self.logger.warning("多租戶模式啟用但未提供API密鑰")
            return False
        
        try:
            tenant = await self.tenant_middleware.authenticate_request(api_key)
            if tenant:
                self.current_tenant = tenant
                self.logger.info(f"租戶認證成功: {tenant.tenant_name}")
                return True
            else:
                self.logger.error("租戶認證失敗")
                return False
                
        except Exception as e:
            self.logger.error(f"租戶認證異常: {e}")
            return False

    def select_optimal_model(self, scenario: ScenarioType = None) -> Optional[str]:
        """選擇最優模型"""
        if not enable_intelligent_model_selection or not self.model_manager:
            # 返回默認主模型
            return model_configs["primary"]["path"]
        
        current_scenario = scenario or self.current_scenario
        
        try:
            result = self.model_manager.smart_inference(
                image_path="",  # 實際使用時會替換
                scenario=current_scenario,
                accuracy_requirement=0.8,
                speed_requirement=30.0
            )
            
            if result and result.get('model_id'):
                selected_model_id = result['model_id']
                
                # 查找對應的模型路徑
                for model_id, config in model_configs.items():
                    if model_id == selected_model_id:
                        self.logger.info(f"智能選擇模型: {model_id} (場景: {current_scenario.value})")
                        return config["path"]
                
                # 如果沒找到，返回默認模型
                return model_configs["primary"]["path"]
            
        except Exception as e:
            self.logger.error(f"智能模型選擇失敗: {e}")
        
        return model_configs["primary"]["path"]

    def get_adaptive_class_config(self, scenario: ScenarioType = None) -> Dict:
        """獲取自適應類別配置"""
        current_scenario = scenario or self.current_scenario
        adaptive_config = {}
        
        for class_id, config in intelligent_class_configs.items():
            if not config["enabled"]:
                continue
            
            # 根據場景選擇閾值
            threshold = config["thresholds"].get(
                current_scenario, 
                config["thresholds"][ScenarioType.HIGH_ACCURACY]
            )
            
            adaptive_config[class_id] = ClassConfig(
                name=config["name"],
                display_name=config["display_name"],
                color=config["color"],
                confidence=threshold,
                sahi_confidence=threshold * 0.5,
                enabled=True
            )
        
        self.logger.info(f"自適應配置生成 (場景: {current_scenario.value}), "
                        f"類別數: {len(adaptive_config)}")
        return adaptive_config

    def create_intelligent_config(self, scenario: ScenarioType = None) -> UnifiedConfig:
        """創建智能化配置"""
        if not YOLO_AVAILABLE:
            raise RuntimeError("原有YOLO系統不可用")
        
        current_scenario = scenario or self.current_scenario
        
        # 選擇最優模型
        optimal_model_path = self.select_optimal_model(current_scenario)
        
        # 獲取自適應類別配置
        adaptive_classes = self.get_adaptive_class_config(current_scenario)
        
        # 創建配置
        config = UnifiedConfig()
        
        # 模型配置
        config.model.model_path = optimal_model_path
        
        # 根據場景調整SAHI設定
        if current_scenario == ScenarioType.HIGH_ACCURACY:
            config.processing.slice.enabled = True
            config.processing.slice.height = 640
            config.processing.slice.width = 640
            config.processing.slice.overlap_ratio = 0.3
        elif current_scenario == ScenarioType.REAL_TIME:
            config.processing.slice.enabled = False  # 實時處理關閉SAHI
        elif current_scenario == ScenarioType.EDGE_COMPUTING:
            config.processing.slice.enabled = False
            config.visualization.enable_three_view = False  # 邊緣設備簡化輸出
        
        # 視覺化配置
        config.visualization.enable_three_view = enable_three_view_output
        config.visualization.three_view_layout = three_view_layout
        config.visualization.font_size = font_size
        config.visualization.output_image_quality = output_image_quality
        
        # 智能過濾配置
        config.enable_intelligent_filtering = enable_intelligent_filtering
        
        # 類別配置
        config.classes = adaptive_classes
        
        # 輸出配置
        config.output_dir = output_path
        
        self.logger.info(f"智能配置生成完成 (場景: {current_scenario.value})")
        return config

    async def process_single_image_intelligent(self, image_path: str, 
                                             scenario: ScenarioType = None) -> Dict[str, Any]:
        """智能化單圖處理"""
        start_time = time.time()
        current_scenario = scenario or self.current_scenario
        
        try:
            # 租戶認證
            if enable_multi_tenant:
                if not await self.authenticate_tenant():
                    return {"success": False, "error": "租戶認證失敗"}
            
            # 分散式處理
            if enable_distributed_processing and self.distributed_engine:
                return await self._process_with_distributed_engine(image_path, current_scenario)
            
            # 標準智能處理
            return await self._process_with_intelligent_yolo(image_path, current_scenario)
            
        except Exception as e:
            self.logger.error(f"智能處理失敗: {e}")
            return {"success": False, "error": str(e)}
        finally:
            processing_time = time.time() - start_time
            self._update_processing_stats(processing_time, True)

    async def _process_with_distributed_engine(self, image_path: str, 
                                             scenario: ScenarioType) -> Dict[str, Any]:
        """使用分散式引擎處理"""
        task = InferenceTask(
            task_id=f"yolo_task_{int(time.time())}",
            input_data={"image_path": image_path, "scenario": scenario.value},
            model_id="intelligent_yolo",
            priority=task_priority,
            requires_gpu=True
        )
        
        task_id = self.distributed_engine.submit_task(task)
        
        # 等待任務完成
        max_wait_time = 60  # 60秒超時
        wait_interval = 0.5
        waited_time = 0
        
        while waited_time < max_wait_time:
            task_status = self.distributed_engine.get_task_status(task_id)
            if task_status and task_status.status.value == 'completed':
                self.logger.info(f"分散式任務完成: {task_id}")
                return {
                    "success": True,
                    "result": task_status.result,
                    "processing_time": task_status.processing_time,
                    "processed_by": "distributed_engine"
                }
            elif task_status and task_status.status.value == 'failed':
                return {
                    "success": False,
                    "error": task_status.error_message or "分散式處理失敗"
                }
            
            await asyncio.sleep(wait_interval)
            waited_time += wait_interval
        
        return {"success": False, "error": "分散式處理超時"}

    async def _process_with_intelligent_yolo(self, image_path: str, 
                                           scenario: ScenarioType) -> Dict[str, Any]:
        """使用智能YOLO處理"""
        # 創建智能配置
        config = self.create_intelligent_config(scenario)
        
        # 創建YOLO推理系統
        with create_inference_system(config=config) as inference_system:
            result = inference_system.process_single_image(
                image_path,
                enable_visualization=True,
                enable_statistics=True
            )
            
            # 添加智能化信息
            if result['success']:
                result['intelligent_info'] = {
                    'selected_scenario': scenario.value,
                    'model_path': config.model.model_path,
                    'adaptive_thresholds': {
                        class_id: cls_config.confidence_threshold 
                        for class_id, cls_config in config.classes.items()
                    },
                    'processed_by': 'intelligent_yolo'
                }
            
            return result

    async def process_directory_intelligent(self, directory_path: str,
                                          scenario: ScenarioType = None) -> Dict[str, Any]:
        """智能化批量目錄處理"""
        current_scenario = scenario or self.current_scenario
        
        try:
            # 租戶認證
            if enable_multi_tenant:
                if not await self.authenticate_tenant():
                    return {"success": False, "error": "租戶認證失敗"}
            
            # 創建智能配置
            config = self.create_intelligent_config(current_scenario)
            
            # 執行批量處理
            with create_inference_system(config=config) as inference_system:
                summary = inference_system.process_directory(directory_path)
                
                # 添加智能化統計
                if 'success_rate' in summary:
                    summary['intelligent_info'] = {
                        'scenario': current_scenario.value,
                        'model_switches': self.processing_stats['model_switches'],
                        'intelligent_features_enabled': {
                            'model_selection': enable_intelligent_model_selection,
                            'multi_tenant': enable_multi_tenant,
                            'load_balancing': enable_load_balancing,
                            'distributed': enable_distributed_processing
                        }
                    }
                
                return summary
            
        except Exception as e:
            self.logger.error(f"智能批量處理失敗: {e}")
            return {"success": False, "error": str(e)}

    def change_scenario(self, new_scenario: ScenarioType):
        """動態切換處理場景"""
        old_scenario = self.current_scenario
        self.current_scenario = new_scenario
        
        if old_scenario != new_scenario:
            self.processing_stats['model_switches'] += 1
            self.logger.info(f"場景切換: {old_scenario.value} → {new_scenario.value}")

    def get_system_status(self) -> Dict[str, Any]:
        """獲取系統狀態"""
        status = {
            'current_scenario': self.current_scenario.value,
            'processing_stats': self.processing_stats.copy(),
            'components_status': {
                'yolo_system': YOLO_AVAILABLE,
                'intelligent_model_selection': bool(self.model_manager),
                'multi_tenant': bool(self.tenant_manager),
                'load_balancing': bool(self.load_balancer),
                'distributed_processing': bool(self.distributed_engine),
                'model_registry': bool(self.model_registry)
            }
        }
        
        # 添加組件具體統計
        if self.model_manager:
            status['model_manager_stats'] = self.model_manager.get_manager_stats()
        
        if self.load_balancer:
            status['load_balancer_stats'] = self.load_balancer.get_statistics()
        
        if self.distributed_engine:
            status['distributed_stats'] = self.distributed_engine.get_cluster_status()
        
        return status

    def _update_processing_stats(self, processing_time: float, success: bool):
        """更新處理統計"""
        self.processing_stats['total_processed'] += 1
        
        if success:
            self.processing_stats['successful_processed'] += 1
        else:
            self.processing_stats['failed_processed'] += 1
        
        # 更新平均處理時間
        total_success = self.processing_stats['successful_processed']
        if total_success > 0:
            current_avg = self.processing_stats['average_processing_time']
            new_avg = ((current_avg * (total_success - 1)) + processing_time) / total_success
            self.processing_stats['average_processing_time'] = new_avg

    def stop_all_components(self):
        """停止所有組件"""
        self.logger.info("🛑 停止所有智能化組件")
        
        try:
            if self.load_balancer:
                self.load_balancer.stop()
            
            if self.distributed_engine:
                self.distributed_engine.stop()
            
            self.logger.info("✅ 所有組件已停止")
            
        except Exception as e:
            self.logger.error(f"停止組件時發生錯誤: {e}")


# ============================================================================
# 🚀 主執行邏輯
# ============================================================================

async def main():
    """主函數 - 執行智能化YOLO推理"""
    
    print("🧠 智能化YOLO推理系統 - Phase 4 整合版")
    print("=" * 60)
    
    if not YOLO_AVAILABLE:
        print("❌ 原有YOLO系統不可用")
        return
    
    try:
        # 創建智能YOLO系統
        intelligent_system = IntelligentYOLOSystem()
        
        # 檢查輸入路徑
        input_path_obj = Path(input_path)
        if not input_path_obj.exists():
            raise ValueError(f"輸入路徑不存在: {input_path}")
        
        # 執行處理
        if input_path_obj.is_file():
            # 單張圖像智能處理
            print(f"📸 智能處理單張圖像: {input_path_obj.name}")
            result = await intelligent_system.process_single_image_intelligent(
                str(input_path_obj),
                scenario=default_scenario
            )
            
            if result['success']:
                print(f"✅ 智能處理完成!")
                if 'intelligent_info' in result:
                    print(f"🧠 智能信息: {result['intelligent_info']}")
                print(f"📊 檢測結果: {len(result.get('detections', []))} 個目標")
            else:
                print(f"❌ 處理失敗: {result['error']}")
        
        elif input_path_obj.is_dir():
            # 批量目錄智能處理
            print(f"📁 智能批量處理目錄: {input_path}")
            summary = await intelligent_system.process_directory_intelligent(
                str(input_path_obj),
                scenario=default_scenario
            )
            
            if 'success_rate' in summary:
                print(f"✅ 智能批量處理完成!")
                print(f"📊 成功率: {summary['success_rate']:.1f}%")
                print(f"📊 總圖像數: {summary['total_images']}")
                print(f"📊 總檢測數: {summary['total_detections']}")
                if 'intelligent_info' in summary:
                    print(f"🧠 智能信息: {summary['intelligent_info']}")
            else:
                print(f"❌ 批量處理失敗: {summary.get('error', '未知錯誤')}")
        
        # 顯示系統狀態
        print(f"\n📊 系統狀態:")
        status = intelligent_system.get_system_status()
        print(f"   當前場景: {status['current_scenario']}")
        print(f"   處理統計: {status['processing_stats']}")
        print(f"   組件狀態: {status['components_status']}")
        
        # 等待用戶輸入
        print(f"\n💡 智能化YOLO推理完成!")
        print(f"   系統將繼續運行，按 Enter 鍵停止...")
        input()
        
        # 停止所有組件
        intelligent_system.stop_all_components()
        
        print("✅ 智能化YOLO系統已完全停止")
        
    except KeyboardInterrupt:
        print("\n⚠️ 收到中斷信號，正在停止系統...")
        if 'intelligent_system' in locals():
            intelligent_system.stop_all_components()
    except Exception as e:
        print(f"❌ 系統運行失敗: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())