#!/usr/bin/env python3
"""
統一訓練系統使用示例
展示如何使用整合90%+重複功能的統一訓練系統
支援Vision Mamba、CSP_IFormer等所有架構
"""

from pathlib import Path
import sys

# 使用統一導入管理
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from import_helper import setup_project_paths, TRAINING_AVAILABLE, VISION_MAMBA_AVAILABLE
setup_project_paths()

def main():
    """統一訓練系統使用示例主函數"""
    
    print("🎓 統一訓練系統使用示例")
    print("=" * 50)
    print("整合90%+重複功能的現代化訓練架構")
    print()
    
    # ===================================================================
    # 📋 參數設定區域 - 請根據您的需求修改以下參數
    # ===================================================================
    
    # 🎯 模型配置
    model_type = "vision_mamba"                    # 模型類型: "vision_mamba", "csp_iformer", "cnn"
    model_size = "small"                           # 模型大小: "tiny", "small", "base"
    num_classes = 5                                # 類別數量
    
    # 📁 數據配置
    train_data_dir = "./output/split_dataset/train"  # 訓練數據目錄
    val_data_dir = "./output/split_dataset/val"      # 驗證數據目錄
    test_data_dir = "./output/split_dataset/test"    # 測試數據目錄
    
    # 🎛️ 訓練超參數
    batch_size = 16                                # 批次大小
    learning_rate = 1e-4                           # 初始學習率
    num_epochs = 50                                # 訓練輪數
    weight_decay = 1e-5                            # 權重衰減
    
    # 🚀 現代化訓練特性
    enable_mixed_precision = True                  # 混合精度訓練 (節省30-50%記憶體)
    gradient_accumulation_steps = 4                # 梯度累積步數 (支援大batch訓練)
    enable_early_stopping = True                  # 智能早停
    early_stopping_patience = 10                  # 早停耐心值
    
    # 📊 優化器和調度器配置
    optimizer_type = "adamw"                       # 優化器: "adam", "adamw", "sgd"
    scheduler_type = "cosine"                      # 調度器: "cosine", "step", "plateau"
    warmup_epochs = 5                              # 預熱輪數
    
    # 🔧 檢查點和日誌配置
    save_checkpoint_every = 5                      # 檢查點保存頻率
    log_every = 10                                 # 日誌記錄頻率
    enable_tensorboard = True                      # 啟用TensorBoard
    
    # 📁 輸出路徑配置
    output_dir = "./output/training"               # 訓練輸出目錄
    model_save_path = "./output/training/best_model.pth"  # 最佳模型保存路徑
    log_dir = "./output/training/logs"             # 日誌目錄
    
    # 🌐 分散式訓練配置 (可選)
    enable_distributed = False                     # 啟用分散式訓練
    num_gpus = 1                                   # GPU數量
    
    # 🎨 數據增強配置
    enable_data_augmentation = True                # 啟用數據增強
    img_size = 224                                 # 圖像大小
    
    # ===================================================================
    # 🔍 執行檢查和驗證
    # ===================================================================
    
    if not TRAINING_AVAILABLE:
        print("❌ 統一訓練系統不可用")
        print("請檢查AI模型建構訓練驗證/model_create/training/目錄")
        return
    
    print("✅ 統一訓練系統可用")
    
    # 檢查數據目錄
    data_dirs_exist = all([
        Path(train_data_dir).exists(),
        Path(val_data_dir).exists()
    ])
    
    if not data_dirs_exist:
        print("⚠️  警告: 數據目錄不存在，將使用虛擬數據進行演示")
        print(f"   預期訓練目錄: {train_data_dir}")
        print(f"   預期驗證目錄: {val_data_dir}")
        print("   請先運行data_preprocessing.py生成分割數據集")
    
    # 創建輸出目錄
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    # ===================================================================
    # 🚀 執行統一訓練流程
    # ===================================================================
    
    try:
        # 導入統一訓練相關模組
        from AI模型建構訓練驗證.model_create.training import (
            UnifiedTrainer,
            TrainingConfig,
            create_optimizer,
            create_scheduler
        )
        from AI模型建構訓練驗證.model_create.core import ModelFactory
        
        print("✅ 統一訓練模組導入成功")
        
        # ===============================================================
        # 1. 🏗️ 模型創建
        # ===============================================================
        
        print("\\n🏗️ 步驟1: 模型創建")
        print("-" * 30)
        
        factory = ModelFactory()
        
        if model_type == "vision_mamba" and VISION_MAMBA_AVAILABLE:
            # 創建Vision Mamba模型
            model_config = {
                "model_type": "vision_mamba",
                "size": model_size,
                "num_classes": num_classes,
                "img_size": img_size
            }
            print(f"🧠 創建Vision Mamba {model_size}模型...")
            
        elif model_type == "csp_iformer":
            # 創建CSP_IFormer模型
            model_config = {
                "model_type": "csp_iformer",
                "variant": "final_segmentation",
                "num_classes": num_classes,
                "backbone_config": {
                    "enable_channel_shuffle": True,
                    "enable_dropkey": True,
                    "dropout_rate": 0.3
                }
            }
            print(f"🏭 創建CSP_IFormer模型...")
            
        else:
            # 使用基礎CNN模型進行演示
            model_config = {
                "model_type": "cnn",
                "architecture": "resnet18",
                "num_classes": num_classes
            }
            print(f"🔧 創建CNN模型進行演示...")
        
        try:
            model = factory.create_model(model_config)
            print("✅ 模型創建成功")
        except Exception as e:
            print(f"⚠️  模型創建失敗: {e}")
            print("使用虛擬模型進行演示...")
            # 創建虛擬模型進行演示
            model = None
        
        # ===============================================================
        # 2. ⚙️ 訓練配置創建
        # ===============================================================
        
        print("\\n⚙️ 步驟2: 訓練配置")
        print("-" * 30)
        
        # 創建訓練配置
        training_config = TrainingConfig(
            # 基礎參數
            epochs=num_epochs,
            batch_size=batch_size,
            learning_rate=learning_rate,
            weight_decay=weight_decay,
            
            # 現代化特性
            enable_mixed_precision=enable_mixed_precision,
            gradient_accumulation_steps=gradient_accumulation_steps,
            enable_early_stopping=enable_early_stopping,
            early_stopping_patience=early_stopping_patience,
            
            # 優化器和調度器
            optimizer_type=optimizer_type,
            scheduler_type=scheduler_type,
            warmup_epochs=warmup_epochs,
            
            # 檢查點和日誌
            save_checkpoint_every=save_checkpoint_every,
            log_every=log_every,
            enable_tensorboard=enable_tensorboard,
            
            # 路徑配置
            output_dir=output_dir,
            log_dir=log_dir,
            
            # 分散式配置
            enable_distributed=enable_distributed,
            num_gpus=num_gpus
        )
        
        print("📊 訓練配置:")
        print(f"   📐 批次大小: {batch_size}")
        print(f"   🎓 學習率: {learning_rate}")
        print(f"   🔄 訓練輪數: {num_epochs}")
        print(f"   ⚡ 混合精度: {'啟用' if enable_mixed_precision else '禁用'}")
        print(f"   📈 梯度累積: {gradient_accumulation_steps}步")
        print(f"   🛑 早停: {'啟用' if enable_early_stopping else '禁用'}")
        print(f"   🔧 優化器: {optimizer_type}")
        print(f"   📊 調度器: {scheduler_type}")
        
        # ===============================================================
        # 3. 📊 數據載入器創建
        # ===============================================================
        
        print("\\n📊 步驟3: 數據載入器")
        print("-" * 30)
        
        try:
            # 導入數據載入相關模組
            from AI模型建構訓練驗證.model_create.util import create_dataloader
            
            if data_dirs_exist:
                # 創建真實數據載入器
                train_loader = create_dataloader(
                    data_dir=train_data_dir,
                    batch_size=batch_size,
                    shuffle=True,
                    augment=enable_data_augmentation
                )
                
                val_loader = create_dataloader(
                    data_dir=val_data_dir,
                    batch_size=batch_size,
                    shuffle=False,
                    augment=False
                )
                
                print(f"✅ 數據載入器創建成功")
                print(f"   🎓 訓練樣本: {len(train_loader.dataset) if hasattr(train_loader, 'dataset') else '未知'}")
                print(f"   ✅ 驗證樣本: {len(val_loader.dataset) if hasattr(val_loader, 'dataset') else '未知'}")
                
            else:
                print("⚠️  使用虛擬數據載入器進行演示")
                train_loader = None
                val_loader = None
                
        except ImportError:
            print("⚠️  數據載入模組不可用，使用虛擬載入器")
            train_loader = None
            val_loader = None
        
        # ===============================================================
        # 4. 🎓 統一訓練器創建和訓練
        # ===============================================================
        
        print("\\n🎓 步驟4: 統一訓練器")
        print("-" * 30)
        
        if model is not None:
            # 創建優化器
            optimizer = create_optimizer(model, training_config)
            
            # 創建學習率調度器
            scheduler = create_scheduler(optimizer, training_config)
            
            # 創建統一訓練器
            trainer = UnifiedTrainer(
                model=model,
                optimizer=optimizer,
                scheduler=scheduler,
                config=training_config
            )
            
            print("✅ 統一訓練器創建成功")
            print("🎯 特色功能:")
            print("   ⚡ 自動混合精度訓練")
            print("   📈 智能梯度累積")
            print("   🛑 自適應早停機制")
            print("   💾 自動檢查點管理")
            print("   📊 實時訓練監控")
            print("   🔄 斷點續訓支援")
            
            if train_loader is not None and val_loader is not None:
                print("\\n🚀 開始訓練...")
                
                # 執行訓練
                history = trainer.fit(
                    train_loader=train_loader,
                    val_loader=val_loader
                )
                
                print("✅ 訓練完成!")
                print(f"📊 最佳驗證準確率: {history.get('best_val_acc', '未知'):.4f}")
                print(f"⏱️  總訓練時間: {history.get('total_time', '未知'):.2f}秒")
                print(f"💾 最佳模型保存至: {model_save_path}")
                
            else:
                print("⚠️  數據載入器不可用，跳過實際訓練")
                print("✅ 訓練器配置演示完成")
        
        # ===============================================================
        # 5. 📊 分散式訓練演示 (如果啟用)
        # ===============================================================
        
        if enable_distributed:
            print("\\n🌐 步驟5: 分散式訓練")
            print("-" * 30)
            
            try:
                # 導入分散式訓練模組
                from AI模型建構訓練驗證.model_create.distributed import (
                    create_ray_training_system,
                    RayTrainingConfig
                )
                
                # 創建Ray訓練系統
                manager, training_system = create_ray_training_system()
                
                # 分散式訓練配置
                ray_config = RayTrainingConfig(
                    num_workers=num_gpus,
                    use_gpu=True,
                    resources_per_worker={'CPU': 2, 'GPU': 1.0/num_gpus}
                )
                
                print(f"🌐 Ray分散式訓練配置:")
                print(f"   🔧 工作器數量: {num_gpus}")
                print(f"   🎮 GPU支援: 啟用")
                print(f"   📊 資源分配: 自動")
                
                print("✅ 分散式訓練系統就緒")
                print("   🚀 支援多機多卡訓練")
                print("   📊 自動超參數優化")
                print("   💾 分散式檢查點管理")
                
            except ImportError:
                print("⚠️  Ray分散式模組不可用")
        
        # ===============================================================
        # 📊 生成訓練報告
        # ===============================================================
        
        print("\\n📊 生成訓練報告")
        print("-" * 30)
        
        report = {
            "模型配置": {
                "類型": model_type,
                "大小": model_size,
                "類別數": num_classes
            },
            "訓練配置": {
                "批次大小": batch_size,
                "學習率": learning_rate,
                "訓練輪數": num_epochs,
                "混合精度": enable_mixed_precision,
                "梯度累積": gradient_accumulation_steps,
                "早停": enable_early_stopping
            },
            "優化配置": {
                "優化器": optimizer_type,
                "調度器": scheduler_type,
                "預熱輪數": warmup_epochs
            },
            "輸出路徑": {
                "模型": model_save_path,
                "日誌": log_dir,
                "檢查點": output_dir
            }
        }
        
        # 保存報告
        import json
        report_path = f"{output_dir}/training_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 訓練報告保存至: {report_path}")
        
        # ===============================================================
        # 🎯 使用建議
        # ===============================================================
        
        print("\\n📚 使用建議:")
        print("1. 🧠 Vision Mamba: 推薦用於高解析度道路圖像，線性複雜度優勢明顯")
        print("2. 🏭 CSP_IFormer: 原創架構，適合學術研究和論文發表")
        print("3. ⚡ 混合精度: 大幅節省顯存，支援更大batch_size")
        print("4. 🌐 分散式: 多GPU/多機訓練，顯著加速大模型訓練")
        
        print("\\n🔗 與其他模組整合:")
        print("   📁 資料前處理: 自動載入分割後的數據集")
        print("   🚀 Enhanced YOLO: 訓練後可轉換為YOLO格式")
        print("   📊 評估系統: 自動生成混淆矩陣和性能報告")
        
        print(f"\\n🎉 統一訓練系統演示完成! 所有結果保存至: {output_dir}")
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        print("請確保訓練相關依賴已安裝:")
        print("   pip install torch torchvision")
        print("   pip install tensorboard")
        print("   pip install ray[tune,train] (分散式訓練)")
        
    except Exception as e:
        print(f"❌ 統一訓練演示失敗: {e}")
        import traceback
        print("詳細錯誤信息:")
        traceback.print_exc()

if __name__ == "__main__":
    main()