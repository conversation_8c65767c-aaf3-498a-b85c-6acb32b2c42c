import os
import shutil
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog
from PIL import Image


def is_image_file(filepath):
    """檢查文件是否為有效的圖像文件"""
    try:
        with Image.open(filepath) as img:
            img.verify()
        return True
    except (IOError, SyntaxError):
        return False


def copy_images():
    # 選擇源資料夾

    source_folder = filedialog.askdirectory(title="選擇包含圖像的資料夾")
    if not source_folder:
        return

    # 詢問要複製的圖像數量
    try:
        num_images = simpledialog.askinteger("輸入數量", "要複製多少張圖像?", minvalue=1)
        if not num_images:
            return
    except:
        messagebox.showerror("錯誤", "請輸入有效的數字")
        return

    # 創建輸出資料夾
    output_folder = os.path.join(source_folder, "output")
    os.makedirs(output_folder, exist_ok=True)

    # 獲取所有圖像文件
    image_files = []
    for root, _, files in os.walk(source_folder):
        for file in files:
            file_path = os.path.join(root, file)
            if is_image_file(file_path):
                image_files.append(file_path)

    if not image_files:
        messagebox.showinfo("無圖像", "在選定的資料夾中未找到任何圖像文件")
        return

    # 限制數量不超過找到的圖像總數
    num_images = min(num_images, len(image_files))

    # 複製圖像
    copied = 0
    for i, image_path in enumerate(image_files[:num_images]):
        try:
            filename = os.path.basename(image_path)
            dest_path = os.path.join(output_folder, filename)

            # 處理文件名衝突
            counter = 1
            while os.path.exists(dest_path):
                name, ext = os.path.splitext(filename)
                dest_path = os.path.join(
                    output_folder, f"{name}_{counter}{ext}")
                counter += 1

            shutil.copy2(image_path, dest_path)
            copied += 1
        except Exception as e:
            print(f"複製 {image_path} 時出錯: {e}")

    messagebox.showinfo(
        "完成", f"成功複製 {copied}/{num_images} 張圖像到\n{output_folder}")


# 創建GUI界面
root = tk.Tk()
root.title("圖像批量複製工具")
root.geometry("400x200")

label = tk.Label(root, text="圖像批量複製工具", font=("Arial", 16))
label.pack(pady=20)

instruction = tk.Label(
    root, text="1. 點擊按鈕選擇包含圖像的資料夾\n2. 輸入要複製的圖像數量\n3. 圖像將被複製到該資料夾下的output子資料夾中")
instruction.pack(pady=10)

start_button = tk.Button(root, text="開始複製圖像", command=copy_images)
start_button.pack(pady=20)

root.mainloop()
