# 🔧 ROI三視圖座標轉換修復

## 📋 問題描述

當啟用ROI功能時，三視圖顯示的邊界框位置不正確。這是因為：

1. **檢測階段**：推理在ROI裁切後的圖像上進行，檢測座標相對於ROI圖像
2. **顯示階段**：三視圖在原始圖像上繪製，但使用的是ROI座標系
3. **座標不匹配**：ROI座標需要轉換回原圖座標系才能正確顯示

## ✅ 解決方案

### 1. **新增座標轉換方法**
```python
def _transform_detections_to_original_space(self, detections: List[Dict], roi_offset: Tuple[int, int]) -> List[Dict]:
    """將ROI空間的檢測座標轉換到原圖空間"""
```

**功能**：
- 接收ROI空間的檢測結果和偏移量
- 將bbox座標轉換到原圖座標系
- 保持其他檢測屬性不變

### 2. **更新三視圖生成方法**
```python
def create_three_view(..., roi_offset: Optional[Tuple[int, int]] = None) -> np.ndarray:
```

**新增功能**：
- 接收`roi_offset`參數
- 在繪製前自動進行座標轉換
- 確保邊界框在正確位置顯示

### 3. **更新方法調用**
- `generate_three_view_advanced()`: 新增roi_offset參數
- `self.three_view_generator.create_three_view()`: 傳遞`result.get('roi_offset')`

## 🔄 轉換邏輯

### 座標轉換公式
```python
# ROI座標 → 原圖座標
x_original = x_roi + x_offset  
y_original = y_roi + y_offset

# 應用到bbox
bbox_original = [
    x1 + x_offset,  # x1
    y1 + y_offset,  # y1
    x2 + x_offset,  # x2  
    y2 + y_offset   # y2
]
```

### ROI偏移量獲取
ROI偏移量在`_apply_roi_cropping()`方法中計算：
```python
roi_offset = (x1_roi, y1_roi)  # ROI區域的左上角座標
```

## 📁 修改的檔案

### `/models/inference/unified_yolo_inference.py`

#### 新增方法
- `_transform_detections_to_original_space()` (line 814-850)

#### 修改方法
- `create_three_view()`: 新增roi_offset參數 (line 145-153)
- `generate_three_view_advanced()`: 新增roi_offset參數 (line 761-767)

#### 修改調用
- `create_three_view()` call: 傳遞roi_offset (line 792-800)
- `self.three_view_generator.create_three_view()` call: 傳遞roi_offset (line 3060-3069)

## 🎯 使用效果

### 修復前
- ❌ 邊界框位置錯誤，偏移到圖像左上角
- ❌ ROI區域外的檢測框顯示在錯誤位置
- ❌ GT和預測框位置不匹配

### 修復後  
- ✅ 邊界框顯示在正確位置
- ✅ ROI座標自動轉換到原圖座標系
- ✅ GT和預測框完美對齊
- ✅ 三視圖顯示準確無誤

## 🧪 測試驗證

### 測試步驟
1. 啟用ROI功能：`enable_roi_processing = True`
2. 設定ROI參數：`roi_top_ratio`, `roi_bottom_ratio`等
3. 運行推理：`python run_unified_yolo.py`
4. 檢查三視圖輸出：查看`images/`目錄中的`*_three_view.jpg`

### 預期結果
- 邊界框位置與實際檢測物體對齊
- GT標註和預測結果位置匹配
- ROI區域內外的檢測都正確顯示

## 🔧 技術細節

### 座標系統
1. **ROI座標系**：相對於ROI裁切後圖像的座標
2. **原圖座標系**：相對於完整原始圖像的座標
3. **轉換關係**：`原圖座標 = ROI座標 + ROI偏移量`

### 處理流程
```
原始圖像 → ROI裁切 → 推理 → 檢測結果(ROI座標)
                                     ↓
三視圖生成 ← 座標轉換 ← 檢測結果(原圖座標)
```

### 錯誤處理
- ROI偏移量為None時，跳過座標轉換
- 檢測列表為空時，直接返回
- 保持原有的錯誤處理邏輯

## 💡 向後兼容性

- ✅ 非ROI模式：roi_offset=None，不進行轉換
- ✅ 現有調用：新參數為可選，默認值為None
- ✅ 其他功能：不影響現有的推理和可視化功能

修復完成！現在ROI模式下的三視圖邊界框將正確顯示在對應位置。