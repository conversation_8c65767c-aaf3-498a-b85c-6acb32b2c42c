import matplotlib.pyplot as plt
from ultralytics import YOLO
from pathlib import Path
from datetime import datetime
import logging
import sys
import csv
import shutil
import time
import io
import numpy as np
import cv2
import re
import json
import itertools


class StreamToLogger(io.StringIO):
    def __init__(self, logger, level):
        super().__init__()
        self.logger = logger
        self.level = level

    def write(self, message):
        if message.rstrip() != "":
            self.logger.log(self.level, message.rstrip())

    def flush(self):
        pass

# 在程式最前面做
logging.basicConfig(level=logging.INFO)
custom_logger = logging.getLogger("yolo_stdout_redirect")
sl = StreamToLogger(custom_logger, logging.DEBUG)
sys.stdout = sl

# 強制設定 matplotlib 字型為 微軟正黑體（如有需要可調整）
plt.rcParams['font.family'] = 'Microsoft JhengHei'
plt.rcParams['axes.unicode_minus'] = False  # 避免負號亂碼

class YoloTaskRunner:
    def __init__(self, task_mode="object_detect", base_output_root=r"D:\路面缺陷資料集\處理結果",
                 gt_label_dir=None, use_trained_model=False):
        self.task_mode = task_mode
        self.use_trained_model = use_trained_model
        self.gt_label_dir = gt_label_dir
        
        # 先 resolve 模型路徑 & data.yaml
        self.model_path, self.data_yaml = self._resolve_paths()
        self.model = YOLO(self.model_path)
        
        # 這時還沒有設置 output_dir，先設為 None
        self.output_dir = None
        
        # 預設測試資料夾
        self.test_images_dir = Path(r"C:\Users\<USER>\Desktop\road_segment\yolov11_segmentation\test\images")
        
        # 用於紀錄各階段的耗時
        self.summary_records = []
        
        # 先僅初始化 console logger（因為此時沒有 output_dir）
        self._init_logger()
        

    def _resolve_paths(self):
        """根據 task_mode 與 use_trained_model 選擇模型路徑。"""
        if self.task_mode == "segment":
            if self.use_trained_model:
                model_path = r"D:\10435\road_detect_mission\Road_detect_project\weights\全下\weights\last.pt"
            else:
                model_path = r"D:\路面缺陷資料集\YOLO\ultralytics\download_model\semantic_segmentation\yolo11x-seg.pt"
        elif self.task_mode == "object_detect":
            if self.use_trained_model:
                model_path = r"D:\10435\road_detect_mission\Road_detect_project\weights\last.pt"
            else:
                model_path = r"D:\路面缺陷資料集\YOLO\ultralytics\download_model\object_detection\yolo12x.pt"
        elif self.task_mode == "classification":
            model_path = r"C:\Users\<USER>\Desktop\road_classify\cls_model.pt"
        else:
            raise ValueError(f"❌ 不支援的任務模式: {self.task_mode}")
        
        data_yaml = r"D:\10435\road_detect_mission\Road_detect_project\YOLO\ultralytics\data.yaml"
        return model_path, data_yaml

    def _init_logger(self):
        """初始化 logger。若 self.output_dir != None，則 file handler 一併設定。"""
        self.logger = logging.getLogger("YoloTaskRunner")
        self.logger.setLevel(logging.DEBUG)
        
        # 先清空現有 handler
        if self.logger.hasHandlers():
            self.logger.handlers.clear()
        
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        
        # 只要有 console
        ch = logging.StreamHandler(sys.stdout)
        ch.setFormatter(formatter)
        self.logger.addHandler(ch)
        
        # 如果已經指定 output_dir，就加上 file handler
        if self.output_dir is not None:
            log_file = self.output_dir / "yolo_task.log"
            fh = logging.FileHandler(log_file, encoding="utf-8")
            fh.setFormatter(formatter)
            self.logger.addHandler(fh)
        
        self.logger.info("📓 Logging initialized.")
        if self.output_dir is not None:
            self.logger.info(f"📁 本次任務輸出資料夾：{self.output_dir}")
        self.logger.info(f"💡 任務模式：{self.task_mode}")
    
    def set_custom_infer_folder(self, input_folder: str, output_folder: str):
        """自訂推論資料夾與結果輸出位置"""
        
        self.test_images_dir = Path(input_folder)
        
        # 取得上兩層資料夾名稱
        dataset_name = self.test_images_dir.parents[1].name  # e.g. road_yolo_dataset_各類450筆_内20250415_082348
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 根據 task_mode 決定
        task_prefix = {
            "object_detect": "object_detect_處理結果",
            "segment": "semantic_segmentation_處理結果",
            "classification": "classification_處理結果"
        }.get(self.task_mode, "unknown_task")
        
        # 最終: D:\路面缺陷資料集\處理結果\object_detect_處理結果\{dataset_name}_{timestamp}
        complete_name = f"{dataset_name}_{timestamp}"
        self.output_dir = Path(output_folder) / task_prefix / complete_name
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 重新初始化 logger，這次會將 log 寫到 self.output_dir
        self._init_logger()
        
        self.logger.info(f"📁 推譯資料夾：{self.test_images_dir}")
        self.logger.info(f"📂 輸出結果將儲存至：{self.output_dir}")

    def combine_gt_and_pred(self):
        """
        1. 讀取 self.output_dir/"predict"、self.test_images_dir、self.gt_label_dir
        2. 對每張圖：
           a. 畫出 GT 矩形框與英文標籤
           b. 讀取預測圖並 resize
           c. 左右合併，並在合併圖上加上 "Ground Truth"/"Prediction" 標籤
           d. 存到 self.output_dir/"combined"
        3. 刪除 predict、gt_overlay 資料夾
        """
        predict_dir = self.output_dir / "predict"
        if not predict_dir.exists():
            self.logger.error(f"❌ 找不到 predict 資料夾：{predict_dir}")
            return

        combined_dir = self.output_dir / "combined"
        combined_dir.mkdir(parents=True, exist_ok=True)

        # 確保 class_keys 跟訓練時 data.yaml 的順序一致
        
        class_keys = [
            "expansion_joint", "joint", "linear_crack", "Alligator_crack",
            "potholes", "patch", "manhole", "deformation",
            "dirt", "lane_line_linear"
        ]
        '''
        class_keys = [
           'face', 'license_plate', 'mask'
        ]
        '''

        id2key = {i: k for i, k in enumerate(class_keys)}

        img_paths = list(self.test_images_dir.glob("*.jpg")) + \
                    list(self.test_images_dir.glob("*.png")) + \
                    list(self.test_images_dir.glob("*.jpeg"))
        gt_dir = Path(self.gt_label_dir)

        color_map = {}
        def get_color(cls_id):
            if cls_id not in color_map:
                color_map[cls_id] = tuple(np.random.randint(0, 255, 3).tolist())
            return color_map[cls_id]

        for img_path in img_paths:
            orig = cv2.imread(str(img_path))
            if orig is None:
                self.logger.warning(f"⚠️ 讀不到影像：{img_path.name}")
                continue
            h, w = orig.shape[:2]

            # 1. 畫 GT overlay + 英文標籤
            gt_img = orig.copy()
            label_file = gt_dir / f"{img_path.stem.lower()}.txt"
            # 原本的 GT overlay 段落，替換成下面這一段：
            if label_file.exists():
                for line in open(label_file, encoding="utf-8"):
                    parts = line.strip().split()
                    if len(parts) < 5:
                        continue
                    cls_id = int(parts[0])
                    coords = list(map(float, parts[1:]))
            
                    col = get_color(cls_id)
            
                    # --- polygon 模式（coords >= 6） ---
                    if len(coords) >= 6:
                        # 取所有頂點，normalized → 絕對座標
                        pts = np.array([
                            (int(coords[i] * w), int(coords[i+1] * h))
                            for i in range(0, len(coords), 2)
                        ], dtype=np.int32).reshape(-1, 1, 2)
                        cv2.polylines(gt_img, [pts], isClosed=True, color=col, thickness=2)
            
                        # 英文 key 標在第一個頂點
                        text = id2key.get(cls_id, f"class_{cls_id}")
                        x0, y0 = pts[0][0]
                        cv2.putText(gt_img, text, (x0, max(y0-10, 0)),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, col, 2)
            
                    # --- box 模式（coords < 6） ---
                    else:
                        x_c, y_c, bw, bh = coords[:4]
                        x1 = int((x_c - bw/2) * w)
                        y1 = int((y_c - bh/2) * h)
                        x2 = int((x_c + bw/2) * w)
                        y2 = int((y_c + bh/2) * h)
                        cv2.rectangle(gt_img, (x1, y1), (x2, y2), col, 2)
            
                        text = id2key.get(cls_id, f"class_{cls_id}")
                        cv2.putText(gt_img, text, (x1, max(y1-10, 0)),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.8, col, 2)
            # 2. 讀預測圖 & resize
            pred_path = predict_dir / img_path.name
            if pred_path.exists():
                pred_img = cv2.imread(str(pred_path))
            else:
                pred_img = np.zeros_like(orig)
            if pred_img.shape[:2] != (h, w):
                pred_img = cv2.resize(pred_img, (w, h), interpolation=cv2.INTER_AREA)

            # 3.a 左右合併
            combined = np.hstack((gt_img, pred_img))

            # 3.b 標記 GT / Prediction
            font = cv2.FONT_HERSHEY_SIMPLEX
            cv2.putText(combined, "Ground Truth",
                        (10, 30), font, 1.0, (0, 255, 0), 2)
            cv2.putText(combined, "Prediction",
                        (w + 10, 30), font, 1.0, (0, 0, 255), 2)

            # 3.c 存到 combined 資料夾
            out_path = combined_dir / img_path.name
            cv2.imwrite(str(out_path), combined)
            self.logger.info(f"✅ 儲存合併圖：{out_path.relative_to(self.output_dir)}")

        # 4. 刪除舊的 predict、gt_overlay
        for folder in ("predict", "gt_overlay"):
            p = self.output_dir / folder
            if p.exists():
                shutil.rmtree(p, ignore_errors=True)
                self.logger.info(f"🗑️ 已刪除舊資料夾：{p.relative_to(self.output_dir)}")

        self.logger.info(f"🎉 完成：最終只保留 combined 資料夾 ({combined_dir.relative_to(self.output_dir)})")

    def _plot_confusion_matrix(self, metrics_obj, save_name: str, normalize: bool = False):
        """
        繪製混淆矩陣（normalize=False 為未歸一化，True 為歸一化），
        存檔到 self.output_dir/save_name。
        """
        import numpy as np
        import matplotlib.pyplot as plt
    
        # 取出原始混淆矩陣，shape = [num_classes, num_classes]
        cm = metrics_obj.box.confusion_matrix
        # 類別名稱
        names = metrics_obj.names if hasattr(metrics_obj, "names") else {}
        classes = [names[i] if i in names else str(i) for i in range(cm.shape[0])]
    
        if normalize:
            cm = cm.astype("float") / cm.sum(axis=1, keepdims=True)
    
        plt.figure(figsize=(8, 8))
        plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
        title = "Confusion Matrix" + (" (Normalized)" if normalize else "")
        plt.title(title)
        plt.colorbar()
        tick_marks = np.arange(len(classes))
        plt.xticks(tick_marks, classes, rotation=90)
        plt.yticks(tick_marks, classes)
    
        fmt = '.2f' if normalize else 'd'
        thresh = cm.max() / 2.0
        for i, j in itertools.product(range(cm.shape[0]), range(cm.shape[1])):
            plt.text(j, i, format(cm[i, j], fmt),
                     horizontalalignment="center",
                     color="white" if cm[i, j] > thresh else "black")
    
        plt.ylabel('True label')
        plt.xlabel('Predicted label')
        plt.tight_layout()
        out_path = self.output_dir / save_name
        plt.savefig(out_path, bbox_inches='tight')
        plt.close()
        self.logger.info(f"📊 已儲存 {'歸一化' if normalize else '未歸一化'} 混淆矩陣：{save_name}")
    
    def train(self, epochs=1, imgsz=640, batch=32, conf=0.5, device=0, **kwargs):
        self.logger.info("🚀 開始訓練...")
        start = time.time()
    
        train_config = {
            "epochs": epochs,
            "imgsz": imgsz,
            "batch": batch,
            "conf": conf,
            "device": device
        }
        train_config.update(kwargs)
    
        config_path = self.output_dir / "train_config.json"
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(train_config, f, indent=2, ensure_ascii=False)
        self.logger.info(f"已保存訓練參數設定到: {config_path}")
    
        results = self.model.train(
            data=self.data_yaml,
            epochs=epochs,
            imgsz=imgsz,
            batch=batch,
            conf=conf,
            device=device,
            **kwargs
        )
    
        end = time.time()
        duration = end - start
        minutes = duration / 60
        hours = minutes / 60
        self.logger.info(f"⏱️ 訓練總耗時：{duration:.2f} 秒 ({minutes:.2f} 分鐘 / {hours:.2f} 小時)")
    
        self.summary_records.append({
            "task": "train",
            "duration": duration,
            "minutes": minutes,
            "hours": hours,
            "count": None,
            "fps": None
        })
    
        self._copy_yolo_results(results, subfolder="train_runs_result")
        self.logger.info("✅ 訓練完成！")
    
    def validate(self):
        self.logger.info("🔍 開始驗證 val 集...")
        start = time.time()
    
        # 让 YOLO 自己画混淆矩阵
        metrics = self.model.val(data=self.data_yaml, plots=True)
    
        end = time.time()
        duration = end - start
        self.logger.info(f"⏱️ 驗證耗時：{duration:.2f} 秒")
    
        self.summary_records.append({
            "task": "validate",
            "duration": duration,
            "minutes": duration / 60,
            "hours": duration / 3600,
            "count": None,
            "fps": None
        })
    
        self._save_metrics_to_csv(metrics, "val_metrics.csv")
        self._plot_mAP(metrics, "val_mAP_plot.png")
        # 不再手動畫混淆矩陣，讓 Ultralytics 幫我們做
        # self._plot_confusion_matrix(metrics, "val_confusion_matrix.png", normalize=False)
        # self._plot_confusion_matrix(metrics, "val_confusion_matrix_norm.png", normalize=True)
    
        self._copy_yolo_results(metrics, subfolder="val_runs_results")
        self.logger.info("✅ 驗證完成！")

    def test(self):
        self.logger.info("📊 開始測試集評估...")
        start = time.time()

        # 加上 plots=True
        results = self.model.val(data=self.data_yaml, split='test', plots=True)

        end = time.time()
        duration = end - start
        self.logger.info(f"⏱️ 測試集評估耗時：{duration:.2f} 秒")

        self.summary_records.append({
            "task": "test",
            "duration": duration,
            "minutes": duration / 60,
            "hours": duration / 3600,
            "count": None,
            "fps": None
        })

        # 存 CSV／mAP 圖
        self._save_metrics_to_csv(results, "test_metrics.csv")
        self._plot_mAP(results, "test_mAP_plot.png")

        # 將 Ultralytics 幫你畫好的混淆矩陣一起複製到 runs_results
        self._copy_yolo_results(results, subfolder="test_runs_results")
        self.logger.info("✅ 測試集評估完成！")
    
    def infer(self, classes: list[int] | None = None, skip_empty: bool = True, **kwargs):
        """
        推論測試集，支援：
        - classes: 只偵測指定的 class id，例如 [0,2,3]（Ultralytics 內建過濾）
        - skip_empty: 如果一張圖沒有任何偵測結果，就不存它
        其他參數同 Ultralytics 原生：conf, iou, agnostic_nms, visualize, max_det, device…
        """
        self.logger.info("🧠 開始推譯並儲存測試集結果…")
    
        image_paths = list(self.test_images_dir.glob("*.jpg")) + \
                      list(self.test_images_dir.glob("*.jpeg")) + \
                      list(self.test_images_dir.glob("*.png"))
    
        if not image_paths:
            self.logger.warning("⚠️ 找不到任何圖像檔案，請檢查資料夾內容或副檔名。")
            return
    
        predict_dir = self.output_dir / "predict"
        predict_dir.mkdir(exist_ok=True)
    
        start = time.time()
        saved_count = 0
        for img_path in image_paths:
            infer_kwargs = kwargs.copy()
            if classes is not None:
                infer_kwargs["classes"] = classes
        
            results = self.model(source=img_path, **infer_kwargs)
            dets = results[0]
        
            # skip 完全沒東西的圖片
            if skip_empty and len(dets.boxes) == 0:
                self.logger.info(f"⚠️ {img_path.name} 無任何偵測，已跳過保存")
                continue
        
            if self.task_mode in {"segment", "object_detect"}:
                save_path = predict_dir / img_path.name
            
                # 1) 取得帶框的 BGR 影像
                annotated = dets.plot()  # already BGR
            
                # 2) 縮放到 1200×900（寬×高）
                resized = cv2.resize(annotated, (1200, 900), interpolation=cv2.INTER_AREA)
            
                # 3) 存檔（cv2.imwrite 預期輸入是 BGR）
                cv2.imwrite(str(save_path), resized)
                self.logger.info(f"🖼️ 已儲存（1200×900）推論結果：{save_path.name}")
        
            elif self.task_mode == "classification":
                label = dets.probs.top1
                self.logger.info(f"🧠 {img_path.name} → 預測類別: {label}")
        
            saved_count += 1
        
        end = time.time()
        duration = end - start
        fps = saved_count / duration if duration > 0 else 0
        self.logger.info(f"✅ 共儲存 {saved_count} 張圖像")
        self.logger.info(f"⏱️ 推論耗時：{duration:.2f} 秒，平均每秒處理 {fps:.2f} 張圖像 (FPS)")
        self.summary_records.append({
            "task": "infer",
            "duration": duration,
            "minutes": duration / 60,
            "hours": duration / 3600,
            "count": saved_count,
            "fps": fps
        })


    
    def run_selected_tasks(self, train=False, val=False, test=False, infer=False,
                           train_params=None, infer_params=None):
        if train:
            if train_params is None:
                train_params = {}
            self.train(**train_params)
        if val:
            self.validate()
        if test:
            self.test()
        if infer:
            if infer_params is None:
                infer_params = {}
            self.infer(**infer_params)
        self._save_summary_csv()
    
    def _copy_yolo_results(self, results, subfolder="runs_results"):
            """
            將 results.save_dir 下的所有東西複製到
            self.output_dir / subfolder，
            並把原始 runs/xxx 刪掉
            """
            try:
                save_dir = getattr(results, "save_dir", None)
                if save_dir and Path(save_dir).exists():
                    target_dir = self.output_dir / subfolder
                    shutil.copytree(save_dir, target_dir, dirs_exist_ok=True)
                    self.logger.info(f"📂 已複製 YOLO 輸出資料夾 {save_dir} → {target_dir}")
                    shutil.rmtree(save_dir, ignore_errors=True)
                    self.logger.info(f"🧹 已刪除原始 YOLO 預設資料夾：{save_dir}")
                # 同步清空上層 runs 資料夾
                runs_root = Path("runs")
                if runs_root.exists():
                    shutil.rmtree(runs_root, ignore_errors=True)
                    self.logger.info(f"🧹 已清除 YOLO 預設 runs 資料夾：{runs_root}")
            except Exception as e:
                self.logger.error(f"❌ 複製或清除 YOLO 輸出發生錯誤: {e}")
    
    def _save_metrics_to_csv(self, metrics_obj, save_name="metrics.csv"):
        try:
            names = metrics_obj.names
            metrics = metrics_obj.box
            if metrics is None:
                self.logger.warning("⚠️ 無法提取 box 評估指標")
                return
            valid_class_indices = metrics.ap_class_index
            csv_path = self.output_dir / save_name
            with open(csv_path, mode="w", newline="", encoding="utf-8-sig") as f:
                writer = csv.writer(f)
                writer.writerow(["class", "precision", "recall", "mAP50", "mAP50-95"])
                for i in valid_class_indices:
                    p, r, ap50, ap = metrics.class_result(i)
                    class_name = names.get(i, f"class_{i}")
                    writer.writerow([
                        class_name,
                        round(p, 4),
                        round(r, 4),
                        round(ap50, 4),
                        round(ap, 4)
                    ])
                writer.writerow([
                    "all",
                    round(metrics.mp, 4),
                    round(metrics.mr, 4),
                    round(metrics.map50, 4),
                    round(metrics.map, 4)
                ])
            self.logger.info(f"📄 已儲存評估結果至：{csv_path}")
        except Exception as e:
            self.logger.error(f"❌ 儲存 CSV 發生錯誤: {e}")
    
    def _plot_mAP(self, metrics_obj, save_name="map50_plot.png"):
        try:
            metrics = metrics_obj.box
            names = metrics_obj.names
            if metrics is None:
                self.logger.warning("⚠️ 無法提取 box 指標，無法繪製圖表")
                return
            valid_class_indices = metrics.ap_class_index
            map50_values = []
            class_names = []
            for i in valid_class_indices:
                _, _, ap50, _ = metrics.class_result(i)
                map50_values.append(round(ap50, 4))
                class_names.append(names.get(i, f"class_{i}"))
            plt.figure(figsize=(10, 6))
            bars = plt.barh(class_names, map50_values, color='skyblue')
            plt.xlabel("mAP@50")
            plt.title("mAP@50 per class")
            for bar, val in zip(bars, map50_values):
                plt.text(bar.get_width() + 0.01, bar.get_y() + 0.2, f"{val:.2f}")
            plt.tight_layout()
            save_path = self.output_dir / save_name
            plt.savefig(save_path)
            plt.close()
            self.logger.info(f"📊 已儲存 mAP50 圖表至：{save_path}")
        except Exception as e:
            self.logger.error(f"❌ 繪製 mAP 圖表錯誤: {e}")
    
    def _save_summary_csv(self):
        if not self.summary_records:
            return
        summary_path = self.output_dir / "summary.csv"
        try:
            with open(summary_path, mode="w", newline="", encoding="utf-8-sig") as f:
                writer = csv.writer(f)
                writer.writerow(["任務", "耗時 (秒)", "耗時 (分)", "耗時 (時)", "張數", "FPS"])
                for record in self.summary_records:
                    writer.writerow([
                        record["task"],
                        f"{record['duration']:.2f}",
                        f"{record['minutes']:.2f}",
                        f"{record['hours']:.2f}",
                        record["count"] if record["count"] is not None else "-",
                        f"{record['fps']:.2f}" if record["fps"] is not None else "-"
                    ])
            self.logger.info(f"📈 已儲存任務統計至：{summary_path}")
        except Exception as e:
            self.logger.error(f"❌ 儲存 summary.csv 發生錯誤: {e}")
    
    def draw_gt_overlay(self, label_type: str = "auto"):
        """
        label_type: "od" | "polygon" | "auto"
          - "od": 只用前 4 個值畫 bounding box
          - "polygon": 全部當 segmentation polygon (len(coords)>=6)
          - "auto": 依 length 判斷 (>=6 polygon, <6 box)
        """
        self.logger.info(f"🎨 開始繪製 GT Overlay (模式={label_type}) …")
        overlay_dir = self.output_dir / "gt_overlay"
        overlay_dir.mkdir(exist_ok=True)
    
        test_images = list(self.test_images_dir.glob("*.*"))
        if not test_images:
            self.logger.warning("⚠️ 沒有任何影像可處理！")
            return
    
        test_labels_dir = Path(self.gt_label_dir)
        if not test_labels_dir.exists():
            self.logger.warning("⚠️ GT 標籤資料夾不存在！")
            return
    
        names = getattr(self.model, "names", {})
    
        for img_path in test_images:
            label_path = test_labels_dir / f"{img_path.stem.lower()}.txt"
            if not label_path.exists():
                continue
    
            img = cv2.imread(str(img_path))
            if img is None:
                continue
            h, w = img.shape[:2]
    
            with open(label_path, encoding="utf-8") as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) < 5:
                        continue
                    cid = int(parts[0])
                    coords = list(map(float, parts[1:]))
    
                    # decide mode
                    mode = label_type
                    if mode == "auto":
                        mode = "polygon" if len(coords) >= 6 else "od"
    
                    if mode == "od":
                        # box 模式
                        x_c, y_c, bw, bh = coords[:4]
                        x1 = int((x_c - bw/2) * w)
                        y1 = int((y_c - bh/2) * h)
                        x2 = int((x_c + bw/2) * w)
                        y2 = int((y_c + bh/2) * h)
                        cv2.rectangle(img, (x1, y1), (x2, y2), (255, 0, 0), 3)
                        cv2.putText(img, names.get(cid, str(cid)),
                                    (x1, max(y1-10,0)),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.7,
                                    (255,0,0), 2)
    
                    elif mode == "polygon":
                        # polygon 模式
                        # 假設所有 coords 都是 normalized (0–1)
                        pts = np.array([
                            (int(coords[i]*w), int(coords[i+1]*h))
                            for i in range(0, len(coords), 2)
                        ], dtype=np.int32).reshape(-1,1,2)
                        cv2.polylines(img, [pts], True, (0,255,0), 3)
                        cv2.putText(img, names.get(cid, str(cid)),
                                    tuple(pts[0][0]),
                                    cv2.FONT_HERSHEY_SIMPLEX, 0.7,
                                    (0,255,255), 2)
    
                    else:
                        # unknown mode
                        continue
    
            out_p = overlay_dir / img_path.name
            cv2.imwrite(str(out_p), img)
    
        self.logger.info(f"✅ GT Overlay ({label_type}) 繪製完成，路徑: {overlay_dir}")

if __name__ == "__main__":
    # 建立 Runner，並傳入 gt_label_dir
    runner = YoloTaskRunner(
        task_mode="segment", 
        use_trained_model=True,
        gt_label_dir=r"D:\10435\road_detect_mission\Road_detect_project\YOLO\ultralytics\datasets\SEGMENT\road_train_all\labels\test"
    )
    
    runner.set_custom_infer_folder(
        input_folder=r"D:\10435\road_detect_mission\Road_detect_project\YOLO\ultralytics\datasets\SEGMENT\road_train_all\images\test",
        output_folder=r"D:\license_plate\處理結果"
    )
    runner.run_selected_tasks(
        train=True,
        val=True,
        test=True,
        infer=True,
        train_params={  # 雖然這裡不跑 train，也要保留結構
            "epochs": 300,
            "imgsz": 640,
            "batch": 8,
            "device": 0,
            "augment": True,
            "patience": 20,
            "seed": 0,
            "scale": 0.1,
            "shear": 10,
            "perspective": 0.001,
            "resume": True,
            #"model": r"D:\10435\road_detect_mission\Road_detect_project\weights\全下\weights\last.pt"
        },
        infer_params={
            "conf": 0.1,
            "iou": 0.1,
            "agnostic_nms": False,
            "visualize": False,
            #"classes": [2, 3,4],   # 只偵測這三個 class id
            "skip_empty": True      # 跳過無偵測結果的圖
        }
    )
    runner.combine_gt_and_pred()