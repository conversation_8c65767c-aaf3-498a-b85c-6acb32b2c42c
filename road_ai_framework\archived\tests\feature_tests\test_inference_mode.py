#!/usr/bin/env python3
"""
測試推理模式選擇功能
驗證 enable_advanced_slice_inference 參數是否正確控制推理引擎選擇
"""

def test_inference_mode(enable_advanced_slice_inference):
    print(f"🔧 測試推理模式選擇:")
    print(f"   enable_advanced_slice_inference = {enable_advanced_slice_inference}")

    if enable_advanced_slice_inference:
        print("✅ 選擇: 高級切片推理模式")
        print("   - 使用 AdvancedSliceInference")
        print("   - 支援切片推理和物件融合")
        print("   - 創建 AdvancedInferenceWrapper")
        print("   - predict_batch支援 labelme_integration 和 enable_resume 參數")
    else:
        print("✅ 選擇: 一般推理模式")  
        print("   - 使用 UnifiedYOLOInference")
        print("   - 直接YOLO推理")
        print("   - 兼容原有功能")
        print("   - predict_batch只支援基本參數，手動處理LabelMe")

    return enable_advanced_slice_inference

# 測試兩種模式
print("=" * 50)
test_inference_mode(True)
print("\n" + "=" * 50)
test_inference_mode(False)
print("\n🎯 測試完成")