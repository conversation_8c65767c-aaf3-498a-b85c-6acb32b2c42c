#!/usr/bin/env python3
"""
🔍 SAHI融合策略调试脚本
分析为什么SAHI融合策略没有生效
"""

import logging
from pathlib import Path
from typing import List, Dict, Any
import numpy as np

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 添加项目路径
import sys
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from inference_system.core.base_inference import Detection
    from inference_system.processing.fusion_engine import FusionEngine
    from inference_system.config import FusionConfig, FusionStrategy
    print("✅ 成功导入推理系统模块")
except ImportError as e:
    print(f"❌ 导入推理系统模块失败: {e}")
    print("尝试替代导入方法...")
    try:
        from inference_system import *
        print("✅ 使用通配符导入成功")
    except ImportError as e2:
        print(f"❌ 通配符导入也失败: {e2}")
        sys.exit(1)

def create_mock_detections() -> List[Detection]:
    """创建模拟的重叠检测结果"""
    detections = []
    
    # 创建4个高度重叠的Linear_crack检测
    # 这些检测应该被SAHI融合策略合并
    base_bbox = [100, 100, 200, 150]  # x1, y1, x2, y2
    
    for i in range(4):
        # 每个检测略有偏移，但重叠IoU > 0.1
        offset = i * 5
        bbox = [
            base_bbox[0] + offset,
            base_bbox[1] + offset, 
            base_bbox[2] + offset,
            base_bbox[3] + offset
        ]
        
        detection = Detection(
            bbox=bbox,
            confidence=0.8 + i * 0.05,
            class_id=6,  # linear_crack
            class_name="linear_crack",
            mask=None,
            area=(bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
        )
        detections.append(detection)
        
        print(f"检测 {i}: bbox={bbox}, confidence={detection.confidence:.3f}")
    
    return detections

def test_sahi_fusion_strategy():
    """测试SAHI融合策略"""
    print("🔍 开始SAHI融合策略测试")
    print("=" * 60)
    
    # 1. 创建模拟检测数据
    mock_detections = create_mock_detections()
    print(f"📊 输入检测数量: {len(mock_detections)}")
    
    # 2. 创建融合配置
    fusion_config = FusionConfig()
    fusion_config.strategy = FusionStrategy.SAHI_OVERLAP_MERGE
    fusion_config.iou_threshold = 0.1
    fusion_config.confidence_threshold = 0.1
    
    # SAHI专用参数
    fusion_config.sahi_merge_iou_threshold = 0.1
    fusion_config.enable_mask_iou_calculation = False  # 暂时关闭mask计算
    fusion_config.sahi_merge_confidence_strategy = "max"
    
    print(f"🔧 融合配置:")
    print(f"   策略: {fusion_config.strategy}")
    print(f"   IoU阈值: {fusion_config.sahi_merge_iou_threshold}")
    print(f"   置信度策略: {fusion_config.sahi_merge_confidence_strategy}")
    
    # 3. 创建融合引擎并执行融合
    try:
        fusion_engine = FusionEngine(fusion_config)
        
        print("\n🔀 执行SAHI融合...")
        fused_detections = fusion_engine.fuse(mock_detections)
        
        print(f"📊 输出检测数量: {len(fused_detections)}")
        print("\n📋 融合后的检测结果:")
        for i, det in enumerate(fused_detections):
            print(f"   检测 {i}: bbox={det.bbox}, confidence={det.confidence:.3f}")
        
        # 4. 验证融合是否生效
        if len(fused_detections) < len(mock_detections):
            print(f"✅ SAHI融合成功！从 {len(mock_detections)} 个检测合并为 {len(fused_detections)} 个")
            print(f"   合并了 {len(mock_detections) - len(fused_detections)} 个重叠检测")
        else:
            print(f"❌ SAHI融合失效！检测数量没有减少")
            print(f"   输入: {len(mock_detections)}, 输出: {len(fused_detections)}")
        
        # 5. 输出融合引擎统计信息
        print(f"\n📈 融合引擎统计:")
        stats = fusion_engine.stats
        print(f"   总融合次数: {stats['total_fusions']}")
        print(f"   策略使用情况: {stats['strategy_usage']}")
        print(f"   输入检测总数: {stats['input_detections']}")
        print(f"   输出检测总数: {stats['output_detections']}")
        
    except Exception as e:
        print(f"❌ 融合过程出现异常: {str(e)}")
        import traceback
        traceback.print_exc()

def analyze_fusion_engine_code():
    """分析融合引擎代码结构"""
    print("\n🔬 分析融合引擎代码结构")
    print("=" * 60)
    
    try:
        from inference_system.processing.fusion_engine import FusionEngine
        from inference_system.config import FusionStrategy
        
        # 检查支持的策略
        dummy_config = FusionConfig()
        fusion_engine = FusionEngine(dummy_config)
        
        print("📋 支持的融合策略:")
        for strategy in FusionStrategy:
            strategy_func = fusion_engine.strategies.get(strategy)
            print(f"   {strategy.value}: {'✅ 支持' if strategy_func else '❌ 不支持'}")
        
        # 检查SAHI策略是否存在
        sahi_strategy = fusion_engine.strategies.get(FusionStrategy.SAHI_OVERLAP_MERGE)
        if sahi_strategy:
            print(f"\n✅ SAHI_OVERLAP_MERGE 策略函数存在: {sahi_strategy.__name__}")
        else:
            print(f"\n❌ SAHI_OVERLAP_MERGE 策略函数不存在!")
            
    except Exception as e:
        print(f"❌ 代码分析失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 SAHI融合策略调试工具")
    print("分析为什么融合策略没有生效")
    print("=" * 80)
    
    # 分析代码结构
    analyze_fusion_engine_code()
    
    # 测试融合策略
    test_sahi_fusion_strategy()
    
    print("\n" + "=" * 80)
    print("🏁 调试完成")