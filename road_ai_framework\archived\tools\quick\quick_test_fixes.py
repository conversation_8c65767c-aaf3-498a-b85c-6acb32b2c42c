#!/usr/bin/env python3
"""
🚀 快速測試修復效果
"""

import os
import sys
from pathlib import Path

# 設置環境
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def quick_test():
    """快速測試"""
    print("🚀 快速測試修復效果")
    print("=" * 50)
    
    # 修改run_unified_yolo.py中的配置進行測試
    print("📋 修改配置進行快速測試...")
    
    # 讀取當前配置
    run_script = current_dir / "run_unified_yolo.py"
    if not run_script.exists():
        print("❌ run_unified_yolo.py不存在")
        return
    
    # 顯示需要修改的配置
    print("\\n🔧 請在run_unified_yolo.py中設置以下參數:")
    print("```python")
    print("# 測試路徑配置")
    print('input_path = r"/mnt/d/image/5_test_image/test_2_org"')
    print('output_path = r"/mnt/d/image/5_test_image/test_2_out_fixed"')
    print('labelme_dir = r"/mnt/d/image/5_test_image/test_2_org"')
    print('segmentation_model_path = r"/mnt/d/4_road_crack/best.pt"')
    print()
    print("# 啟用三視圖和調試")
    print("enable_three_view_output = True")
    print("save_visualizations = True")
    print("batch_processing = True")
    print("```")
    
    print("\\n🎯 修復內容預覽:")
    print("  📏 標題間距: 行間距從5增加到15，標題高度從20增加到40")
    print("  🎨 GT顏色調試: 添加詳細的類別匹配和顏色獲取調試信息") 
    print("  📝 中文字體: 中文標題自動轉換為英文避免問號問題")
    
    print("\\n🧪 測試步驟:")
    print("  1. 修改run_unified_yolo.py中的路徑配置")
    print("  2. 運行: python run_unified_yolo.py")
    print("  3. 查看控制台的調試信息（GT顏色匹配過程）")
    print("  4. 檢查生成的三視圖文件中的:")
    print("     - 標題行間距是否足夠")
    print("     - GT與pred顏色是否一致")
    print("     - 文字是否正確顯示")
    
    print("\\n📊 預期調試輸出:")
    print("```")
    print("Debug: GT類別名稱: patch")
    print("Debug: 可用類別配置: [0, 1, 2, 3, 4, 5, 6]")
    print("Debug: 檢查類別ID 5: name='patch', display_name='patch'")
    print("Debug: 找到匹配的類別ID: 5")
    print("Debug: 類別 patch (ID: 5) 的RGB顏色: [0, 255, 255]")
    print("Debug: 轉換後BGR顏色: (255, 255, 0)")
    print("```")
    
    print("\\n🎨 顏色一致性驗證:")
    print("  - GT中的patch應該顯示為青色 (0, 255, 255)")
    print("  - pred中的patch也應該顯示為青色")
    print("  - 如果仍不一致，調試信息會顯示原因")
    
    print("\\n📝 中文字體解決方案:")
    print("  標題會自動轉換:")
    print("  - '原始圖像' → 'Original Image'")
    print("  - '預測結果-分割' → 'Prediction-Segmentation'")
    print("  - '個檢測' → ' detections'")
    print("  - '個標註' → ' annotations'")


def show_code_changes():
    """顯示代碼修改摘要"""
    print("\\n" + "=" * 50)
    print("📝 代碼修改摘要")
    print("=" * 50)
    
    changes = [
        {
            "file": "unified_yolo_inference.py",
            "method": "_add_title_to_image",
            "changes": [
                "行間距從5增加到15",
                "標題高度從20增加到40", 
                "字體大小從0.7調整到0.8",
                "添加中文到英文的自動轉換"
            ]
        },
        {
            "file": "unified_yolo_inference.py", 
            "method": "_get_class_color_for_gt",
            "changes": [
                "添加詳細的調試信息輸出",
                "顯示類別匹配過程",
                "顯示RGB到BGR顏色轉換",
                "增強錯誤處理和日誌記錄"
            ]
        }
    ]
    
    for change in changes:
        print(f"\\n📄 {change['file']}")
        print(f"🔧 方法: {change['method']}")
        for item in change['changes']:
            print(f"  • {item}")


def main():
    """主函數"""
    quick_test()
    show_code_changes()
    
    print("\\n" + "=" * 50)
    print("✅ 修復準備完成!")
    print("🚀 請按照上述步驟運行測試")
    print("💡 如果仍有問題，請檢查調試信息輸出")


if __name__ == "__main__":
    main()