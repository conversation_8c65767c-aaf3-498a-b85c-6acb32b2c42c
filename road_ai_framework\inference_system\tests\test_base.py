#!/usr/bin/env python3
"""
🧪 基礎測試框架
提供統一的測試工具和模擬數據
"""

import unittest
import numpy as np
import tempfile
import shutil
from pathlib import Path
from typing import List, Dict, Any

from ..core import (
    BaseInference, StandardResult, Detection, ConfusionMetrics, 
    TimingInfo, InferenceStatus
)
from ..config import UnifiedConfig, create_default_config


class MockInference(BaseInference):
    """模擬推理引擎，用於測試"""
    
    def __init__(self, device: str = "cpu"):
        super().__init__(device)
        self.predict_calls = []
        self.predict_batch_calls = []
    
    def predict(self, image, output_dir=None, gt_annotations=None, **kwargs) -> StandardResult:
        """模擬單張推理"""
        self.predict_calls.append({
            'image': type(image).__name__,
            'output_dir': output_dir,
            'kwargs': kwargs
        })
        
        # 創建模擬結果
        detections = [
            Detection(
                bbox=[100, 100, 200, 200],
                confidence=0.85,
                class_id=2,
                class_name="linear_crack",
                area=10000
            )
        ]
        
        timing = TimingInfo(
            total_time=0.1,
            inference_time=0.05,
            preprocessing_time=0.02,
            postprocessing_time=0.02,
            visualization_time=0.01
        )
        
        result = self._create_standard_result(
            status=InferenceStatus.SUCCESS,
            detections=detections,
            timing=timing
        )
        
        self._update_statistics(result)
        return result
    
    def predict_batch(self, images, output_dir=None, gt_annotations=None, **kwargs) -> List[StandardResult]:
        """模擬批次推理"""
        self.predict_batch_calls.append({
            'image_count': len(images),
            'output_dir': output_dir,
            'kwargs': kwargs
        })
        
        return [self.predict(img, output_dir, None, **kwargs) for img in images]


class BaseTestCase(unittest.TestCase):
    """基礎測試類，提供常用的測試工具"""
    
    def setUp(self):
        """測試設置"""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # 創建測試配置
        self.config = create_default_config()
        self.config.input_path = str(self.temp_path / "input")
        self.config.output_path = str(self.temp_path / "output")
        
        # 創建測試目錄
        Path(self.config.input_path).mkdir(parents=True, exist_ok=True)
        Path(self.config.output_path).mkdir(parents=True, exist_ok=True)
    
    def tearDown(self):
        """測試清理"""
        if self.temp_path.exists():
            shutil.rmtree(self.temp_path)
    
    def create_test_image(self, width: int = 640, height: int = 640) -> np.ndarray:
        """創建測試圖像"""
        return np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    
    def save_test_image(self, image: np.ndarray, filename: str) -> str:
        """保存測試圖像"""
        import cv2
        image_path = self.temp_path / "input" / filename
        cv2.imwrite(str(image_path), image)
        return str(image_path)
    
    def create_test_gt_annotations(self) -> List[Dict[str, Any]]:
        """創建測試GT標註"""
        return [
            {
                'bbox': [100, 100, 200, 200],
                'class_id': 2,
                'class_name': 'linear_crack',
                'area': 10000
            },
            {
                'bbox': [300, 300, 400, 400], 
                'class_id': 3,
                'class_name': 'Alligator_crack',
                'area': 10000
            }
        ]
    
    def assert_standard_result(self, result: StandardResult):
        """驗證標準結果格式"""
        self.assertIsInstance(result, StandardResult)
        self.assertIsInstance(result.status, InferenceStatus)
        self.assertIsInstance(result.detections, list)
        self.assertIsInstance(result.timing, TimingInfo)
        self.assertIsInstance(result.metadata, dict)
        
        # 驗證檢測結果
        for detection in result.detections:
            self.assertIsInstance(detection, Detection)
            self.assertEqual(len(detection.bbox), 4)
            self.assertGreaterEqual(detection.confidence, 0.0)
            self.assertLessEqual(detection.confidence, 1.0)
    
    def assert_detection_format(self, detection: Detection):
        """驗證檢測結果格式"""
        self.assertIsInstance(detection.bbox, list)
        self.assertEqual(len(detection.bbox), 4)
        self.assertIsInstance(detection.confidence, float)
        self.assertIsInstance(detection.class_id, int)
        self.assertIsInstance(detection.class_name, str)
        
        # 驗證bbox格式 [x1, y1, x2, y2]
        x1, y1, x2, y2 = detection.bbox
        self.assertLessEqual(x1, x2)
        self.assertLessEqual(y1, y2)
    
    def assert_timing_info(self, timing: TimingInfo):
        """驗證計時信息"""
        self.assertIsInstance(timing, TimingInfo)
        self.assertGreaterEqual(timing.total_time, 0)
        self.assertGreaterEqual(timing.inference_time, 0)
        self.assertLessEqual(timing.inference_time, timing.total_time)


def create_test_suite():
    """創建測試套件"""
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 載入所有測試
    from . import unit
    suite.addTests(loader.loadTestsFromModule(unit))
    
    return suite


if __name__ == "__main__":
    # 運行基礎測試
    unittest.main()