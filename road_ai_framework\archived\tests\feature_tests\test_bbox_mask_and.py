#!/usr/bin/env python3
"""
測試bbox和mask的AND關係邏輯
驗證修改後的LabelMe輸出是否正確
"""

def test_bbox_mask_validation():
    """測試bbox和mask驗證邏輯"""
    
    # 模擬不同的檢測結果
    test_cases = [
        {
            "name": "有bbox和mask",
            "detection": {
                "bbox": [10, 20, 100, 200],
                "mask": [[1, 0], [0, 1]],  # 模擬mask
                "class_id": 2,
                "confidence": 0.8
            },
            "expected": True
        },
        {
            "name": "只有bbox沒有mask",
            "detection": {
                "bbox": [10, 20, 100, 200],
                "class_id": 2,
                "confidence": 0.8
            },
            "expected": False
        },
        {
            "name": "只有mask沒有bbox",
            "detection": {
                "mask": [[1, 0], [0, 1]],
                "class_id": 2,
                "confidence": 0.8
            },
            "expected": False
        },
        {
            "name": "bbox為None",
            "detection": {
                "bbox": None,
                "mask": [[1, 0], [0, 1]],
                "class_id": 2,
                "confidence": 0.8
            },
            "expected": False
        },
        {
            "name": "mask為None",
            "detection": {
                "bbox": [10, 20, 100, 200],
                "mask": None,
                "class_id": 2,
                "confidence": 0.8
            },
            "expected": False
        },
        {
            "name": "bbox長度不正確",
            "detection": {
                "bbox": [10, 20, 100],  # 只有3個元素
                "mask": [[1, 0], [0, 1]],
                "class_id": 2,
                "confidence": 0.8
            },
            "expected": False
        }
    ]
    
    print("🧪 測試bbox和mask的AND關係驗證邏輯\n")
    
    for i, case in enumerate(test_cases, 1):
        print(f"測試 {i}: {case['name']}")
        detection = case["detection"]
        
        # 實現與修改後代碼相同的驗證邏輯
        has_bbox = 'bbox' in detection and detection['bbox'] is not None and len(detection.get('bbox', [])) == 4
        has_mask = 'mask' in detection and detection['mask'] is not None
        
        result = has_bbox and has_mask
        expected = case["expected"]
        
        status = "✅ 通過" if result == expected else "❌ 失敗"
        print(f"   bbox: {'有效' if has_bbox else '無效'}")
        print(f"   mask: {'有效' if has_mask else '無效'}")
        print(f"   AND結果: {result}")
        print(f"   預期結果: {expected}")
        print(f"   {status}\n")
        
        if result != expected:
            print(f"❌ 測試失敗！案例: {case['name']}")
            return False
    
    print("✅ 所有測試通過！bbox和mask的AND關係邏輯正確。")
    return True

def test_filtering_logic():
    """測試過濾邏輯"""
    print("\n🔍 測試過濾邏輯:")
    
    # 模擬檢測結果列表
    detections = [
        {"bbox": [10, 20, 100, 200], "mask": [[1, 0]], "class_id": 2, "confidence": 0.8},  # 有效
        {"bbox": [10, 20, 100, 200], "class_id": 3, "confidence": 0.7},  # 缺少mask
        {"mask": [[1, 0]], "class_id": 4, "confidence": 0.6},  # 缺少bbox
        {"bbox": [10, 20, 100, 200], "mask": [[1, 0]], "class_id": 5, "confidence": 0.9},  # 有效
        {"bbox": None, "mask": [[1, 0]], "class_id": 6, "confidence": 0.5},  # bbox為None
    ]
    
    print(f"輸入檢測數量: {len(detections)}")
    
    # 過濾邏輯
    valid_detections = []
    for det in detections:
        has_bbox = 'bbox' in det and det['bbox'] is not None and len(det.get('bbox', [])) == 4
        has_mask = 'mask' in det and det['mask'] is not None
        
        if has_bbox and has_mask:
            valid_detections.append(det)
    
    print(f"過濾後檢測數量: {len(valid_detections)}")
    print(f"有效的class_ids: {[det['class_id'] for det in valid_detections]}")
    
    expected_valid_ids = [2, 5]  # 只有這兩個應該通過
    actual_valid_ids = [det['class_id'] for det in valid_detections]
    
    if actual_valid_ids == expected_valid_ids:
        print("✅ 過濾邏輯測試通過！")
        return True
    else:
        print(f"❌ 過濾邏輯測試失敗！預期: {expected_valid_ids}, 實際: {actual_valid_ids}")
        return False

if __name__ == "__main__":
    print("🚀 bbox和mask AND關係測試")
    print("=" * 50)
    
    test1_result = test_bbox_mask_validation()
    test2_result = test_filtering_logic()
    
    print("\n" + "=" * 50)
    if test1_result and test2_result:
        print("🎉 所有測試通過！修改邏輯正確。")
    else:
        print("❌ 部分測試失敗，請檢查邏輯。")