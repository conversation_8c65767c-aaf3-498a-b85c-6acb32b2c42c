# ⚖️ 智能負載均衡器
# Phase 4 核心功能 - AI驅動的動態負載均衡

import asyncio
import logging
import threading
import time
import statistics
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from enum import Enum
import json
import heapq
import random

class LoadBalancingStrategy(Enum):
    """負載均衡策略枚舉"""
    ROUND_ROBIN = "round_robin"           # 輪詢
    WEIGHTED_ROUND_ROBIN = "weighted_rr"  # 加權輪詢
    LEAST_CONNECTIONS = "least_conn"      # 最少連接
    WEIGHTED_LEAST_CONN = "weighted_lc"   # 加權最少連接
    RESPONSE_TIME = "response_time"       # 響應時間
    INTELLIGENT = "intelligent"          # AI智能選擇
    RESOURCE_AWARE = "resource_aware"     # 資源感知
    GEOGRAPHIC = "geographic"             # 地理位置

class ServerStatus(Enum):
    """服務器狀態枚舉"""
    HEALTHY = "healthy"                   # 健康
    DEGRADED = "degraded"                 # 性能下降
    OVERLOADED = "overloaded"             # 過載
    MAINTENANCE = "maintenance"           # 維護中
    OFFLINE = "offline"                   # 離線

@dataclass
class ServerMetrics:
    """服務器性能指標"""
    # 基礎指標
    cpu_usage: float = 0.0               # CPU使用率
    memory_usage: float = 0.0            # 記憶體使用率
    disk_usage: float = 0.0              # 磁碟使用率
    network_io: float = 0.0              # 網絡IO
    
    # 業務指標
    active_connections: int = 0          # 活躍連接數
    requests_per_second: float = 0.0     # 每秒請求數
    average_response_time: float = 0.0   # 平均響應時間
    error_rate: float = 0.0              # 錯誤率
    
    # AI推理指標
    gpu_usage: float = 0.0               # GPU使用率
    gpu_memory_usage: float = 0.0        # GPU記憶體使用率
    inference_queue_size: int = 0        # 推理隊列大小
    model_loading_time: float = 0.0      # 模型加載時間
    
    # 歷史統計
    response_time_p95: float = 0.0       # 95百分位響應時間
    response_time_p99: float = 0.0       # 99百分位響應時間
    uptime_percentage: float = 100.0     # 可用性百分比
    
    # 時間戳
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class ServerNode:
    """服務器節點"""
    node_id: str
    hostname: str
    ip_address: str
    port: int
    
    # 配置信息
    weight: int = 100                    # 權重 (1-1000)
    max_connections: int = 1000          # 最大連接數
    health_check_url: str = "/health"    # 健康檢查URL
    
    # 地理信息
    region: str = "default"              # 地區
    availability_zone: str = "default"   # 可用區
    latitude: float = 0.0                # 緯度
    longitude: float = 0.0               # 經度
    
    # 狀態信息
    status: ServerStatus = ServerStatus.HEALTHY
    last_health_check: datetime = field(default_factory=datetime.now)
    is_enabled: bool = True
    
    # 性能指標
    current_metrics: ServerMetrics = field(default_factory=ServerMetrics)
    metrics_history: List[ServerMetrics] = field(default_factory=list)
    
    # 連接統計
    current_connections: int = 0
    total_requests: int = 0
    failed_requests: int = 0
    
    # 維護信息
    maintenance_window: Optional[Dict[str, Any]] = None
    last_restart: Optional[datetime] = None

@dataclass
class LoadBalancingRule:
    """負載均衡規則"""
    rule_id: str
    name: str
    condition: str                       # 條件表達式
    action: str                          # 動作
    priority: int = 100                  # 優先級
    enabled: bool = True
    
    # 高級配置
    sticky_sessions: bool = False        # 會話保持
    health_check_interval: int = 30      # 健康檢查間隔(秒)
    failover_threshold: int = 3          # 故障轉移閾值
    circuit_breaker: bool = False        # 熔斷器
    
    # 統計信息
    applied_count: int = 0
    last_applied: Optional[datetime] = None


class IntelligentLoadBalancer:
    """智能負載均衡器"""
    
    def __init__(self, strategy: LoadBalancingStrategy = LoadBalancingStrategy.INTELLIGENT):
        self.logger = logging.getLogger(__name__)
        self.strategy = strategy
        
        # 服務器管理
        self.servers: Dict[str, ServerNode] = {}
        self.server_pools: Dict[str, List[str]] = {"default": []}
        
        # 負載均衡狀態
        self.current_server_index = 0
        self.connection_counts: Dict[str, int] = {}
        self.session_affinity: Dict[str, str] = {}  # session_id -> server_id
        
        # 規則引擎
        self.rules: List[LoadBalancingRule] = []
        
        # 健康檢查
        self.health_check_enabled = True
        self.health_check_interval = 30
        self.health_check_thread = None
        
        # 智能決策相關
        self.decision_cache: Dict[str, tuple] = {}  # 決策緩存
        self.prediction_model = None  # AI預測模型
        
        # 統計和監控
        self.routing_stats: Dict[str, Any] = {
            'total_requests': 0,
            'successful_routes': 0,
            'failed_routes': 0,
            'strategy_switches': 0
        }
        
        # 啟動後台任務
        self.running = True
        self._start_background_tasks()

    def add_server(self, server: ServerNode, pool_name: str = "default") -> bool:
        """添加服務器"""
        try:
            self.servers[server.node_id] = server
            
            # 添加到服務器池
            if pool_name not in self.server_pools:
                self.server_pools[pool_name] = []
            self.server_pools[pool_name].append(server.node_id)
            
            # 初始化連接計數
            self.connection_counts[server.node_id] = 0
            
            self.logger.info(f"添加服務器: {server.node_id} ({server.hostname}:{server.port})")
            return True
            
        except Exception as e:
            self.logger.error(f"添加服務器失敗: {e}")
            return False

    def remove_server(self, node_id: str) -> bool:
        """移除服務器"""
        try:
            if node_id not in self.servers:
                return False
            
            # 從所有池中移除
            for pool in self.server_pools.values():
                if node_id in pool:
                    pool.remove(node_id)
            
            # 清理相關數據
            del self.servers[node_id]
            if node_id in self.connection_counts:
                del self.connection_counts[node_id]
            
            # 清理會話親和性
            sessions_to_remove = [sid for sid, server in self.session_affinity.items() 
                                if server == node_id]
            for sid in sessions_to_remove:
                del self.session_affinity[sid]
            
            self.logger.info(f"移除服務器: {node_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"移除服務器失敗: {e}")
            return False

    def select_server(self, request_context: Optional[Dict[str, Any]] = None,
                     pool_name: str = "default") -> Optional[ServerNode]:
        """選擇服務器"""
        try:
            self.routing_stats['total_requests'] += 1
            
            # 檢查會話親和性
            if request_context:
                session_id = request_context.get('session_id')
                if session_id and session_id in self.session_affinity:
                    server_id = self.session_affinity[session_id]
                    server = self.servers.get(server_id)
                    if server and server.status == ServerStatus.HEALTHY:
                        return server
            
            # 獲取可用服務器
            available_servers = self._get_available_servers(pool_name)
            if not available_servers:
                self.routing_stats['failed_routes'] += 1
                return None
            
            # 根據策略選擇服務器
            selected_server = self._apply_strategy(available_servers, request_context)
            
            if selected_server:
                self.routing_stats['successful_routes'] += 1
                
                # 更新連接計數
                self.connection_counts[selected_server.node_id] += 1
                selected_server.current_connections += 1
                
                # 處理會話親和性
                if request_context and request_context.get('enable_sticky_session'):
                    session_id = request_context.get('session_id')
                    if session_id:
                        self.session_affinity[session_id] = selected_server.node_id
            else:
                self.routing_stats['failed_routes'] += 1
            
            return selected_server
            
        except Exception as e:
            self.logger.error(f"選擇服務器失敗: {e}")
            self.routing_stats['failed_routes'] += 1
            return None

    def _get_available_servers(self, pool_name: str) -> List[ServerNode]:
        """獲取可用服務器列表"""
        pool_servers = self.server_pools.get(pool_name, [])
        available_servers = []
        
        for server_id in pool_servers:
            server = self.servers.get(server_id)
            if (server and server.is_enabled and 
                server.status in [ServerStatus.HEALTHY, ServerStatus.DEGRADED] and
                server.current_connections < server.max_connections):
                available_servers.append(server)
        
        return available_servers

    def _apply_strategy(self, servers: List[ServerNode], 
                       context: Optional[Dict[str, Any]] = None) -> Optional[ServerNode]:
        """應用負載均衡策略"""
        if not servers:
            return None
        
        if self.strategy == LoadBalancingStrategy.ROUND_ROBIN:
            return self._round_robin_select(servers)
        elif self.strategy == LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN:
            return self._weighted_round_robin_select(servers)
        elif self.strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
            return self._least_connections_select(servers)
        elif self.strategy == LoadBalancingStrategy.WEIGHTED_LEAST_CONN:
            return self._weighted_least_connections_select(servers)
        elif self.strategy == LoadBalancingStrategy.RESPONSE_TIME:
            return self._response_time_select(servers)
        elif self.strategy == LoadBalancingStrategy.INTELLIGENT:
            return self._intelligent_select(servers, context)
        elif self.strategy == LoadBalancingStrategy.RESOURCE_AWARE:
            return self._resource_aware_select(servers)
        elif self.strategy == LoadBalancingStrategy.GEOGRAPHIC:
            return self._geographic_select(servers, context)
        else:
            return servers[0]  # 默認選擇第一個

    def _round_robin_select(self, servers: List[ServerNode]) -> ServerNode:
        """輪詢選擇"""
        server = servers[self.current_server_index % len(servers)]
        self.current_server_index += 1
        return server

    def _weighted_round_robin_select(self, servers: List[ServerNode]) -> ServerNode:
        """加權輪詢選擇"""
        total_weight = sum(server.weight for server in servers)
        if total_weight == 0:
            return self._round_robin_select(servers)
        
        # 使用加權隨機選擇
        weight_point = random.randint(1, total_weight)
        current_weight = 0
        
        for server in servers:
            current_weight += server.weight
            if weight_point <= current_weight:
                return server
        
        return servers[-1]  # 備用

    def _least_connections_select(self, servers: List[ServerNode]) -> ServerNode:
        """最少連接選擇"""
        return min(servers, key=lambda s: s.current_connections)

    def _weighted_least_connections_select(self, servers: List[ServerNode]) -> ServerNode:
        """加權最少連接選擇"""
        scores = []
        for server in servers:
            if server.weight == 0:
                score = float('inf')
            else:
                score = server.current_connections / server.weight
            scores.append((score, server))
        
        return min(scores, key=lambda x: x[0])[1]

    def _response_time_select(self, servers: List[ServerNode]) -> ServerNode:
        """響應時間選擇"""
        return min(servers, key=lambda s: s.current_metrics.average_response_time)

    def _intelligent_select(self, servers: List[ServerNode], 
                           context: Optional[Dict[str, Any]] = None) -> ServerNode:
        """智能AI選擇"""
        # 計算每個服務器的綜合分數
        scores = []
        
        for server in servers:
            score = self._calculate_server_score(server, context)
            scores.append((score, server))
        
        # 選擇最高分的服務器
        return max(scores, key=lambda x: x[0])[1]

    def _calculate_server_score(self, server: ServerNode, 
                               context: Optional[Dict[str, Any]] = None) -> float:
        """計算服務器綜合分數"""
        metrics = server.current_metrics
        
        # 基礎性能分數 (0-1)
        cpu_score = max(0, 1 - metrics.cpu_usage / 100)
        memory_score = max(0, 1 - metrics.memory_usage / 100)
        connection_score = max(0, 1 - server.current_connections / server.max_connections)
        response_time_score = max(0, 1 - metrics.average_response_time / 5000)  # 假設5秒為最差
        
        # 權重配置
        weights = {
            'cpu': 0.25,
            'memory': 0.20,
            'connections': 0.20,
            'response_time': 0.25,
            'error_rate': 0.10
        }
        
        # 錯誤率分數
        error_score = max(0, 1 - metrics.error_rate)
        
        # 服務器權重影響
        weight_score = server.weight / 1000  # 歸一化到0-1
        
        # 計算綜合分數
        base_score = (
            cpu_score * weights['cpu'] +
            memory_score * weights['memory'] +
            connection_score * weights['connections'] +
            response_time_score * weights['response_time'] +
            error_score * weights['error_rate']
        )
        
        # 應用服務器權重
        final_score = base_score * weight_score
        
        # 根據上下文調整分數
        if context:
            # 地理位置偏好
            if 'client_region' in context and context['client_region'] == server.region:
                final_score *= 1.1  # 同地區加分
            
            # 請求類型偏好
            request_type = context.get('request_type', 'default')
            if request_type == 'inference' and metrics.gpu_usage < 80:
                final_score *= 1.2  # GPU推理任務偏好GPU使用率低的服務器
        
        return final_score

    def _resource_aware_select(self, servers: List[ServerNode]) -> ServerNode:
        """資源感知選擇"""
        # 計算資源利用率
        resource_scores = []
        
        for server in servers:
            metrics = server.current_metrics
            
            # 計算綜合資源利用率 (越低越好)
            resource_utilization = (
                metrics.cpu_usage * 0.3 +
                metrics.memory_usage * 0.3 +
                metrics.gpu_usage * 0.2 +
                (server.current_connections / server.max_connections) * 100 * 0.2
            )
            
            resource_scores.append((resource_utilization, server))
        
        # 選擇資源利用率最低的服務器
        return min(resource_scores, key=lambda x: x[0])[1]

    def _geographic_select(self, servers: List[ServerNode], 
                          context: Optional[Dict[str, Any]] = None) -> ServerNode:
        """地理位置選擇"""
        if not context or 'client_location' not in context:
            return self._least_connections_select(servers)
        
        client_lat = context['client_location'].get('latitude', 0)
        client_lon = context['client_location'].get('longitude', 0)
        
        # 計算距離（簡化版）
        distances = []
        for server in servers:
            distance = ((server.latitude - client_lat) ** 2 + 
                       (server.longitude - client_lon) ** 2) ** 0.5
            distances.append((distance, server))
        
        # 選擇距離最近的服務器
        return min(distances, key=lambda x: x[0])[1]

    def update_server_metrics(self, node_id: str, metrics: ServerMetrics):
        """更新服務器指標"""
        server = self.servers.get(node_id)
        if not server:
            return
        
        # 更新當前指標
        server.current_metrics = metrics
        
        # 添加到歷史記錄
        server.metrics_history.append(metrics)
        
        # 保持歷史記錄在合理範圍內
        if len(server.metrics_history) > 100:
            server.metrics_history = server.metrics_history[-50:]
        
        # 更新服務器狀態
        self._update_server_status(server)

    def _update_server_status(self, server: ServerNode):
        """更新服務器狀態"""
        metrics = server.current_metrics
        
        # 定義閾值
        cpu_threshold = 80
        memory_threshold = 85
        error_threshold = 0.05
        response_threshold = 2000  # 2秒
        
        # 判斷狀態
        if (metrics.cpu_usage > cpu_threshold or 
            metrics.memory_usage > memory_threshold or
            metrics.error_rate > error_threshold or
            metrics.average_response_time > response_threshold):
            
            if server.status == ServerStatus.HEALTHY:
                server.status = ServerStatus.DEGRADED
                self.logger.warning(f"服務器 {server.node_id} 性能下降")
        else:
            if server.status == ServerStatus.DEGRADED:
                server.status = ServerStatus.HEALTHY
                self.logger.info(f"服務器 {server.node_id} 恢復健康")

    def release_connection(self, node_id: str):
        """釋放連接"""
        if node_id in self.connection_counts:
            self.connection_counts[node_id] = max(0, self.connection_counts[node_id] - 1)
        
        server = self.servers.get(node_id)
        if server:
            server.current_connections = max(0, server.current_connections - 1)

    def add_rule(self, rule: LoadBalancingRule):
        """添加負載均衡規則"""
        self.rules.append(rule)
        self.rules.sort(key=lambda r: r.priority, reverse=True)
        self.logger.info(f"添加負載均衡規則: {rule.name}")

    def get_statistics(self) -> Dict[str, Any]:
        """獲取統計信息"""
        total_servers = len(self.servers)
        healthy_servers = sum(1 for s in self.servers.values() 
                            if s.status == ServerStatus.HEALTHY)
        total_connections = sum(self.connection_counts.values())
        
        server_stats = []
        for server in self.servers.values():
            server_stats.append({
                'node_id': server.node_id,
                'hostname': server.hostname,
                'status': server.status.value,
                'current_connections': server.current_connections,
                'cpu_usage': server.current_metrics.cpu_usage,
                'memory_usage': server.current_metrics.memory_usage,
                'response_time': server.current_metrics.average_response_time
            })
        
        return {
            'strategy': self.strategy.value,
            'total_servers': total_servers,
            'healthy_servers': healthy_servers,
            'total_connections': total_connections,
            'routing_stats': self.routing_stats.copy(),
            'server_stats': server_stats
        }

    def _start_background_tasks(self):
        """啟動後台任務"""
        if self.health_check_enabled:
            self.health_check_thread = threading.Thread(
                target=self._health_check_worker, daemon=True
            )
            self.health_check_thread.start()

    def _health_check_worker(self):
        """健康檢查工作線程"""
        while self.running:
            try:
                for server in self.servers.values():
                    if server.is_enabled:
                        self._perform_health_check(server)
                
                time.sleep(self.health_check_interval)
                
            except Exception as e:
                self.logger.error(f"健康檢查失敗: {e}")

    def _perform_health_check(self, server: ServerNode):
        """執行健康檢查"""
        try:
            # 模擬健康檢查
            # 實際實現中會發送HTTP請求到health_check_url
            
            server.last_health_check = datetime.now()
            
            # 模擬健康檢查結果
            if server.current_metrics.cpu_usage > 95:
                server.status = ServerStatus.OVERLOADED
            elif server.current_metrics.error_rate > 0.1:
                server.status = ServerStatus.DEGRADED
            else:
                server.status = ServerStatus.HEALTHY
                
        except Exception as e:
            self.logger.error(f"健康檢查失敗 {server.node_id}: {e}")
            server.status = ServerStatus.OFFLINE

    def stop(self):
        """停止負載均衡器"""
        self.running = False
        if self.health_check_thread:
            self.health_check_thread.join(timeout=5)
        self.logger.info("智能負載均衡器已停止")


# 使用示例和測試
if __name__ == "__main__":
    # 配置日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建智能負載均衡器
    balancer = IntelligentLoadBalancer(LoadBalancingStrategy.INTELLIGENT)
    
    # 添加測試服務器
    servers = [
        ServerNode(
            node_id="server1",
            hostname="ai-server-1",
            ip_address="************",
            port=8000,
            weight=100,
            region="asia-east"
        ),
        ServerNode(
            node_id="server2", 
            hostname="ai-server-2",
            ip_address="************",
            port=8000,
            weight=150,
            region="asia-east"
        ),
        ServerNode(
            node_id="server3",
            hostname="ai-server-3", 
            ip_address="************",
            port=8000,
            weight=80,
            region="us-west"
        )
    ]
    
    for server in servers:
        balancer.add_server(server)
    
    # 模擬服務器指標更新
    balancer.update_server_metrics("server1", ServerMetrics(
        cpu_usage=45.2,
        memory_usage=62.8,
        gpu_usage=35.5,
        active_connections=25,
        average_response_time=120.5,
        error_rate=0.01
    ))
    
    balancer.update_server_metrics("server2", ServerMetrics(
        cpu_usage=78.9,
        memory_usage=82.1,
        gpu_usage=67.3,
        active_connections=45,
        average_response_time=250.8,
        error_rate=0.02
    ))
    
    # 測試服務器選擇
    print("🧪 測試服務器選擇:")
    for i in range(5):
        context = {
            'client_region': 'asia-east',
            'request_type': 'inference',
            'session_id': f'session_{i%3}'
        }
        
        selected_server = balancer.select_server(context)
        if selected_server:
            print(f"   請求 {i+1}: 選擇服務器 {selected_server.node_id}")
        else:
            print(f"   請求 {i+1}: 無可用服務器")
    
    # 獲取統計信息
    stats = balancer.get_statistics()
    print(f"\n📊 負載均衡統計:")
    print(f"   總服務器數: {stats['total_servers']}")
    print(f"   健康服務器數: {stats['healthy_servers']}")
    print(f"   總連接數: {stats['total_connections']}")
    print(f"   成功路由: {stats['routing_stats']['successful_routes']}")
    print(f"   失敗路由: {stats['routing_stats']['failed_routes']}")
    
    # 停止負載均衡器
    balancer.stop()