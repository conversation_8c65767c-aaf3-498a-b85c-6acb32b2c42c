#!/usr/bin/env python3
"""
🏆 高精度分類器 - 基於最佳預訓練模型

特點：
- 使用torchvision中accuracy最高的預訓練模型 (EfficientNet V2 Large: 88.4% ImageNet accuracy)
- 支援資料夾結構的分類數據 (資料夾名稱作為標籤)
- 整合albumentations進行數據增強
- 完整的訓練/驗證/測試流程
- 詳細的評估指標和可視化
- 集成項目現有的訓練框架

使用方法：
1. 修改下方 "🔧 配置參數區域" 中的參數 (特別是 DATA_DIR)
2. 直接運行: python high_accuracy_classifier.py

數據結構：
data_dir/
├── train/
│   ├── class1/
│   │   ├── image1.jpg
│   │   └── image2.jpg
│   └── class2/
│       ├── image3.jpg
│       └── image4.jpg
├── val/
│   └── ...
└── test/
    └── ...
"""

import os
import sys
import json
import time
import logging
import warnings
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import torchvision.models as models
import torchvision.transforms as transforms

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image
import cv2
from sklearn.metrics import (
    accuracy_score, precision_recall_fscore_support, 
    confusion_matrix, classification_report
)
from sklearn.utils.class_weight import compute_class_weight

# 數據增強
import albumentations as A
from albumentations.pytorch import ToTensorV2

# 進度條和可視化
from tqdm import tqdm

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore', category=UserWarning)

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('classifier_training.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class ClassifierConfig:
    """分類器配置類"""
    # 基礎配置
    data_dir: str = "./data"
    output_dir: str = "./output"
    num_classes: int = 5  # 簡化後的5類分類任務
    
    # 模型配置
    model_name: str = "efficientnet_v2_l"  # 最高accuracy的預訓練模型
    image_size: int = 480  # EfficientNet V2 Large 推薦尺寸
    pretrained: bool = True
    
    # 訓練配置
    batch_size: int = 4  # 考慮到模型較大，使用較小的batch size
    num_epochs: int = 100
    learning_rate: float = 0.001
    weight_decay: float = 1e-4
    
    # 優化配置
    optimizer: str = "adamw"  # AdamW通常對大模型效果更好
    scheduler: str = "cosine"  # 餘弦退火學習率
    warmup_epochs: int = 5
    
    # 高級功能
    mixed_precision: bool = True  # 混合精度訓練，節省記憶體
    gradient_clipping: float = 1.0
    early_stopping_patience: int = 15
    
    # 數據增強配置
    use_advanced_augmentation: bool = True
    cutmix_prob: float = 0.5
    mixup_alpha: float = 0.2
    
    # 硬體配置
    device: str = "auto"  # auto, cuda, cpu
    num_workers: int = 4
    pin_memory: bool = True
    
    # 評估配置
    test_time_augmentation: bool = False
    save_best_only: bool = True
    save_predictions: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ClassifierConfig':
        """從字典創建配置"""
        return cls(**config_dict)


class FolderClassificationDataset(Dataset):
    """
    資料夾結構的分類數據集
    支援自動標籤映射和albumentations增強
    """
    
    def __init__(self, 
                 data_dir: str,
                 split: str = "train",
                 transform: Optional[A.Compose] = None,
                 class_to_idx: Optional[Dict[str, int]] = None):
        """
        初始化數據集
        
        Args:
            data_dir: 數據目錄路徑
            split: 數據分割 ("train", "val", "test")
            transform: albumentations變換
            class_to_idx: 類別到索引的映射
        """
        self.data_dir = Path(data_dir) / split
        self.split = split
        self.transform = transform
        
        # 支援的圖像格式
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
        # 收集數據
        self.samples = []
        self.classes = []
        
        if not self.data_dir.exists():
            raise FileNotFoundError(f"數據目錄不存在: {self.data_dir}")
        
        # 自動發現類別
        if class_to_idx is None:
            self.class_to_idx = self._discover_classes()
        else:
            self.class_to_idx = class_to_idx
            
        self.idx_to_class = {v: k for k, v in self.class_to_idx.items()}
        self.classes = list(self.class_to_idx.keys())
        
        # 收集所有樣本
        self._collect_samples()
        
        logger.info(f"📁 {split.upper()} 數據集載入完成:")
        logger.info(f"   類別數量: {len(self.classes)}")
        logger.info(f"   樣本數量: {len(self.samples)}")
        logger.info(f"   類別分佈: {dict(Counter(sample[1] for sample in self.samples))}")
    
    def _discover_classes(self) -> Dict[str, int]:
        """自動發現類別"""
        class_names = []
        for item in self.data_dir.iterdir():
            if item.is_dir():
                class_names.append(item.name)
        
        class_names.sort()  # 確保順序一致
        return {name: idx for idx, name in enumerate(class_names)}
    
    def _collect_samples(self):
        """收集所有樣本"""
        for class_name, class_idx in self.class_to_idx.items():
            class_dir = self.data_dir / class_name
            if not class_dir.exists():
                logger.warning(f"類別目錄不存在: {class_dir}")
                continue
            
            for img_path in class_dir.iterdir():
                if img_path.suffix.lower() in self.image_extensions:
                    self.samples.append((str(img_path), class_idx))
        
        if not self.samples:
            raise ValueError(f"在 {self.data_dir} 中未找到任何圖像文件")
    
    def __len__(self) -> int:
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int]:
        img_path, label = self.samples[idx]
        
        # 載入圖像
        try:
            image = cv2.imread(img_path)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        except Exception as e:
            logger.error(f"載入圖像失敗 {img_path}: {e}")
            # 返回黑色圖像作為備用
            image = np.zeros((224, 224, 3), dtype=np.uint8)
        
        # 應用變換
        if self.transform:
            transformed = self.transform(image=image)
            image = transformed['image']
        else:
            # 基礎變換
            image = torch.from_numpy(image).permute(2, 0, 1).float() / 255.0
        
        return image, label


class HighAccuracyClassifier(nn.Module):
    """
    高精度分類器
    基於torchvision中accuracy最高的預訓練模型
    """
    
    def __init__(self, 
                 num_classes: int,
                 model_name: str = "efficientnet_v2_l",
                 pretrained: bool = True,
                 dropout_rate: float = 0.3):
        """
        初始化分類器
        
        Args:
            num_classes: 類別數量
            model_name: 模型名稱
            pretrained: 是否使用預訓練權重
            dropout_rate: Dropout比率
        """
        super().__init__()
        
        self.num_classes = num_classes
        self.model_name = model_name
        
        # 創建骨幹網路
        self.backbone = self._create_backbone(model_name, pretrained)
        
        # 獲取特徵維度
        self.feature_dim = self._get_feature_dim()
        
        # 創建分類頭 (使用BatchNorm1d獲得更好性能，配合drop_last避免batch_size=1)
        self.classifier = nn.Sequential(
            nn.Dropout(dropout_rate),
            nn.Linear(self.feature_dim, 512),
            nn.BatchNorm1d(512),  # 預訓練模型使用BatchNorm性能更好
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate),
            nn.Linear(512, num_classes)
        )
        
        # 🔥 預訓練模型微調：適度凍結低層特徵
        if pretrained:
            self._freeze_backbone(freeze_ratio=0.2)  # 5類簡單任務，較少凍結
        else:
            logger.info("🔥 從零訓練模式：所有層都可訓練")
    
    def _create_backbone(self, model_name: str, pretrained: bool) -> nn.Module:
        """創建骨幹網路"""
        try:
            if model_name == "efficientnet_v2_m":
                model = models.efficientnet_v2_m(weights='DEFAULT' if pretrained else None)
                # 移除分類層
                model.classifier = nn.Identity()
                return model
            elif model_name == "efficientnet_v2_l":
                model = models.efficientnet_v2_l(weights='DEFAULT' if pretrained else None)
                # 移除分類層
                model.classifier = nn.Identity()
                return model
            elif model_name == "convnext_large":
                model = models.convnext_large(weights='DEFAULT' if pretrained else None)
                model.classifier = nn.Identity()
                return model
            elif model_name == "swin_v2_t":
                model = models.swin_v2_t(weights='DEFAULT' if pretrained else None)
                model.head = nn.Identity()
                return model
            else:
                # 備用方案：使用ResNet152
                logger.warning(f"不支援的模型 {model_name}，使用 ResNet152")
                model = models.resnet152(weights='DEFAULT' if pretrained else None)
                model.fc = nn.Identity()
                return model
        except Exception as e:
            logger.error(f"創建模型失敗: {e}")
            # 最終備用方案
            logger.info("使用 ResNet50 作為備用方案")
            model = models.resnet50(weights='DEFAULT' if pretrained else None)
            model.fc = nn.Identity()
            return model
    
    def _get_feature_dim(self) -> int:
        """獲取特徵維度"""
        dummy_input = torch.randn(1, 3, 224, 224)
        with torch.no_grad():
            features = self.backbone(dummy_input)
            return features.shape[-1] if features.dim() == 2 else features.shape[1]
    
    def _freeze_backbone(self, freeze_ratio: float = 0.8):
        """凍結骨幹網路的部分層"""
        # 修正：先解凍所有骨幹層，以確保漸進式策略生效
        for param in self.backbone.parameters():
            param.requires_grad = True
            
        params = list(self.backbone.parameters())
        freeze_num = int(len(params) * freeze_ratio)
        
        for param in params[:freeze_num]:
            param.requires_grad = False
        
        # 修正：更新日誌記錄以反映實際凍結的參數數量
        total_params = len(params)
        frozen_params = sum(1 for p in self.backbone.parameters() if not p.requires_grad)
        logger.info(f"凍結了 {frozen_params}/{total_params} ({frozen_params/total_params:.1%}) 個參數層")
    
    def unfreeze_all(self):
        """解凍所有層"""
        for param in self.parameters():
            param.requires_grad = True
        logger.info("解凍所有層")
    
    def _freeze_backbone_progressive(self, stage: int):
        """🏆 人孔蓋專用：漸進式解凍策略"""
        freeze_ratios = {
            1: 0.4,  # 第1階段：溫和凍結40% (初始)
            2: 0.2,  # 第2階段：輕度凍結20%  
            3: 0.1,  # 第3階段：極輕凍結10%
            4: 0.0   # 第4階段：完全解凍
        }
        
        if stage in freeze_ratios:
            self._freeze_backbone(freeze_ratios[stage])
            logger.info(f"🔄 切換到凍結階段{stage}，凍結比例：{freeze_ratios[stage]*100}%")
        else:
            logger.warning(f"無效的凍結階段: {stage}")
    
    def _setup_layered_learning_rates(self, base_lr: float, weight_decay: float):
        """🏆 高精度方案：不同層使用不同學習率"""
        backbone_params = []
        classifier_params = []
        
        for name, param in self.named_parameters():
            if 'classifier' in name:
                classifier_params.append(param)
            else:
                backbone_params.append(param)
        
        # 返回參數組配置 - 人孔蓋專用調整
        return [
            {'params': backbone_params, 'lr': base_lr * 0.5, 'weight_decay': weight_decay},  # 骨幹網路適中學習率
            {'params': classifier_params, 'lr': base_lr * 2.0, 'weight_decay': weight_decay}  # 分類頭更高學習率
        ]
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        features = self.backbone(x)
        if features.dim() > 2:
            features = F.adaptive_avg_pool2d(features, (1, 1))
            features = features.flatten(1)
        return self.classifier(features)


class ClassificationTrainer:
    """
    分類訓練器
    整合完整的訓練、驗證、測試流程
    """
    
    def __init__(self, config: ClassifierConfig):
        self.config = config
        self.device = self._setup_device()
        self.output_dir = Path(config.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 訓練狀態
        self.current_epoch = 0
        self.best_accuracy = 0.0
        self.training_history = {
            'train_loss': [], 'train_acc': [],
            'val_loss': [], 'val_acc': []
        }
        
        # 初始化模型和數據
        self.model = None
        self.train_loader = None
        self.val_loader = None
        self.test_loader = None
        self.class_names = None
        
        # 損失函數和優化器
        self.criterion = None
        self.optimizer = None
        self.scheduler = None
        self.scaler = None
        
        logger.info(f"🚀 分類訓練器初始化完成，設備: {self.device}")
        
        # 漸進式class_weights的變量初始化
        self.original_class_weights = None
        self.mild_class_weights = None 
        self.current_class_weights = None
    
    def _setup_device(self) -> torch.device:
        """設置運算設備"""
        if self.config.device == "auto":
            if torch.cuda.is_available():
                device = torch.device("cuda")
                logger.info(f"🎮 使用GPU: {torch.cuda.get_device_name()}")
            else:
                device = torch.device("cpu")
                logger.info("💻 使用CPU")
        else:
            device = torch.device(self.config.device)
        
        return device
    
    def setup_data(self, train: bool = True):
        """設置數據載入器"""
        val_transform = self._get_val_transforms()
        class_to_idx = None
        
        # 在測試模式下，嘗試從現有配置加載類別映射
        if not train:
            class_mapping_path = Path(self.config.output_dir) / "class_mapping.json"
            if class_mapping_path.exists():
                with open(class_mapping_path, "r", encoding="utf-8") as f:
                    class_to_idx = json.load(f)
                logger.info(f"從 {class_mapping_path} 加載了類別映射")
            else:
                # 如果沒有映射文件，則從 test 文件夾推斷
                logger.warning("找不到 class_mapping.json，將從 test 文件夾結構推斷類別。")
                # 這需要 FolderClassificationDataset 支持在沒有 class_to_idx 的情況下處理 test split
                pass # 讓 test_dataset 自行發現類別
        
        # 設置訓練和驗證數據 (僅在訓練模式下)
        if train:
            train_transform = self._get_train_transforms()
            train_dataset = FolderClassificationDataset(
                self.config.data_dir, "train", train_transform
            )
            class_to_idx = train_dataset.class_to_idx
            
            val_dataset = FolderClassificationDataset(
                self.config.data_dir, "val", val_transform, class_to_idx
            )
            
            self.train_loader = DataLoader(
                train_dataset,
                batch_size=self.config.batch_size,
                shuffle=True,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory,
                drop_last=True  # 避免最後一個batch只有1個樣本
            )
            
            self.val_loader = DataLoader(
                val_dataset,
                batch_size=self.config.batch_size,
                shuffle=False,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory
            )
            
            self.class_names = train_dataset.classes
            self.config.num_classes = len(self.class_names)
            
            with open(self.output_dir / "class_mapping.json", "w", encoding="utf-8") as f:
                json.dump(class_to_idx, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📊 訓練/驗證數據載入完成:")
            logger.info(f"   訓練集: {len(train_dataset)} 樣本")
            logger.info(f"   驗證集: {len(val_dataset)} 樣本")

        # 設置測試數據
        test_dir = Path(self.config.data_dir) / "test"
        if test_dir.exists():
            test_dataset = FolderClassificationDataset(
                self.config.data_dir, "test", val_transform, class_to_idx
            )
            self.test_loader = DataLoader(
                test_dataset,
                batch_size=self.config.batch_size,
                shuffle=False,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory
            )
            
            if not train: # 如果是僅測試模式，從測試集更新類別信息
                self.class_names = test_dataset.classes
                self.config.num_classes = len(self.class_names)

            logger.info(f"   測試集: {len(test_dataset)} 樣本")
        
        logger.info(f"   類別: {self.class_names}")
    
    def _get_train_transforms(self) -> A.Compose:
        """獲取訓練時的數據增強"""
        if self.config.use_advanced_augmentation:
            return A.Compose([
                A.OneOf([
                    A.Resize(self.config.image_size, self.config.image_size),
                    A.CenterCrop(self.config.image_size, self.config.image_size),
                ], p=1),
                A.OneOf([
                    A.RandomRotate90(p=0.6),
                    A.Rotate(limit=180, p=0.6),
                ], p=0.6),
                A.VerticalFlip(p=0.5),
                A.HorizontalFlip(p=0.5),
                A.Transpose(p=0.5),
                A.RGBShift(r_shift_limit=5, g_shift_limit=5, b_shift_limit=5, p=0.5),
                A.OneOf([
                    A.RandomBrightnessContrast(brightness_limit=0.4, contrast_limit=0.4, p=0.6),
                    A.CLAHE(clip_limit=4.0, tile_grid_size=(8, 8), p=0.6),
                ], p=0.8),
                A.OneOf([
                    A.GaussNoise(var_limit=(5.0, 10.0), p=0.4),
                    A.GaussianBlur(blur_limit=(3, 7), p=0.3),
                    A.MotionBlur(blur_limit=(3, 5), p=0.3),
                    A.Sharpen(alpha=(0.3, 0.6), p=0.4),
                ], p=0.6),
                A.CoarseDropout(max_holes=8, max_height=32, max_width=32, p=0.3),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ])
        else:
            return A.Compose([
                A.Resize(self.config.image_size, self.config.image_size),
                A.CenterCrop(self.config.image_size, self.config.image_size),
                A.RandomRotate90(p=0.5),
                A.VerticalFlip(p=0.5),
                A.HorizontalFlip(p=0.5),
                A.RandomBrightnessContrast(p=0.3),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ])
    
    def _get_val_transforms(self) -> A.Compose:
        """獲取驗證時的數據變換"""
        return A.Compose([
            A.Resize(self.config.image_size, self.config.image_size),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2()
        ])
    
    def setup_model(self):
        """設置模型"""
        self.model = HighAccuracyClassifier(
            num_classes=self.config.num_classes,
            model_name=self.config.model_name,
            pretrained=self.config.pretrained
        ).to(self.device)
        
        # 🏆 高精度方案：加權損失函數處理類別不平衡
        try:
            # 提取所有訓練標籤
            all_labels = [sample[1] for sample in self.train_loader.dataset.samples]
            class_weights = compute_class_weight(
                'balanced',
                classes=np.unique(all_labels),
                y=all_labels
            )
            # 🎯 漸進式class_weights策略
            # 初期使用較溫和的權重，後期逐漸加強
            self.original_class_weights = torch.tensor(class_weights, dtype=torch.float32, device=self.device)
            
            # 計算溫和化權重（將極端權重拉近1.0）
            mild_weights = torch.ones_like(self.original_class_weights)
            for i, weight in enumerate(class_weights):
                if weight > 1.0:
                    # 大權重溫和化：原權重^0.5
                    mild_weights[i] = weight ** 0.5
                else:
                    # 小權重保持
                    mild_weights[i] = weight
            
            self.mild_class_weights = mild_weights
            self.current_class_weights = self.mild_class_weights.clone()
            
            self.criterion = nn.CrossEntropyLoss(
                weight=self.current_class_weights, 
                label_smoothing=0.1
            )
            
            logger.info(f"🎯 使用漸進式加權損失函數:")
            logger.info(f"   原始權重: {class_weights}")
            logger.info(f"   初期權重: {mild_weights.cpu().numpy()}")
            
            # 顯示類別分佈和權重對應
            for i, (class_name, orig_weight, mild_weight) in enumerate(zip(self.class_names, class_weights, mild_weights)):
                class_count = sum(1 for sample in self.train_loader.dataset.samples if sample[1] == i)
                logger.info(f"   {class_name}: {class_count}樣本, 原始權重={orig_weight:.3f}, 初期權重={mild_weight:.3f}")
        except Exception as e:
            logger.warning(f"無法計算類別權重，使用標準交叉熵損失: {e}")
            self.criterion = nn.CrossEntropyLoss(label_smoothing=0.1)
        
        # 🚀 優化器設置：根據訓練模式選擇策略
        if self.config.optimizer.lower() == "adamw":
            if self.config.pretrained and self.config.scheduler.lower() != "cosine_warm_restarts":
                # 預訓練模型使用分層學習率 (除非使用CosineWarmRestarts)
                param_groups = self.model._setup_layered_learning_rates(
                    self.config.learning_rate, self.config.weight_decay
                )
                self.optimizer = optim.AdamW(param_groups)
                logger.info("🎯 使用分層學習率優化器")
            else:
                # 從零訓練或CosineWarmRestarts使用統一學習率
                self.optimizer = optim.AdamW(
                    self.model.parameters(),
                    lr=self.config.learning_rate,
                    weight_decay=self.config.weight_decay
                )
                logger.info("🚀 使用統一學習率優化器")
        else:
            self.optimizer = optim.Adam(
                self.model.parameters(),
                lr=self.config.learning_rate,
                weight_decay=self.config.weight_decay
            )
        
        # 學習率調度器
        if self.config.scheduler.lower() == "cosine":
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, T_max=self.config.num_epochs
            )
        elif self.config.scheduler.lower() == "cosine_warm_restarts":
            # 🚀 CosineAnnealingWarmRestarts：每batch執行
            # 計算總steps數
            self.total_steps = len(self.train_loader) * self.config.num_epochs
            # T_0: 第一個週期的步數 (約10個epoch的長度)
            T_0 = len(self.train_loader) * 10
            self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
                self.optimizer, 
                T_0=T_0,              # 第一個重啟週期：10 epochs
                T_mult=2,             # 每次重啟後週期加倍
                eta_min=1e-8,         # 最小學習率：1e-6 (適合預訓練模型)
                last_epoch=-1
            )
            logger.info(f"🚀 CosineWarmRestarts設置完成:")
            logger.info(f"   總steps: {self.total_steps:,}")
            logger.info(f"   T_0: {T_0} steps (~10 epochs)")
            logger.info(f"   學習率範圍: {self.config.learning_rate:.2e} → 1e-6")
        else:
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='max', patience=5, factor=0.5
            )
        
        # 混合精度
        if self.config.mixed_precision:
            self.scaler = torch.cuda.amp.GradScaler()
        
        # 計算模型參數
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        logger.info(f"🏗️ 模型設置完成:")
        logger.info(f"   模型: {self.config.model_name}")
        logger.info(f"   總參數: {total_params:,}")
        logger.info(f"   可訓練參數: {trainable_params:,}")
        logger.info(f"   類別數: {self.config.num_classes}")
    
    def train_epoch(self) -> Tuple[float, float]:
        """訓練一個epoch"""
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        pbar = tqdm(self.train_loader, desc=f"訓練 Epoch {self.current_epoch+1}")
        
        for batch_idx, (data, target) in enumerate(pbar):
            data, target = data.to(self.device), target.to(self.device)
            
            self.optimizer.zero_grad()
            
            if self.config.mixed_precision and self.scaler:
                with torch.cuda.amp.autocast():
                    output = self.model(data)
                    loss = self.criterion(output, target)
                
                self.scaler.scale(loss).backward()
                
                if self.config.gradient_clipping > 0:
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.gradient_clipping)
                
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                output = self.model(data)
                loss = self.criterion(output, target)
                loss.backward()
                
                if self.config.gradient_clipping > 0:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.gradient_clipping)
                
                self.optimizer.step()
            
            # 🚀 CosineWarmRestarts：每batch執行調度器
            if self.config.scheduler.lower() == "cosine_warm_restarts":
                self.scheduler.step()
            
            # 統計
            total_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
            
            # 更新進度條
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100. * correct / total:.2f}%'
            })
        
        avg_loss = total_loss / len(self.train_loader)
        accuracy = 100. * correct / total
        
        return avg_loss, accuracy
    
    def validate(self) -> Tuple[float, float]:
        """驗證模型"""
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            pbar = tqdm(self.val_loader, desc="驗證")
            for data, target in pbar:
                data, target = data.to(self.device), target.to(self.device)
                
                if self.config.mixed_precision:
                    with torch.amp.autocast(device_type='cuda', dtype=torch.float16):
                        output = self.model(data)
                        loss = self.criterion(output, target)
                else:
                    output = self.model(data)
                    loss = self.criterion(output, target)
                
                total_loss += loss.item()
                _, predicted = torch.max(output.data, 1)
                total += target.size(0)
                correct += (predicted == target).sum().item()
                
                pbar.set_postfix({
                    'Loss': f'{loss.item():.4f}',
                    'Acc': f'{100. * correct / total:.2f}%'
                })
        
        avg_loss = total_loss / len(self.val_loader)
        accuracy = 100. * correct / total
        
        return avg_loss, accuracy
    
    def _update_class_weights_progressive(self, epoch: int):
        """🎯 漸進式更新class_weights"""
        if self.original_class_weights is None or self.mild_class_weights is None:
            return
            
        # 定義權重轉換的時間點
        transition_epochs = [30, 60, 100]  # 在這些輪次逐步加強權重
        
        if epoch < transition_epochs[0]:
            # 第0-30輪：使用溫和權重
            alpha = 0.0
        elif epoch < transition_epochs[1]: 
            # 第30-60輪：線性插值 溫和→中等
            alpha = (epoch - transition_epochs[0]) / (transition_epochs[1] - transition_epochs[0]) * 0.5
        elif epoch < transition_epochs[2]:
            # 第60-100輪：線性插值 中等→完整
            alpha = 0.5 + (epoch - transition_epochs[1]) / (transition_epochs[2] - transition_epochs[1]) * 0.5
        else:
            # 第100輪後：使用完整權重
            alpha = 1.0
            
        # 計算當前權重：linear interpolation between mild and original
        self.current_class_weights = (1 - alpha) * self.mild_class_weights + alpha * self.original_class_weights
        
        # 更新損失函數的權重
        self.criterion.weight = self.current_class_weights
        
        # 每10輪記錄一次權重變化
        if epoch % 10 == 0:
            logger.info(f"📊 第{epoch}輪權重調整: alpha={alpha:.2f}")
            for i, (class_name, current_weight) in enumerate(zip(self.class_names, self.current_class_weights)):
                orig_weight = self.original_class_weights[i].item()
                logger.info(f"   {class_name}: {current_weight:.3f} (目標: {orig_weight:.3f})")
    
    def train(self):
        """完整訓練流程"""
        logger.info("🎯 開始訓練")
        
        patience_counter = 0
        start_time = time.time()
        
        for epoch in range(self.config.num_epochs):
            self.current_epoch = epoch
            
            # 🚀 訓練策略：根據調度器類型執行不同策略
            if self.config.scheduler.lower() == "cosine_warm_restarts":
                # CosineWarmRestarts自動管理學習率，但需要管理class_weights
                self._update_class_weights_progressive(epoch)
                pass
            elif not self.config.pretrained:
                # 從零訓練：手動學習率調度 (僅當非CosineWarmRestarts時)
                if epoch == 50:  # 第50輪：降低學習率
                    for param_group in self.optimizer.param_groups:
                        param_group['lr'] *= 0.5
                    logger.info("🔥 第50輪：學習率減半，進入精調階段")
                elif epoch == 100:  # 第100輪：再次降低
                    for param_group in self.optimizer.param_groups:
                        param_group['lr'] *= 0.5
                    logger.info("🔥 第100輪：學習率再減半，最終精調")
            else:
                # 預訓練模型的解凍策略
                if epoch == 10:  # 第10輪：快速解凍到20%
                    self.model._freeze_backbone_progressive(2)
                    logger.info("🔓 第10輪：快速解凍到20%層")
                elif epoch == 25:  # 第25輪：解凍到10%
                    self.model._freeze_backbone_progressive(3)
                    logger.info("🔓 第25輪：解凍到10%層")
                elif epoch == 50:  # 第50輪：完全解凍
                    self.model._freeze_backbone_progressive(4)
                    # 降低學習率進入精調階段
                    for param_group in self.optimizer.param_groups:
                        param_group['lr'] *= 0.5
                    logger.info("🔓 第50輪：完全解凍，學習率降低一半")
                    logger.info("💡 進入精調階段，專注人孔蓋細節特徵")
            
            # 訓練
            train_loss, train_acc = self.train_epoch()
            
            # 驗證
            val_loss, val_acc = self.validate()
            
            # 更新學習率 (除了CosineWarmRestarts，其他在epoch結束時執行)
            if self.config.scheduler.lower() == "cosine":
                self.scheduler.step()
            elif self.config.scheduler.lower() == "cosine_warm_restarts":
                # CosineWarmRestarts已經在每個batch執行，這裡不需要額外步進
                pass
            else:
                self.scheduler.step(val_acc)
            
            # 記錄歷史
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_acc'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_acc'].append(val_acc)
            
            # 🏆 高精度方案：詳細的訓練進度日誌
            logger.info(f"Epoch {epoch+1}/{self.config.num_epochs}:")
            logger.info(f"  訓練 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
            logger.info(f"  驗證 - Loss: {val_loss:.4f}, Acc: {val_acc:.2f}%")
            
            # 顯示學習率信息
            if self.config.scheduler.lower() == "cosine_warm_restarts":
                # CosineWarmRestarts：顯示當前學習率和調度器狀態
                current_lr = self.optimizer.param_groups[0]['lr']
                logger.info(f"  🚀 當前學習率: {current_lr:.2e}")
                # 計算當前在重啟週期中的位置
                last_restart = self.scheduler.last_epoch // self.scheduler.T_i
                progress_in_cycle = (self.scheduler.last_epoch % self.scheduler.T_i) / self.scheduler.T_i
                logger.info(f"  🔄 重啟週期: {last_restart+1}, 週期進度: {progress_in_cycle:.1%}")
            elif len(self.optimizer.param_groups) > 1:
                # 分層學習率：顯示不同參數組的學習率
                for i, param_group in enumerate(self.optimizer.param_groups):
                    lr_type = "分類頭" if i == 1 else "骨幹網路"
                    logger.info(f"  學習率({lr_type}): {param_group['lr']:.6f}")
            else:
                # 統一學習率：顯示單一學習率
                logger.info(f"  學習率: {self.optimizer.param_groups[0]['lr']:.6f}")
            
            # 顯示當前凍結狀態
            frozen_params = sum(1 for p in self.model.backbone.parameters() if not p.requires_grad)
            total_params = sum(1 for p in self.model.backbone.parameters())
            freeze_ratio = frozen_params / total_params if total_params > 0 else 0
            logger.info(f"  凍結狀態: {frozen_params}/{total_params} ({freeze_ratio:.1%})")
            
            # 保存最佳模型
            if val_acc > self.best_accuracy:
                self.best_accuracy = val_acc
                self.save_model("best_model.pth")
                patience_counter = 0
                logger.info(f"🏆 新的最佳模型! 驗證準確率: {val_acc:.2f}%")
            else:
                patience_counter += 1
            
            # 早停
            if patience_counter >= self.config.early_stopping_patience:
                logger.info(f"⏰ 早停觸發 (patience: {self.config.early_stopping_patience})")
                break
            
            # 定期保存
            # if (epoch + 1) % 10 == 0:
            #     self.save_model(f"checkpoint_epoch_{epoch+1}.pth")
        
        # 訓練完成
        training_time = time.time() - start_time
        logger.info(f"✅ 訓練完成!")
        logger.info(f"  總時間: {training_time/3600:.2f} 小時")
        logger.info(f"  最佳驗證準確率: {self.best_accuracy:.2f}%")
        
        # 保存訓練歷史和配置
        self.save_training_history()
        self.save_config()
        
        # 生成訓練曲線
        self.plot_training_curves()
    
    def test(self, model_path: str) -> Dict[str, Any]:
        """測試模型"""
        if self.test_loader is None:
            logger.warning("沒有測試數據，跳過測試")
            return {}
        
        logger.info("🧪 開始測試")
        
        # 載入指定模型
        self.load_model(model_path)
        
        self.model.eval()
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for data, target in tqdm(self.test_loader, desc="測試"):
                data, target = data.to(self.device), target.to(self.device)
                
                if self.config.mixed_precision:
                    with torch.amp.autocast(device_type='cuda', dtype=torch.float16):
                        output = self.model(data)
                else:
                    output = self.model(data)
                
                _, predicted = torch.max(output, 1)
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(target.cpu().numpy())
        
        # 計算指標
        accuracy = accuracy_score(all_targets, all_predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(
            all_targets, all_predictions, average='weighted'
        )
        
        # 分類報告
        report = classification_report(
            all_targets, all_predictions,
            target_names=self.class_names,
            output_dict=True
        )
        
        results = {
            'test_accuracy': accuracy * 100,
            'test_precision': precision * 100,
            'test_recall': recall * 100,
            'test_f1': f1 * 100,
            'classification_report': report
        }
        
        logger.info(f"🎯 測試結果:")
        logger.info(f"  準確率: {accuracy*100:.2f}%")
        logger.info(f"  精確率: {precision*100:.2f}%")
        logger.info(f"  召回率: {recall*100:.2f}%")
        logger.info(f"  F1分數: {f1*100:.2f}%")
        
        # 保存測試結果
        with open(self.output_dir / "test_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 生成混淆矩陣
        self.plot_confusion_matrix(all_targets, all_predictions)
        
        return results
    
    def save_model(self, filename: str):
        """保存模型"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'best_accuracy': self.best_accuracy,
            'config': self.config.to_dict(),
            'class_names': self.class_names
        }
        
        if self.scaler:
            checkpoint['scaler_state_dict'] = self.scaler.state_dict()
        
        torch.save(checkpoint, self.output_dir / filename)
    
    def load_model(self, model_path: str):
        """載入模型"""
        checkpoint = torch.load(model_path, map_location=self.device)
        
        # 從 checkpoint 恢復配置，這在僅測試模式下很重要
        loaded_config_dict = checkpoint.get('config', {})
        self.config = ClassifierConfig.from_dict({**self.config.to_dict(), **loaded_config_dict})
        self.config.num_classes = len(checkpoint.get('class_names', []))

        # 重新設置模型以匹配加載的配置
        self.setup_model() 

        self.model.load_state_dict(checkpoint['model_state_dict'])
        
        # 僅在存在時加載優化器和調度器狀態
        if self.optimizer and 'optimizer_state_dict' in checkpoint:
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if self.scheduler and checkpoint.get('scheduler_state_dict'):
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        if self.scaler and checkpoint.get('scaler_state_dict'):
            self.scaler.load_state_dict(checkpoint['scaler_state_dict'])
        
        self.current_epoch = checkpoint.get('epoch', 0)
        self.best_accuracy = checkpoint.get('best_accuracy', 0.0)
        self.class_names = checkpoint.get('class_names', [])
        
        logger.info(f"✅ 模型載入完成: {model_path}")
    
    def save_training_history(self):
        """保存訓練歷史"""
        history_df = pd.DataFrame(self.training_history)
        history_df.to_csv(self.output_dir / "training_history.csv", index=False)
        
        with open(self.output_dir / "training_history.json", "w") as f:
            json.dump(self.training_history, f, indent=2)
    
    def save_config(self):
        """保存配置"""
        with open(self.output_dir / "config.json", "w", encoding="utf-8") as f:
            json.dump(self.config.to_dict(), f, ensure_ascii=False, indent=2)
    
    def plot_training_curves(self):
        """繪製訓練曲線"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        epochs = range(1, len(self.training_history['train_loss']) + 1)
        
        # 損失曲線
        ax1.plot(epochs, self.training_history['train_loss'], 'b-', label='訓練損失')
        ax1.plot(epochs, self.training_history['val_loss'], 'r-', label='驗證損失')
        ax1.set_title('訓練和驗證損失')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('損失')
        ax1.legend()
        ax1.grid(True)
        
        # 準確率曲線
        ax2.plot(epochs, self.training_history['train_acc'], 'b-', label='訓練準確率')
        ax2.plot(epochs, self.training_history['val_acc'], 'r-', label='驗證準確率')
        ax2.set_title('訓練和驗證準確率')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('準確率 (%)')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / "training_curves.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"📈 訓練曲線已保存: {self.output_dir / 'training_curves.png'}")
    
    def plot_confusion_matrix(self, y_true: List[int], y_pred: List[int]):
        """繪製混淆矩陣"""
        cm = confusion_matrix(y_true, y_pred)
        
        plt.figure(figsize=(12, 10))
        sns.heatmap(
            cm, annot=True, fmt='d', cmap='Blues',
            xticklabels=self.class_names,
            yticklabels=self.class_names
        )
        plt.title('混淆矩陣')
        plt.xlabel('預測類別')
        plt.ylabel('實際類別')
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig(self.output_dir / "confusion_matrix.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"📊 混淆矩陣已保存: {self.output_dir / 'confusion_matrix.png'}")


# ==================== 🔧 配置參數區域 ====================
# 在這裡設定你的訓練參數，修改後直接運行即可

# ===== 🎯 運行模式 =====
MODE = "train"  # "train" 或 "test" - 🏆 高精度方案
# 如果是 "test" 模式，請設定下方模型路徑
MODEL_PATH_FOR_TESTING = r"D:\5_Hole_cover\classification\ai_result\best_model.pth"

# ===== 📁 數據配置 =====
DATA_DIR = r"D:\5_Hole_cover\classification\ai_train"                          # 數據目錄路徑 (必須設定)
OUTPUT_DIR = r"D:\5_Hole_cover\classification\ai_result"           # 輸出目錄

# ===== 🤖 模型配置 =====
MODEL_NAME = "efficientnet_v2_l"            # 預訓練模型名稱 - 輕量高效
# 可選: "efficientnet_v2_m" (85.3%), "efficientnet_v2_l" (88.4%), "convnext_large" (87.8%)
IMAGE_SIZE = 384                             # EfficientNet V2-M預訓練尺寸：384x384
PRETRAINED = True                            # 使用預訓練模型：更快收斂

# ===== ⚡ 訓練配置 - 🏆 高精度方案 =====
BATCH_SIZE = 8                               # 🏆 最佳泛化性能的選擇
NUM_EPOCHS = 200                             # 🏆 充分訓練
LEARNING_RATE = 1e-3                         # 🚀 預訓練模型微調學習率
WEIGHT_DECAY = 2e-4                          # 🏆 增強正則化

# ===== 🚀 優化配置 - 🏆 高精度方案 =====
OPTIMIZER = "adamw"                          # 優化器 ("adamw", "adam")
SCHEDULER = "cosine_warm_restarts"           # 🚀 週期性重啟：1e-3→1e-6
MIXED_PRECISION = True                       # 混合精度訓練 (節省記憶體)
GRADIENT_CLIPPING = 1.0                      # 🏆 更嚴格的梯度裁剪
EARLY_STOPPING_PATIENCE = NUM_EPOCHS                # 🏆 更多耐心等待

# ===== 🔄 數據增強配置 =====
USE_ADVANCED_AUGMENTATION = True             # 高級數據增強
CUTMIX_PROB = 0.5                           # CutMix機率
MIXUP_ALPHA = 0.2                           # MixUp參數

# ===== 💻 硬體配置 =====
DEVICE = "auto"                              # 運算設備 ("auto", "cuda", "cpu")
NUM_WORKERS = 4                              # 數據載入工作進程數
PIN_MEMORY = True                            # 固定記憶體

# ==================== 🔧 配置參數區域結束 ====================


def run_training(config: ClassifierConfig) -> bool:
    """執行完整的訓練流程"""
    logger.info("🚀 開始高精度分類器訓練")
    logger.info(f"📁 數據目錄: {config.data_dir}")
    logger.info(f"📂 輸出目錄: {config.output_dir}")
    logger.info(f"🤖 模型: {config.model_name}")
    logger.info(f"🖼️ 圖像尺寸: {config.image_size}")
    logger.info(f"📦 批次大小: {config.batch_size}")
    logger.info(f"🔄 訓練輪數: {config.num_epochs}")
    logger.info(f"⚡ 設備: {config.device}")
    logger.info(f"🔧 混合精度: {'啟用' if config.mixed_precision else '關閉'}")
    logger.info(f"🎨 高級增強: {'啟用' if config.use_advanced_augmentation else '關閉'}")

    trainer = ClassificationTrainer(config)
    
    try:
        # 設置數據和模型
        trainer.setup_data()
        trainer.setup_model()
        
        # 訓練
        trainer.train()
        
        # 測試
        test_results = trainer.test(model_path=str(Path(config.output_dir) / "best_model.pth"))
        
        logger.info("🎉 訓練完成!")
        if test_results:
            logger.info(f"🏆 最終測試準確率: {test_results['test_accuracy']:.2f}%")
            
    except Exception as e:
        logger.error(f"❌ 訓練過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    return True


def run_testing(config: ClassifierConfig, model_path: str) -> bool:
    """僅執行測試流程"""
    logger.info("🧪 開始僅測試模式")
    logger.info(f"📁 數據目錄: {config.data_dir}")
    logger.info(f"📂 輸出目錄: {config.output_dir}")
    logger.info(f"📦 批次大小: {config.batch_size}")
    logger.info(f"⚡ 設備: {config.device}")
    logger.info(f"模型路徑: {model_path}")

    if not Path(model_path).exists():
        logger.error(f"❌ 模型文件不存在: {model_path}")
        return False

    trainer = ClassificationTrainer(config)

    try:
        # 設置數據和模型
        trainer.setup_data(train=False) # 測試模式不需要訓練集
        trainer.setup_model()
        
        # 測試
        test_results = trainer.test(model_path=model_path)
        
        logger.info("🎉 測試完成!")
        if test_results:
            logger.info(f"🏆 最終測試準確率: {test_results['test_accuracy']:.2f}%")

    except Exception as e:
        logger.error(f"❌ 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    return True


def main():
    """主函數 - 根據MODE選擇執行訓練或測試"""
    
    config = ClassifierConfig(
        data_dir=DATA_DIR,
        output_dir=OUTPUT_DIR,
        model_name=MODEL_NAME,
        image_size=IMAGE_SIZE,
        pretrained=PRETRAINED,
        batch_size=BATCH_SIZE,
        num_epochs=NUM_EPOCHS,
        learning_rate=LEARNING_RATE,
        weight_decay=WEIGHT_DECAY,
        optimizer=OPTIMIZER,
        scheduler=SCHEDULER,
        mixed_precision=MIXED_PRECISION,
        gradient_clipping=GRADIENT_CLIPPING,
        early_stopping_patience=EARLY_STOPPING_PATIENCE,
        use_advanced_augmentation=USE_ADVANCED_AUGMENTATION,
        cutmix_prob=CUTMIX_PROB,
        mixup_alpha=MIXUP_ALPHA,
        device=DEVICE,
        num_workers=NUM_WORKERS,
        pin_memory=PIN_MEMORY
    )

    # 檢查數據目錄
    if not Path(config.data_dir).exists():
        logger.error(f"❌ 數據目錄不存在: {config.data_dir}")
        return False

    if MODE == "train":
        train_dir = Path(config.data_dir) / "train"
        val_dir = Path(config.data_dir) / "val"
        if not train_dir.exists() or not val_dir.exists():
            logger.error("❌ 訓練模式下 'train' 和 'val' 子目錄必須存在")
            return False
        return run_training(config)
    elif MODE == "test":
        test_dir = Path(config.data_dir) / "test"
        if not test_dir.exists():
            logger.error("❌ 測試模式下 'test' 子目錄必須存在")
            return False
        return run_testing(config, MODEL_PATH_FOR_TESTING)
    else:
        logger.error(f"❌ 無效的運行模式: {MODE}. 請選擇 'train' 或 'test'.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
