#!/usr/bin/env python3
"""
🎯 YOLO推理系統配置模板
快速配置指南 - 複製需要的設定到主腳本中
"""

# ============================================================================
# 📋 基本配置 (必需)
# ============================================================================

# 🤖 模型設定
segmentation_model_path = r"path/to/your/model.pt"    # 修改為您的模型路徑
input_path = r"path/to/input/images"                  # 修改為輸入圖像路徑
output_path = r"path/to/output"                       # 修改為輸出目錄路徑

# ============================================================================
# 🧠 記憶體清理配置 (解決記憶體緩慢上升問題)
# ============================================================================

# 🧠 記憶體清理設定 - 確實運行的關鍵配置
enable_memory_cleanup = True                          # 啟用記憶體清理功能
memory_cleanup_trigger = "hybrid"                    # 觸發模式: hybrid(推薦)/time_based/usage_based
memory_cleanup_interval = 15.0                       # 清理間隔(秒) - 降低以更頻繁清理
memory_cleanup_after_images = 5                      # 每N張圖像後清理 - 降低以更頻繁清理
memory_cpu_threshold = 70.0                          # CPU記憶體使用率觸發閾值(%) - 降低以更積極清理
memory_gpu_threshold = 70.0                          # GPU記憶體使用率觸發閾值(%) - 降低以更積極清理
memory_cleanup_on_batch_start = True                 # 批次開始時清理
memory_cleanup_on_batch_end = True                   # 批次結束時清理

# ============================================================================
# 🧠 智能過濾配置 (已優化 - 簡化邏輯)
# ============================================================================

# 🔍 基本智能過濾
enable_intelligent_filtering = True                   # 啟用智能過濾功能
alligator_contains_linear_threshold = 0.2             # 重疊檢測IoU閾值 (有重疊時大box優先保留)

# 📏 長寬比過濾 (獨立功能 - 可選)
enable_aspect_ratio_filter = False                    # 是否啟用長寬比獨立過濾
linear_aspect_ratio_threshold = 0.3                   # 長寬比閾值 (小於此值認為是線狀)

# 📐 面積比過濾 (獨立功能 - 可選)  
enable_area_ratio_filter = False                      # 是否啟用面積比獨立過濾
area_ratio_threshold = 0.1                            # 面積比閾值 (小於此值認為面積差異大)

# ============================================================================
# 🔗 物件連結配置 (統一距離閾值)
# ============================================================================

# 🔗 物件連結設定
enable_object_connection = True                       # 啟用物件連接功能
connection_distance_threshold = 30.0                  # 連接距離閾值(pixels) - 統一的距離設定
connection_same_class_only = True                     # 僅連接相同類別物件
enable_crack_line_connection = True                   # 啟用裂縫線段連接

# ============================================================================
# 🔀 融合策略配置
# ============================================================================

# 🔀 融合策略設定
fusion_strategy = "largest_object"                    # 策略: standard_nms/largest_object/no_fusion
fusion_iou_threshold = 0.4                           # 融合IoU閾值

# 🎯 SAHI專用重疊合併設定
enable_sahi_overlap_merge = True                     # SAHI重疊合併獨立開關
sahi_merge_iou_threshold = 0.1                       # SAHI合併IoU閾值

# ============================================================================
# 📊 使用建議
# ============================================================================

"""
💡 配置建議:

1. 🧠 記憶體問題解決:
   - 降低 memory_cpu_threshold 和 memory_gpu_threshold 到 70% 
   - 減少 memory_cleanup_interval 到 15秒
   - 減少 memory_cleanup_after_images 到 5張圖像

2. 🔍 智能過濾優化:
   - 基本配置只需設定 alligator_contains_linear_threshold
   - 有重疊時自動保留大box，邏輯簡化
   - 長寬比和面積比現在是獨立功能，可選開啟

3. 🔗 距離閾值統一:
   - 只保留 connection_distance_threshold
   - 移除了重複的 adjacent_distance_threshold

4. 🚀 性能優化:
   - 使用 "largest_object" 融合策略獲得更好的重疊處理
   - 啟用 SAHI重疊合併提升切片推理效果
"""

# ============================================================================
# 🔧 安裝依賴 (如果系統不可用)
# ============================================================================

"""
如果出現"記憶體清理功能已啟用但系統不可用"錯誤:

pip install psutil
pip install torch torchvision
pip install opencv-python
pip install ultralytics
pip install sahi

完整安裝:
pip install -r requirements.txt
"""