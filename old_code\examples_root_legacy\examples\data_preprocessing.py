#!/usr/bin/env python3
"""
資料前處理使用示例
展示如何使用策略模式重構的資料前處理系統
包括標註轉換、數據增強、數據集分割等功能
"""

from pathlib import Path
import sys

# 使用統一導入管理
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from import_helper import setup_project_paths, DATA_PROCESSING_AVAILABLE
setup_project_paths()

def main():
    """資料前處理使用示例主函數"""
    
    print("📁 資料前處理使用示例")
    print("=" * 50)
    print("策略模式重構的智能資料處理系統")
    print()
    
    # ===================================================================
    # 📋 參數設定區域 - 請根據您的需求修改以下參數
    # ===================================================================
    
    # 🎯 標註轉換配置
    # 格式轉換: LabelMe → YOLO, VOC → LabelMe, COCO → LabelMe等
    input_annotation_dir = "./test_image"          # 輸入標註目錄
    output_annotation_dir = "./output/converted"   # 輸出標註目錄
    input_format = "auto"                          # 輸入格式: "auto", "labelme", "voc", "coco", "yolo"
    output_format = "yolo"                         # 輸出格式: "yolo", "labelme", "voc", "coco"
    
    # 🔧 轉換參數
    batch_size = 100                               # 批次處理大小
    enable_validation = True                       # 啟用轉換結果驗證
    preserve_hierarchy = True                      # 保持目錄結構
    
    # 🎨 數據增強配置
    enable_augmentation = True                     # 啟用數據增強
    augmentation_methods = [                       # 增強方法列表
        "rotation", "flip", "scale", "color"
    ]
    augmentation_factor = 2                        # 增強倍數
    
    # 📊 數據集分割配置
    enable_dataset_split = True                    # 啟用數據集分割
    split_ratios = {                               # 分割比例
        "train": 0.7,
        "val": 0.15,
        "test": 0.15
    }
    ensure_class_balance = True                    # 確保類別平衡
    min_samples_per_class = 5                      # 每類最少樣本數
    
    # 🌐 全景圖像處理配置 (針對道路監控)
    enable_panorama_processing = False             # 啟用全景圖像處理
    panorama_rotations = [                         # OPK旋轉參數 (攝影測量專業)
        {"axis": "omega", "angle": 15.0},          # 縱搖角
        {"axis": "phi", "angle": -10.0},           # 橫搖角
        {"axis": "kappa", "angle": 45.0}           # 航向角
    ]
    
    # 📁 路徑配置
    output_base_dir = "./output"                   # 輸出基礎目錄
    log_file_path = "./output/preprocessing.log"   # 日誌文件路徑
    
    # ===================================================================
    # 🔍 執行檢查和驗證
    # ===================================================================
    
    if not DATA_PROCESSING_AVAILABLE:
        print("❌ 資料前處理模組不可用")
        print("請檢查資料前處理/tools/目錄和相關文件")
        return
    
    print("✅ 資料前處理模組可用")
    
    # 檢查輸入路徑
    if not Path(input_annotation_dir).exists():
        print(f"⚠️  警告: 輸入目錄不存在: {input_annotation_dir}")
        print("將創建示例目錄結構進行演示")
        # 這裡可以創建示例數據
    
    # 創建輸出目錄
    Path(output_base_dir).mkdir(parents=True, exist_ok=True)
    
    # ===================================================================
    # 🚀 執行資料前處理流程
    # ===================================================================
    
    try:
        # 導入資料前處理相關模組
        from 資料前處理.tools.annotation_converter_v2 import AnnotationConverterV2
        from 資料前處理.tools.format_detector import FormatDetector
        from 資料前處理.shared.config_manager import ConfigManager
        
        print("✅ 資料前處理模組導入成功")
        
        # ===============================================================
        # 1. 📋 智能格式檢測
        # ===============================================================
        
        print("\\n🔍 步驟1: 智能格式檢測")
        print("-" * 30)
        
        detector = FormatDetector()
        if Path(input_annotation_dir).exists():
            detected_format = detector.detect_format(input_annotation_dir)
            confidence = detector.get_confidence_score()
            
            print(f"✅ 檢測到格式: {detected_format}")
            print(f"📊 信心度: {confidence:.2f}")
            
            if input_format == "auto":
                input_format = detected_format
                print(f"🎯 自動使用檢測到的格式: {input_format}")
        else:
            print("⚠️  輸入目錄不存在，使用指定格式")
        
        # ===============================================================
        # 2. 🔄 標註格式轉換
        # ===============================================================
        
        print("\\n🔄 步驟2: 標註格式轉換")
        print("-" * 30)
        
        # 創建轉換器
        converter = AnnotationConverterV2(
            input_dir=input_annotation_dir,
            output_dir=output_annotation_dir,
            input_format=input_format,
            output_format=output_format,
            config={
                'batch_size': batch_size,
                'enable_validation': enable_validation,
                'preserve_hierarchy': preserve_hierarchy
            }
        )
        
        print(f"🔧 配置轉換: {input_format} → {output_format}")
        print(f"📦 批次大小: {batch_size}")
        print(f"✅ 驗證啟用: {enable_validation}")
        
        if Path(input_annotation_dir).exists():
            # 執行轉換
            result = converter.run()
            
            print("✅ 標註轉換完成")
            print(f"📊 處理文件: {result.get('processed_files', 0)}/{result.get('total_files', 0)}")
            print(f"⏱️  處理時間: {result.get('processing_time', 0):.2f}秒")
            
            if result.get('errors'):
                print(f"⚠️  錯誤數量: {len(result['errors'])}")
        else:
            print("⚠️  跳過轉換（輸入目錄不存在）")
        
        # ===============================================================
        # 3. 🎨 數據增強 (如果啟用)
        # ===============================================================
        
        if enable_augmentation:
            print("\\n🎨 步驟3: 數據增強")
            print("-" * 30)
            
            try:
                # 導入增強器
                from 資料前處理.img_augmenter import ImageAugmenter
                
                augmenter = ImageAugmenter(
                    source_dirs=[output_annotation_dir],
                    target_dir=f"{output_base_dir}/augmented",
                    task_type='both'  # 支援分割+檢測雙重任務
                )
                
                # 配置增強參數
                augmenter.configure_placement(
                    enable_grid_placement=True,
                    avoid_similar_positions=True,
                    min_distance_ratio=0.1
                )
                
                print(f"🎯 增強方法: {augmentation_methods}")
                print(f"📊 增強倍數: {augmentation_factor}")
                print("🧩 網格放置: 啟用")
                
                # 執行增強
                augmented_data = augmenter.run()
                print("✅ 數據增強完成")
                print(f"📊 生成圖像: {augmented_data.get('generated_count', 0)}")
                
            except ImportError:
                print("⚠️  數據增強模組不可用，跳過此步驟")
        
        # ===============================================================
        # 4. 📊 數據集分割 (如果啟用)
        # ===============================================================
        
        if enable_dataset_split:
            print("\\n📊 步驟4: 數據集分割")
            print("-" * 30)
            
            try:
                # 導入分割器
                from 資料前處理.dataset_divider import DatasetDivider
                
                # 使用增強後的數據或轉換後的數據
                split_input_dir = f"{output_base_dir}/augmented" if enable_augmentation else output_annotation_dir
                
                divider = DatasetDivider(
                    input_dir=split_input_dir,
                    output_dir=f"{output_base_dir}/split_dataset",
                    split_ratios=split_ratios
                )
                
                print(f"📐 分割比例: {split_ratios}")
                print(f"⚖️  類別平衡: {ensure_class_balance}")
                print(f"📊 最少樣本數: {min_samples_per_class}")
                
                # 執行分割
                if Path(split_input_dir).exists():
                    split_result = divider.split_with_class_balance(
                        ensure_class_balance=ensure_class_balance,
                        min_samples_per_class=min_samples_per_class,
                        generate_report=True
                    )
                    
                    print("✅ 數據集分割完成")
                    print(f"🎓 訓練集: {split_result.get('train_count', 0)} 樣本")
                    print(f"✅ 驗證集: {split_result.get('val_count', 0)} 樣本")
                    print(f"🧪 測試集: {split_result.get('test_count', 0)} 樣本")
                else:
                    print("⚠️  輸入目錄不存在，跳過數據集分割")
                
            except ImportError:
                print("⚠️  數據集分割模組不可用，跳過此步驟")
        
        # ===============================================================
        # 5. 🌐 全景圖像處理 (如果啟用)
        # ===============================================================
        
        if enable_panorama_processing:
            print("\\n🌐 步驟5: 全景圖像處理")
            print("-" * 30)
            
            try:
                # 導入全景處理器
                from 資料前處理.panorama_augmenter import PanoramaAugmenter
                
                augmenter = PanoramaAugmenter()
                
                print("📐 OPK旋轉參數 (攝影測量專業):")
                for rotation in panorama_rotations:
                    print(f"   {rotation['axis']}: {rotation['angle']}°")
                
                # 這裡可以添加全景圖像處理邏輯
                print("✅ 全景圖像處理配置完成")
                
            except ImportError:
                print("⚠️  全景處理模組不可用，跳過此步驟")
        
        # ===============================================================
        # 📊 生成處理報告
        # ===============================================================
        
        print("\\n📊 生成處理報告")
        print("-" * 30)
        
        report = {
            "處理時間": "completed",
            "輸入格式": input_format,
            "輸出格式": output_format,
            "數據增強": "啟用" if enable_augmentation else "禁用",
            "數據集分割": "啟用" if enable_dataset_split else "禁用",
            "全景處理": "啟用" if enable_panorama_processing else "禁用",
            "輸出目錄": output_base_dir
        }
        
        # 保存報告
        import json
        report_path = f"{output_base_dir}/processing_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 處理報告保存至: {report_path}")
        
        # ===============================================================
        # 🎯 使用建議
        # ===============================================================
        
        print("\\n📚 使用建議:")
        print("1. 🎯 道路檢測: 推薦LabelMe→YOLO轉換，支援多種形狀")
        print("2. 🎨 數據不足: 使用智能增強器，避免目標重疊")
        print("3. 📊 大規模數據: 啟用批次處理和類別平衡分割")
        print("4. 🌐 道路監控: 使用全景圖像處理和OPK旋轉")
        
        print("\\n🔗 與其他模組整合:")
        print("   🚀 Enhanced YOLO: 可直接使用轉換後的YOLO格式")
        print("   🧠 Vision Mamba: 支援高解析度增強後的圖像")
        print("   🏭 統一訓練: 自動載入分割後的數據集")
        
        print(f"\\n🎉 資料前處理完成! 所有結果保存至: {output_base_dir}")
        
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        print("請確保資料前處理相關依賴已安裝:")
        print("   pip install opencv-python numpy")
        print("   pip install pillow matplotlib")
        print("   pip install PyQt6 (GUI功能)")
        
    except Exception as e:
        print(f"❌ 資料前處理失敗: {e}")
        import traceback
        print("詳細錯誤信息:")
        traceback.print_exc()

if __name__ == "__main__":
    main()