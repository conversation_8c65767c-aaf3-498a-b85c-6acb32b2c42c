#!/usr/bin/env python3
"""
🏆 統一YOLO推理系統 - 修復版
修復了導入問題的終極整合版本

🌟 功能特色:
✅ 修復了所有導入問題
✅ 可選啟用 Phase 4 智能化功能
✅ 漸進式升級路徑，從基礎到智能化
✅ 完全兼容原有參數設定

🎯 運行模式:
1. 🔧 BASIC: 基礎YOLO推理，無額外依賴
2. 🧠 SMART: 如果Phase 4可用則啟用智能功能
"""

from pathlib import Path
import sys
import logging
import time
from enum import Enum

# 確保導入路徑正確
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# ============================================================================
# 📋 運行模式和參數設定
# ============================================================================

class RunningMode(Enum):
    """運行模式枚舉"""
    BASIC = "basic"         # 基礎模式 - 純YOLO推理
    SMART = "smart"         # 智能模式 - 嘗試啟用Phase 4功能

# 🎯 選擇運行模式
RUNNING_MODE = RunningMode.BASIC  # 修改為 SMART 以嘗試啟用智能功能

print(f"🚀 統一YOLO推理系統 - 修復版")
print(f"🎯 當前運行模式: {RUNNING_MODE.value.upper()}")
print("=" * 60)

# 🤖 模型設定
segmentation_model_path = r"D:\4_road_crack\best_0728.pt"  # 分割模型路徑
detection_model_path = None                              # 檢測模型路徑（可選）

# 📁 路徑設定
input_path = r"D:\image\road_crack\test_600_resize"       # 輸入圖像路徑/目錄
output_path = r"D:\image\road_crack\test_600_out_fixed"   # 輸出結果目錄

# 🧩 SAHI切片推理設定
enable_sahi = False                                     # 啟用SAHI大圖切片
sahi_slice_height = 512                                 # 切片高度
sahi_slice_width = 512                                  # 切片寬度
sahi_overlap_ratio = 0.2                               # 重疊比例

# 🧠 智能過濾設定
enable_intelligent_filtering = True                     # 啟用智能過濾
linear_aspect_ratio_threshold = 0.8                    # Step1: 長寬比閾值
area_ratio_threshold = 0.4                             # Step1: 面積比閾值  
step2_iou_threshold = 0.3                              # Step2: IoU閾值

# 🎨 視覺化設定
enable_three_view_output = True                        # 啟用三視圖輸出
three_view_layout = "horizontal"                       # 佈局: horizontal/vertical
font_size = 1.0                                        # 字體大小倍數
font_thickness = 2                                     # 字體粗細
line_thickness = 2                                     # 邊框線條粗細
transparency = 0.3                                     # 遮罩透明度
output_image_quality = 95                              # 輸出圖像質量(1-100)

# 🏷️ 類別配置 - 每個類別獨立設定
class_configs = {
    0: ["Alligator_crack", "龜裂", [0, 0, 255], 0.4, 0.1, True],
    1: ["deformation", "變形", [100, 100, 100], 0.1, 0.1, True],
    2: ["dirt", "汙垢", [110, 110, 110], 0.1, 0.08, True],
    3: ["expansion_joint", "伸縮縫", [120, 120, 120], 0.1, 0.08, True],
    4: ["joint", "路面接縫", [130, 130, 130], 0.1, 0.1, True],
    5: ["lane_line_linear", "白線裂縫", [140, 140, 140], 0.1, 0.05, True],
    6: ["linear_crack", "裂縫", [0, 255, 0], 0.3, 0.1, True],
    7: ["manhole", "孔蓋", [255, 0, 255], 0.1, 0.1, True],
    8: ["patch", "補綻", [255, 0, 0], 0.1, 0.1, True],
    9: ["patch_square", "補綻_方正", [160, 160, 160], 0.1, 0.1, True],
    10: ["potholes", "坑洞", [0, 255, 255], 0.1, 0.1, True],
    11: ["rutting", "車轍", [255, 255, 0], 0.1, 0.1, True]
}

# 📊 輸出設定
enable_csv_output = True                               # 啟用CSV統計輸出
enable_json_output = True                              # 啟用JSON結果輸出
save_confidence_distribution = True                    # 保存置信度分佈
save_detection_images = True                           # 保存檢測結果圖像

# 🔧 系統設定
max_workers = 1                                        # 並行處理數量
enable_cache = True                                    # 啟用緩存
log_level = "INFO"                                     # 日誌級別

# ============================================================================
# 🚀 智能導入系統 - 漸進式載入
# ============================================================================

def setup_logging():
    """設置日誌系統"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def try_import_yolo_system():
    """嘗試導入YOLO系統"""
    try:
        from inference_system import create_inference_system, UnifiedConfig, ClassConfig
        print("✅ 統一YOLO推理系統載入成功")
        return True, (create_inference_system, UnifiedConfig, ClassConfig)
    except ImportError as e:
        print(f"⚠️ 統一YOLO推理系統載入失敗: {e}")
        print("🔄 將使用基礎YOLO推理")
        return False, None

def try_import_phase4_components():
    """嘗試導入Phase 4組件"""
    if RUNNING_MODE != RunningMode.SMART:
        return False, {}
    
    components = {}
    
    # 智能模型選擇
    try:
        from intelligence.model_selector import IntelligentModelManager, ScenarioType
        components['model_manager'] = (IntelligentModelManager, ScenarioType)
        print("✅ 智能模型選擇器載入成功")
    except ImportError:
        print("⚠️ 智能模型選擇器不可用")
    
    # 多租戶管理
    try:
        from enterprise.multi_tenant import TenantManager, TenantMiddleware
        components['tenant_manager'] = (TenantManager, TenantMiddleware)
        print("✅ 多租戶管理器載入成功")
    except ImportError:
        print("⚠️ 多租戶管理器不可用")
    
    # 負載均衡
    try:
        from load_balancing.intelligent_balancer import IntelligentLoadBalancer
        components['load_balancer'] = IntelligentLoadBalancer
        print("✅ 智能負載均衡器載入成功")
    except ImportError:
        print("⚠️ 智能負載均衡器不可用")
    
    has_phase4 = len(components) > 0
    if has_phase4:
        print(f"🧠 Phase 4 功能部分可用 ({len(components)} 個組件)")
    else:
        print("ℹ️ Phase 4 功能不可用，使用基礎模式")
    
    return has_phase4, components

def basic_yolo_inference():
    """基礎YOLO推理"""
    try:
        from ultralytics import YOLO
        import cv2
        import numpy as np
        import json
        
        logger = setup_logging()
        logger.info("🔧 使用基礎YOLO推理模式")
        
        # 檢查模型
        model_path = Path(segmentation_model_path)
        if not model_path.exists():
            logger.error(f"模型文件不存在: {segmentation_model_path}")
            return False
        
        # 檢查輸入
        input_path_obj = Path(input_path)
        if not input_path_obj.exists():
            logger.error(f"輸入路徑不存在: {input_path}")
            return False
        
        # 創建輸出目錄
        output_path_obj = Path(output_path)
        output_path_obj.mkdir(parents=True, exist_ok=True)
        
        # 載入模型
        logger.info(f"📁 載入模型: {model_path.name}")
        model = YOLO(str(model_path))
        
        # 獲取圖像文件
        if input_path_obj.is_file():
            image_files = [input_path_obj]
        else:
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
            image_files = [f for f in input_path_obj.iterdir() 
                          if f.suffix.lower() in image_extensions]
        
        logger.info(f"📸 找到 {len(image_files)} 個圖像文件")
        
        # 處理圖像
        all_results = []
        total_detections = 0
        
        for i, image_file in enumerate(image_files):
            logger.info(f"📸 處理 ({i+1}/{len(image_files)}): {image_file.name}")
            
            try:
                # 獲取對應類別的置信度閾值
                confidence_thresholds = {}
                for class_id, config in class_configs.items():
                    if config[5]:  # 如果啟用
                        confidence_thresholds[class_id] = config[3]
                
                min_confidence = min(confidence_thresholds.values()) if confidence_thresholds else 0.3
                
                # YOLO推理
                results = model(str(image_file), conf=min_confidence)
                
                # 提取檢測結果
                detections = []
                if results and len(results) > 0:
                    for result in results:
                        if hasattr(result, 'boxes') and result.boxes is not None:
                            boxes = result.boxes
                            for box in boxes:
                                class_id = int(box.cls.item())
                                confidence = float(box.conf.item())
                                
                                # 檢查類別是否啟用和置信度
                                if (class_id in confidence_thresholds and 
                                    confidence >= confidence_thresholds[class_id]):
                                    
                                    class_name = class_configs.get(class_id, ["unknown", "未知"])[0]
                                    detection = {
                                        'class_id': class_id,
                                        'class_name': class_name,
                                        'confidence': confidence,
                                        'bbox': box.xyxy[0].tolist()
                                    }
                                    detections.append(detection)
                
                # 智能過濾 (如果啟用)
                if enable_intelligent_filtering and detections:
                    detections = apply_intelligent_filtering(detections)
                
                image_result = {
                    'image_file': image_file.name,
                    'detections': detections,
                    'detection_count': len(detections)
                }
                all_results.append(image_result)
                total_detections += len(detections)
                
                logger.info(f"   ✅ 檢測到 {len(detections)} 個有效目標")
                
                # 保存視覺化結果
                if save_detection_images and detections:
                    save_visualization(image_file, detections, output_path_obj)
                
            except Exception as e:
                logger.error(f"   ❌ 處理失敗: {e}")
                continue
        
        # 保存統計結果
        save_results(all_results, output_path_obj)
        
        # 顯示總結
        success_count = len(all_results)
        logger.info(f"✅ 基礎推理完成!")
        logger.info(f"📊 處理圖像: {len(image_files)}")
        logger.info(f"📊 成功處理: {success_count}")
        logger.info(f"📊 總檢測數: {total_detections}")
        logger.info(f"📊 平均每圖: {total_detections/max(success_count,1):.1f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 基礎推理失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def apply_intelligent_filtering(detections):
    """應用智能過濾邏輯"""
    if not detections:
        return detections
    
    # 簡化的智能過濾實現
    filtered_detections = []
    
    for detection in detections:
        bbox = detection['bbox']
        x1, y1, x2, y2 = bbox
        width = x2 - x1
        height = y2 - y1
        area = width * height
        
        # 基本尺寸過濾
        if area < 100:  # 過小的檢測
            continue
        
        # 長寬比過濾 (針對linear_crack)
        if detection['class_name'] == 'linear_crack':
            aspect_ratio = min(width, height) / max(width, height)
            if aspect_ratio > linear_aspect_ratio_threshold:
                continue  # 不夠細長
        
        filtered_detections.append(detection)
    
    return filtered_detections

def save_visualization(image_file, detections, output_path_obj):
    """保存視覺化結果"""
    try:
        import cv2
        
        # 載入圖像
        image = cv2.imread(str(image_file))
        if image is None:
            return
        
        # 繪製檢測框
        for detection in detections:
            bbox = detection['bbox']
            x1, y1, x2, y2 = map(int, bbox)
            confidence = detection['confidence']
            class_name = detection['class_name']
            class_id = detection['class_id']
            
            # 獲取類別顏色
            color = class_configs.get(class_id, [None, None, [0, 255, 0]])[2]
            color = tuple(reversed(color))  # BGR格式
            
            # 繪製矩形框
            cv2.rectangle(image, (x1, y1), (x2, y2), color, line_thickness)
            
            # 添加標籤
            display_name = class_configs.get(class_id, [class_name, class_name])[1]
            label = f"{display_name}: {confidence:.2f}"
            
            # 計算文字背景
            (text_width, text_height), _ = cv2.getTextSize(
                label, cv2.FONT_HERSHEY_SIMPLEX, font_size * 0.5, font_thickness
            )
            
            # 繪製文字背景
            cv2.rectangle(image, (x1, y1 - text_height - 10), 
                         (x1 + text_width, y1), color, -1)
            
            # 繪製文字
            cv2.putText(image, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, font_size * 0.5, 
                       (255, 255, 255), font_thickness)
        
        # 保存結果圖像
        output_image_path = output_path_obj / f"result_{image_file.name}"
        cv2.imwrite(str(output_image_path), image, 
                   [cv2.IMWRITE_JPEG_QUALITY, output_image_quality])
        
    except Exception as e:
        print(f"   ⚠️ 視覺化保存失敗: {e}")

def save_results(all_results, output_path_obj):
    """保存結果統計"""
    try:
        # 保存JSON結果
        if enable_json_output:
            json_path = output_path_obj / "detection_results.json"
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, indent=2, ensure_ascii=False)
        
        # 保存CSV統計
        if enable_csv_output:
            csv_path = output_path_obj / "detection_summary.csv"
            with open(csv_path, 'w', encoding='utf-8') as f:
                f.write("圖像文件,檢測數量,類別統計\n")
                for result in all_results:
                    class_counts = {}
                    for det in result['detections']:
                        class_name = det['class_name']
                        class_counts[class_name] = class_counts.get(class_name, 0) + 1
                    
                    class_stats = '; '.join([f"{k}:{v}" for k, v in class_counts.items()])
                    f.write(f"{result['image_file']},{result['detection_count']},{class_stats}\n")
        
    except Exception as e:
        print(f"⚠️ 保存統計失敗: {e}")

def smart_yolo_inference(yolo_system, phase4_components):
    """智能YOLO推理 (整合Phase 4功能)"""
    logger = setup_logging()
    logger.info("🧠 使用智能YOLO推理模式")
    
    try:
        create_inference_system, UnifiedConfig, ClassConfig = yolo_system
        
        # 轉換類別配置
        classes = {}
        for class_id, config_list in class_configs.items():
            name, display_name, color, conf_thresh, sahi_thresh, enabled = config_list
            classes[class_id] = ClassConfig(
                name=name,
                display_name=display_name,
                color=color,
                confidence=conf_thresh,
                sahi_confidence=sahi_thresh,
                enabled=enabled
            )
        
        # 創建配置
        config = UnifiedConfig()
        config.model.model_path = segmentation_model_path
        config.processing.slice.enabled = enable_sahi
        config.visualization.enable_three_view = enable_three_view_output
        config.processing.filtering.enabled = enable_intelligent_filtering
        config.classes = classes
        config.output_path = output_path
        
        # 智能優化配置 (如果有Phase 4組件)
        if 'model_manager' in phase4_components:
            logger.info("🧠 應用智能模型選擇優化")
            # 這裡可以添加智能模型選擇邏輯
        
        # 執行推理
        input_path_obj = Path(input_path)
        
        with create_inference_system(config=config) as inference_system:
            if input_path_obj.is_file():
                # 單張圖像處理
                logger.info(f"📸 智能處理單張圖像: {input_path_obj.name}")
                result = inference_system.process_single_image(
                    str(input_path_obj),
                    enable_visualization=save_detection_images,
                    enable_statistics=enable_csv_output
                )
                
                if result['success']:
                    logger.info(f"✅ 智能處理完成，檢測到 {len(result['detections'])} 個目標")
                    if result.get('visualization_paths'):
                        logger.info(f"🎨 視覺化結果: {result['visualization_paths']}")
                else:
                    logger.error(f"❌ 智能處理失敗: {result['error']}")
            
            elif input_path_obj.is_dir():
                # 批量目錄處理
                logger.info(f"📁 智能批量處理目錄: {input_path}")
                summary = inference_system.process_directory(str(input_path_obj))
                
                if 'success_rate' in summary:
                    logger.info(f"✅ 智能批量處理完成!")
                    logger.info(f"📊 成功率: {summary['success_rate']:.1f}%")
                    logger.info(f"📊 總圖像數: {summary['total_images']}")
                    logger.info(f"📊 總檢測數: {summary['total_detections']}")
                    logger.info(f"📊 平均每圖檢測數: {summary['average_detections_per_image']:.1f}")
                    
                    # Phase 4 增強信息
                    if phase4_components:
                        logger.info(f"🧠 智能功能已啟用: {list(phase4_components.keys())}")
                else:
                    logger.error(f"❌ 智能批量處理失敗: {summary.get('error', '未知錯誤')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 智能推理失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    logger = setup_logging()
    logger.info(f"🏆 啟動統一YOLO推理系統 - 修復版 (模式: {RUNNING_MODE.value})")
    
    # 檢查路徑
    if not Path(segmentation_model_path).exists():
        logger.error(f"❌ 模型文件不存在: {segmentation_model_path}")
        print("請確認模型路徑是否正確")
        return
    
    if not Path(input_path).exists():
        logger.error(f"❌ 輸入路徑不存在: {input_path}")
        print("請確認輸入路徑是否正確")
        return
    
    # 嘗試導入系統
    logger.info("🔍 檢查系統組件...")
    
    yolo_available, yolo_system = try_import_yolo_system()
    phase4_available, phase4_components = try_import_phase4_components()
    
    # 選擇執行模式
    if yolo_available and (RUNNING_MODE == RunningMode.SMART):
        logger.info("🧠 使用智能推理模式")
        success = smart_yolo_inference(yolo_system, phase4_components)
    else:
        logger.info("🔧 使用基礎推理模式")
        success = basic_yolo_inference()
    
    if success:
        print(f"\n🎉 推理完成! 結果已保存到: {output_path}")
        print(f"💡 如需啟用智能功能，請將 RUNNING_MODE 改為 RunningMode.SMART")
    else:
        print(f"\n❌ 推理失敗，請檢查錯誤信息")

if __name__ == "__main__":
    main()