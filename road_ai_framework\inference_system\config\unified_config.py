#!/usr/bin/env python3
"""
🔧 統一配置系統
整合原有的10+個配置類為層次化結構，提供YAML和Python API
"""

import os
import yaml
import json
from pathlib import Path
from dataclasses import dataclass, field, asdict
from typing import Dict, List, Optional, Union, Any
from enum import Enum


class FusionStrategy(Enum):
    """融合策略枚舉 - 簡化為三種核心策略"""
    STANDARD_NMS = "standard_nms"      # 標準NMS
    LARGEST_OBJECT = "largest_object"  # 保留最大物件
    NO_FUSION = "no_fusion"           # 不執行融合


@dataclass
class ModelConfig:
    """模型配置"""
    detection_model_path: str = ""
    segmentation_model_path: str = ""
    device: str = "cuda"
    half_precision: bool = True
    img_size: int = 640


@dataclass
class SliceConfig:
    """切片配置"""
    enabled: bool = False
    height: int = 640
    width: int = 640
    overlap_ratio: float = 0.2
    adaptive_overlap: bool = True
    min_slice_size: int = 320


@dataclass
class SAHIConfig:
    """SAHI專用配置 - 獨立的重疊合併設定"""
    enable_sahi_overlap_merge: bool = False    # SAHI專用重疊合併開關
    sahi_merge_iou_threshold: float = 0.1      # SAHI合併IoU閾值
    enable_mask_iou_calculation: bool = True   # 啟用mask IoU計算(優先於bbox IoU)
    sahi_merge_confidence_strategy: str = "max"  # 合併置信度策略: "max", "avg", "weighted_avg"


@dataclass
class FusionConfig:
    """融合配置 - 簡化為三種核心融合策略"""
    strategy: FusionStrategy = FusionStrategy.LARGEST_OBJECT
    iou_threshold: float = 0.4
    confidence_threshold: float = 0.1


@dataclass
class FilteringConfig:
    """智能過濾配置"""
    enabled: bool = False
    linear_aspect_ratio_threshold: float = 0.8
    area_ratio_threshold: float = 0.4
    enable_detection_merge: bool = False
    # 🤖 新增Joint優先規則
    enable_joint_priority_over_crack: bool = False
    joint_crack_overlap_threshold: float = 0.1
    # 🎯 空間分離檢測
    spatial_separation_factor: float = 1.5
    # 🐊 Alligator_crack vs Linear_crack 過濾
    alligator_contains_linear_threshold: float = 0.5
    enable_area_based_priority: bool = True  # 啟用基於Box大小的優先判斷


@dataclass
class ClassConfig:
    """類別配置"""
    name: str
    display_name: str
    color: List[int]  # RGB
    confidence: float
    sahi_confidence: float
    enabled: bool = True
    min_area: int = 0
    max_area: int = 999999


@dataclass
class VisualizationConfig:
    """視覺化配置"""
    save_visualizations: bool = True
    enable_three_view: bool = True
    enable_prediction_only: bool = True  # 單純預測結果輸出
    layout: str = "horizontal"  # "horizontal" or "vertical"
    spacing: int = 10
    font_size: float = 1.0
    font_thickness: int = 2
    font_scale: float = 1.0
    font_path: str = ""
    line_thickness: int = 2
    fill_alpha: float = 0.3
    # "unified", "intelligent", "simple", "outline_only"
    mask_render_mode: str = "outline_only"
    output_image_quality: int = 95
    # 🖼️ 儲存時的圖像縮放比例（用於三視圖和單張預測儲存）
    output_image_scale: float = 0.3


@dataclass
class ROIConfig:
    """ROI檢測配置"""
    enabled: bool = False
    margins: Dict[str, float] = field(default_factory=lambda: {
        'top': 1.0, 'bottom': 1.0, 'left': 1.0, 'right': 1.0
    })  # 0.0-5.0範圍，5是圖像中心
    inference_mode: str = "normal"  # "normal" or "sahi"
    enable_preview: bool = True
    preview_colors: Dict[str, List[int]] = field(default_factory=lambda: {
        'top': [255, 0, 0],     # 紅色
        'bottom': [0, 255, 0],  # 綠色
        'left': [0, 0, 255],    # 藍色
        'right': [255, 255, 0]  # 黃色
    })


@dataclass
class ImageProcessingConfig:
    """圖像處理配置"""
    input_scale: float = 0.3  # 輸入圖像縮放比例
    enable_resize: bool = True  # 啟用圖像縮放


@dataclass
class OutputConfig:
    """輸出配置"""
    save_predictions: bool = True
    save_statistics: bool = True
    enable_labelme_output: bool = True
    labelme_output_dirname: str = "labelme_annotations"
    labelme_include_points: bool = True
    labelme_include_center_points: bool = True  # 🆕 專門控制center point
    labelme_point_radius: int = 3
    labelme_export_mode: str = "auto"  # "seg", "det", "auto"
    include_image_data: bool = False  # 是否在JSON中包含圖像base64數據
    enable_csv_output: bool = True
    csv_output_dirname: str = "reports"
    images_dirname: str = "images"  # 圖像保存目錄
    
    # 🆕 空預測圖像跳過功能
    skip_empty_prediction_images: bool = False  # 跳過沒有檢測結果的圖像生成
    skip_empty_labelme_export: bool = False     # 跳過沒有檢測結果的LabelMe JSON導出
    
    # 🎯 LabelMe點數量控制 (NEW) - 平衡精度和性能
    labelme_max_polygon_points: int = 50        # 多邊形最大點數 (10-200)
    labelme_min_polygon_points: int = 8         # 多邊形最小點數 (3-50)
    labelme_simplify_epsilon: float = 2.0       # 輪廓簡化參數 (0.5-10.0)
    labelme_adaptive_simplify: bool = True      # 啟用自適應簡化
    labelme_large_object_threshold: int = 10000 # 大目標面積闾值(pixels²)
    labelme_small_object_max_points: int = 20   # 小目標最大點數
    labelme_large_object_max_points: int = 100  # 大目標最大點數
    labelme_enable_point_optimization: bool = True  # 啟用點優化
    labelme_optimization_mode: str = "balanced" # 優化模式: fast/balanced/accurate


@dataclass  
class CleanupConfig:
    """🧹 圖像清理配置"""
    enabled: bool = False                           # 是否啟用清理功能
    strategy: str = "by_time"                       # 清理策略: by_time/by_count/by_size/combined
    
    # 時間策略配置
    max_age_days: int = 7                          # 保留天數
    
    # 數量策略配置  
    max_files: int = 100                           # 最大文件數量
    
    # 大小策略配置
    max_size_mb: int = 1000                        # 最大目錄大小MB
    
    # 清理時機
    cleanup_on_start: bool = True                  # 程序啟動時清理
    cleanup_on_finish: bool = False                # 程序結束時清理
    cleanup_interval_hours: int = 24               # 自動清理間隔(小時)
    
    # 安全設定
    target_directories: List[str] = field(default_factory=lambda: [
        "images", "roi_preview", "temp"
    ])
    file_extensions: List[str] = field(default_factory=lambda: [
        ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"
    ])
    protected_patterns: List[str] = field(default_factory=lambda: [
        "*_original.*", "*_master.*", "README.*", "*.md"
    ])
    
    # 日誌設定
    log_cleanup_operations: bool = True            # 記錄清理操作
    dry_run: bool = False                         # 乾運行模式


@dataclass
class ProcessingConfig:
    """處理配置"""
    slice: SliceConfig = field(default_factory=SliceConfig)
    sahi: SAHIConfig = field(default_factory=SAHIConfig)      # 新增：SAHI專用配置
    fusion: FusionConfig = field(default_factory=FusionConfig)
    filtering: FilteringConfig = field(default_factory=FilteringConfig)
    roi: ROIConfig = field(default_factory=ROIConfig)
    image_processing: ImageProcessingConfig = field(
        default_factory=ImageProcessingConfig)
    max_det: int = 1000
    enable_roi_preview: bool = True
    enable_overall_inference: bool = True
    enable_adjacent_merge: bool = True
    adjacent_distance_threshold: float = 30.0
    # 🔗 物件連接功能配置 (Stage 3)
    enable_object_connection: bool = False
    connection_distance_threshold: float = 30.0
    connection_same_class_only: bool = True
    connection_confidence_weight: float = 0.3
    enable_crack_line_connection: bool = False


@dataclass
class UnifiedConfig:
    """
    統一配置類 - 整合所有配置到層次化結構
    替代原有的10+個分散配置類
    """

    # 核心配置
    model: ModelConfig = field(default_factory=ModelConfig)
    processing: ProcessingConfig = field(default_factory=ProcessingConfig)
    visualization: VisualizationConfig = field(
        default_factory=VisualizationConfig)
    output: OutputConfig = field(default_factory=OutputConfig)
    cleanup: CleanupConfig = field(default_factory=CleanupConfig)  # 🧹 清理配置

    # 類別配置
    classes: Dict[int, ClassConfig] = field(default_factory=dict)

    # 路徑配置
    input_path: str = ""
    output_path: str = ""

    # 排除配置
    excluded_class_ids: List[int] = field(default_factory=list)
    excluded_class_names: List[str] = field(default_factory=list)
    included_class_ids: Optional[List[int]] = None

    @classmethod
    def from_yaml(cls, yaml_path: str) -> 'UnifiedConfig':
        """
        從YAML文件載入配置

        Args:
            yaml_path: YAML配置文件路徑

        Returns:
            UnifiedConfig: 配置實例
        """
        yaml_path = Path(yaml_path)
        if not yaml_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {yaml_path}")

        with open(yaml_path, 'r', encoding='utf-8') as f:
            config_dict = yaml.safe_load(f)

        return cls.from_dict(config_dict)

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'UnifiedConfig':
        """
        從字典載入配置

        Args:
            config_dict: 配置字典

        Returns:
            UnifiedConfig: 配置實例
        """
        # 創建子配置對象
        model_config = ModelConfig(**config_dict.get('model', {}))

        # 處理處理配置
        processing_dict = config_dict.get('processing', {})
        slice_config = SliceConfig(**processing_dict.get('slice', {}))
        sahi_config = SAHIConfig(**processing_dict.get('sahi', {}))        # 新增：SAHI配置
        fusion_config = FusionConfig(**processing_dict.get('fusion', {}))
        filtering_config = FilteringConfig(
            **processing_dict.get('filtering', {}))
        roi_config = ROIConfig(**processing_dict.get('roi', {}))
        image_processing_config = ImageProcessingConfig(
            **processing_dict.get('image_processing', {}))

        processing_config = ProcessingConfig(
            slice=slice_config,
            sahi=sahi_config,
            fusion=fusion_config,
            filtering=filtering_config,
            roi=roi_config,
            image_processing=image_processing_config,
            **{k: v for k, v in processing_dict.items()
               if k not in ['slice', 'sahi', 'fusion', 'filtering', 'roi', 'image_processing']}
        )

        # 視覺化配置
        visualization_config = VisualizationConfig(
            **config_dict.get('visualization', {}))

        # 輸出配置
        output_config = OutputConfig(**config_dict.get('output', {}))

        # 🧹 清理配置
        cleanup_config = CleanupConfig(**config_dict.get('cleanup', {}))

        # 類別配置
        classes_dict = {}
        for class_id, class_data in config_dict.get('classes', {}).items():
            if isinstance(class_data, list):
                # 兼容舊格式: [name, display_name, color, conf, sahi_conf, enabled]
                classes_dict[int(class_id)] = ClassConfig(
                    name=class_data[0],
                    display_name=class_data[1],
                    color=class_data[2],
                    confidence=class_data[3],
                    sahi_confidence=class_data[4],
                    enabled=class_data[5] if len(class_data) > 5 else True
                )
            else:
                # 新格式: 完整配置對象
                classes_dict[int(class_id)] = ClassConfig(**class_data)

        # 創建主配置對象
        return cls(
            model=model_config,
            processing=processing_config,
            visualization=visualization_config,
            output=output_config,
            cleanup=cleanup_config,
            classes=classes_dict,
            input_path=config_dict.get('input_path', ''),
            output_path=config_dict.get('output_path', ''),
            excluded_class_ids=config_dict.get('excluded_class_ids', []),
            excluded_class_names=config_dict.get('excluded_class_names', []),
            included_class_ids=config_dict.get('included_class_ids')
        )

    def to_yaml(self, yaml_path: str):
        """
        導出配置到YAML文件

        Args:
            yaml_path: 輸出YAML文件路徑
        """
        config_dict = asdict(self)

        # 轉換枚舉為字符串
        if 'processing' in config_dict and 'fusion' in config_dict['processing']:
            fusion_config = config_dict['processing']['fusion']
            if 'strategy' in fusion_config:
                fusion_config['strategy'] = fusion_config['strategy'].value if hasattr(
                    fusion_config['strategy'], 'value') else fusion_config['strategy']

        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False,
                      allow_unicode=True)

    def get_class_config(self, class_id: int) -> Optional[ClassConfig]:
        """獲取指定類別的配置"""
        return self.classes.get(class_id)

    def add_class_config(self, class_id: int, config: ClassConfig):
        """添加類別配置"""
        self.classes[class_id] = config

    def get_enabled_classes(self) -> Dict[int, ClassConfig]:
        """獲取所有啟用的類別配置"""
        return {cid: config for cid, config in self.classes.items()
                if config.enabled and cid not in self.excluded_class_ids
                and config.name not in self.excluded_class_names}

    def validate(self) -> List[str]:
        """
        驗證配置的完整性和正確性

        Returns:
            List[str]: 驗證錯誤列表，空列表表示驗證通過
        """
        errors = []

        # 驗證模型路徑
        if self.model.segmentation_model_path and not Path(self.model.segmentation_model_path).exists():
            errors.append(f"分割模型文件不存在: {self.model.segmentation_model_path}")

        if self.model.detection_model_path and not Path(self.model.detection_model_path).exists():
            errors.append(f"檢測模型文件不存在: {self.model.detection_model_path}")

        # 驗證路徑
        if self.input_path and not Path(self.input_path).exists():
            errors.append(f"輸入路徑不存在: {self.input_path}")

        # 驗證類別配置
        if not self.classes:
            errors.append("未配置任何類別")

        for class_id, class_config in self.classes.items():
            if class_config.confidence < 0 or class_config.confidence > 1:
                errors.append(
                    f"類別{class_id}置信度超出範圍[0,1]: {class_config.confidence}")

            if len(class_config.color) != 3:
                errors.append(f"類別{class_id}顏色格式錯誤，需要RGB三元組")

        # 驗證融合配置
        if self.processing.fusion.iou_threshold < 0 or self.processing.fusion.iou_threshold > 1:
            errors.append(
                f"融合IoU閾值超出範圍[0,1]: {self.processing.fusion.iou_threshold}")

        return errors


class ConfigManager:
    """
    配置管理器 - 提供配置載入、驗證、更新等功能
    """

    def __init__(self, config: Optional[UnifiedConfig] = None):
        self.config = config or UnifiedConfig()
        self._config_history = []  # 配置變更歷史

    def load_from_yaml(self, yaml_path: str) -> UnifiedConfig:
        """載入YAML配置"""
        self.config = UnifiedConfig.from_yaml(yaml_path)
        self._add_to_history("load_yaml", yaml_path)
        return self.config

    def load_from_dict(self, config_dict: Dict[str, Any]) -> UnifiedConfig:
        """載入字典配置"""
        self.config = UnifiedConfig.from_dict(config_dict)
        self._add_to_history("load_dict", "from_dict")
        return self.config

    def update_config(self, updates: Dict[str, Any]):
        """動態更新配置"""
        # 實現深度字典合併
        self._deep_update(asdict(self.config), updates)
        self.config = UnifiedConfig.from_dict(asdict(self.config))
        self._add_to_history("update", updates)

    def save_to_yaml(self, yaml_path: str):
        """保存配置到YAML"""
        self.config.to_yaml(yaml_path)
        self._add_to_history("save_yaml", yaml_path)

    def validate_config(self) -> bool:
        """驗證配置"""
        errors = self.config.validate()
        if errors:
            for error in errors:
                print(f"❌ 配置錯誤: {error}")
            return False
        return True

    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value

    def _add_to_history(self, action: str, details: Any):
        """添加配置變更歷史"""
        from datetime import datetime
        self._config_history.append({
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'details': str(details)
        })

        # 保持歷史記錄在合理大小
        if len(self._config_history) > 100:
            self._config_history = self._config_history[-50:]

    def get_config_history(self) -> List[Dict[str, Any]]:
        """獲取配置變更歷史"""
        return self._config_history.copy()


def create_default_config() -> UnifiedConfig:
    """創建默認配置"""
    config = UnifiedConfig()

    # 設置默認類別配置
    default_classes = {
        2: ClassConfig("linear_crack", "Linear Crack", [0, 0, 255], 0.2, 0.08, True),
        3: ClassConfig("Alligator_crack", "Alligator Crack", [255, 255, 0], 0.3, 0.15, True),
        4: ClassConfig("potholes", "Potholes", [255, 0, 255], 0.4, 0.2, True),
        5: ClassConfig("patch_square", "Square Patch", [255, 165, 0], 0.4, 0.2, True),
        6: ClassConfig("patch_linear", "Linear Patch", [0, 255, 255], 0.4, 0.2, True),
        7: ClassConfig("joint", "Joint", [255, 192, 203], 0.4, 0.2, True),
        8: ClassConfig("dirt", "Dirt", [139, 69, 19], 0.4, 0.2, False),
        9: ClassConfig("manhole", "Manhole", [128, 0, 128], 0.4, 0.2, True),
        10: ClassConfig("lane_line_linear", "Lane Line", [0, 255, 0], 0.4, 0.2, True),
        11: ClassConfig("expansion_joint", "Expansion Joint", [255, 20, 147], 0.4, 0.2, True)
    }

    config.classes = default_classes
    return config


def load_config_from_legacy(legacy_config_manager) -> UnifiedConfig:
    """
    從舊版配置管理器遷移到統一配置

    Args:
        legacy_config_manager: 舊版UnifiedYOLOConfigManager實例

    Returns:
        UnifiedConfig: 新的統一配置
    """
    config = UnifiedConfig()

    # 遷移模型配置
    if hasattr(legacy_config_manager, 'segmentation_model_path'):
        config.model.segmentation_model_path = legacy_config_manager.segmentation_model_path

    # 遷移類別配置
    if hasattr(legacy_config_manager, 'classes'):
        for class_id, legacy_class in legacy_config_manager.classes.items():
            config.classes[class_id] = ClassConfig(
                name=legacy_class.name,
                display_name=legacy_class.display_name,
                color=legacy_class.color,
                confidence=legacy_class.confidence,
                sahi_confidence=legacy_class.sahi_confidence,
                enabled=legacy_class.enabled
            )

    return config
