#!/usr/bin/env python3
"""
🏆 統一YOLO推理系統 - 終極整合版
完美整合原有功能 + Phase 4 智能化升級

🌟 功能特色:
✅ 完全兼容原有 run_unified_yolo_new.py 的所有參數設定
✅ 可選啟用 Phase 4 智能化功能
✅ 漸進式升級路徑，從基礎到智能化
✅ 企業級部署就緒，支援多種運行模式

🎯 運行模式:
1. 🔧 經典模式: 保持原有功能，零學習成本
2. 🧠 智能模式: 啟用 Phase 4 AI 驅動功能  
3. 🏢 企業模式: 多租戶 + 負載均衡 + 分散式
4. 🌐 全能模式: 所有功能全開，世界級平台
"""

from pathlib import Path
import sys
import logging
import asyncio
import time
from typing import Dict, List, Optional, Any, Union
from enum import Enum

# 確保導入路徑正確 - 指向父目錄（road_ai_framework根目錄）
current_dir = Path(__file__).parent  # scripts/
project_root = current_dir.parent    # road_ai_framework/
sys.path.insert(0, str(project_root))

# ============================================================================
# 📋 運行模式選擇
# ============================================================================


class RunningMode(Enum):
    """運行模式枚舉"""
    CLASSIC = "classic"       # 經典模式 - 原有功能
    INTELLIGENT = "intelligent"  # 智能模式 - AI驅動
    ENTERPRISE = "enterprise"    # 企業模式 - 多租戶+負載均衡
    ULTIMATE = "ultimate"        # 全能模式 - 所有功能


# 🎯 選擇運行模式 - 只需修改這一行!
# 可選: CLASSIC, INTELLIGENT, ENTERPRISE, ULTIMATE
RUNNING_MODE = RunningMode.CLASSIC

print(f"🚀 統一YOLO推理系統 - 終極整合版")
print(f"🎯 當前運行模式: {RUNNING_MODE.value.upper()}")
print("=" * 60)

# ============================================================================
# 📋 原有參數設定區域 - 完全兼容
# ============================================================================

# 🤖 模型設定
segmentation_model_path = r"D:\4_road_crack\best_0728.pt"  # 分割模型路徑
detection_model_path = None                              # 檢測模型路徑（可選）

# 📁 路徑設定
input_path = r"D:\image\road_crack\test_600_resize"                      # 輸入圖像路徑/目錄
output_path = r"D:\image\road_crack\test_600_resize_11"                # 輸出結果目錄

# 🧩 SAHI切片推理設定
enable_sahi = True                                     # 啟用SAHI大圖切片
sahi_slice_height = 1280                                 # 切片高度
sahi_slice_width = 1280                                  # 切片寬度
sahi_overlap_ratio = 0.5                               # 重疊比例

# 🔀 融合策略設定
# 融合策略: standard_nms/largest_object/no_fusion
fusion_strategy = "largest_object"
# 融合IoU閾值
fusion_iou_threshold = 0.4
fusion_confidence_threshold = 0.1                      # 融合置信度閾值

# 🎯 SAHI專用重疊合併設定 (獨立功能)
enable_sahi_overlap_merge = True                       # SAHI重疊合併獨立開關
sahi_merge_iou_threshold = 0.1                         # SAHI合併IoU閾值
# 啟用mask IoU計算(優先於box IoU)
enable_mask_iou_calculation = True
# 合併置信度策略: max/avg/weighted_avg
sahi_merge_confidence_strategy = "max"

# 🧠 智能過濾設定
enable_intelligent_filtering = True                   # 啟用智能過濾功能
# 🐊 Alligator_crack vs Linear_crack 重疊檢測
alligator_contains_linear_threshold = 0.2             # 重疊檢測IoU閾值（有重疊時大box優先）
# 📏 長寬比過濾（獨立功能）
enable_aspect_ratio_filter = False                    # 啟用長寬比獨立過濾
linear_aspect_ratio_threshold = 0.3                   # 長寬比閾值（小於此值認為是線狀）
# 📐 面積比過濾（獨立功能）
enable_area_ratio_filter = False                      # 啟用面積比獨立過濾
area_ratio_threshold = 0.1                            # 面積比閾值（小於此值認為面積差異大）

# 🤖 統一的Joint優先過濾規則 - 合併原Step2和新增規則
# Joint與Linear_crack重疊時優先保留Joint
enable_joint_priority_over_crack = True
# Joint-Crack重疊判斷閾值（提高=更嚴格，減少誤過濾）
joint_crack_overlap_threshold = 0.3
# 🎯 空間分離係數：中心距離超過最大邊長的此倍數時認為是分離的（提高=更保守）
spatial_separation_factor = 2.0

# 🔗 物件連結功能設定
enable_object_connection = True                        # 啟用物件連接功能
# 連接距離閾值(pixels) - 恢復原始設計值
connection_distance_threshold = 30.0
connection_same_class_only = True                      # 僅連接相同類別物件
connection_confidence_weight = 0.01                     # 連接時置信度權重
# 啟用裂縫線段連接（針對linear_crack類別）
enable_crack_line_connection = True

# 🎨 視覺化設定
enable_three_view_output = False                       # 啟用三視圖輸出
enable_prediction_only_output = True                   # 啟用單純預測結果輸出
three_view_layout = "horizontal"                       # 佈局: horizontal/vertical
font_size = 1.0                                        # 字體大小倍數
font_thickness = 2                                     # 字體粗細
line_thickness = 2                                     # 邊框線條粗細
transparency = 0.3                                     # 遮罩透明度
output_image_quality = 85                              # 輸出圖像質量(1-100)

# 🖼️ 圖像處理設定
# 儲存圖像縮放比例 (用於三視圖和單張預測儲存)
output_image_scale = 0.3

# 🔄 中斷恢復功能設定 (NEW)
enable_resume_from_checkpoint = True                   # 啟用中斷恢復功能
# 檢查點文件名稱 (相對於output_path)
resume_checkpoint_file = "resume_checkpoint.json"

# 🎯 ROI檢測設定 (0到5範圍，5是圖像中心)
enable_roi_detection = True                           # ROI功能開關
roi_margins = {
    'top': 2.5,     # 上邊界 (0.0-5.0，0是邊緣，5是中心)
    'bottom': 1.5,  # 下邊界
    'left': 0.5,    # 左邊界
    'right': 0.5    # 右邊界
}
# ROI推理模式: "normal" 或 "sahi" - 自動根據enable_sahi調整
roi_inference_mode = "sahi" if enable_sahi else "normal"  # 🔧 自動調整邏輯
enable_roi_preview = True                              # 啟用ROI預覽功能

# 🏷️ 類別配置 - 每個類別獨立設定
# 格式: [名稱, 顯示名, RGB顏色, 置信度閾值, SAHI閾值, 是否啟用]
class_configs = {
    0: ["Alligator_crack", "Alligator Crack", [0, 0, 255], 0.7, 0.1, True],
    1: ["deformation", "Deformation", [100, 100, 100], 0.1, 0.1, True],
    2: ["dirt", "Dirt", [110, 110, 110], 0.1, 0.1, False],
    3: ["expansion_joint", "Expansion Joint", [120, 120, 120], 0.1, 0.1, False],
    4: ["joint", "Joint", [130, 130, 130], 0.1, 0.1, False],
    5: ["lane_line_linear", "Lane Line Linear", [140, 140, 140], 0.1, 0.1, False],
    6: ["linear_crack", "Linear Crack", [0, 255, 0], 0.3, 0.1, True],
    7: ["manhole", "Manhole", [255, 0, 255], 0.1, 0.1, True],
    8: ["patch", "Patch", [255, 0, 0], 0.4, 0.1, True],
    9: ["patch_square", "Square Patch", [160, 160, 160], 0.1, 0.1, True],
    10: ["potholes", "Potholes", [0, 255, 255], 0.2, 0.1, True],
    11: ["rutting", "Rutting", [255, 255, 0], 0.1, 0.1, True]
}

# 📊 輸出設定
enable_csv_output = True                               # 啟用CSV統計輸出
enable_json_output = True                              # 啟用JSON結果輸出
save_confidence_distribution = True                    # 保存置信度分佈
save_detection_images = True                           # 保存檢測結果圖像

# 🚫 空預測圖像跳過設定 (NEW)
skip_empty_prediction_images = True                   # 跳過沒有檢測結果的圖像生成
skip_empty_labelme_export = True                      # 跳過沒有檢測結果的LabelMe JSON導出

# 🏷️ LabelMe標註輸出設定 (NEW)
enable_labelme_export = True                           # 啟用LabelMe格式標註輸出
# 包含point調整功能 - 設為False不輸出center point
labelme_include_points = False
# 專門控制center point的開關 (NEW)
labelme_include_center_points = False
labelme_output_dirname = "labelme_annotations"        # LabelMe標註輸出目錄名
labelme_point_radius = 3                               # 點標記半徑
# 輸出模式: "seg", "det", "auto" (根據模型自動選擇)
labelme_export_mode = "auto"

# 🎯 LabelMe點數量控制 (NEW) - 平衡精度和性能
# 輪廓點數量控制：控制分割mask轉換為多邊形時的點數量
labelme_max_polygon_points = 50                       # 多邊形最大點數 (10-200, 默認50)
labelme_min_polygon_points = 20                        # 多邊形最小點數 (3-50, 默認8) 
labelme_simplify_epsilon = 0.5                        # 輪廓簡化參數 (0.5-10.0, 默認2.0)
# 自適應簡化：根據目標大小調整簡化程度
labelme_adaptive_simplify = False                      # 啟用自適應簡化
labelme_large_object_threshold = 10000                # 大目標面積閾值(pixels²)
labelme_small_object_max_points = 20                  # 小目標最大點數
labelme_large_object_max_points = 100                 # 大目標最大點數
# 性能優化設定
labelme_enable_point_optimization = False              # 啟用點優化
labelme_optimization_mode = "balanced"                # 優化模式: fast/balanced/accurate

# 📦 後處理功能設定 (NEW)
enable_post_processing = True                          # 啟用後處理功能
# 🚚 圖像搬移功能：將input中與labelme JSON同名的JPG搬移到指定目錄
enable_matched_image_move = True                       # 啟用匹配圖像搬移功能
matched_image_move_target = "labelme_annotations"           # 搬移目標目錄名（相對於output_path）
# 搬移模式: "move", "copy", "link" 
matched_image_move_mode = "copy"                       # move=搬移, copy=複製, link=建立軟連結
# 是否保持原始目錄結構
matched_image_keep_structure = True                    # 保持input中的子目錄結構
# 是否覆蓋目標目錄中的同名檔案
matched_image_overwrite = True                         # 覆蓋目標檔案
# 支援的圖像格式
matched_image_extensions = [".jpg", ".jpeg", ".png", ".bmp", ".tiff"]  # 支援的圖像格式

# 🔧 系統設定
max_workers = 8                                        # 並行處理數量
enable_cache = True                                    # 啟用緩存
# 日誌級別: DEBUG/INFO/WARNING/ERROR
log_level = "INFO"

# 📊 即時報告設定 (NEW)
enable_real_time_reporting = True                     # 啟用即時統計報告更新
report_update_interval = 10                           # 報告更新間隔(圖像數量)
show_progress_bar = True                               # 顯示進度條

# 🧹 圖像清理設定 (NEW) - 已移除，改用記憶體清理功能
# enable_image_cleanup = True                           # 啟用圖像清理功能
# cleanup_strategy = "by_time"                          # 清理策略: by_time/by_count/by_size/combined
# cleanup_max_age_days = 7                              # 保留天數 (by_time策略)
# cleanup_max_files = 100                               # 最大文件數量 (by_count策略)
# cleanup_max_size_mb = 1000                            # 最大目錄大小MB (by_size策略)
# cleanup_on_start = True                               # 程序啟動時清理
# cleanup_on_finish = False                             # 程序結束時清理
# cleanup_interval_hours = 24                          # 自動清理間隔(小時)
# cleanup_dry_run = False                              # 乾運行模式(僅記錄不刪除)

# 🧠 記憶體清理設定 - 🔥 強力清理配置
enable_memory_cleanup = True                          # 啟用記憶體清理功能

# 🔥 激進清理模式設定 (強烈推薦!)
memory_aggressive_mode = True                         # 啟用激進清理模式
# 觸發模式: time_based/usage_based/count_based/hybrid/aggressive
memory_cleanup_trigger = "aggressive" if memory_aggressive_mode else "hybrid"

# 🕐 清理頻率設定 (激進模式)
memory_cleanup_interval = 10.0 if memory_aggressive_mode else 30.0      # 清理間隔(秒) - 激進: 10秒
memory_cleanup_after_images = 3 if memory_aggressive_mode else 10       # 每N張圖像後清理 - 激進: 3張

# 🎯 觸發閾值 (激進模式降低閾值)
memory_cpu_threshold = 60.0 if memory_aggressive_mode else 85.0         # CPU記憶體觸發閾值(%) - 激進: 60%
memory_gpu_threshold = 60.0 if memory_aggressive_mode else 85.0         # GPU記憶體觸發閾值(%) - 激進: 60%

# 🔄 清理時機 (全面覆蓋)
memory_cleanup_on_batch_start = True                 # 批次開始時清理
memory_cleanup_on_batch_end = True                   # 批次結束時清理
memory_cleanup_on_image_start = memory_aggressive_mode  # 每張圖像開始前清理 (激進模式)
memory_cleanup_on_image_end = memory_aggressive_mode    # 每張圖像結束後清理 (激進模式)
memory_cleanup_on_model_load = True                  # 模型載入後清理
memory_cleanup_on_inference_error = True             # 推理錯誤時清理

# 🛠️ 清理組件 (全開)
memory_enable_torch_cleanup = True                   # 啟用PyTorch記憶體清理
memory_enable_opencv_cleanup = True                  # 啟用OpenCV記憶體清理
memory_enable_python_gc = True                       # 啟用Python垃圾回收
memory_enable_cuda_cleanup = True                    # 啟用CUDA記憶體清理
memory_enable_shared_memory_cleanup = True           # 啟用共享記憶體清理
memory_enable_cache_cleanup = True                   # 啟用緩存清理

# 📊 監控與診斷
memory_enable_monitoring = True                      # 啟用記憶體監控
memory_log_usage = True                              # 記錄記憶體使用
memory_enable_leak_detection = memory_aggressive_mode  # 啟用記憶體洩漏檢測 (激進模式)
memory_enable_detailed_logging = memory_aggressive_mode # 啟用詳細日誌 (激進模式)

# 🚨 緊急清理設定
memory_emergency_threshold = 90.0                    # 緊急清理閾值(%)
memory_enable_emergency_cleanup = True               # 啟用緊急清理
memory_emergency_force_gc = True                     # 緊急情況強制垃圾回收
memory_emergency_clear_cache = True                  # 緊急情況清空所有緩存

# 🔧 進階清理選項
memory_enable_tensor_cleanup = memory_aggressive_mode   # 啟用張量清理 (激進模式)
memory_enable_gradient_cleanup = memory_aggressive_mode # 啟用梯度清理 (激進模式)
memory_enable_model_cleanup = False                     # 模型清理 (謹慎使用)
memory_force_sync_cleanup = memory_aggressive_mode      # 強制同步清理 (激進模式)

# ============================================================================
# 🧠 Phase 4 智能化參數設定 - 僅在智能模式下生效
# ============================================================================

# 智能模型選擇 (INTELLIGENT, ENTERPRISE, ULTIMATE 模式)
enable_intelligent_model_selection = RUNNING_MODE in [
    RunningMode.INTELLIGENT, RunningMode.ENTERPRISE, RunningMode.ULTIMATE]
# HIGH_ACCURACY, REAL_TIME, EDGE_COMPUTING, BATCH_PROCESSING
intelligent_scenario = "HIGH_ACCURACY"

# 多租戶支援 (ENTERPRISE, ULTIMATE 模式)
enable_multi_tenant = RUNNING_MODE in [
    RunningMode.ENTERPRISE, RunningMode.ULTIMATE]
tenant_api_key = None  # 設定租戶API密鑰，None則使用默認租戶

# 負載均衡 (ENTERPRISE, ULTIMATE 模式)
enable_load_balancing = RUNNING_MODE in [
    RunningMode.ENTERPRISE, RunningMode.ULTIMATE]
# ROUND_ROBIN, LEAST_CONNECTIONS, INTELLIGENT
load_balancing_strategy = "INTELLIGENT"

# 分散式處理 (ULTIMATE 模式)
enable_distributed_processing = RUNNING_MODE == RunningMode.ULTIMATE
max_distributed_tasks = 8
task_priority = "NORMAL"  # LOW, NORMAL, HIGH, CRITICAL

# 模型版本管理 (INTELLIGENT, ENTERPRISE, ULTIMATE 模式)
enable_model_registry = RUNNING_MODE in [
    RunningMode.INTELLIGENT, RunningMode.ENTERPRISE, RunningMode.ULTIMATE]
auto_model_update = True

# ============================================================================
# 🚀 導入系統模組 - 根據模式動態導入
# ============================================================================

# 基礎YOLO系統
try:
    from inference_system import create_inference_system, UnifiedConfig, ClassConfig
    YOLO_AVAILABLE = True
    print("✅ 基礎YOLO系統載入成功")
except ImportError as e:
    print(f"❌ 基礎YOLO系統載入失敗: {e}")
    YOLO_AVAILABLE = False

# 記憶體清理系統
try:
    from inference_system.performance import (
        MemoryCleaner, MemoryCleanupConfig, MemoryType, CleanupTrigger, create_memory_cleaner
    )
    MEMORY_CLEANUP_AVAILABLE = True
    print("✅ 記憶體清理系統載入成功")
except ImportError as e:
    print(f"⚠️ 記憶體清理系統載入失敗: {e}")
    MEMORY_CLEANUP_AVAILABLE = False

# Phase 4 智能化模組 (根據運行模式選擇性導入)
PHASE4_AVAILABLE = False
if RUNNING_MODE != RunningMode.CLASSIC:
    try:
        # 智能模型選擇
        if enable_intelligent_model_selection:
            from intelligence.model_selector import IntelligentModelManager, ScenarioType, ModelType, InferenceRequest

        # 多租戶架構
        if enable_multi_tenant:
            from enterprise.multi_tenant import TenantManager, TenantType, SubscriptionTier, TenantMiddleware

        # 智能負載均衡
        if enable_load_balancing:
            from load_balancing.intelligent_balancer import IntelligentLoadBalancer, LoadBalancingStrategy

        # 分散式推理
        if enable_distributed_processing:
            from distributed.distributed_inference import DistributedInferenceEngine, DistributedCluster, InferenceTask, TaskPriority

        # 模型版本管理
        if enable_model_registry:
            from versioning.model_registry import ModelRegistryManager

        PHASE4_AVAILABLE = True
        print("✅ Phase 4 智能化模組載入成功")

    except ImportError as e:
        print(f"⚠️ Phase 4 智能化模組載入失敗: {e}")
        print("🔄 自動降級到經典模式")
        RUNNING_MODE = RunningMode.CLASSIC
        PHASE4_AVAILABLE = False

# ============================================================================
# 🏗️ 統一配置生成函數 - 兼容所有模式
# ============================================================================


def create_ultimate_config(intelligent_components: Dict[str, Any] = None):
    """創建終極統一配置 - 根據運行模式自動適配"""

    if not YOLO_AVAILABLE:
        raise RuntimeError("基礎YOLO系統不可用")

    logger = logging.getLogger(__name__)

    # 轉換類別配置
    classes = {}
    for class_id, config_list in class_configs.items():
        name, display_name, color, conf_thresh, sahi_thresh, enabled = config_list

        # 智能模式下根據場景調整閾值
        if enable_intelligent_model_selection and intelligent_scenario:
            if intelligent_scenario == "HIGH_ACCURACY":
                conf_thresh *= 0.8  # 降低閾值提高召回率
            elif intelligent_scenario == "REAL_TIME":
                conf_thresh *= 1.2  # 提高閾值減少誤檢
            elif intelligent_scenario == "EDGE_COMPUTING":
                conf_thresh *= 1.1  # 邊緣設備略微提高閾值

        classes[class_id] = ClassConfig(
            name=name,
            display_name=display_name,
            color=color,
            confidence=conf_thresh,
            sahi_confidence=sahi_thresh,
            enabled=enabled
        )

    # 創建統一配置
    config = UnifiedConfig()

    # --- 智能模型選擇邏輯 ---
    # 優先使用全域配置作為基礎/備援路徑
    seg_path = segmentation_model_path
    det_path = detection_model_path

    if enable_intelligent_model_selection and intelligent_components and 'model_manager' in intelligent_components:
        model_manager = intelligent_components['model_manager']
        logger.info(f"🧠 正在根據場景 '{intelligent_scenario}' 執行智能模型選擇...")

        try:
            scenario_enum = ScenarioType[intelligent_scenario.upper()]
            # Create inference request for model selection
            request = InferenceRequest(
                scenario=scenario_enum,
                max_latency=1000,  # Default max latency
                min_accuracy=0.8,  # Default min accuracy
                max_memory_usage=8192  # Default max memory in MB
            )
            selected_model = model_manager.select_optimal_model(request)

            if selected_model and Path(selected_model.model_path).exists():
                logger.info(f"✅ 智能模型選擇成功: '{selected_model.model_id}'")
                if selected_model.model_type == ModelType.SEGMENTATION:
                    seg_path = selected_model.model_path
                    logger.info(f"   -> 分割模型更新為: {seg_path}")
                elif selected_model.model_type == ModelType.DETECTION:
                    det_path = selected_model.model_path
                    logger.info(f"   -> 檢測模型更新為: {det_path}")
            else:
                if selected_model:
                    logger.warning(
                        f"⚠️ 智能模型選擇成功但模型文件 '{selected_model.model_path}' 不存在。")
                else:
                    logger.warning(
                        f"⚠️ 智能模型選擇未能根據場景 '{intelligent_scenario}' 找到合適模型。")
                logger.warning(f"   -> 將退回使用手動配置的模型路徑: {seg_path}")

        except Exception as e:
            logger.error(f"❌ 智能模型選擇時發生嚴重錯誤: {e}")
            logger.warning(f"   -> 將退回使用手動配置的模型路徑: {seg_path}")

    # 模型配置 - 確保路徑正確設定
    if seg_path:
        config.model.segmentation_model_path = seg_path
        logger.info(f"✅ 分割模型路徑設定為: {seg_path}")
    if det_path:
        config.model.detection_model_path = det_path
        logger.info(f"✅ 檢測模型路徑設定為: {det_path}")

    # 驗證模型路徑
    if not config.model.segmentation_model_path and not config.model.detection_model_path:
        raise ValueError("必須至少指定一個模型路徑（分割模型或檢測模型）")

    # 檢查模型文件是否存在
    for model_type, model_path in [("分割", config.model.segmentation_model_path), ("檢測", config.model.detection_model_path)]:
        if model_path and not Path(model_path).exists():
            logger.warning(f"⚠️ {model_type}模型文件不存在: {model_path}")
            # 但不要拋出異常，讓ModelAdapter處理

    # SAHI配置 - 智能模式下根據場景調整
    config.processing.slice.enabled = enable_sahi
    if enable_intelligent_model_selection:
        if intelligent_scenario == "HIGH_ACCURACY":
            config.processing.slice.enabled = True  # 高精度模式強制啟用SAHI
            config.processing.slice.height = 640
            config.processing.slice.width = 640
            config.processing.slice.overlap_ratio = 0.3
        elif intelligent_scenario == "REAL_TIME":
            config.processing.slice.enabled = False  # 實時模式關閉SAHI
        elif intelligent_scenario == "EDGE_COMPUTING":
            config.processing.slice.enabled = False  # 邊緣計算關閉SAHI
    else:
        config.processing.slice.height = sahi_slice_height
        config.processing.slice.width = sahi_slice_width
        config.processing.slice.overlap_ratio = sahi_overlap_ratio

    # 輸出配置
    config.output.enable_labelme_output = enable_labelme_export
    config.output.labelme_output_dirname = labelme_output_dirname

    # 🚫 新增空預測跳過配置
    config.output.skip_empty_prediction_images = skip_empty_prediction_images
    config.output.skip_empty_labelme_export = skip_empty_labelme_export

    # 🏷️ LabelMe point配置更新
    config.output.labelme_include_points = labelme_include_points
    config.output.labelme_include_center_points = labelme_include_center_points
    
    # 🎯 LabelMe點數量控制配置
    config.output.labelme_max_polygon_points = labelme_max_polygon_points
    config.output.labelme_min_polygon_points = labelme_min_polygon_points
    config.output.labelme_simplify_epsilon = labelme_simplify_epsilon
    config.output.labelme_adaptive_simplify = labelme_adaptive_simplify
    config.output.labelme_large_object_threshold = labelme_large_object_threshold
    config.output.labelme_small_object_max_points = labelme_small_object_max_points
    config.output.labelme_large_object_max_points = labelme_large_object_max_points
    config.output.labelme_enable_point_optimization = labelme_enable_point_optimization
    config.output.labelme_optimization_mode = labelme_optimization_mode
    
    # 🔍 配置驗證和自動修正
    if enable_labelme_export:
        if labelme_max_polygon_points < labelme_min_polygon_points:
            logger.error(f"❌ 配置錯誤: 最大點數({labelme_max_polygon_points})不能小於最小點數({labelme_min_polygon_points})")
            # 自動修正 - 直接更新config對象，避免局部變量作用域問題
            corrected_max_points = max(labelme_max_polygon_points, labelme_min_polygon_points + 5)
            config.output.labelme_max_polygon_points = corrected_max_points
            logger.info(f"⚙️ 自動修正最大點數為: {corrected_max_points}")
        
        # 高級性能建議 - 使用config對象中的值
        current_max_points = config.output.labelme_max_polygon_points
        if labelme_optimization_mode == "accurate" and current_max_points > 150:
            logger.warning(f"⚠️ 精度模式下點數過多({current_max_points})可能導致卡頓，建議使用'balanced'模式")
        elif labelme_optimization_mode == "fast" and current_max_points > 50:
            logger.info(f"💡 快速模式下建議點數≤ 30點以獲得最佳性能")
        
        # 配置統計 - 使用config對象中的值
        total_polygon_range = current_max_points - labelme_min_polygon_points
        logger.debug(f"📈 點數範圍: {total_polygon_range}點, 簡化參數: {labelme_simplify_epsilon}")

    # 視覺化配置
    config.visualization.save_visualizations = save_detection_images  # 🔧 主要開關：控制是否生成視覺化
    config.visualization.enable_three_view = enable_three_view_output
    config.visualization.enable_prediction_only = enable_prediction_only_output
    config.visualization.three_view_layout = three_view_layout
    config.visualization.font_size = font_size
    config.visualization.font_thickness = font_thickness
    config.visualization.line_thickness = line_thickness
    config.visualization.transparency = transparency
    config.visualization.output_image_quality = output_image_quality

    # 圖像處理配置
    config.processing.image_processing.input_scale = 1.0  # 推理時保持原始大小
    # 🖼️ 儲存時的圖像縮放配置
    config.visualization.output_image_scale = output_image_scale
    config.processing.image_processing.enable_resize = True

    # ROI配置
    config.processing.roi.enabled = enable_roi_detection
    config.processing.roi.margins = roi_margins
    config.processing.roi.inference_mode = roi_inference_mode
    config.processing.roi.enable_preview = enable_roi_preview

    # 智能過濾配置
    config.processing.filtering.enabled = enable_intelligent_filtering
    config.processing.filtering.linear_aspect_ratio_threshold = linear_aspect_ratio_threshold
    config.processing.filtering.area_ratio_threshold = area_ratio_threshold
    # 🐊 新增Alligator_crack vs Linear_crack過濾配置
    config.processing.filtering.alligator_contains_linear_threshold = alligator_contains_linear_threshold
    # 📏 獨立功能配置
    config.processing.filtering.enable_aspect_ratio_filter = enable_aspect_ratio_filter
    config.processing.filtering.enable_area_ratio_filter = enable_area_ratio_filter

    # 🔀 簡化融合策略配置 - 只保留三種核心策略
    from inference_system.config.unified_config import FusionStrategy
    try:
        # 將字符串轉換為枚舉（簡化版本）
        strategy_mapping = {
            "standard_nms": FusionStrategy.STANDARD_NMS,
            "largest_object": FusionStrategy.LARGEST_OBJECT,
            "no_fusion": FusionStrategy.NO_FUSION
        }
        config.processing.fusion.strategy = strategy_mapping.get(
            fusion_strategy, FusionStrategy.LARGEST_OBJECT)
        config.processing.fusion.iou_threshold = fusion_iou_threshold
        config.processing.fusion.confidence_threshold = fusion_confidence_threshold

        logger.info(
            f"🔀 融合策略設定為: {fusion_strategy} (IoU閾值: {fusion_iou_threshold})")

        # 🎯 SAHI專用重疊合併配置（獨立於融合策略）
        config.processing.sahi.enable_sahi_overlap_merge = enable_sahi_overlap_merge
        config.processing.sahi.sahi_merge_iou_threshold = sahi_merge_iou_threshold
        config.processing.sahi.enable_mask_iou_calculation = enable_mask_iou_calculation
        config.processing.sahi.sahi_merge_confidence_strategy = sahi_merge_confidence_strategy

        if enable_sahi_overlap_merge:
            logger.info(
                f"🎯 SAHI重疊合併已啟用: IoU閾值={sahi_merge_iou_threshold}, Mask IoU優先={enable_mask_iou_calculation}, 置信度策略={sahi_merge_confidence_strategy}")
        else:
            logger.info("🎯 SAHI重疊合併已禁用")

    except Exception as e:
        logger.warning(f"⚠️ 融合策略配置失敗，使用預設值: {e}")
        config.processing.fusion.strategy = FusionStrategy.LARGEST_OBJECT

    # 🔗 物件連接功能配置 (Stage 3)
    config.processing.enable_object_connection = enable_object_connection
    config.processing.connection_distance_threshold = connection_distance_threshold
    config.processing.connection_same_class_only = connection_same_class_only
    config.processing.connection_confidence_weight = connection_confidence_weight
    config.processing.enable_crack_line_connection = enable_crack_line_connection
    if enable_object_connection:
        logger.info(
            f"🔗 物件連接功能啟用: 距離閾值={connection_distance_threshold}px, 同類別限制={connection_same_class_only}, 裂縫連接={enable_crack_line_connection}")

    # 🤖 統一的Joint優先智能過濾配置 (合併原Step2和新增規則)
    config.processing.filtering.enable_joint_priority_over_crack = enable_joint_priority_over_crack
    config.processing.filtering.joint_crack_overlap_threshold = joint_crack_overlap_threshold
    # 🎯 添加空間分離係數配置
    if not hasattr(config.processing.filtering, 'spatial_separation_factor'):
        config.processing.filtering.spatial_separation_factor = spatial_separation_factor
    else:
        config.processing.filtering.spatial_separation_factor = spatial_separation_factor

    if enable_joint_priority_over_crack:
        logger.info(
            f"🤖 Joint優先規則啟用: Joint與Linear_crack重疊時優先保留Joint (重疊閾值: {joint_crack_overlap_threshold})")
        if enable_intelligent_filtering:
            logger.info("   📋 此規則已整合到智能過濾中")
        else:
            logger.info("   🔧 此規則將獨立運行（智能過濾未啟用）")
    else:
        logger.info(f"📋 Joint優先規則未啟用")

    # 🐊 Alligator_crack vs Linear_crack 過濾配置
    if enable_intelligent_filtering:
        logger.info(
            f"🐊 Alligator包含Linear過濾啟用: IoU閾值={alligator_contains_linear_threshold}")
    logger.info(
        f"🎯 空間分離檢測啟用: 係數={spatial_separation_factor} (中心距離>最大邊長×{spatial_separation_factor}時保留兩者)")
    
    # 🎯 LabelMe點數控制日誌和性能分析
    if enable_labelme_export:
        logger.info(f"🏷️ LabelMe標註輸出設定:")
        logger.info(f"   多邊形點數範圍: {labelme_min_polygon_points}-{labelme_max_polygon_points}點")
        if labelme_adaptive_simplify:
            logger.info(f"   🧠 自適應簡化: 小目標≤{labelme_small_object_max_points}點, 大目標≤{labelme_large_object_max_points}點")
            logger.info(f"   大小區分闾值: {labelme_large_object_threshold}px²")
        else:
            logger.info(f"   🔧 固定簡化: 簡化參數={labelme_simplify_epsilon}")
        logger.info(f"   ⚡ 性能優化: {labelme_optimization_mode}模式")
        
        # 性能建議和警告
        if labelme_max_polygon_points > 100:
            logger.warning(f"⚠️ 點數設定較高({labelme_max_polygon_points}點), 可能影響性能")
            logger.info(f"   💡 建議: 設定為50-80點或使用'fast'優化模式")
        elif labelme_optimization_mode == "fast":
            logger.info(f"🚀 已啟用快速模式，性能優先於精度")
        elif labelme_optimization_mode == "accurate":
            logger.info(f"🎯 已啟用精度模式，精度優先於性能")

    # 📦 後處理功能配置
    if enable_post_processing:
        # 將後處理配置儲存到config中，以便後續使用
        config._post_processing_config = {
            'enable_matched_image_move': enable_matched_image_move,
            'target_directory': matched_image_move_target,
            'move_mode': matched_image_move_mode,
            'keep_structure': matched_image_keep_structure,
            'overwrite_existing': matched_image_overwrite,
            'supported_extensions': matched_image_extensions
        }
        
        logger.info(f"📦 後處理功能啟用:")
        logger.info(f"   圖像搬移: {enable_matched_image_move}")
        logger.info(f"   目標目錄: {matched_image_move_target}")
        logger.info(f"   操作模式: {matched_image_move_mode}")
        logger.info(f"   保持結構: {matched_image_keep_structure}")
        logger.info(f"   支援格式: {', '.join(matched_image_extensions)}")
    else:
        logger.info(f"📦 後處理功能未啟用")
    
    # 🧠 記憶體清理配置 - 替換舊的文件清理功能
    if MEMORY_CLEANUP_AVAILABLE and enable_memory_cleanup:
        # 將字符串轉換為枚舉
        trigger_mapping = {
            "time_based": CleanupTrigger.TIME_BASED,
            "usage_based": CleanupTrigger.USAGE_BASED,
            "count_based": CleanupTrigger.COUNT_BASED,
            "hybrid": CleanupTrigger.HYBRID,
            "manual": CleanupTrigger.MANUAL
        }

        memory_cleanup_config = MemoryCleanupConfig(
            enabled=enable_memory_cleanup,
            trigger_mode=trigger_mapping.get(
                memory_cleanup_trigger, CleanupTrigger.HYBRID),
            cleanup_interval_seconds=memory_cleanup_interval,
            cleanup_after_n_images=memory_cleanup_after_images,
            cleanup_on_batch_start=memory_cleanup_on_batch_start,
            cleanup_on_batch_end=memory_cleanup_on_batch_end,
            cpu_memory_threshold=memory_cpu_threshold,
            gpu_memory_threshold=memory_gpu_threshold,
            enable_torch_cleanup=memory_enable_torch_cleanup,
            enable_opencv_cleanup=memory_enable_opencv_cleanup,
            enable_python_gc=memory_enable_python_gc,
            enable_cuda_cleanup=memory_enable_cuda_cleanup,
            enable_memory_monitoring=memory_enable_monitoring,
            log_memory_usage=memory_log_usage
        )

        # 將記憶體清理配置存儲到config中，以便後續使用
        config._memory_cleanup_config = memory_cleanup_config

        logger.info(f"🧠 記憶體清理功能啟用:")
        logger.info(f"   觸發模式: {memory_cleanup_trigger}")
        logger.info(f"   清理間隔: {memory_cleanup_interval}秒")
        logger.info(f"   每{memory_cleanup_after_images}張圖像清理")
        logger.info(
            f"   CPU閾值: {memory_cpu_threshold}%, GPU閾值: {memory_gpu_threshold}%")
    else:
        if enable_memory_cleanup:
            logger.warning("⚠️ 記憶體清理功能已啟用但系統不可用")
        else:
            logger.info("🧠 記憶體清理功能未啟用")

    # 類別配置
    config.classes = classes

    # 系統配置
    config.output_path = output_path
    # max_workers and enable_cache are not part of UnifiedConfig
    # These would be handled at the system level

    return config

# ============================================================================
# 🧠 智能化系統封裝類
# ============================================================================


class UltimateYOLOSystem:
    """終極YOLO推理系統 - 整合所有功能"""

    def __init__(self):
        self.logger = self._setup_logging()
        self.running_mode = RUNNING_MODE

        # Phase 4 組件
        self.intelligent_components = {}

        self.logger.info(f"🏆 初始化終極YOLO系統 (模式: {self.running_mode.value})")

        if PHASE4_AVAILABLE:
            self._initialize_intelligent_components()

    def _setup_logging(self) -> logging.Logger:
        """設置日誌系統"""
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('ultimate_yolo.log', encoding='utf-8')
            ]
        )
        return logging.getLogger(__name__)

    def _initialize_intelligent_components(self):
        """初始化智能化組件"""
        try:
            # 智能模型管理器
            if enable_intelligent_model_selection:
                self.logger.info("🧠 初始化智能模型管理器")
                self.intelligent_components['model_manager'] = IntelligentModelManager(
                )

            # 多租戶管理器
            if enable_multi_tenant:
                self.logger.info("🏢 初始化多租戶管理器")
                self.intelligent_components['tenant_manager'] = TenantManager()
                self.intelligent_components['tenant_middleware'] = TenantMiddleware(
                    self.intelligent_components['tenant_manager']
                )

            # 智能負載均衡器
            if enable_load_balancing:
                self.logger.info("⚖️ 初始化智能負載均衡器")
                strategy = getattr(
                    LoadBalancingStrategy, load_balancing_strategy, LoadBalancingStrategy.INTELLIGENT)
                self.intelligent_components['load_balancer'] = IntelligentLoadBalancer(
                    strategy)

            # 分散式推理引擎
            if enable_distributed_processing:
                self.logger.info("🌐 初始化分散式推理引擎")
                cluster_config = DistributedCluster(
                    cluster_id="ultimate_yolo_cluster",
                    cluster_name="終極YOLO推理集群",
                    coordinator_node="coordinator_001"
                )
                self.intelligent_components['distributed_engine'] = DistributedInferenceEngine(
                    cluster_config)

            # 模型註冊中心
            if enable_model_registry:
                self.logger.info("📦 初始化模型註冊中心")
                self.intelligent_components['model_registry'] = ModelRegistryManager(
                )

            self.logger.info("✅ 智能化組件初始化完成")

        except Exception as e:
            self.logger.error(f"智能化組件初始化失敗: {e}")

    async def process_with_ultimate_features(self, input_path_obj: Path):
        """使用終極功能處理"""

        # 租戶認證 (企業模式)
        if enable_multi_tenant and 'tenant_middleware' in self.intelligent_components:
            if tenant_api_key:
                tenant = await self.intelligent_components['tenant_middleware'].authenticate_request(tenant_api_key)
                if not tenant:
                    self.logger.error("❌ 租戶認證失敗")
                    return
                self.logger.info(f"✅ 租戶認證成功: {tenant.tenant_name}")

        # 創建配置
        config = create_ultimate_config(self.intelligent_components)

        # 🧠 初始化記憶體清理器 (如果啟用)
        memory_cleaner = None
        if (MEMORY_CLEANUP_AVAILABLE and enable_memory_cleanup and
                hasattr(config, '_memory_cleanup_config')):
            try:
                memory_cleaner = create_memory_cleaner(
                    config._memory_cleanup_config)

                # 執行啟動時記憶體清理
                if config._memory_cleanup_config.cleanup_on_batch_start:
                    cleanup_result = memory_cleaner.cleanup_memory(
                        MemoryType.ALL, "startup")
                    if cleanup_result.get("success"):
                        effect = cleanup_result.get("effect", {})
                        cpu_freed = effect.get("cpu_memory_freed_mb", 0)
                        gpu_freed = effect.get("gpu_memory_freed_mb", 0)
                        self.logger.info(
                            f"🧠 啟動記憶體清理完成: CPU釋放{cpu_freed:.1f}MB, GPU釋放{gpu_freed:.1f}MB")
                    else:
                        self.logger.warning("⚠️ 啟動記憶體清理遇到問題")

            except Exception as e:
                self.logger.error(f"❌ 記憶體清理器初始化失敗: {e}")
                memory_cleaner = None

        # 執行推理
        try:
            # 🔄 計算檢查點文件的完整路徑
            checkpoint_path = None
            if enable_resume_from_checkpoint:
                checkpoint_path = str(
                    Path(output_path) / resume_checkpoint_file)

            with create_inference_system(
                config=config,
                enable_resume=enable_resume_from_checkpoint,
                checkpoint_file=checkpoint_path
            ) as inference_system:

                # 🧠 將記憶體清理器注入到推理系統中 (如果可用)
                if memory_cleaner and hasattr(inference_system, 'set_memory_cleaner'):
                    inference_system.set_memory_cleaner(memory_cleaner)
                    self.logger.info("✅ 記憶體清理器已注入推理系統")

                if input_path_obj.is_file():
                    # 單張圖像處理
                    self.logger.info(f"📸 處理單張圖像: {input_path_obj.name}")
                    result = inference_system.process_single_image(
                        str(input_path_obj),
                        enable_visualization=save_detection_images,
                        enable_statistics=enable_csv_output
                    )

                    if result['success']:
                        self.logger.info(
                            f"✅ 處理完成，檢測到 {len(result['detections'])} 個目標")
                        self.logger.info(
                            f"📊 統計信息: {result['detection_statistics']}")

                        # 智能化信息
                        if PHASE4_AVAILABLE:
                            intelligent_info = {
                                'running_mode': self.running_mode.value,
                                'intelligent_features': {
                                    'model_selection': enable_intelligent_model_selection,
                                    'multi_tenant': enable_multi_tenant,
                                    'load_balancing': enable_load_balancing,
                                    'distributed': enable_distributed_processing
                                },
                                'scenario': intelligent_scenario if enable_intelligent_model_selection else None
                            }
                            self.logger.info(f"🧠 智能化信息: {intelligent_info}")

                        if result.get('visualization_paths'):
                            self.logger.info(
                                f"🎨 視覺化結果: {result['visualization_paths']}")
                    else:
                        self.logger.error(f"❌ 處理失敗: {result['error']}")

                elif input_path_obj.is_dir():
                    # 批量目錄處理
                    self.logger.info(f"📁 批量處理目錄: {input_path}")
                    summary = inference_system.process_directory(
                        str(input_path_obj))

                    if 'success_rate' in summary:
                        self.logger.info(f"✅ 批量處理完成!")
                        self.logger.info(
                            f"📊 成功率: {summary['success_rate']:.1f}%")
                        self.logger.info(f"📊 總圖像數: {summary['total_images']}")
                        self.logger.info(
                            f"📊 總檢測數: {summary['total_detections']}")
                        self.logger.info(
                            f"📊 平均每圖檢測數: {summary['average_detections_per_image']:.1f}")

                        # 智能化統計
                        if PHASE4_AVAILABLE:
                            intelligent_summary = {
                                'running_mode': self.running_mode.value,
                                'enhanced_features_active': len(self.intelligent_components),
                                'performance_boost': "AI優化已啟用" if enable_intelligent_model_selection else "標準模式"
                            }
                            self.logger.info(f"🧠 智能化統計: {intelligent_summary}")
                    else:
                        self.logger.error(
                            f"❌ 批量處理失敗: {summary.get('error', '未知錯誤')}")

        except Exception as e:
            self.logger.error(f"❌ 推理執行失敗: {e}")
            raise
        finally:
            # 🧠 執行結束時記憶體清理 (如果啟用)
            if (memory_cleaner and hasattr(config, '_memory_cleanup_config') and
                    config._memory_cleanup_config.cleanup_on_batch_end):
                try:
                    cleanup_result = memory_cleaner.cleanup_memory(
                        MemoryType.ALL, "finish")
                    if cleanup_result.get("success"):
                        effect = cleanup_result.get("effect", {})
                        cpu_freed = effect.get("cpu_memory_freed_mb", 0)
                        gpu_freed = effect.get("gpu_memory_freed_mb", 0)
                        total_freed = effect.get("total_memory_freed_mb", 0)
                        self.logger.info(
                            f"🧠 結束記憶體清理完成: 總共釋放{total_freed:.1f}MB (CPU:{cpu_freed:.1f}MB, GPU:{gpu_freed:.1f}MB)")
                    else:
                        self.logger.warning("⚠️ 結束記憶體清理遇到問題")

                    # 清理記憶體清理器資源
                    if hasattr(memory_cleaner, 'cleanup'):
                        memory_cleaner.cleanup()

                except Exception as cleanup_error:
                    self.logger.error(f"❌ 結束記憶體清理失敗: {cleanup_error}")

    def get_system_status(self) -> Dict[str, Any]:
        """獲取系統狀態"""
        status = {
            'running_mode': self.running_mode.value,
            'yolo_system_available': YOLO_AVAILABLE,
            'phase4_available': PHASE4_AVAILABLE,
            'memory_cleanup_available': MEMORY_CLEANUP_AVAILABLE,
            'intelligent_components': list(self.intelligent_components.keys()),
            'feature_status': {
                'intelligent_model_selection': enable_intelligent_model_selection,
                'multi_tenant_support': enable_multi_tenant,
                'load_balancing': enable_load_balancing,
                'distributed_processing': enable_distributed_processing,
                'model_registry': enable_model_registry,
                'memory_cleanup_enabled': enable_memory_cleanup and MEMORY_CLEANUP_AVAILABLE,
                'post_processing_enabled': enable_post_processing,
                'matched_image_move_enabled': enable_matched_image_move
            }
        }

        # 添加組件統計
        for name, component in self.intelligent_components.items():
            if hasattr(component, 'get_statistics'):
                status[f'{name}_stats'] = component.get_statistics()
            elif hasattr(component, 'get_manager_stats'):
                status[f'{name}_stats'] = component.get_manager_stats()

        return status

    def stop_all_components(self):
        """停止所有組件"""
        self.logger.info("🛑 停止所有組件")

        try:
            # 停止Phase 4組件
            for name, component in self.intelligent_components.items():
                if hasattr(component, 'stop'):
                    component.stop()
                    self.logger.info(f"   {name} 已停止")

            self.logger.info("✅ 所有組件已停止")

        except Exception as e:
            self.logger.error(f"停止組件時發生錯誤: {e}")
    
    def post_process_images(self, input_path_str: str, output_path_str: str):
        """
        📦 後處理功能：搬移input中與labelme JSON同名的圖像檔案
        
        Args:
            input_path_str: 輸入目錄路徑
            output_path_str: 輸出目錄路徑
        """
        if not enable_post_processing or not enable_matched_image_move:
            self.logger.info("📦 後處理功能已禁用")
            return
            
        try:
            import shutil
            from pathlib import Path
            
            input_dir = Path(input_path_str)
            output_dir = Path(output_path_str)
            labelme_dir = output_dir / labelme_output_dirname
            target_dir = output_dir / matched_image_move_target
            
            # 檢查labelme目錄是否存在
            if not labelme_dir.exists():
                self.logger.warning(f"⚠️ LabelMe目錄不存在: {labelme_dir}")
                return
            
            # 獲取所有JSON檔案
            json_files = list(labelme_dir.rglob("*.json"))
            if not json_files:
                self.logger.info("📦 未找到LabelMe JSON檔案，跳過後處理")
                return
                
            # 創建目標目錄
            target_dir.mkdir(parents=True, exist_ok=True)
            
            moved_count = 0
            copied_count = 0
            linked_count = 0
            skipped_count = 0
            
            self.logger.info(f"📦 開始後處理：從 {input_dir} 搬移匹配的圖像到 {target_dir}")
            self.logger.info(f"📦 找到 {len(json_files)} 個JSON檔案，開始匹配圖像...")
            
            for json_file in json_files:
                # 獲取JSON檔案的基本名稱（不含副檔名）
                json_stem = json_file.stem
                
                # 在input目錄中尋找同名的圖像檔案
                matched_images = []
                for ext in matched_image_extensions:
                    # 支援巢狀目錄結構
                    if input_dir.is_dir():
                        pattern_files = list(input_dir.rglob(f"{json_stem}{ext}"))
                        matched_images.extend(pattern_files)
                    else:
                        # 單檔案情況
                        single_file = input_dir.parent / f"{json_stem}{ext}"
                        if single_file.exists():
                            matched_images.append(single_file)
                
                if not matched_images:
                    continue
                    
                for img_file in matched_images:
                    try:
                        # 計算相對路徑（如果需要保持目錄結構）
                        if matched_image_keep_structure and input_dir.is_dir():
                            relative_path = img_file.relative_to(input_dir)
                            target_file = target_dir / relative_path
                        else:
                            target_file = target_dir / img_file.name
                        
                        # 確保目標目錄存在
                        target_file.parent.mkdir(parents=True, exist_ok=True)
                        
                        # 檢查目標檔案是否已存在
                        if target_file.exists() and not matched_image_overwrite:
                            self.logger.debug(f"⏭️ 跳過已存在的檔案: {target_file.name}")
                            skipped_count += 1
                            continue
                        
                        # 執行操作
                        if matched_image_move_mode == "move":
                            shutil.move(str(img_file), str(target_file))
                            moved_count += 1
                            self.logger.debug(f"🚚 移動: {img_file.name} -> {target_file}")
                            
                        elif matched_image_move_mode == "copy":
                            shutil.copy2(str(img_file), str(target_file))
                            copied_count += 1
                            self.logger.debug(f"📋 複製: {img_file.name} -> {target_file}")
                            
                        elif matched_image_move_mode == "link":
                            # 軟連結 (Windows需要管理員權限或開發者模式)
                            try:
                                if target_file.exists():
                                    target_file.unlink()
                                target_file.symlink_to(img_file)
                                linked_count += 1
                                self.logger.debug(f"🔗 連結: {img_file.name} -> {target_file}")
                            except OSError as e:
                                # 降級到複製
                                self.logger.warning(f"⚠️ 建立軟連結失敗，降級為複製: {e}")
                                shutil.copy2(str(img_file), str(target_file))
                                copied_count += 1
                                
                    except Exception as e:
                        self.logger.error(f"⚠️ 處理檔案 {img_file.name} 失敗: {e}")
                        continue
            
            # 統計報告
            total_operations = moved_count + copied_count + linked_count
            if total_operations > 0:
                self.logger.info(f"✅ 後處理完成! 總操作數: {total_operations}")
                if moved_count > 0:
                    self.logger.info(f"   🚚 移動: {moved_count} 個檔案")
                if copied_count > 0:
                    self.logger.info(f"   📋 複製: {copied_count} 個檔案")
                if linked_count > 0:
                    self.logger.info(f"   🔗 連結: {linked_count} 個檔案")
                if skipped_count > 0:
                    self.logger.info(f"   ⏭️ 跳過: {skipped_count} 個檔案")
                self.logger.info(f"   📁 目標目錄: {target_dir}")
            else:
                self.logger.info("📦 後處理完成，但未找到匹配的圖像檔案")
                
        except Exception as e:
            self.logger.error(f"❌ 後處理功能執行失敗: {e}")
            import traceback
            self.logger.error(f"錯誤詳情: {traceback.format_exc()}")

# ============================================================================
# 🚀 主執行邏輯
# ============================================================================


async def main():
    """主函數 - 執行終極YOLO推理"""

    # 設置日誌
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    logger = logging.getLogger(__name__)
    logger.info(f"🏆 啟動終極YOLO推理系統 (模式: {RUNNING_MODE.value})")

    if not YOLO_AVAILABLE:
        logger.error("❌ 基礎YOLO系統不可用，無法繼續")
        return

    try:
        # 創建終極系統
        ultimate_system = UltimateYOLOSystem()

        # 檢查輸入路徑
        input_path_obj = Path(input_path)
        if not input_path_obj.exists():
            raise ValueError(f"輸入路徑不存在: {input_path}")

        # 顯示系統狀態
        status = ultimate_system.get_system_status()
        logger.info(f"📊 系統狀態: {status['running_mode']} 模式")
        logger.info(f"🧠 智能功能: {list(status['intelligent_components'])}")

        # 執行處理
        logger.info(
            f"⚙️ 配置載入完成，啟用類別數: {len([c for c in class_configs.values() if c[5]])}")
        await ultimate_system.process_with_ultimate_features(input_path_obj)
        
        # 📦 執行後處理功能
        logger.info(f"📦 開始執行後處理功能...")
        ultimate_system.post_process_images(input_path, output_path)

        # 等待用戶輸入
        print(f"\n💡 終極YOLO推理完成!")
        print(f"   當前模式: {RUNNING_MODE.value.upper()}")
        print(f"   系統將繼續運行，按 Enter 鍵停止...")
        input()

        # 停止所有組件
        ultimate_system.stop_all_components()

        print("✅ 終極YOLO系統已完全停止")

    except KeyboardInterrupt:
        logger.warning("⚠️ 收到中斷信號，正在停止系統...")
        if 'ultimate_system' in locals():
            ultimate_system.stop_all_components()
    except Exception as e:
        logger.error(f"❌ 系統執行失敗: {str(e)}")
        raise

    logger.info("🎉 終極YOLO推理系統執行完成")


if __name__ == "__main__":
    # 顯示運行模式說明
    print(f"\n🎯 運行模式說明:")
    print(f"   📱 CLASSIC: 經典模式 - 保持原有功能，零學習成本")
    print(f"   🧠 INTELLIGENT: 智能模式 - AI驅動的模型選擇和優化")
    print(f"   🏢 ENTERPRISE: 企業模式 - 多租戶+負載均衡+企業級功能")
    print(f"   🌟 ULTIMATE: 全能模式 - 所有功能全開，世界級AI平台")
    print(f"\n💡 要更改模式，請修改腳本頂部的 RUNNING_MODE 變數")
    print("=" * 60)

    asyncio.run(main())
