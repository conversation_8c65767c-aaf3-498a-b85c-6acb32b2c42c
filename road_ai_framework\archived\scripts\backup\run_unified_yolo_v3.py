#!/usr/bin/env python3
"""
🚀 統一YOLO推理系統 v3.0 - 生產級主運行腳本
整合Phase 1+2的所有功能：模組化架構 + 企業級性能

新功能特色:
✨ 企業級性能: 並發處理、智能緩存、實時監控
✨ 生產就緒: 完整日誌、錯誤追蹤、自動恢復
✨ 可觀測性: 性能儀表板、告警機制、數據導出
✨ 高可用性: 故障隔離、優雅降級、資源管理
"""

import sys
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
import time
from datetime import datetime

# 設置項目路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 導入新架構組件
from inference_system import (
    create_inference_system, 
    UnifiedConfig, 
    ClassConfig
)
from inference_system.performance import (
    ConcurrentProcessor,
    CacheManager, 
    PerformanceMonitor,
    MemoryOptimizer
)
from inference_system.extensions import PluginManager

# ============================================================================
# 🎯 生產級配置 - 所有設定集中管理
# ============================================================================

class ProductionConfig:
    """生產級配置管理"""
    
    # 🤖 模型配置
    MODEL_PATH = r"D:\4_road_crack\best_0728.pt"
    BACKUP_MODEL_PATH = None  # 備用模型路徑
    
    # 📁 路徑配置
    INPUT_PATH = r"D:\image\road_crack\test_600_resize"
    OUTPUT_PATH = r"D:\image\road_crack\test_600_out_v3"
    CACHE_PATH = r"D:\image\road_crack\cache"
    LOGS_PATH = r"D:\image\road_crack\logs"
    
    # ⚡ 性能配置
    ENABLE_CONCURRENT_PROCESSING = True
    MAX_WORKERS = 8  # 並發工作數
    ENABLE_CACHING = True
    CACHE_SIZE_MB = 500  # 緩存大小
    ENABLE_PERFORMANCE_MONITORING = True
    ENABLE_MEMORY_OPTIMIZATION = True
    
    # 🧩 推理配置
    ENABLE_SAHI = False
    SAHI_SLICE_HEIGHT = 512
    SAHI_SLICE_WIDTH = 512 
    SAHI_OVERLAP_RATIO = 0.2
    
    # 🧠 智能過濾
    ENABLE_INTELLIGENT_FILTERING = True
    LINEAR_ASPECT_RATIO_THRESHOLD = 0.8
    AREA_RATIO_THRESHOLD = 0.4
    STEP2_IOU_THRESHOLD = 0.3
    
    # 🎨 視覺化配置
    ENABLE_THREE_VIEW_OUTPUT = True
    THREE_VIEW_LAYOUT = "horizontal"
    FONT_SIZE = 1.0
    FONT_THICKNESS = 2
    OUTPUT_IMAGE_QUALITY = 95
    
    # 🏷️ 類別配置
    CLASS_CONFIGS = {
        0: ["Alligator_crack", "龜裂", [0, 0, 255], 0.4, 0.1, True],
        1: ["deformation", "變形", [100, 100, 100], 0.1, 0.1, True],
        2: ["dirt", "汙垢", [110, 110, 110], 0.1, 0.08, True],
        3: ["expansion_joint", "伸縮縫", [120, 120, 120], 0.1, 0.08, True],
        4: ["joint", "路面接縫", [130, 130, 130], 0.1, 0.1, True],
        5: ["lane_line_linear", "白線裂縫", [140, 140, 140], 0.1, 0.05, True],
        6: ["linear_crack", "裂縫", [0, 255, 0], 0.3, 0.1, True],
        7: ["manhole", "孔蓋", [255, 0, 255], 0.1, 0.1, True],
        8: ["patch", "補綻", [255, 0, 0], 0.1, 0.1, True],
        9: ["patch_square", "補綻_方正", [160, 160, 160], 0.1, 0.1, True],
        10: ["potholes", "坑洞", [0, 255, 255], 0.1, 0.1, True],
        11: ["rutting", "車轍", [255, 255, 0], 0.1, 0.1, True]
    }
    
    # 📊 輸出配置
    ENABLE_CSV_OUTPUT = True
    ENABLE_JSON_OUTPUT = True
    ENABLE_PERFORMANCE_REPORTS = True
    SAVE_DETECTION_IMAGES = True
    
    # 🔧 系統配置
    LOG_LEVEL = "INFO"
    ENABLE_GRACEFUL_SHUTDOWN = True
    MAX_PROCESSING_TIME_HOURS = 24  # 最大處理時間限制
    CHECKPOINT_INTERVAL = 50  # 檢查點間隔
    
    # 📱 告警配置
    ENABLE_ALERTS = True
    CPU_ALERT_THRESHOLD = 90.0
    MEMORY_ALERT_THRESHOLD = 85.0
    ERROR_RATE_THRESHOLD = 5.0  # 錯誤率閾值(%)


def setup_production_logging(config: ProductionConfig) -> logging.Logger:
    """設置生產級日誌系統"""
    
    # 創建日誌目錄
    logs_dir = Path(config.LOGS_PATH)
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # 配置日誌格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    
    # 創建logger
    logger = logging.getLogger("UnifiedYOLO_v3")
    logger.setLevel(getattr(logging, config.LOG_LEVEL))
    
    # 清除既有handlers
    logger.handlers.clear()
    
    # 文件處理器 - 詳細日誌
    log_file = logs_dir / f"unified_yolo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(logging.Formatter(log_format))
    logger.addHandler(file_handler)
    
    # 控制台處理器 - 重要信息
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, config.LOG_LEVEL))
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    ))
    logger.addHandler(console_handler)
    
    # 錯誤文件處理器
    error_file = logs_dir / f"errors_{datetime.now().strftime('%Y%m%d')}.log"
    error_handler = logging.FileHandler(error_file, encoding='utf-8')
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(logging.Formatter(log_format))
    logger.addHandler(error_handler)
    
    logger.info(f"📋 生產級日誌系統初始化完成")
    logger.info(f"   主日誌: {log_file}")
    logger.info(f"   錯誤日誌: {error_file}")
    
    return logger


def create_production_config(prod_config: ProductionConfig) -> UnifiedConfig:
    """創建生產級統一配置"""
    
    config = UnifiedConfig()
    
    # 模型配置
    config.model.model_path = prod_config.MODEL_PATH
    config.model.device = "auto"
    
    # SAHI配置
    config.sahi.enabled = prod_config.ENABLE_SAHI
    config.sahi.slice_height = prod_config.SAHI_SLICE_HEIGHT
    config.sahi.slice_width = prod_config.SAHI_SLICE_WIDTH
    config.sahi.overlap_height_ratio = prod_config.SAHI_OVERLAP_RATIO
    config.sahi.overlap_width_ratio = prod_config.SAHI_OVERLAP_RATIO
    
    # 視覺化配置
    config.visualization.enable_three_view = prod_config.ENABLE_THREE_VIEW_OUTPUT
    config.visualization.three_view_layout = prod_config.THREE_VIEW_LAYOUT
    config.visualization.font_size = prod_config.FONT_SIZE
    config.visualization.font_thickness = prod_config.FONT_THICKNESS
    config.visualization.output_image_quality = prod_config.OUTPUT_IMAGE_QUALITY
    
    # 智能過濾配置
    config.enable_intelligent_filtering = prod_config.ENABLE_INTELLIGENT_FILTERING
    config.linear_aspect_ratio_threshold = prod_config.LINEAR_ASPECT_RATIO_THRESHOLD
    config.area_ratio_threshold = prod_config.AREA_RATIO_THRESHOLD
    config.step2_iou_threshold = prod_config.STEP2_IOU_THRESHOLD
    
    # 類別配置
    config.classes = {}
    for class_id, (name, display_name, color, conf_thresh, sahi_thresh, enabled) in prod_config.CLASS_CONFIGS.items():
        config.classes[class_id] = ClassConfig(
            id=class_id,
            name=name,
            display_name=display_name,
            color=color,
            confidence_threshold=conf_thresh,
            sahi_confidence_threshold=sahi_thresh,
            enabled=enabled
        )
    
    # 輸出配置
    config.output_dir = prod_config.OUTPUT_PATH
    config.enable_cache = prod_config.ENABLE_CACHING
    config.max_workers = prod_config.MAX_WORKERS
    
    return config


class ProductionInferenceSystem:
    """生產級推理系統管理器"""
    
    def __init__(self, prod_config: ProductionConfig):
        self.prod_config = prod_config
        self.logger = setup_production_logging(prod_config)
        
        # 生產級組件
        self.config = create_production_config(prod_config)
        self.inference_system = None
        self.concurrent_processor = None
        self.cache_manager = None
        self.performance_monitor = None
        self.memory_optimizer = None
        self.plugin_manager = None
        
        # 運行統計
        self.start_time = None
        self.processed_images = 0
        self.failed_images = 0
        self.total_detections = 0
        
        self.logger.info("🏭 生產級推理系統管理器初始化完成")
    
    def initialize_components(self):
        """初始化所有生產級組件"""
        try:
            self.logger.info("🔧 初始化生產級組件...")
            
            # 1. 核心推理系統
            self.inference_system = create_inference_system(config=self.config)
            self.logger.info("✅ 核心推理系統初始化完成")
            
            # 2. 並發處理器
            if self.prod_config.ENABLE_CONCURRENT_PROCESSING:
                self.concurrent_processor = ConcurrentProcessor(
                    self.config,
                    max_workers=self.prod_config.MAX_WORKERS,
                    use_processes=False  # 使用線程模式更穩定
                )
                self.logger.info(f"✅ 並發處理器初始化完成 (workers: {self.prod_config.MAX_WORKERS})")
            
            # 3. 緩存管理器  
            if self.prod_config.ENABLE_CACHING:
                self.cache_manager = CacheManager(
                    self.config,
                    cache_dir=self.prod_config.CACHE_PATH,
                    max_memory_size=self.prod_config.CACHE_SIZE_MB,
                    max_disk_size=self.prod_config.CACHE_SIZE_MB * 2
                )
                self.logger.info(f"✅ 緩存管理器初始化完成 (size: {self.prod_config.CACHE_SIZE_MB}MB)")
            
            # 4. 性能監控器
            if self.prod_config.ENABLE_PERFORMANCE_MONITORING:
                self.performance_monitor = PerformanceMonitor(
                    self.config,
                    monitoring_interval=2.0
                )
                self.performance_monitor.set_thresholds(
                    cpu_threshold=self.prod_config.CPU_ALERT_THRESHOLD,
                    memory_threshold=self.prod_config.MEMORY_ALERT_THRESHOLD
                )
                self.performance_monitor.start_monitoring()
                self.logger.info("✅ 性能監控器初始化完成")
            
            # 5. 記憶體優化器
            if self.prod_config.ENABLE_MEMORY_OPTIMIZATION:
                self.memory_optimizer = MemoryOptimizer(
                    self.config,
                    auto_cleanup_threshold=self.prod_config.MEMORY_ALERT_THRESHOLD
                )
                self.memory_optimizer.start_auto_cleanup()
                self.logger.info("✅ 記憶體優化器初始化完成")
            
            # 6. 插件管理器
            self.plugin_manager = PluginManager(self.config)
            self.logger.info("✅ 插件管理器初始化完成")
            
            self.logger.info("🎉 所有生產級組件初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ 組件初始化失敗: {str(e)}")
            raise
    
    def process_with_caching(self, image_path: str) -> Dict[str, Any]:
        """帶緩存的推理處理"""
        if not self.cache_manager:
            return self.inference_system.process_single_image(image_path)
        
        # 生成緩存鍵
        cache_key = self.cache_manager.get_cache_key(
            image_path, 
            model_hash=self.prod_config.MODEL_PATH
        )
        
        # 嘗試從緩存獲取
        cached_result = self.cache_manager.get(cache_key)
        if cached_result:
            self.logger.debug(f"💾 緩存命中: {Path(image_path).name}")
            return cached_result
        
        # 執行推理
        result = self.inference_system.process_single_image(image_path)
        
        # 保存到緩存
        if result.get('success', False):
            self.cache_manager.put(cache_key, result)
        
        return result
    
    def process_single_image(self, image_path: str) -> Dict[str, Any]:
        """處理單張圖像（生產級）"""
        start_time = time.time()
        
        try:
            # 記錄處理開始
            self.logger.info(f"📸 開始處理: {Path(image_path).name}")
            
            # 執行推理（帶緩存）
            result = self.process_with_caching(image_path)
            
            # 更新統計
            processing_time = time.time() - start_time
            if result.get('success', False):
                self.processed_images += 1
                detections = result.get('detections', [])
                self.total_detections += len(detections)
                
                self.logger.info(f"✅ 處理完成: {len(detections)}個檢測結果 ({processing_time:.2f}s)")
            else:
                self.failed_images += 1
                self.logger.warning(f"⚠️ 處理失敗: {result.get('error', 'Unknown error')}")
            
            # 記錄到性能監控
            if self.performance_monitor:
                self.performance_monitor.record_processing(processing_time)
            
            return result
            
        except Exception as e:
            self.failed_images += 1
            processing_time = time.time() - start_time
            self.logger.error(f"❌ 處理異常 {Path(image_path).name}: {str(e)}")
            
            return {
                'success': False,
                'image_path': image_path,
                'error': str(e),
                'processing_time': processing_time
            }
    
    def process_directory_concurrent(self, input_dir: str) -> Dict[str, Any]:
        """並發批量處理目錄"""
        input_path = Path(input_dir)
        if not input_path.exists():
            raise ValueError(f"輸入目錄不存在: {input_dir}")
        
        # 查找圖像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        if not image_files:
            raise ValueError(f"未找到圖像文件: {input_dir}")
        
        image_paths = [str(img) for img in image_files]
        self.logger.info(f"📁 發現 {len(image_paths)} 張圖像，開始並發處理...")
        
        self.start_time = time.time()
        
        # 進度回調函數
        def progress_callback(current: int, total: int, result: Dict[str, Any]):
            progress_percent = (current / total) * 100
            self.logger.info(f"📈 並發處理進度: {current}/{total} ({progress_percent:.1f}%)")
        
        # 執行並發處理
        if self.concurrent_processor:
            results = self.concurrent_processor.process_batch_concurrent(
                image_paths,
                self.process_single_image,
                progress_callback
            )
        else:
            # 順序處理模式
            results = []
            for i, image_path in enumerate(image_paths, 1):
                result = self.process_single_image(image_path)
                results.append(result)
                progress_callback(i, len(image_paths), result)
        
        # 計算總結統計
        total_time = time.time() - self.start_time
        successful_results = [r for r in results if r.get('success', False)]
        
        summary = {
            'total_images': len(image_paths),
            'successful_images': len(successful_results),
            'failed_images': len(image_paths) - len(successful_results),
            'total_detections': self.total_detections,
            'total_processing_time': total_time,
            'average_processing_time': total_time / len(image_paths),
            'throughput_per_second': len(image_paths) / total_time,
            'results': results
        }
        
        self.logger.info(f"🎉 並發批量處理完成:")
        self.logger.info(f"   總圖像數: {summary['total_images']}")
        self.logger.info(f"   成功處理: {summary['successful_images']}")
        self.logger.info(f"   處理失敗: {summary['failed_images']}")
        self.logger.info(f"   總檢測數: {summary['total_detections']}")
        self.logger.info(f"   處理速度: {summary['throughput_per_second']:.2f} 張/秒")
        
        return summary
    
    def generate_performance_report(self) -> str:
        """生成性能報告"""
        try:
            if not self.performance_monitor:
                return "性能監控未啟用"
            
            # 導出性能指標
            reports_dir = Path(self.prod_config.OUTPUT_PATH) / "performance_reports"
            report_file = self.performance_monitor.export_metrics(
                str(reports_dir),
                format='json'
            )
            
            # 獲取性能摘要
            summary = self.performance_monitor.get_performance_summary()
            
            self.logger.info(f"📊 性能報告已生成: {report_file}")
            return report_file
            
        except Exception as e:
            self.logger.error(f"❌ 性能報告生成失敗: {str(e)}")
            return f"報告生成失敗: {str(e)}"
    
    def cleanup(self):
        """清理所有生產級組件"""
        self.logger.info("🧹 開始清理生產級組件...")
        
        try:
            if self.performance_monitor:
                self.performance_monitor.cleanup()
                self.logger.debug("✅ 性能監控器已清理")
            
            if self.memory_optimizer:
                self.memory_optimizer.cleanup()
                self.logger.debug("✅ 記憶體優化器已清理")
            
            if self.cache_manager:
                self.cache_manager.cleanup()
                self.logger.debug("✅ 緩存管理器已清理")
            
            if self.concurrent_processor:
                self.concurrent_processor.cleanup()
                self.logger.debug("✅ 並發處理器已清理")
            
            if self.plugin_manager:
                self.plugin_manager.cleanup()
                self.logger.debug("✅ 插件管理器已清理")
            
            if self.inference_system:
                self.inference_system.cleanup()
                self.logger.debug("✅ 推理系統已清理")
            
            self.logger.info("🧹 所有組件清理完成")
            
        except Exception as e:
            self.logger.error(f"❌ 組件清理失敗: {str(e)}")


def main():
    """主函數 - 生產級入口"""
    
    # 創建生產配置
    prod_config = ProductionConfig()
    
    # 創建生產級推理系統
    production_system = ProductionInferenceSystem(prod_config)
    
    try:
        # 顯示啟動信息
        print("🚀 統一YOLO推理系統 v3.0 - 生產級版本")
        print("=" * 70)
        print("🌟 企業級功能:")
        print(f"   ⚡ 並發處理: {'啟用' if prod_config.ENABLE_CONCURRENT_PROCESSING else '禁用'} ({prod_config.MAX_WORKERS} workers)")
        print(f"   💾 智能緩存: {'啟用' if prod_config.ENABLE_CACHING else '禁用'} ({prod_config.CACHE_SIZE_MB}MB)")
        print(f"   📊 性能監控: {'啟用' if prod_config.ENABLE_PERFORMANCE_MONITORING else '禁用'}")
        print(f"   🧠 記憶體優化: {'啟用' if prod_config.ENABLE_MEMORY_OPTIMIZATION else '禁用'}")
        print(f"   🧩 SAHI切片: {'啟用' if prod_config.ENABLE_SAHI else '禁用'}")
        print(f"   🧠 智能過濾: {'啟用' if prod_config.ENABLE_INTELLIGENT_FILTERING else '禁用'}")
        print(f"   🎨 三視圖: {'啟用' if prod_config.ENABLE_THREE_VIEW_OUTPUT else '禁用'}")
        print("=" * 70)
        
        # 初始化組件
        production_system.initialize_components()
        
        # 檢查輸入路徑
        if not Path(prod_config.INPUT_PATH).exists():
            raise ValueError(f"輸入路徑不存在: {prod_config.INPUT_PATH}")
        
        # 創建輸出目錄
        Path(prod_config.OUTPUT_PATH).mkdir(parents=True, exist_ok=True)
        
        # 執行推理
        input_path = Path(prod_config.INPUT_PATH)
        
        if input_path.is_file():
            # 單張圖像處理
            print(f"📸 處理單張圖像: {input_path.name}")
            result = production_system.process_single_image(str(input_path))
            
            if result['success']:
                detections = result.get('detections', [])
                print(f"✅ 處理完成，檢測到 {len(detections)} 個目標")
            else:
                print(f"❌ 處理失敗: {result.get('error', 'Unknown error')}")
        
        elif input_path.is_dir():
            # 批量目錄處理
            print(f"📁 批量處理目錄: {prod_config.INPUT_PATH}")
            summary = production_system.process_directory_concurrent(str(input_path))
            
            print(f"\n🎉 批量處理完成:")
            print(f"   📊 總圖像數: {summary['total_images']}")
            print(f"   ✅ 成功處理: {summary['successful_images']}")
            print(f"   ❌ 處理失敗: {summary['failed_images']}")
            print(f"   🎯 成功率: {summary['successful_images']/summary['total_images']*100:.1f}%")
            print(f"   🔍 總檢測數: {summary['total_detections']}")
            print(f"   ⏱️  處理速度: {summary['throughput_per_second']:.2f} 張/秒")
        
        else:
            raise ValueError(f"無效的輸入路徑: {prod_config.INPUT_PATH}")
        
        # 生成性能報告
        if prod_config.ENABLE_PERFORMANCE_REPORTS:
            print(f"\n📊 生成性能報告...")
            report_file = production_system.generate_performance_report()
            print(f"📋 性能報告: {report_file}")
        
        print(f"\n🎉 統一YOLO推理系統 v3.0 執行完成!")
        print(f"📁 輸出目錄: {prod_config.OUTPUT_PATH}")
        print(f"📋 日誌目錄: {prod_config.LOGS_PATH}")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用戶中斷執行")
        production_system.logger.warning("用戶中斷執行")
        
    except Exception as e:
        print(f"\n❌ 執行失敗: {str(e)}")
        production_system.logger.error(f"系統執行失敗: {str(e)}")
        raise
        
    finally:
        # 清理資源
        production_system.cleanup()


if __name__ == "__main__":
    main()