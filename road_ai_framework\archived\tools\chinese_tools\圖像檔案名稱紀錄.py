import os

def save_image_names_to_txt(folder_path, output_file="image_names.txt"):
    # 獲取資料夾中的所有 .jpg 文件（不區分大小寫）
    image_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.jpg')]
    
    # 按字母順序排序
    image_files.sort()
    
    # 寫入到文字檔
    with open(output_file, 'w', encoding='utf-8') as f:
        for image_name in image_files:
            f.write(image_name + '\n')
    
    print(f"成功將 {len(image_files)} 個 .jpg 檔名儲存到 {output_file}")

def main():
    # 輸入資料夾路徑
    folder_path = r'D:\image\road_crack\test_600_resize'
    
    # 檢查路徑是否存在
    if not os.path.isdir(folder_path):
        print(f"錯誤: {folder_path} 不是有效的資料夾路徑")
        return
    
    # 可選：自訂輸出檔名
    output_file = input("請輸入輸出文字檔名稱 (預設: image_names.txt): ").strip()
    if not output_file:
        output_file = r"D:\image\road_crack\image_names.txt"
    
    save_image_names_to_txt(folder_path, output_file)

if __name__ == "__main__":
    main()