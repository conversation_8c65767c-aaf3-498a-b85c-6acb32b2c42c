#!/usr/bin/env python3
"""
🧪 模型適配器單元測試
"""

import unittest
import numpy as np
from unittest.mock import Mock, patch, MagicMock

from ...core.model_adapter import ModelAdapter
from ...core.base_inference import ModelLoadError, InferenceProcessError, Detection
from ...config import ModelConfig
from ..test_base import BaseTestCase


class TestModelAdapter(BaseTestCase):
    """模型適配器測試"""
    
    def test_model_adapter_creation(self):
        """測試模型適配器創建"""
        model_config = ModelConfig(
            segmentation_model_path="test.pt",
            device="cpu",
            img_size=640
        )
        
        # 由於沒有真實模型，這會失敗，但我們可以測試配置
        with self.assertRaises(ModelLoadError):
            ModelAdapter(model_config)
    
    @patch('ultralytics.YOLO')
    def test_yolo_model_loading(self, mock_yolo):
        """測試YOLO模型載入（使用模擬）"""
        # 設置模擬
        mock_model = Mock()
        mock_model.names = {0: 'background', 2: 'linear_crack', 3: 'Alligator_crack'}
        mock_yolo.return_value = mock_model
        
        # 創建模擬模型文件
        test_model_path = self.temp_path / "test.pt"
        test_model_path.touch()
        
        model_config = ModelConfig(
            segmentation_model_path=str(test_model_path),
            device="cpu",
            img_size=640
        )
        
        # 這應該成功
        with patch('pathlib.Path.exists', return_value=True):
            adapter = ModelAdapter(model_config)
            self.assertEqual(adapter.model_type, "yolo")
            self.assertIsNotNone(adapter.model)
    
    def test_model_path_validation(self):
        """測試模型路徑驗證"""
        # 空路徑應該失敗
        model_config = ModelConfig()
        with self.assertRaises(ModelLoadError):
            ModelAdapter(model_config)
        
        # 不存在的路徑應該失敗
        model_config = ModelConfig(segmentation_model_path="/nonexistent/model.pt")
        with self.assertRaises(ModelLoadError):
            ModelAdapter(model_config)
    
    @patch('ultralytics.YOLO')
    def test_inference_execution(self, mock_yolo):
        """測試推理執行（使用模擬）"""
        # 設置模擬模型
        mock_model = Mock()
        mock_model.names = {2: 'linear_crack'}
        
        # 模擬推理結果
        mock_result = Mock()
        mock_boxes = Mock()
        mock_boxes.xyxy = [Mock()]
        mock_boxes.xyxy[0].cpu.return_value.numpy.return_value = [100, 100, 200, 200]
        mock_boxes.conf = [Mock()]
        mock_boxes.conf[0].cpu.return_value.numpy.return_value = 0.85
        mock_boxes.cls = [Mock()]
        mock_boxes.cls[0].cpu.return_value.numpy.return_value = 2
        
        mock_result.boxes = mock_boxes
        mock_result.masks = None
        
        mock_model.return_value = [mock_result]
        mock_yolo.return_value = mock_model
        
        # 創建適配器
        test_model_path = self.temp_path / "test.pt"
        test_model_path.touch()
        
        model_config = ModelConfig(
            segmentation_model_path=str(test_model_path),
            device="cpu"
        )
        
        with patch('pathlib.Path.exists', return_value=True):
            adapter = ModelAdapter(model_config)
            
            # 執行推理
            test_image = self.create_test_image()
            detections = adapter.inference(test_image)
            
            # 驗證結果
            self.assertIsInstance(detections, list)
            self.assertGreater(len(detections), 0)
            
            detection = detections[0]
            self.assertIsInstance(detection, Detection)
            self.assertEqual(detection.class_name, 'linear_crack')
            self.assertEqual(detection.confidence, 0.85)
    
    def test_model_info(self):
        """測試模型信息獲取"""
        model_config = ModelConfig(
            segmentation_model_path="test.pt",
            device="cpu",
            img_size=640
        )
        
        # 即使模型載入失敗，也應該能獲取配置信息
        try:
            adapter = ModelAdapter(model_config)
            info = adapter.get_model_info()
        except ModelLoadError:
            # 預期的錯誤，我們只測試信息結構
            info = {
                'model_type': None,
                'model_path': "test.pt",
                'device': "cpu",
                'img_size': 640
            }
        
        self.assertIn('model_path', info)
        self.assertIn('device', info)
        self.assertIn('img_size', info)
    
    def test_cleanup(self):
        """測試資源清理"""
        model_config = ModelConfig(device="cpu")
        
        # 創建模擬適配器
        adapter = ModelAdapter.__new__(ModelAdapter)
        adapter.config = model_config
        adapter.model = Mock()
        adapter.model_type = "yolo"
        
        # 測試清理
        adapter.cleanup()
        self.assertIsNone(adapter.model)


if __name__ == "__main__":
    unittest.main()