#!/usr/bin/env python3
"""
🚀 統一YOLO推理系統主入口
提供簡化的API接口，整合所有功能模組
"""

import logging
from pathlib import Path
from typing import Optional, Dict, Any, List

from .config import UnifiedConfig
from .core import UnifiedInferenceEngine, ModelAdapter
from .io import CSVManager, GroundTruthLoader, ResumeManager, LabelMeExporter, RealTimeReporter
from .utils import calculate_detection_statistics


class UnifiedYOLOInference:
    """
    統一YOLO推理系統主類
    
    提供簡化的API接口，封裝所有底層複雜度
    """
    
    def __init__(self, config: UnifiedConfig, enable_resume: bool = False, checkpoint_file: Optional[str] = None):
        """
        初始化統一推理系統
        
        Args:
            config: 統一配置對象
            enable_resume: 是否啟用中斷恢復功能
            checkpoint_file: 檢查點文件路徑
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.enable_resume = enable_resume
        
        # 初始化核心組件
        self.model_adapter = ModelAdapter(config.model)
        self.inference_engine = UnifiedInferenceEngine(config)
        self.csv_manager = CSVManager(config)
        self.gt_loader = GroundTruthLoader(config)
        
        # 🔄 初始化中斷恢復管理器
        self.resume_manager = None
        if enable_resume:
            self.resume_manager = ResumeManager(config, checkpoint_file)
            self.logger.info("🔄 中斷恢復功能已啟用")
        
        # 🏷️ 初始化LabelMe導出器
        self.labelme_exporter = None
        if config.output.enable_labelme_output:
            self.labelme_exporter = LabelMeExporter(config)
            self.logger.info("🏷️ LabelMe標註導出功能已啟用")
        
        # 📊 初始化即時統計報告器
        self.real_time_reporter = RealTimeReporter(
            output_dir=config.output_path,
            report_interval=10  # 每10張圖像輸出一次進度報告
        )
        
        self.logger.info("🚀 統一YOLO推理系統初始化完成")
    
    def process_single_image(self, image_path: str, 
                           enable_visualization: bool = True,
                           enable_statistics: bool = True) -> Dict[str, Any]:
        """
        處理單張圖像
        
        Args:
            image_path: 圖像路徑
            enable_visualization: 是否生成視覺化結果
            enable_statistics: 是否計算統計信息
            
        Returns:
            Dict: 處理結果，包含檢測結果、統計信息等
        """
        try:
            self.logger.info(f"📸 處理圖像: {Path(image_path).name}")
            
            # 執行推理
            result = self.inference_engine.predict(image_path, output_dir=self.config.output_path)
            
            # 載入GT標註（如果有）
            gt_annotations = None
            if enable_statistics:
                gt_annotations = self.gt_loader.load_gt_for_image(image_path)
            
            # 生成視覺化結果
            if enable_visualization:
                result.visualization_paths = self.inference_engine.generate_visualizations(
                    image_path, result.detections, gt_annotations
                )
            
            # 更新CSV統計
            if enable_statistics:
                self.csv_manager.update_statistics(
                    image_path=image_path,
                    detections=result.detections,
                    timing_info=result.timing,
                    confusion_metrics=result.metrics
                )
            
            # 🏷️ 導出LabelMe標註
            labelme_path = None
            if self.labelme_exporter and result.detections:
                labelme_path = self.labelme_exporter.export_image_annotations(
                    image_path=image_path,
                    detections=result.detections,
                    original_image_size=None  # 讓導出器自動讀取圖像尺寸
                )
            
            # 計算額外統計信息
            detection_stats = calculate_detection_statistics(result.detections)
            
            return {
                'success': True,
                'image_path': image_path,
                'detections': result.detections,
                'timing_info': result.timing,
                'confusion_metrics': result.metrics,
                'detection_statistics': detection_stats,
                'visualization_paths': getattr(result, 'visualization_paths', None),
                'labelme_annotation_path': labelme_path,
                'gt_available': gt_annotations is not None
            }
            
        except Exception as e:
            self.logger.error(f"❌ 圖像處理失敗 {image_path}: {str(e)}")
            return {
                'success': False,
                'image_path': image_path,
                'error': str(e)
            }
    
    def process_directory(self, input_dir: str, 
                         pattern: str = "*.jpg") -> Dict[str, Any]:
        """
        批量處理目錄中的圖像（支持中斷恢復）
        
        Args:
            input_dir: 輸入目錄路徑
            pattern: 圖像文件匹配模式
            
        Returns:
            Dict: 批量處理結果統計
        """
        try:
            import time
            start_time = time.time()
            
            input_path = Path(input_dir)
            if not input_path.exists():
                raise ValueError(f"輸入目錄不存在: {input_dir}")
            
            # 查找所有圖像文件
            image_files = list(input_path.glob(pattern))
            if not image_files:
                # 嘗試其他常見格式
                for ext in ["*.png", "*.jpeg", "*.bmp", "*.tiff"]:
                    image_files.extend(input_path.glob(ext))
            
            if not image_files:
                raise ValueError(f"未找到匹配的圖像文件: {input_dir}/{pattern}")
            
            # 轉換為字符串路徑
            image_paths = [str(img) for img in image_files]
            
            # 🔄 中斷恢復邏輯
            if self.enable_resume and self.resume_manager:
                if self.resume_manager.can_resume():
                    # 恢復現有會話
                    checkpoint = self.resume_manager.load_checkpoint()
                    pending_images = self.resume_manager.get_pending_images()
                    
                    # 顯示恢復信息
                    progress_summary = self.resume_manager.get_progress_summary()
                    self.logger.info(f"🔄 恢復處理會話:")
                    self.logger.info(f"   總圖像: {progress_summary['total_images']}")
                    self.logger.info(f"   已完成: {progress_summary['completed']}")
                    self.logger.info(f"   待處理: {progress_summary['pending']}")
                    self.logger.info(f"   失敗: {progress_summary['failed']}")
                    self.logger.info(f"   進度: {progress_summary['progress_percent']:.1f}%")
                    
                    # 處理待處理的圖像
                    images_to_process = pending_images
                else:
                    # 初始化新會話
                    self.resume_manager.initialize_session(image_paths)
                    images_to_process = image_paths
            else:
                # 不使用中斷恢復
                images_to_process = image_paths
            
            self.logger.info(f"📁 開始批量處理: {len(images_to_process)}張圖像")
            
            # 📊 啟動即時統計會話
            session_id = f"batch_{int(time.time())}"
            self.real_time_reporter.start_session(session_id, len(images_to_process))
            
            # 批量處理統計
            successful_images = 0
            failed_images = 0
            total_detections = 0
            results = []
            
            # 🎯 ROI 一致性管理：使用第一張圖像設定 ROI，後續沿用
            roi_initialized_for_batch = False
            
            for i, image_path in enumerate(images_to_process, 1):
                image_file = Path(image_path)
                self.logger.info(f"📸 處理進度: {i}/{len(images_to_process)} - {image_file.name}")
                
                # 🔄 標記圖像開始處理
                if self.resume_manager:
                    self.resume_manager.mark_image_start(image_path)
                
                # 🎯 ROI 設定邏輯（僅針對第一張圖像）
                if self.config.processing.roi.enabled:
                    if not roi_initialized_for_batch:
                        # 第一張圖像：設定 ROI
                        self.logger.info(f"🎯 使用第一張圖像設定ROI參考: {image_file.name}")
                        # 確保 ImageProcessor 被重置，以便用第一張圖像重新設定 ROI
                        if hasattr(self.inference_engine, 'image_processor') and self.inference_engine.image_processor:
                            self.inference_engine.image_processor.reset_roi()
                        roi_initialized_for_batch = True
                    else:
                        # 後續圖像：沿用第一張圖像的 ROI 設定
                        self.logger.debug(f"🎯 沿用已設定的ROI配置: {image_file.name}")
                
                # 📊 通知即時報告器開始處理
                self.real_time_reporter.update_image_start(image_path)
                
                # 處理單張圖像
                process_start_time = time.time()
                result = self.process_single_image(image_path)
                processing_duration = time.time() - process_start_time
                
                results.append(result)
                
                if result['success']:
                    successful_images += 1
                    detection_count = len(result['detections'])
                    total_detections += detection_count
                    
                    # 📊 更新即時統計報告
                    timing_info = result.get('timing_info')
                    self.real_time_reporter.update_image_completed(
                        image_path=image_path,
                        detections=result['detections'],
                        timing_info=timing_info,
                        success=True
                    )
                    
                    # 🔄 標記圖像完成
                    if self.resume_manager:
                        self.resume_manager.mark_image_completed(image_path, detection_count, processing_duration)
                        
                        # 每10張圖像顯示一次進度
                        if i % 10 == 0:
                            progress = self.resume_manager.get_progress_summary()
                            self.logger.info(f"📊 進度更新: {progress['progress_percent']:.1f}% " +
                                           f"({progress['completed']}/{progress['total_images']})")
                else:
                    failed_images += 1
                    
                    # 📊 更新即時統計報告（失敗情況）
                    self.real_time_reporter.update_image_completed(
                        image_path=image_path,
                        detections=[],
                        timing_info=None,
                        success=False,
                        error_message=result.get('error', 'Unknown error')
                    )
                    
                    # 🔄 標記圖像失敗
                    if self.resume_manager:
                        self.resume_manager.mark_image_failed(image_path, result.get('error', 'Unknown error'))
            
            # 保存最終統計
            self.csv_manager.save_final_statistics()
            
            # 🏷️ 批量導出LabelMe標註
            labelme_summary = None
            if self.labelme_exporter:
                labelme_summary = self.labelme_exporter.export_batch_annotations(results)
            
            # 🔄 處理完成後的清理
            if self.resume_manager:
                final_progress = self.resume_manager.get_progress_summary()
                
                # 如果所有圖像都處理完成，清理檢查點
                if final_progress['pending'] == 0 and final_progress['processing'] == 0:
                    failed_list = self.resume_manager.export_failed_list(
                        str(Path(self.config.output_path) / "failed_images.txt")
                    )
                    self.resume_manager.cleanup_checkpoint()
                    self.logger.info("✨ 所有圖像處理完成，檢查點已清理")
                    if failed_list:
                        self.logger.warning(f"⚠️ {len(failed_list)}張圖像處理失敗，詳情請查看 failed_images.txt")
            
            # 計算總處理時間
            total_time = time.time() - start_time
            
            # 📊 結束即時統計會話並生成最終報告
            final_report = self.real_time_reporter.end_session()
            
            # 計算實際統計（包含恢復會話的完整統計）
            if self.resume_manager:
                progress_summary = self.resume_manager.get_progress_summary()
                actual_total = progress_summary['total_images']
                actual_completed = progress_summary['completed']
                actual_failed = progress_summary['failed']
                actual_success_rate = progress_summary['progress_percent']
            else:
                actual_total = len(image_paths)
                actual_completed = successful_images
                actual_failed = failed_images
                actual_success_rate = successful_images / len(image_paths) * 100
            
            summary = {
                'total_images': actual_total,
                'successful_images': actual_completed,
                'failed_images': actual_failed,
                'success_rate': actual_success_rate,
                'total_detections': total_detections,
                'average_detections_per_image': total_detections / actual_completed if actual_completed > 0 else 0,
                'total_processing_time': total_time,
                'resume_enabled': self.enable_resume,
                'labelme_export_summary': labelme_summary,
                'real_time_report': final_report,
                'results': results
            }
            
            self.logger.info(f"✅ 批量處理完成: {actual_completed}/{actual_total}張成功 " +
                           f"(成功率: {actual_success_rate:.1f}%)")
            self.logger.info(f"⏱️ 總處理時間: {total_time:.2f}秒")
            
            return summary
            
        except Exception as e:
            # 🔄 異常情況下也要保存進度
            if self.resume_manager:
                self.resume_manager.save_checkpoint()
            
            # 📊 異常情況下也要結束統計會話
            if hasattr(self, 'real_time_reporter'):
                self.real_time_reporter.end_session()
            
            self.logger.error(f"❌ 批量處理失敗: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def cleanup(self):
        """清理系統資源"""
        self.logger.debug("🧹 清理統一推理系統資源")
        
        if hasattr(self, 'inference_engine'):
            self.inference_engine.cleanup()
        
        if hasattr(self, 'model_adapter'):
            self.model_adapter.cleanup()
        
        if hasattr(self, 'gt_loader'):
            self.gt_loader.cleanup()
        
        if hasattr(self, 'labelme_exporter'):
            self.labelme_exporter.cleanup()
        
        if hasattr(self, 'real_time_reporter'):
            self.real_time_reporter.cleanup()
    
    def __enter__(self):
        """上下文管理器進入"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self.cleanup()


def create_inference_system(config_path: Optional[str] = None,
                           config: Optional[UnifiedConfig] = None,
                           enable_resume: bool = False,
                           checkpoint_file: Optional[str] = None,
                           **kwargs) -> UnifiedYOLOInference:
    """
    工廠函數：創建統一推理系統
    
    Args:
        config_path: 配置文件路徑
        config: 配置對象（直接傳遞）
        enable_resume: 是否啟用中斷恢復功能
        checkpoint_file: 檢查點文件路徑
        **kwargs: 配置覆蓋參數
        
    Returns:
        UnifiedYOLOInference: 統一推理系統實例
    """
    # 優先使用直接傳遞的配置對象
    if config is not None:
        target_config = config
    elif config_path:
        target_config = UnifiedConfig.from_yaml(config_path)
    else:
        target_config = UnifiedConfig()
    
    # 應用覆蓋參數
    for key, value in kwargs.items():
        if hasattr(target_config, key):
            setattr(target_config, key, value)
    
    return UnifiedYOLOInference(target_config, enable_resume=enable_resume, checkpoint_file=checkpoint_file)


def main():
    """
    主函數入口
    適用於命令行直接執行
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="統一YOLO推理系統")
    parser.add_argument("--config", type=str, help="配置文件路徑")
    parser.add_argument("--input", type=str, required=True, help="輸入圖像或目錄路徑")
    parser.add_argument("--model", type=str, help="模型文件路徑")
    parser.add_argument("--output", type=str, help="輸出目錄路徑")
    parser.add_argument("--sahi", action="store_true", help="啟用SAHI切片推理")
    parser.add_argument("--verbose", "-v", action="store_true", help="詳細輸出")
    
    args = parser.parse_args()
    
    # 設置日誌級別
    if args.verbose:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)
    
    # 創建配置
    config_kwargs = {}
    if args.model:
        config_kwargs['model_path'] = args.model
    if args.output:
        config_kwargs['output_dir'] = args.output
    if args.sahi:
        config_kwargs['enable_sahi'] = True
    
    # 創建推理系統
    try:
        with create_inference_system(args.config, **config_kwargs) as inference_system:
            input_path = Path(args.input)
            
            if input_path.is_file():
                # 單張圖像處理
                result = inference_system.process_single_image(str(input_path))
                if result['success']:
                    print(f"✅ 處理成功: {len(result['detections'])}個檢測結果")
                else:
                    print(f"❌ 處理失敗: {result['error']}")
            
            elif input_path.is_dir():
                # 批量處理
                summary = inference_system.process_directory(str(input_path))
                if 'success_rate' in summary:
                    print(f"✅ 批量處理完成: {summary['success_rate']:.1f}%成功率")
                    print(f"📊 總檢測數: {summary['total_detections']}")
                else:
                    print(f"❌ 批量處理失敗: {summary['error']}")
            
            else:
                print(f"❌ 輸入路徑無效: {args.input}")
                
    except Exception as e:
        print(f"❌ 系統錯誤: {str(e)}")


if __name__ == "__main__":
    main()