# 🧭 全倉整理提案與搬遷清單（草案）

> 生成時間：2025-08-08  
> 整理範圍：d:\99_AI_model（整個工作區）  
> 目標：在不影響核心功能與測試的前提下，將舊版/重複/零散腳本與文檔歸檔到 old_code，並建立清晰的目錄結構與使用導引

---

## 1) 高層結構總覽（現況）

- 核心框架（現代化）
  - road_ai_framework/ （完整、條理清晰，包含 core/models/inference_system/...）
  - road_ai_framework/docs/（已含完整整理報告與說明）
  - road_ai_framework/examples/（完整使用示例）
  - road_ai_framework/tests/（測試模組）
- 倉庫根目錄（混雜內容較多）
  - docs/（API 文檔）
  - examples/（與 road_ai_framework/examples 重複）
  - 多個單檔腳本：color_fix_demo.py、coordinate_converter.py、fix_save_results_error.py、resize_labelme_dataset.py 等
  - 多個臨時/輸出資料夾：aug_shear_perspective_outputs、augmented_outputs、output、reports、tttest 等
  - 多個測試/檢查腳本：test_*.py（同時也有 tests/ 測試套件）
  - AI模型建構訓練驗證/（可能為舊訓練程式；CI 腳本引用）
  - 資料前處理/（與 road_ai_framework/data_processing 重疊）
  - old_code/（既有歸檔區）

---

## 2) 整理原則

- 安全優先：不移動目前被腳本或測試套件依賴的資料/路徑（特別是 test_image、test_gt_data、model_registry/registry.db、CI 引用的資料夾）
- 合併重複：根目錄 examples/ 與 road_ai_framework/examples/ 重複，保留後者，前者歸檔
- 歸檔舊版：個別零散工具腳本、舊訓練/前處理模組以「可回溯」方式移至 old_code
- 輸出統一：影像示例/中間輸出移至 output/ 或 old_code/artifacts，避免污染根目錄
- 文檔單點：高層文檔集中到 road_ai_framework/docs/ 與根 docs/，舊版說明歸檔

---

## 3) 建議的目標結構（精簡版）

- road_ai_framework/（保留）
  - core/、models/、inference_system/、data_processing/、configs/、examples/、tests/ ...（現況保持）
  - docs/（主文檔，包含完整整理報告）
- docs/（保留）
  - api/（API 文檔，保留）
  - 📋_全倉整理_提案與搬遷清單.md（本文件）
- tests/（保留現有 pytest 測試）
- output/、reports/（保留，統一中間輸出）
- old_code/（歸檔區，新增分區）
  - scripts_legacy/
  - examples_root_legacy/
  - data_processing_legacy/
  - training_legacy/
  - docs_legacy/
  - artifacts/

---

## 4) 建議搬遷清單（待您確認）

說明：以下為「首批安全搬遷」。標註「需確認」表示可能被 CI 或腳本引用，建議先保留或改腳本後再移動。

### A. 根目錄 examples/ → old_code/examples_root_legacy/
- examples/**（整包搬遷）
  - 理由：與 road_ai_framework/examples/ 功能重疊；保留後者

### B. 零散工具腳本 → old_code/scripts_legacy/
- color_fix_demo.py
- coordinate_converter.py
- diagnose_user_test_data.py
- fix_save_results_error.py
- generate_docs.py、generate_docs_simple.py（若改用 road_ai_framework/docs 生成流程，則歸檔）
- image_sample_extractor.py（已在 road_ai_framework/tools 提供替代）
- import_helper.py（road_ai_framework/core/import_helper.py 為主）
- improved_gt_loading.py
- resize_labelme_dataset.py
- simple_debug_gt.py
- run_tests.py（如已用 scripts/ci_cd_automation.py 或 tests/test_suite.py，則歸檔）
- deepseek_python_20250703_*.py（臨時研究腳本）

### C. 臨時輸出/示例影像 → output/ 或 old_code/artifacts/
- aug_shear_perspective_outputs/ → output/samples/ 或 old_code/artifacts/
- augmented_outputs/ → output/samples/ 或 old_code/artifacts/
- tttest/ → output/samples/ 或 old_code/artifacts/
- ultimate_yolo.log（若僅作參考）→ old_code/artifacts/logs/

注意：以下保留不動（避免測試/範例壞掉）
- test_image/、test_image2/、test_gt_data/、test_output_gt/

### D. 舊訓練與前處理模組（需確認）
- 資料前處理/ → old_code/data_processing_legacy/ 〈需確認〉
  - 理由：road_ai_framework/data_processing/ 已提供現代化版本
- AI模型建構訓練驗證/ → old_code/training_legacy/ 〈需確認〉
  - 注意：scripts/ci_cd_automation.py 內部 required_dirs 仍引用該資料夾（移動前需一併調整）

### E. 舊版/重複文檔 → old_code/docs_legacy/
- 根目錄下過期/重複 md（若與 road_ai_framework/docs/ 內容重複者）
  - e.g. gemini.md、CLAUDE.md 等助理說明稿（如無長期價值可歸檔）

---

## 5) 風險與相容性檢查

- 測試套件相依：root 下 test_*.py 與 tests/ 並存，暫不搬動 root 的測試腳本，避免路徑耦合破壞
- CI 腳本（scripts/ci_cd_automation.py）引用 required_dirs：
  - 'AI模型建構訓練驗證/model_create'、'資料前處理'、'tests'、'test_image'
  - 若要搬遷 D 段目錄，需同步更新 CI 檢查邏輯或替換為 road_ai_framework 下的新路徑
- model_registry/registry.db：若有運行時使用，建議保留現位置（或在配置中明確掛載路徑）

---

## 6) 執行計畫（建議流程）

1) 您確認本清單（可劃掉不想搬或新增要搬項目）
2) 我執行第一批「安全搬遷」：A、B、C（僅移不影響測試/CI 的項目）
3) 跑快速驗證：
   - pytest -q
   - python scripts/ci_cd_automation.py --skip-quality（如需）
4) 第二批「需確認項」：D、E 視 CI/測試結果與您需求調整
5) 更新導引：在 README 與 road_ai_framework/docs 增加搬遷說明與舊檔案指向

---

## 7) 之後可做的優化（選做）

- 建立 datasets/ 目錄，集中 test_image/test_gt_data 的示例測資（待測試引用調整）
- 建立 scripts/ 資料夾（根層），集中保留仍會直接呼叫的實用腳本
- 為 road_ai_framework 增加 Makefile/pack 脚本（快速測試/打包）

---

## 8) 下一步需要您的回覆

- 是否同意立即執行 A、B、C 三批搬遷？（安全批次，不影響 CI/測試）
- D、E 兩批是否要搬？若要搬，是否同意我同步調整 scripts/ci_cd_automation.py 的引用？

我可以在您確認後，一次性完成搬遷與驗證；過程中會保留舊路徑備援與完整變更紀錄，確保隨時可回退。
