#!/usr/bin/env python3
"""
測試增強版ROI預覽功能
"""

import sys
from pathlib import Path
import os

# 添加路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_roi_preview_with_test_image():
    """使用測試圖像測試ROI預覽功能"""
    print("🖼️ 測試ROI預覽功能...")
    
    try:
        # 檢查測試圖像目錄（適配Linux路徑）
        test_image_dir = Path("/mnt/d/image/5_test_image_test/test64")
        if not test_image_dir.exists():
            print(f"❌ 測試圖像目錄不存在: {test_image_dir}")
            print("💡 嘗試檢查其他可能的路徑...")
            
            # 嘗試其他可能的路徑
            alt_paths = [
                Path("/mnt/d/image/5_test_image_test/測試影像"),  # 直接指向測試影像目錄
                Path("/mnt/d/image/5_test_image_test"),
                Path("./test_images"),
                Path("../test_images")
            ]
            
            for alt_path in alt_paths:
                if alt_path.exists():
                    test_image_dir = alt_path
                    print(f"✅ 找到測試圖像目錄: {test_image_dir}")
                    break
            else:
                print(f"❌ 無法找到測試圖像目錄")
                return False
        
        # 找到第一張圖像
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        test_image = None
        for ext in image_extensions:
            images = list(test_image_dir.glob(f"*{ext}"))
            if images:
                test_image = images[0]
                break
        
        if not test_image:
            print(f"❌ 在 {test_image_dir} 中未找到測試圖像")
            return False
        
        print(f"✅ 找到測試圖像: {test_image}")
        
        # 創建配置
        from models.inference.config_manager import UnifiedYOLOConfigManager
        config_manager = UnifiedYOLOConfigManager()
        
        # 設置ROI參數
        config_manager.sahi.enable_sahi_roi = True
        config_manager.sahi.roi_top_ratio = 3.0
        config_manager.sahi.roi_bottom_ratio = 2.8
        config_manager.sahi.roi_left_ratio = 3.0
        config_manager.sahi.roi_right_ratio = 4.0
        config_manager.visualization.enable_roi_preview = True
        
        print(f"✅ ROI配置設置完成:")
        print(f"   top={config_manager.sahi.roi_top_ratio}, bottom={config_manager.sahi.roi_bottom_ratio}")
        print(f"   left={config_manager.sahi.roi_left_ratio}, right={config_manager.sahi.roi_right_ratio}")
        
        # 創建推理實例（不加載模型，只測試ROI預覽）
        from models.inference.unified_yolo_inference import UnifiedYOLOInference
        inference = UnifiedYOLOInference(config_manager)
        
        # 設置輸出目錄（適配Linux路徑）
        output_dir = Path("/mnt/d/image/5_test_image_test/test64_out")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 測試ROI功能
        roi_enabled = inference._check_roi_enabled()
        print(f"✅ ROI啟用狀態: {roi_enabled}")
        
        if roi_enabled:
            # 直接調用ROI預覽生成
            print(f"🎯 開始生成ROI預覽...")
            inference._save_roi_preview(str(test_image), str(output_dir))
            
            # 檢查預覽圖是否生成
            preview_path = output_dir / "roi_preview.jpg"
            if preview_path.exists():
                print(f"🎉 ROI預覽圖生成成功!")
                print(f"   預覽圖路徑: {preview_path}")
                print(f"   檔案大小: {preview_path.stat().st_size / 1024:.1f} KB")
                return True
            else:
                print(f"❌ ROI預覽圖未生成")
                return False
        else:
            print(f"❌ ROI功能未啟用")
            return False
            
    except Exception as e:
        print(f"❌ ROI預覽測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_roi_parameters_display():
    """測試ROI參數顯示功能"""
    print("\n📊 測試ROI參數顯示...")
    
    # 測試不同的ROI參數組合
    test_configs = [
        {"top": 2.0, "bottom": 2.0, "left": 2.0, "right": 2.0, "desc": "中心區域"},
        {"top": 3.0, "bottom": 2.8, "left": 3.0, "right": 4.0, "desc": "不對稱區域"},
        {"top": 4.0, "bottom": 4.0, "left": 4.0, "right": 4.0, "desc": "較小區域"},
        {"top": 1.5, "bottom": 1.5, "left": 1.5, "right": 1.5, "desc": "較大區域"},
    ]
    
    for i, config in enumerate(test_configs, 1):
        print(f"   配置 {i} ({config['desc']}):")
        
        # 模擬圖像尺寸
        w, h = 1500, 1000
        
        # 計算ROI邊界
        crop_factor_top = (config['top'] - 1) / 8
        crop_factor_bottom = (config['bottom'] - 1) / 8
        crop_factor_left = (config['left'] - 1) / 8
        crop_factor_right = (config['right'] - 1) / 8
        
        y1_roi = int(h * crop_factor_top)
        y2_roi = int(h - h * crop_factor_bottom)
        x1_roi = int(w * crop_factor_left)
        x2_roi = int(w - w * crop_factor_right)
        
        # 計算ROI尺寸和比例
        roi_width = x2_roi - x1_roi
        roi_height = y2_roi - y1_roi
        width_ratio = roi_width / w * 100
        height_ratio = roi_height / h * 100
        
        print(f"      參數: T={config['top']}, B={config['bottom']}, L={config['left']}, R={config['right']}")
        print(f"      邊界: ({x1_roi}, {y1_roi}) -> ({x2_roi}, {y2_roi})")
        print(f"      尺寸: {roi_width}x{roi_height} ({width_ratio:.1f}% x {height_ratio:.1f}%)")
        
        # 檢查有效性
        if roi_width > 0 and roi_height > 0:
            print(f"      ✅ 配置有效")
        else:
            print(f"      ❌ 配置無效")
        print()

def main():
    """主測試函數"""
    print("=" * 60)
    print("🧪 ROI預覽功能測試")
    print("=" * 60)
    
    tests = [test_roi_parameters_display, test_roi_preview_with_test_image]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 測試失敗: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 測試總結:")
    print(f"   總測試數: {len(tests)}")
    print(f"   通過測試: {sum(1 for r in results if r is not False)}")
    print(f"   失敗測試: {sum(1 for r in results if r is False)}")
    
    if all(r is not False for r in results):
        print("\n🎉 ROI預覽功能測試成功!")
        print("\n✅ 現在你可以:")
        print("   - 運行 run_unified_yolo.py 查看ROI預覽")
        print("   - 在輸出目錄找到 roi_preview.jpg")
        print("   - 看到清晰的ROI區域標示和參數說明")
    else:
        print("\n❌ 部分測試失敗")
    
    print("=" * 60)

if __name__ == "__main__":
    main()