# 🐳 統一YOLO推理系統 - 生產級Docker鏡像
FROM nvidia/cuda:11.8-devel-ubuntu20.04

# 設置環境變量
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_VISIBLE_DEVICES=0

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    git \
    wget \
    curl \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 設置工作目錄
WORKDIR /app

# 複製requirements文件
COPY requirements.txt .

# 安裝Python依賴
RUN pip3 install --no-cache-dir -r requirements.txt

# 複製應用代碼
COPY . .

# 創建必要目錄
RUN mkdir -p /app/data/input \
    /app/data/output \
    /app/data/cache \
    /app/data/logs \
    /app/data/models

# 設置權限
RUN chmod +x /app/run_unified_yolo_v3.py
RUN chmod +x /app/docker/entrypoint.sh

# 暴露端口
EXPOSE 8000

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 入口點
ENTRYPOINT ["/app/docker/entrypoint.sh"]

# 默認命令
CMD ["api"]