#!/usr/bin/env python3
"""
🧪 測試SAHI融合策略功能
驗證IoU>0.1同類別合併功能是否正確實現
"""

import sys
from pathlib import Path
import numpy as np

# 添加路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from road_ai_framework.inference_system.processing.fusion_engine import FusionEngine
    from road_ai_framework.inference_system.config.unified_config import (
        FusionConfig, FusionStrategy
    )
    from road_ai_framework.inference_system.core.base_inference import Detection

    print("🧪 測試SAHI融合策略功能")
    print("=" * 50)

    # 創建測試配置
    fusion_config = FusionConfig(
        strategy=FusionStrategy.SAHI_OVERLAP_MERGE,
        sahi_merge_iou_threshold=0.1,
        enable_mask_iou_calculation=True,
        sahi_merge_confidence_strategy="max"
    )

    # 創建融合引擎
    fusion_engine = FusionEngine(fusion_config)
    
    print("✅ 融合引擎創建成功")

    # 測試場景1: 創建兩個高重疊的同類別檢測
    det1 = Detection(
        bbox=[100.0, 100.0, 200.0, 200.0],  # 100x100的框
        confidence=0.8,
        class_id=2,  # linear_crack
        class_name="linear_crack"
    )

    det2 = Detection(
        bbox=[150.0, 150.0, 250.0, 250.0],  # 部分重疊的框
        confidence=0.9,
        class_id=2,  # 同類別
        class_name="linear_crack"
    )

    det3 = Detection(
        bbox=[300.0, 300.0, 400.0, 400.0],  # 不重疊的框
        confidence=0.7,
        class_id=2,
        class_name="linear_crack"
    )

    det4 = Detection(
        bbox=[120.0, 120.0, 220.0, 220.0],  # 與det1高度重疊
        confidence=0.6,
        class_id=3,  # 不同類別
        class_name="Alligator_crack"
    )

    test_detections = [det1, det2, det3, det4]
    print(f"📊 輸入檢測: {len(test_detections)}個")
    
    # 手動計算IoU驗證
    def calculate_iou(bbox1, bbox2):
        x1_max = max(bbox1[0], bbox2[0])
        y1_max = max(bbox1[1], bbox2[1])
        x2_min = min(bbox1[2], bbox2[2])
        y2_min = min(bbox1[3], bbox2[3])
        
        if x2_min <= x1_max or y2_min <= y1_max:
            return 0.0
        
        intersection = (x2_min - x1_max) * (y2_min - y1_max)
        area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
        area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0

    # 驗證IoU計算
    iou_1_2 = calculate_iou(det1.bbox, det2.bbox)
    iou_1_3 = calculate_iou(det1.bbox, det3.bbox)
    iou_1_4 = calculate_iou(det1.bbox, det4.bbox)
    
    print(f"   Det1 vs Det2 (同類別): IoU = {iou_1_2:.3f}")
    print(f"   Det1 vs Det3 (同類別): IoU = {iou_1_3:.3f}")
    print(f"   Det1 vs Det4 (不同類別): IoU = {iou_1_4:.3f}")

    # 執行SAHI融合
    print("\n🔀 執行SAHI融合...")
    result = fusion_engine.fuse(test_detections)
    
    print(f"📈 融合結果: {len(result)}個檢測")
    
    for i, det in enumerate(result):
        print(f"   結果{i+1}: 類別{det.class_id}({det.class_name}), 置信度{det.confidence:.3f}, bbox{det.bbox}")
        if hasattr(det, 'merged_from_count'):
            print(f"           合併自{det.merged_from_count}個檢測")

    # 驗證預期結果
    linear_crack_results = [det for det in result if det.class_id == 2]
    alligator_crack_results = [det for det in result if det.class_id == 3]
    
    print(f"\n🎯 結果分析:")
    print(f"   linear_crack檢測數: {len(linear_crack_results)}")
    print(f"   Alligator_crack檢測數: {len(alligator_crack_results)}")
    
    # 預期：det1和det2應該被合併（IoU>0.1且同類別）
    # det3應該保持獨立（不重疊）
    # det4應該保持獨立（不同類別）
    
    if len(result) <= len(test_detections):
        print("✅ 融合成功：檢測數量減少，說明有合併發生")
    else:
        print("❌ 融合異常：檢測數量未減少")

    # 獲取統計信息
    stats = fusion_engine.get_statistics()
    print(f"\n📊 融合統計:")
    print(f"   策略: {stats['configured_strategy']}")
    print(f"   輸入: {stats['input_detections']}")
    print(f"   輸出: {stats['output_detections']}")
    print(f"   減少率: {stats['reduction_ratio']:.2%}")

    print("\n🎉 SAHI融合策略測試完成！")

except Exception as e:
    print(f"❌ 測試失敗: {e}")
    import traceback
    traceback.print_exc()