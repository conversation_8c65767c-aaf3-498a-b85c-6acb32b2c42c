import os
import shutil

def copy_images_to_json_folder(json_folder, image_folder):
    """依 JSON 檔搬對應圖片到 json_folder"""
    json_files = [f for f in os.listdir(json_folder) if f.endswith('.json')]
    image_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif')
    image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(image_extensions)]

    # 建立圖片檔名對照表（不分大小寫）
    image_map = {os.path.splitext(f)[0].lower(): f for f in image_files}
    copied_count = 0

    for json_file in json_files:
        base_name = os.path.splitext(json_file)[0].lower()
        if base_name in image_map:
            image_file = image_map[base_name]
            src_path = os.path.join(image_folder, image_file)
            dst_path = os.path.join(json_folder, image_file)
            if not os.path.exists(dst_path):
                shutil.copy2(src_path, dst_path)
                copied_count += 1
                print(f"[JSON→IMG] 已複製: {image_file} -> {dst_path}")
        else:
            print(f"[JSON→IMG] 警告: 找不到 {json_file} 的對應圖像文件")

    print(f"[JSON→IMG] 完成! 共複製了 {copied_count} 個圖像文件。")


def copy_json_to_image_folder(json_folder, image_folder):
    """依圖片檔搬對應 JSON 到 image_folder"""
    json_files = [f for f in os.listdir(json_folder) if f.endswith('.json')]
    image_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif')
    image_files = [f for f in os.listdir(image_folder) if f.lower().endswith(image_extensions)]

    # 建立 JSON 檔名對照表（不分大小寫）
    json_map = {os.path.splitext(f)[0].lower(): f for f in json_files}
    copied_count = 0

    for image_file in image_files:
        base_name = os.path.splitext(image_file)[0].lower()
        if base_name in json_map:
            json_file = json_map[base_name]
            src_path = os.path.join(json_folder, json_file)
            dst_path = os.path.join(image_folder, json_file)
            if not os.path.exists(dst_path):
                shutil.copy2(src_path, dst_path)
                copied_count += 1
                print(f"[IMG→JSON] 已複製: {json_file} -> {dst_path}")
        else:
            print(f"[IMG→JSON] 警告: 找不到 {image_file} 的對應 JSON 文件")

    print(f"[IMG→JSON] 完成! 共複製了 {copied_count} 個 JSON 文件。")


def main():
    # 手動設定資料夾路徑
    json_folder = r'\\192.168.1.46\RD_Universe\專案\2025_2.衛工處孔蓋辨識\1.專案執行\所有圖像資料\1.標註資料\2.車底影像_答案\all\output'
    image_folder = r'\\192.168.1.46\RD_Universe\專案\2025_2.衛工處孔蓋辨識\1.專案執行\所有圖像資料\1.標註資料\2.車底影像_答案\all\output\車底'

    if not os.path.isdir(json_folder):
        print(f"錯誤: {json_folder} 不是有效的資料夾路徑")
        return
    if not os.path.isdir(image_folder):
        print(f"錯誤: {image_folder} 不是有效的資料夾路徑")
        return

    # 先搬圖片到 JSON 資料夾
    copy_images_to_json_folder(json_folder, image_folder)

    # 再搬 JSON 到圖片資料夾
    copy_json_to_image_folder(json_folder, image_folder)


if __name__ == "__main__":
    main()
