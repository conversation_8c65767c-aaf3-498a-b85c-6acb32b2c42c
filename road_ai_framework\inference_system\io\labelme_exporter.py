#!/usr/bin/env python3
"""
🏷️ LabelMe標註導出器
支援seg模型精準mask輸出、det模型box輸出、point調整功能
用於產生後續訓練資料
"""

import json
import base64
import logging
import numpy as np
import cv2
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
from io import BytesIO
from PIL import Image

from ..core.base_inference import Detection
from ..config import UnifiedConfig


class LabelMeExporter:
    """
    LabelMe標註導出器
    
    核心功能:
    - seg模型：導出精準mask輪廓
    - det模型：導出bounding box
    - point調整：為每個檢測添加關鍵點
    - 完整LabelMe JSON格式支持
    """
    
    def __init__(self, config: UnifiedConfig):
        """
        初始化LabelMe導出器
        
        Args:
            config: 統一配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 輸出目錄
        self.output_dir = None
        if config.output_path:
            self.output_dir = Path(config.output_path) / config.output.labelme_output_dirname
            self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 導出參數
        self.include_points = getattr(config.output, 'labelme_include_points', True)
        self.include_center_points = getattr(config.output, 'labelme_include_center_points', True)  # 🆕
        self.point_radius = getattr(config.output, 'labelme_point_radius', 3)
        self.export_mode = getattr(config.output, 'labelme_export_mode', 'auto')
        
        # 🎯 點數量控制參數
        self.max_polygon_points = getattr(config.output, 'labelme_max_polygon_points', 50)
        self.min_polygon_points = getattr(config.output, 'labelme_min_polygon_points', 8)
        self.simplify_epsilon = getattr(config.output, 'labelme_simplify_epsilon', 2.0)
        self.adaptive_simplify = getattr(config.output, 'labelme_adaptive_simplify', True)
        self.large_object_threshold = getattr(config.output, 'labelme_large_object_threshold', 10000)
        self.small_object_max_points = getattr(config.output, 'labelme_small_object_max_points', 20)
        self.large_object_max_points = getattr(config.output, 'labelme_large_object_max_points', 100)
        self.enable_point_optimization = getattr(config.output, 'labelme_enable_point_optimization', True)
        self.optimization_mode = getattr(config.output, 'labelme_optimization_mode', 'balanced')
        
        # 統計信息
        self.stats = {
            'total_exported': 0,
            'seg_annotations': 0,
            'det_annotations': 0,
            'point_annotations': 0,
            'export_errors': 0
        }
        
        self.logger.info("🏷️ LabelMe標註導出器初始化完成")
        self.logger.info(f"   輸出目錄: {self.output_dir}")
        self.logger.info(f"   導出模式: {self.export_mode}")
        self.logger.info(f"   包含點標記: {self.include_points}")
        self.logger.info(f"   🎯 點數量控制: {self.min_polygon_points}-{self.max_polygon_points}點")
        if self.adaptive_simplify:
            self.logger.info(f"   🧠 自適應簡化: 小目標≤{self.small_object_max_points}點, 大目標≤{self.large_object_max_points}點")
        self.logger.info(f"   ⚡ 性能優化: {self.optimization_mode}模式")
    
    def _image_to_base64(self, image_path: str) -> str:
        """
        將圖像轉換為base64編碼
        
        Args:
            image_path: 圖像路徑
            
        Returns:
            str: base64編碼字符串
        """
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
                return base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            self.logger.error(f"❌ 圖像base64編碼失敗: {e}")
            return ""
    
    def _mask_to_polygon(self, mask: np.ndarray, detection_area: float = None) -> List[List[float]]:
        """
        將mask轉換為多邊形點列表 - 支援智能點數控制
        
        Args:
            mask: 二值化mask
            detection_area: 檢測目標的面積（用於自適應簡化）
            
        Returns:
            List[List[float]]: 多邊形點列表 [[x1,y1], [x2,y2], ...]
        """
        try:
            # 確保mask是二值化的
            if mask.dtype != np.uint8:
                mask = (mask * 255).astype(np.uint8)
            
            if len(mask.shape) == 3:
                mask = cv2.cvtColor(mask, cv2.COLOR_BGR2GRAY)
            
            # 查找輪廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return []
            
            # 選擇最大的輪廓
            largest_contour = max(contours, key=cv2.contourArea)
            
            # 🧠 智能簡化參數計算
            if self.adaptive_simplify and detection_area is not None:
                # 根據目標大小調整參數
                if detection_area < self.large_object_threshold:
                    # 小目標：更簡化
                    target_max_points = self.small_object_max_points
                    epsilon_factor = 3.0
                else:
                    # 大目標：保持更多細節
                    target_max_points = self.large_object_max_points
                    epsilon_factor = 1.5
            else:
                target_max_points = self.max_polygon_points
                epsilon_factor = self.simplify_epsilon
            
            # ⚡ 性能優化模式
            if self.optimization_mode == "fast":
                epsilon_factor *= 2.0  # 更簡化
                target_max_points = min(target_max_points, 30)
            elif self.optimization_mode == "accurate":
                epsilon_factor *= 0.5  # 更精細
                target_max_points = min(target_max_points, 150)
            # balanced模式保持默認設定
            
            # 得到初始輪廓簡化
            simplified_contour = largest_contour
            
            # 逐步簡化策略：確保點數在目標範圍內
            if self.enable_point_optimization:
                epsilon_base = epsilon_factor * cv2.arcLength(largest_contour, True) / 100
                epsilon_multiplier = 1.0
                max_iterations = 10  # 防止無限迴圈
                
                for iteration in range(max_iterations):
                    epsilon = epsilon_base * epsilon_multiplier
                    simplified_contour = cv2.approxPolyDP(largest_contour, epsilon, True)
                    
                    current_points = len(simplified_contour)
                    
                    # 檢查點數是否符合目標
                    if current_points <= target_max_points and current_points >= self.min_polygon_points:
                        break
                    elif current_points > target_max_points:
                        # 點數過多，增加簡化程度
                        epsilon_multiplier *= 1.5
                    else:
                        # 點數過少，減少簡化程度
                        epsilon_multiplier *= 0.7
                        
                        # 如果點數太少，停止進一步簡化
                        if current_points < self.min_polygon_points:
                            break
            else:
                # 簡單簡化
                epsilon = epsilon_factor * cv2.arcLength(largest_contour, True) / 100
                simplified_contour = cv2.approxPolyDP(largest_contour, epsilon, True)
            
            # 轉換為點列表
            points = []
            for point in simplified_contour:
                x, y = point[0]
                points.append([float(x), float(y)])
            
            # 最終點數檢查和警告
            if len(points) > self.max_polygon_points:
                self.logger.debug(f"⚠️ 多邊形點數過多: {len(points)} > {self.max_polygon_points}，可能影響性能")
            elif len(points) < 3:
                self.logger.warning(f"⚠️ 多邊形點數過少: {len(points)}，降級為矩形")
                return []
            
            self.logger.debug(f"🎯 多邊形簡化完成: {len(points)}點 面積={detection_area:.0f}px² 模式={self.optimization_mode}")
            
            return points
            
        except Exception as e:
            self.logger.error(f"❌ mask轉多邊形失敗: {e}")
            return []
    
    def _generate_center_point(self, bbox: List[float]) -> List[float]:
        """
        生成檢測框中心點
        
        Args:
            bbox: [x1, y1, x2, y2] 格式的邊界框
            
        Returns:
            List[float]: [x, y] 中心點座標
        """
        x1, y1, x2, y2 = bbox
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2
        return [float(center_x), float(center_y)]
    
    def _generate_corner_points(self, bbox: List[float]) -> List[List[float]]:
        """
        生成檢測框四個角點
        
        Args:
            bbox: [x1, y1, x2, y2] 格式的邊界框
            
        Returns:
            List[List[float]]: 四個角點座標列表
        """
        x1, y1, x2, y2 = bbox
        return [
            [float(x1), float(y1)],  # 左上
            [float(x2), float(y1)],  # 右上
            [float(x2), float(y2)],  # 右下
            [float(x1), float(y2)]   # 左下
        ]
    
    def _create_shape_from_detection(self, detection: Detection, shape_type: str = "auto") -> Dict[str, Any]:
        """
        從檢測結果創建LabelMe shape
        
        Args:
            detection: 檢測結果
            shape_type: 形狀類型 ("polygon", "rectangle", "point", "auto")
            
        Returns:
            Dict: LabelMe shape字典
        """
        try:
            # 自動決定形狀類型
            if shape_type == "auto":
                if hasattr(detection, 'mask') and detection.mask is not None:
                    shape_type = "polygon"  # seg模型使用多邊形
                else:
                    shape_type = "rectangle"  # det模型使用矩形
            
            # 基本shape結構
            shape = {
                "label": detection.class_name,
                "points": [],
                "group_id": None,
                "shape_type": shape_type,
                "flags": {
                    "confidence": float(detection.confidence),
                    "class_id": int(detection.class_id)
                }
            }
            
            # 根據類型生成點座標
            if shape_type == "polygon" and hasattr(detection, 'mask') and detection.mask is not None:
                # 分割模式：從mask生成多邊形
                # 🎯 計算檢測目標的面積用於自適應簡化
                detection_area = (detection.bbox[2] - detection.bbox[0]) * (detection.bbox[3] - detection.bbox[1])
                points = self._mask_to_polygon(detection.mask, detection_area)
                if points:
                    shape["points"] = points
                    self.stats['seg_annotations'] += 1
                    # 添加點數量信息到標籤
                    shape["flags"]["polygon_points"] = len(points)
                    shape["flags"]["object_area"] = float(detection_area)
                else:
                    # 如果mask轉換失敗，降級為rectangle
                    shape["shape_type"] = "rectangle"
                    shape["points"] = [
                        [float(detection.bbox[0]), float(detection.bbox[1])],  # 左上
                        [float(detection.bbox[2]), float(detection.bbox[3])]   # 右下
                    ]
                    self.stats['det_annotations'] += 1
                    
            elif shape_type == "rectangle":
                # 檢測模式：使用邊界框
                shape["points"] = [
                    [float(detection.bbox[0]), float(detection.bbox[1])],  # 左上
                    [float(detection.bbox[2]), float(detection.bbox[3])]   # 右下
                ]
                self.stats['det_annotations'] += 1
                
            elif shape_type == "point":
                # 點模式：使用中心點
                center_point = self._generate_center_point(detection.bbox)
                shape["points"] = [center_point]
                self.stats['point_annotations'] += 1
            
            return shape
            
        except Exception as e:
            self.logger.error(f"❌ 創建shape失敗: {e}")
            return None
    
    def _create_adjustment_points(self, detection: Detection) -> List[Dict[str, Any]]:
        """
        為檢測創建調整點
        
        Args:
            detection: 檢測結果
            
        Returns:
            List[Dict]: 調整點shape列表
        """
        if not self.include_points:
            return []
        
        adjustment_points = []
        
        try:
            # 中心點 (根據配置決定是否添加)
            if self.include_center_points:
                center_point = self._generate_center_point(detection.bbox)
                center_shape = {
                    "label": f"{detection.class_name}_center",
                    "points": [center_point],
                    "group_id": None,
                    "shape_type": "point",
                    "flags": {
                        "type": "center_point",
                        "parent_class": detection.class_name,
                        "confidence": float(detection.confidence)
                    }
                }
                adjustment_points.append(center_shape)
            
            # 四個角點（僅用於矩形檢測）
            if not (hasattr(detection, 'mask') and detection.mask is not None):
                corner_points = self._generate_corner_points(detection.bbox)
                for i, corner in enumerate(corner_points):
                    corner_names = ["top_left", "top_right", "bottom_right", "bottom_left"]
                    corner_shape = {
                        "label": f"{detection.class_name}_{corner_names[i]}",
                        "points": [corner],
                        "group_id": None,
                        "shape_type": "point",
                        "flags": {
                            "type": "corner_point",
                            "corner_index": i,
                            "parent_class": detection.class_name,
                            "confidence": float(detection.confidence)
                        }
                    }
                    adjustment_points.append(corner_shape)
            
            self.stats['point_annotations'] += len(adjustment_points)
            return adjustment_points
            
        except Exception as e:
            self.logger.error(f"❌ 創建調整點失敗: {e}")
            return []
    
    def export_image_annotations(self, image_path: str, detections: List[Detection], 
                               original_image_size: Optional[Tuple[int, int]] = None) -> Optional[str]:
        """
        導出單張圖像的LabelMe標註
        
        Args:
            image_path: 圖像路徑
            detections: 檢測結果列表
            original_image_size: 原始圖像尺寸 (height, width)
            
        Returns:
            Optional[str]: 導出的JSON文件路徑，失敗返回None
        """
        if not self.output_dir:
            return None
        
        # 🆕 空預測跳過邏輯（需要考慮類別過濾）
        if not detections:
            skip_empty = getattr(self.config.output, 'skip_empty_labelme_export', False)
            if skip_empty:
                self.logger.debug(f"🚫 跳過空預測圖像的LabelMe導出: {Path(image_path).name}")
                return None
            # 如果不跳過，繼續創建空的LabelMe文件（原有行為）
        else:
            # 檢查是否所有檢測都被類別過濾掉了
            enabled_count = 0
            for detection in detections:
                class_config = self.config.get_class_config(detection.class_id)
                if class_config and class_config.enabled:
                    enabled_count += 1
            
            # 如果所有檢測都被過濾掉，且啟用空預測跳過，則跳過導出
            if enabled_count == 0:
                skip_empty = getattr(self.config.output, 'skip_empty_labelme_export', False)
                if skip_empty:
                    self.logger.debug(f"🚫 跳過所有類別被禁用的圖像LabelMe導出: {Path(image_path).name}")
                    return None
        
        try:
            image_path = Path(image_path)
            
            # 獲取圖像信息
            if original_image_size:
                image_height, image_width = original_image_size
            else:
                # 從圖像文件讀取尺寸
                img = cv2.imread(str(image_path))
                if img is None:
                    self.logger.error(f"❌ 無法讀取圖像: {image_path}")
                    return None
                image_height, image_width = img.shape[:2]
            
            # 創建LabelMe JSON結構
            labelme_data = {
                "version": "5.2.1",
                "flags": {},
                "shapes": [],
                "imagePath": image_path.name,
                "imageData": self._image_to_base64(str(image_path)) if self.config.output.include_image_data else None,
                "imageHeight": image_height,
                "imageWidth": image_width,
                "text": "",
                "lineColor": [0, 255, 0, 128],
                "fillColor": [255, 0, 0, 128],
                "imageDirName": str(image_path.parent.name),
                "createTime": datetime.now().isoformat(),
                "exportInfo": {
                    "tool": "Road AI Framework - Unified YOLO System",
                    "export_mode": self.export_mode,
                    "total_detections": len(detections),
                    "include_points": self.include_points,
                    "point_optimization": {
                        "max_polygon_points": self.max_polygon_points,
                        "min_polygon_points": self.min_polygon_points,
                        "adaptive_simplify": self.adaptive_simplify,
                        "optimization_mode": self.optimization_mode
                    }
                }
            }
            
            # 🆕 先過濾啟用的檢測結果
            enabled_detections = []
            for detection in detections:
                class_config = self.config.get_class_config(detection.class_id)
                if class_config and class_config.enabled:
                    enabled_detections.append(detection)
            
            # 更新檢測統計
            labelme_data["exportInfo"]["total_detections"] = len(enabled_detections)
            labelme_data["exportInfo"]["original_detections"] = len(detections)
            labelme_data["exportInfo"]["filtered_classes"] = len(detections) - len(enabled_detections)
            
            # 處理每個啟用的檢測
            for detection in enabled_detections:
                # 主要檢測shape
                main_shape = self._create_shape_from_detection(detection, self.export_mode)
                if main_shape:
                    labelme_data["shapes"].append(main_shape)
                
                # 調整點
                adjustment_points = self._create_adjustment_points(detection)
                labelme_data["shapes"].extend(adjustment_points)
            
            # 保存JSON文件
            json_filename = image_path.stem + ".json"
            json_path = self.output_dir / json_filename
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(labelme_data, f, indent=2, ensure_ascii=False)
            
            self.stats['total_exported'] += 1
            self.logger.debug(f"📋 LabelMe標註已導出: {json_path}")
            
            return str(json_path)
            
        except Exception as e:
            self.logger.error(f"❌ 導出LabelMe標註失敗 {image_path}: {e}")
            self.stats['export_errors'] += 1
            return None
    
    def export_batch_annotations(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量導出LabelMe標註
        
        Args:
            results: 批量處理結果列表
            
        Returns:
            Dict: 批量導出統計
        """
        successful_exports = 0
        failed_exports = 0
        
        self.logger.info(f"🏷️ 開始批量導出LabelMe標註: {len(results)}個結果")
        
        for result in results:
            if not result.get('success', False):
                continue
                
            image_path = result['image_path']
            detections = result.get('detections', [])
            
            if not detections:
                continue
            
            export_path = self.export_image_annotations(image_path, detections)
            
            if export_path:
                successful_exports += 1
            else:
                failed_exports += 1
        
        # 生成批量統計報告
        summary = {
            'total_images': len(results),
            'successful_exports': successful_exports,
            'failed_exports': failed_exports,
            'export_rate': successful_exports / len(results) * 100 if results else 0,
            'output_directory': str(self.output_dir),
            'statistics': self.stats.copy()
        }
        
        self.logger.info(f"✅ 批量導出完成: {successful_exports}/{len(results)}個成功")
        return summary
    
    def get_export_statistics(self) -> Dict[str, Any]:
        """獲取導出統計信息"""
        return {
            'exporter_stats': self.stats.copy(),
            'output_directory': str(self.output_dir) if self.output_dir else None,
            'export_mode': self.export_mode,
            'include_points': self.include_points,
            'point_radius': self.point_radius,
            'point_control_settings': {
                'max_polygon_points': self.max_polygon_points,
                'min_polygon_points': self.min_polygon_points,
                'adaptive_simplify': self.adaptive_simplify,
                'optimization_mode': self.optimization_mode,
                'enable_point_optimization': self.enable_point_optimization
            }
        }
    
    def validate_export(self, json_path: str) -> Dict[str, Any]:
        """
        驗證導出的LabelMe JSON文件
        
        Args:
            json_path: JSON文件路徑
            
        Returns:
            Dict: 驗證結果
        """
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            validation_result = {
                'valid': True,
                'errors': [],
                'warnings': [],
                'statistics': {
                    'total_shapes': len(data.get('shapes', [])),
                    'shape_types': {},
                    'labels': {}
                }
            }
            
            # 檢查必要字段
            required_fields = ['version', 'shapes', 'imagePath', 'imageHeight', 'imageWidth']
            for field in required_fields:
                if field not in data:
                    validation_result['errors'].append(f"缺少必要字段: {field}")
                    validation_result['valid'] = False
            
            # 統計shape信息
            for shape in data.get('shapes', []):
                shape_type = shape.get('shape_type', 'unknown')
                label = shape.get('label', 'unknown')
                
                validation_result['statistics']['shape_types'][shape_type] = \
                    validation_result['statistics']['shape_types'].get(shape_type, 0) + 1
                validation_result['statistics']['labels'][label] = \
                    validation_result['statistics']['labels'].get(label, 0) + 1
                
                # 檢查點數量
                points = shape.get('points', [])
                if shape_type == 'rectangle' and len(points) != 2:
                    validation_result['warnings'].append(f"矩形{label}點數不正確: {len(points)}")
                elif shape_type == 'point' and len(points) != 1:
                    validation_result['warnings'].append(f"點{label}點數不正確: {len(points)}")
            
            return validation_result
            
        except Exception as e:
            return {
                'valid': False,
                'errors': [f"驗證失敗: {str(e)}"],
                'warnings': [],
                'statistics': {}
            }
    
    def cleanup(self):
        """清理資源"""
        self.logger.info("🧹 LabelMe導出器資源清理完成")
        
        # 輸出最終統計
        if self.stats['total_exported'] > 0:
            self.logger.info("📊 LabelMe導出統計:")
            self.logger.info(f"   總導出數: {self.stats['total_exported']}")
            self.logger.info(f"   分割標註: {self.stats['seg_annotations']}")
            self.logger.info(f"   檢測標註: {self.stats['det_annotations']}")
            self.logger.info(f"   點標註: {self.stats['point_annotations']}")
            if self.stats['export_errors'] > 0:
                self.logger.warning(f"   導出錯誤: {self.stats['export_errors']}")