import os
import json
import shutil
import random
from collections import defaultdict

def get_label_from_json(json_path):
    """讀 Labelme json，取 shapes 的第一個 label 作為此筆資料類別"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        shapes = data.get('shapes', [])
        if not shapes:
            return None
        # 取第一個標籤當類別
        label = shapes[0].get('label', None)
        return label
    except Exception as e:
        print(f"讀取 {json_path} 發生錯誤: {e}")
        return None

def get_all_labels_from_json(json_path):
    """讀 Labelme json，收集所有 shapes 中的標籤"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        shapes = data.get('shapes', [])
        if not shapes:
            return set()
        
        # 收集所有標籤
        labels = set()
        for shape in shapes:
            label = shape.get('label', None)
            if label:
                labels.add(label)
        return labels
    except Exception as e:
        print(f"讀取 {json_path} 發生錯誤: {e}")
        return set()

def collect_all_classes_from_directory(input_dir):
    """從目錄中收集所有類別統計"""
    json_files = [f for f in os.listdir(input_dir) if f.endswith(".json")]
    all_classes = set()
    class_counts = {}
    
    print(f"開始分析 {len(json_files)} 個 JSON 文件...")
    
    for json_file in json_files:
        json_path = os.path.join(input_dir, json_file)
        labels = get_all_labels_from_json(json_path)
        all_classes.update(labels)
        
        for label in labels:
            class_counts[label] = class_counts.get(label, 0) + 1
    
    print(f"找到 {len(all_classes)} 個不同的類別:")
    for label in sorted(all_classes):
        print(f"  {label}: {class_counts[label]} 次標注")
    
    return all_classes, class_counts

def polygon_to_bbox(points):
    """將polygon點座標轉換為bounding box"""
    if not points:
        return None
    
    x_coords = [p[0] for p in points]
    y_coords = [p[1] for p in points]
    
    x_min, x_max = min(x_coords), max(x_coords)
    y_min, y_max = min(y_coords), max(y_coords)
    
    # 返回rectangle的兩個對角點
    return [[x_min, y_min], [x_max, y_max]]

def convert_seg_to_det(json_path, output_path):
    """將分割格式的JSON轉換為檢測格式"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 轉換所有polygon為rectangle
        converted_shapes = []
        for shape in data.get('shapes', []):
            if shape.get('shape_type') == 'polygon':
                # 將polygon轉換為rectangle
                points = shape.get('points', [])
                bbox_points = polygon_to_bbox(points)
                if bbox_points:
                    converted_shape = shape.copy()
                    converted_shape['shape_type'] = 'rectangle'
                    converted_shape['points'] = bbox_points
                    converted_shapes.append(converted_shape)
            else:
                # 保持其他類型不變
                converted_shapes.append(shape)
        
        # 更新data中的shapes
        data['shapes'] = converted_shapes
        
        # 保存轉換後的文件
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
        return True
    except Exception as e:
        print(f"轉換文件 {json_path} 時發生錯誤: {e}")
        return False

def convert_det_to_yolo_format(json_path, output_txt_path, class_mapping):
    """將檢測格式的JSON轉換為YOLO格式的txt文件"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        img_width = data.get('imageWidth', 0)
        img_height = data.get('imageHeight', 0)
        
        if img_width == 0 or img_height == 0:
            print(f"警告: {json_path} 中缺少圖像尺寸信息")
            return False
        
        yolo_lines = []
        for shape in data.get('shapes', []):
            if shape.get('shape_type') == 'rectangle':
                points = shape.get('points', [])
                label = shape.get('label', '')
                
                if len(points) >= 2 and label in class_mapping:
                    x1, y1 = points[0]
                    x2, y2 = points[1]
                    
                    # 計算YOLO格式的相對座標
                    x_center = (x1 + x2) / 2 / img_width
                    y_center = (y1 + y2) / 2 / img_height
                    width = abs(x2 - x1) / img_width
                    height = abs(y2 - y1) / img_height
                    
                    class_id = class_mapping[label]
                    yolo_lines.append(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")
        
        # 保存YOLO格式文件
        with open(output_txt_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(yolo_lines))
            
        return True
    except Exception as e:
        print(f"轉換為YOLO格式時發生錯誤: {e}")
        return False

def stratified_split(data_by_class, train_ratio, val_ratio, test_ratio, seed=42):
    """依類別分層拆分"""
    random.seed(seed)
    train, val, test = [], [], []
    for label, items in data_by_class.items():
        random.shuffle(items)
        n = len(items)
        train_end = int(n * train_ratio)
        val_end = train_end + int(n * val_ratio)
        train.extend(items[:train_end])
        val.extend(items[train_end:val_end])
        test.extend(items[val_end:])
    return train, val, test

def copy_files(file_list, input_dir, output_dir, subset, output_format='seg', class_mapping=None):
    subset_dir = os.path.join(output_dir, subset)
    os.makedirs(subset_dir, exist_ok=True)
    
    # 根據輸出格式創建子目錄
    if output_format == 'yolo':
        images_dir = os.path.join(subset_dir, 'images')
        labels_dir = os.path.join(subset_dir, 'labels')
        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(labels_dir, exist_ok=True)

    for json_file in file_list:
        base_name = os.path.splitext(json_file)[0]
        input_json_path = os.path.join(input_dir, json_file)

        # 處理標註文件
        if output_format == 'seg':
            # 直接複製原始JSON（seg格式）
            shutil.copy2(input_json_path, os.path.join(subset_dir, json_file))
        elif output_format == 'det':
            # 轉換為det格式的JSON
            output_json_path = os.path.join(subset_dir, json_file)
            convert_seg_to_det(input_json_path, output_json_path)
        elif output_format == 'yolo':
            # 先轉換為det格式，再轉換為YOLO txt格式
            temp_det_path = os.path.join(subset_dir, f"temp_{json_file}")
            if convert_seg_to_det(input_json_path, temp_det_path):
                yolo_txt_path = os.path.join(labels_dir, base_name + '.txt')
                convert_det_to_yolo_format(temp_det_path, yolo_txt_path, class_mapping)
                # 刪除臨時文件
                os.remove(temp_det_path)

        # 複製圖片
        found_img = False
        for ext in [".jpg", ".jpeg", ".png", ".bmp"]:
            img_path = os.path.join(input_dir, base_name + ext)
            if os.path.exists(img_path):
                if output_format == 'yolo':
                    shutil.copy2(img_path, os.path.join(images_dir, base_name + ext))
                else:
                    shutil.copy2(img_path, os.path.join(subset_dir, base_name + ext))
                found_img = True
                break
        if not found_img:
            print(f"警告: 找不到 {json_file} 的對應圖片")

def main():
    print("===== 簡易資料集拆分工具 (支持 seg/det 格式轉換) =====\n")
    
    # 輸入目錄
    input_dir = input("輸入資料夾路徑: ").strip()
    if not os.path.exists(input_dir):
        print(f"錯誤：目錄 {input_dir} 不存在")
        return
    
    # 先分析所有類別
    print("\n🔍 正在分析類別...")
    all_classes, class_counts = collect_all_classes_from_directory(input_dir)
    
    # 選擇輸出格式
    print("\n📝 請選擇輸出格式:")
    print("1. seg - 保持分割格式 (polygon)")
    print("2. det - 轉換為檢測格式 (rectangle/bounding box)")
    print("3. yolo - 轉換為YOLO格式 (txt文件)")
    
    format_choice = input("請選擇 (1/2/3): ").strip()
    format_mapping = {'1': 'seg', '2': 'det', '3': 'yolo'}
    output_format = format_mapping.get(format_choice, 'seg')
    
    print(f"選擇的輸出格式: {output_format}")
    
    # 如果選擇YOLO格式，創建類別映射
    class_mapping = None
    if output_format == 'yolo':
        class_mapping = {class_name: idx for idx, class_name in enumerate(sorted(all_classes))}
        print("\n🏷️ 類別映射 (用於YOLO格式):")
        for class_name, class_id in class_mapping.items():
            print(f"  {class_id}: {class_name}")
        
        # 保存類別映射文件
        classes_txt_path = os.path.join(os.path.dirname(input_dir.rstrip("/\\")), "classes.txt")
        with open(classes_txt_path, 'w', encoding='utf-8') as f:
            for class_name in sorted(all_classes):
                f.write(f"{class_name}\n")
        print(f"類別名稱已保存至: {classes_txt_path}")

    # 拆分比例
    ratios = input("\n輸入 train, val, test 比例 (例如: 0.7 0.2 0.1): ").strip().split()
    if len(ratios) != 3:
        print("錯誤：必須輸入三個比例")
        return
    
    try:
        train_ratio, val_ratio, test_ratio = map(float, ratios)
        total = train_ratio + val_ratio + test_ratio
        if abs(total - 1.0) > 1e-6:
            print("錯誤：三個比例加總必須等於 1")
            return
    except ValueError:
        print("錯誤：請輸入有效的數字")
        return

    # 找所有 json
    json_files = [f for f in os.listdir(input_dir) if f.endswith(".json")]
    if not json_files:
        print("錯誤：目錄中沒有找到JSON文件")
        return

    # 依類別分組（使用第一個標籤作為分組依據）
    data_by_class = defaultdict(list)
    for json_file in json_files:
        label = get_label_from_json(os.path.join(input_dir, json_file))
        if label is None:
            print(f"警告: {json_file} 找不到標籤，跳過")
            continue
        data_by_class[label].append(json_file)

    print(f"\n📊 按第一個標籤分組結果 (用於拆分):")
    for k,v in data_by_class.items():
        print(f"  類別 {k}: {len(v)} 個文件")

    # 分層拆分
    print("\n🔄 正在進行分層拆分...")
    train_files, val_files, test_files = stratified_split(data_by_class, train_ratio, val_ratio, test_ratio)

    # 建立輸出資料夾
    parent_dir = os.path.dirname(input_dir.rstrip("/\\"))
    output_dir = os.path.join(parent_dir, f"output_dataset_{output_format}")

    # 複製並轉換檔案
    print("\n📁 正在複製和轉換檔案...")
    copy_files(train_files, input_dir, output_dir, "train", output_format, class_mapping)
    copy_files(val_files, input_dir, output_dir, "val", output_format, class_mapping)
    copy_files(test_files, input_dir, output_dir, "test", output_format, class_mapping)

    # 保存處理結果摘要
    summary_path = os.path.join(output_dir, "dataset_summary.json")
    summary = {
        "input_directory": input_dir,
        "output_format": output_format,
        "total_files": len(json_files),
        "train_files": len(train_files),
        "val_files": len(val_files),
        "test_files": len(test_files),
        "split_ratios": {"train": train_ratio, "val": val_ratio, "test": test_ratio},
        "all_classes": sorted(list(all_classes)),
        "class_counts": class_counts,
        "class_mapping": class_mapping if class_mapping else {}
    }
    
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)

    print(f"\n✅ 拆分完成！")
    print(f"📁 輸出路徑：{output_dir}")
    print(f"📊 檔案分布: train: {len(train_files)}, val: {len(val_files)}, test: {len(test_files)}")
    print(f"📄 處理摘要已保存至: {summary_path}")

def analyze_only():
    """僅分析類別，不進行拆分"""
    print("===== 資料集類別分析工具 =====\n")
    
    input_dir = input("輸入資料夾路徑: ").strip()
    if not os.path.exists(input_dir):
        print(f"錯誤：目錄 {input_dir} 不存在")
        return
    
    all_classes, class_counts = collect_all_classes_from_directory(input_dir)
    
    print(f"\n📊 分析完成！")
    print(f"總類別數: {len(all_classes)}")
    print(f"總標注數: {sum(class_counts.values())}")

def menu():
    """選單"""
    print("\n" + "="*50)
    print("    資料集處理工具")
    print("="*50)
    print("1. 完整處理 (類別分析 + 格式轉換 + 拆分)")
    print("2. 僅分析類別")
    print("3. 退出")
    print("="*50)
    
    while True:
        choice = input("請選擇功能 (1-3): ").strip()
        if choice == '1':
            main()
            break
        elif choice == '2':
            analyze_only()
            break
        elif choice == '3':
            print("謝謝使用！")
            break
        else:
            print("無效選擇，請重新輸入")

if __name__ == "__main__":
    menu()
