#!/usr/bin/env python3
"""
🔗 物件連接處理器
實現距離<30pixel的物件自動連接功能，特別針對裂縫類別優化
"""

import logging
import numpy as np
import cv2
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass

from ..core.base_inference import Detection


@dataclass
class ConnectionResult:
    """連接結果"""
    original_detections: List[Detection]
    connected_detections: List[Detection]
    connection_map: Dict[int, List[int]]  # 原始檢測ID -> 連接的檢測ID列表
    total_connections: int


class ObjectConnectionProcessor:
    """
    物件連接處理器
    
    功能:
    - 距離<閾值的物件自動連接
    - 針對裂縫類別的線段連接優化
    - 可配置的連接策略
    - 連接後物件屬性融合
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化物件連接處理器
        
        Args:
            config: 連接配置
        """
        self.enabled = config.get('enable_object_connection', False)
        self.distance_threshold = config.get('connection_distance_threshold', 30.0)
        self.same_class_only = config.get('connection_same_class_only', True)
        self.confidence_weight = config.get('connection_confidence_weight', 0.3)
        self.enable_crack_connection = config.get('enable_crack_line_connection', False)
        
        self.logger = logging.getLogger(__name__)
        
        # 統計資訊
        self.stats = {
            'total_processed': 0,
            'total_connections_made': 0,
            'crack_connections_made': 0,
            'processing_times': []
        }
    
    def process(self, detections: List[Detection]) -> ConnectionResult:
        """
        處理物件連接
        
        Args:
            detections: 檢測結果列表
            
        Returns:
            ConnectionResult: 連接處理結果
        """
        if not self.enabled or not detections:
            return ConnectionResult(
                original_detections=detections,
                connected_detections=detections[:],  # 使用列表切片複製
                connection_map={},
                total_connections=0
            )
        
        import time
        start_time = time.time()
        
        # 轉換為內部格式便於處理
        internal_detections = self._detections_to_internal(detections)
        
        # 執行連接處理
        connected_detections, connection_map = self._connect_objects(internal_detections)
        
        # 轉換回Detection格式
        final_detections = self._internal_to_detections(connected_detections)
        
        # 更新統計
        processing_time = time.time() - start_time
        connections_made = len(connection_map)
        self.stats['total_processed'] += 1
        self.stats['total_connections_made'] += connections_made
        self.stats['processing_times'].append(processing_time)
        
        self.logger.debug(f"🔗 物件連接完成: {len(detections)} → {len(final_detections)} 物件，{connections_made} 次連接")
        
        return ConnectionResult(
            original_detections=detections,
            connected_detections=final_detections,
            connection_map=connection_map,
            total_connections=connections_made
        )
    
    def _connect_objects(self, detections: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], Dict[int, List[int]]]:
        """
        執行物件連接邏輯
        
        Args:
            detections: 內部格式檢測結果
            
        Returns:
            Tuple[List[Dict], Dict]: (連接後檢測結果, 連接映射)
        """
        if len(detections) <= 1:
            return detections, {}
        
        connection_map = {}
        connected_pairs = set()  # 記錄已連接的物件對
        result_detections = []
        processed_ids = set()  # 記錄已處理的檢測ID
        
        # 為每個檢測分配唯一ID
        for i, det in enumerate(detections):
            det['original_id'] = i
        
        # 按類別分組處理
        if self.same_class_only:
            class_groups = {}
            for det in detections:
                class_id = det['class_id']
                if class_id not in class_groups:
                    class_groups[class_id] = []
                class_groups[class_id].append(det)
            
            for class_id, class_dets in class_groups.items():
                class_result, class_connections = self._connect_class_objects(class_dets)
                result_detections.extend(class_result)
                connection_map.update(class_connections)
        else:
            # 全局連接
            result_detections, connection_map = self._connect_class_objects(detections)
        
        return result_detections, connection_map
    
    def _connect_class_objects(self, detections: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], Dict[int, List[int]]]:
        """
        在同類別內執行物件連接
        
        Args:
            detections: 同類別檢測結果
            
        Returns:
            Tuple[List[Dict], Dict]: (連接後檢測結果, 連接映射)
        """
        if len(detections) <= 1:
            return detections, {}
        
        result_detections = []
        connection_map = {}
        processed_ids = set()
        
        # 構建距離矩陣
        distances = self._calculate_distance_matrix(detections)
        
        # 尋找可連接的物件對
        for i in range(len(detections)):
            if i in processed_ids:
                continue
            
            current_det = detections[i]
            connected_group = [current_det]
            group_indices = [i]
            
            # 尋找與當前物件距離小於閾值的其他物件
            for j in range(i + 1, len(detections)):
                if j in processed_ids:
                    continue
                
                if distances[i][j] < self.distance_threshold:
                    # 檢查是否適合連接
                    if self._should_connect(current_det, detections[j]):
                        connected_group.append(detections[j])
                        group_indices.append(j)
            
            # 處理連接組
            if len(connected_group) > 1:
                # 創建連接後的物件
                merged_detection = self._merge_detections(connected_group)
                result_detections.append(merged_detection)
                
                # 記錄連接映射
                primary_id = group_indices[0]
                connected_ids = group_indices[1:]
                connection_map[primary_id] = connected_ids
                
                # 標記所有相關物件已處理
                for idx in group_indices:
                    processed_ids.add(idx)
                
                # 如果是裂縫類別，記錄裂縫連接統計
                if (self.enable_crack_connection and 
                    current_det.get('class_name', '').lower().find('crack') != -1):
                    self.stats['crack_connections_made'] += 1
                    self.logger.debug(f"🔗 裂縫連接: 合併{len(connected_group)}個片段")
                
            else:
                # 單獨物件，直接添加
                result_detections.append(current_det)
                processed_ids.add(i)
        
        return result_detections, connection_map
    
    def _calculate_distance_matrix(self, detections: List[Dict[str, Any]]) -> np.ndarray:
        """
        計算檢測物件間的距離矩陣
        
        Args:
            detections: 檢測結果列表
            
        Returns:
            np.ndarray: 距離矩陣
        """
        n = len(detections)
        distances = np.full((n, n), float('inf'))
        
        for i in range(n):
            for j in range(i + 1, n):
                dist = self._calculate_object_distance(detections[i], detections[j])
                distances[i][j] = dist
                distances[j][i] = dist  # 對稱矩陣
        
        return distances
    
    def _calculate_object_distance(self, det1: Dict[str, Any], det2: Dict[str, Any]) -> float:
        """
        計算兩個檢測物件之間的距離
        
        Args:
            det1: 第一個檢測物件
            det2: 第二個檢測物件
            
        Returns:
            float: 物件間距離
        """
        bbox1 = det1['bbox']
        bbox2 = det2['bbox']
        
        # 計算邊界框中心點
        center1 = [(bbox1[0] + bbox1[2]) / 2, (bbox1[1] + bbox1[3]) / 2]
        center2 = [(bbox2[0] + bbox2[2]) / 2, (bbox2[1] + bbox2[3]) / 2]
        
        # 歐氏距離
        distance = ((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)**0.5
        
        # 如果是裂縫類別，使用更精細的距離計算
        if (self.enable_crack_connection and 
            det1.get('class_name', '').lower().find('crack') != -1 and
            det2.get('class_name', '').lower().find('crack') != -1):
            distance = self._calculate_crack_distance(det1, det2)
        
        return distance
    
    def _calculate_crack_distance(self, det1: Dict[str, Any], det2: Dict[str, Any]) -> float:
        """
        計算裂縫物件間的特殊距離（考慮形狀和方向）
        
        Args:
            det1: 第一個裂縫檢測
            det2: 第二個裂縫檢測
            
        Returns:
            float: 裂縫間距離
        """
        bbox1 = det1['bbox']
        bbox2 = det2['bbox']
        
        # 計算邊界框的最近邊緣距離
        # 這對於細長的裂縫物件更準確
        x1_min, y1_min, x1_max, y1_max = bbox1
        x2_min, y2_min, x2_max, y2_max = bbox2
        
        # 計算矩形間的最小距離
        dx = max(0, max(x1_min - x2_max, x2_min - x1_max))
        dy = max(0, max(y1_min - y2_max, y2_min - y1_max))
        
        edge_distance = (dx**2 + dy**2)**0.5
        
        # 如果邊界框重疊，使用中心點距離
        if edge_distance == 0:
            center1 = [(x1_min + x1_max) / 2, (y1_min + y1_max) / 2]
            center2 = [(x2_min + x2_max) / 2, (y2_min + y2_max) / 2]
            edge_distance = ((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)**0.5
        
        return edge_distance
    
    def _should_connect(self, det1: Dict[str, Any], det2: Dict[str, Any]) -> bool:
        """
        判斷兩個檢測物件是否應該連接
        
        Args:
            det1: 第一個檢測物件
            det2: 第二個檢測物件
            
        Returns:
            bool: 是否應該連接
        """
        # 基本檢查：類別必須相同（如果啟用同類別限制）
        if self.same_class_only and det1['class_id'] != det2['class_id']:
            return False
        
        # 置信度檢查：兩個物件的置信度都應該reasonable
        if det1['confidence'] < 0.1 or det2['confidence'] < 0.1:
            return False
        
        # 大小檢查：避免連接過於不同大小的物件
        area1 = self._calculate_bbox_area(det1['bbox'])
        area2 = self._calculate_bbox_area(det2['bbox'])
        
        # 面積比不應該相差太大
        area_ratio = min(area1, area2) / max(area1, area2)
        if area_ratio < 0.1:  # 面積相差10倍以上
            return False
        
        # 裂縫特殊檢查
        if (self.enable_crack_connection and 
            det1.get('class_name', '').lower().find('crack') != -1):
            return self._should_connect_cracks(det1, det2)
        
        return True
    
    def _should_connect_cracks(self, crack1: Dict[str, Any], crack2: Dict[str, Any]) -> bool:
        """
        判斷兩個裂縫是否應該連接
        
        Args:
            crack1: 第一個裂縫檢測
            crack2: 第二個裂縫檢測
            
        Returns:
            bool: 是否應該連接
        """
        # 計算裂縫的方向（長寬比和主軸方向）
        bbox1 = crack1['bbox']
        bbox2 = crack2['bbox']
        
        width1 = bbox1[2] - bbox1[0]
        height1 = bbox1[3] - bbox1[1]
        width2 = bbox2[2] - bbox2[0]
        height2 = bbox2[3] - bbox2[1]
        
        # 長寬比
        ratio1 = max(width1, height1) / min(width1, height1)
        ratio2 = max(width2, height2) / min(width2, height2)
        
        # 都是線狀裂縫才考慮連接
        if ratio1 > 2.0 and ratio2 > 2.0:
            # 檢查方向是否相似
            orientation1 = 'vertical' if height1 > width1 else 'horizontal'
            orientation2 = 'vertical' if height2 > width2 else 'horizontal'
            
            # 相同方向的裂縫更可能連接
            if orientation1 == orientation2:
                return True
        
        return True  # 預設允許連接
    
    def _merge_detections(self, detections: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        合併多個檢測物件為一個
        
        Args:
            detections: 待合併的檢測物件列表
            
        Returns:
            Dict: 合併後的檢測物件
        """
        if len(detections) == 1:
            return detections[0].copy()
        
        # 計算合併後的邊界框（包圍所有原始邊界框）
        all_bboxes = [det['bbox'] for det in detections]
        x_min = min(bbox[0] for bbox in all_bboxes)
        y_min = min(bbox[1] for bbox in all_bboxes)
        x_max = max(bbox[2] for bbox in all_bboxes)
        y_max = max(bbox[3] for bbox in all_bboxes)
        
        merged_bbox = [x_min, y_min, x_max, y_max]
        
        # 計算加權平均置信度
        total_confidence = sum(det['confidence'] for det in detections)
        total_area = sum(self._calculate_bbox_area(det['bbox']) for det in detections)
        
        # 使用面積加權的置信度
        weighted_confidence = sum(
            det['confidence'] * self._calculate_bbox_area(det['bbox']) 
            for det in detections
        ) / total_area
        
        # 使用主要類別（出現最多的類別）
        class_counts = {}
        for det in detections:
            class_id = det['class_id']
            class_counts[class_id] = class_counts.get(class_id, 0) + 1
        
        primary_class = max(class_counts.keys(), key=lambda k: class_counts[k])
        primary_detection = next(det for det in detections if det['class_id'] == primary_class)
        
        # 創建合併後的檢測物件
        merged_detection = {
            'bbox': merged_bbox,
            'confidence': min(weighted_confidence, 1.0),  # 確保不超過1.0
            'class_id': primary_class,
            'class_name': primary_detection['class_name'],
            'area': self._calculate_bbox_area(merged_bbox),
            'merged_from': len(detections),  # 記錄由多少個物件合併而成
            'original_confidences': [det['confidence'] for det in detections]
        }
        
        # 合併mask（如果有）
        masks = [det.get('mask') for det in detections if det.get('mask') is not None]
        if masks:
            merged_detection['mask'] = self._merge_masks(masks, merged_bbox)
        
        return merged_detection
    
    def _merge_masks(self, masks: List[np.ndarray], merged_bbox: List[float]) -> Optional[np.ndarray]:
        """
        合併多個mask
        
        Args:
            masks: mask列表
            merged_bbox: 合併後的邊界框
            
        Returns:
            Optional[np.ndarray]: 合併後的mask
        """
        if not masks:
            return None
        
        try:
            # 簡單合併：取所有mask的並集
            merged_mask = masks[0].copy()
            for mask in masks[1:]:
                if mask.shape == merged_mask.shape:
                    merged_mask = np.logical_or(merged_mask, mask)
            
            return merged_mask
        except Exception as e:
            self.logger.warning(f"Mask合併失敗: {e}")
            return None
    
    def _calculate_bbox_area(self, bbox: List[float]) -> float:
        """計算邊界框面積"""
        return (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
    
    def _detections_to_internal(self, detections: List[Detection]) -> List[Dict[str, Any]]:
        """轉換Detection對象為內部字典格式"""
        internal = []
        for det in detections:
            internal.append({
                'bbox': det.bbox,
                'confidence': det.confidence,
                'class_id': det.class_id,
                'class_name': det.class_name,
                'mask': det.mask,
                'area': det.area
            })
        return internal
    
    def _internal_to_detections(self, internal_detections: List[Dict[str, Any]]) -> List[Detection]:
        """轉換內部字典格式為Detection對象"""
        detections = []
        for det in internal_detections:
            detection = Detection(
                bbox=det['bbox'],
                confidence=det['confidence'],
                class_id=det['class_id'],
                class_name=det['class_name'],
                mask=det.get('mask'),
                area=det.get('area')
            )
            detections.append(detection)
        return detections
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取連接處理統計信息"""
        avg_processing_time = (
            sum(self.stats['processing_times']) / len(self.stats['processing_times'])
            if self.stats['processing_times'] else 0.0
        )
        
        return {
            'enabled': self.enabled,
            'total_processed': self.stats['total_processed'],
            'total_connections_made': self.stats['total_connections_made'],
            'crack_connections_made': self.stats['crack_connections_made'],
            'average_processing_time': avg_processing_time,
            'config': {
                'distance_threshold': self.distance_threshold,
                'same_class_only': self.same_class_only,
                'confidence_weight': self.confidence_weight,
                'enable_crack_connection': self.enable_crack_connection
            }
        }
    
    def reset_statistics(self):
        """重置統計信息"""
        self.stats = {
            'total_processed': 0,
            'total_connections_made': 0,
            'crack_connections_made': 0,
            'processing_times': []
        }