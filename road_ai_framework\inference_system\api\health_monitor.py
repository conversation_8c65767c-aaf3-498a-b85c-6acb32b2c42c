#!/usr/bin/env python3
"""
🏥 健康監控器
提供服務健康檢查和狀態監控功能
"""

import psutil
import logging
import threading
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from ..config import UnifiedConfig


class HealthStatus(Enum):
    """健康狀態枚舉"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"  
    UNHEALTHY = "unhealthy"
    CRITICAL = "critical"


@dataclass
class HealthCheck:
    """健康檢查項目"""
    name: str
    status: HealthStatus
    message: str
    timestamp: datetime
    duration_ms: float
    metadata: Dict[str, Any] = field(default_factory=dict)


class HealthMonitor:
    """
    健康監控器
    
    提供系統健康狀態檢查和監控功能
    """
    
    def __init__(self, check_interval: float = 30.0):
        """
        初始化健康監控器
        
        Args:
            check_interval: 檢查間隔(秒)
        """
        self.check_interval = check_interval
        self.logger = logging.getLogger(__name__)
        
        # 監控狀態
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        
        # 健康檢查結果
        self.health_checks: Dict[str, HealthCheck] = {}
        self.overall_status = HealthStatus.HEALTHY
        
        # 系統閾值
        self.cpu_threshold = 90.0  # CPU使用率閾值
        self.memory_threshold = 85.0  # 記憶體使用率閾值
        self.disk_threshold = 90.0  # 磁盤使用率閾值
        
        # 統計信息
        self.start_time = datetime.now()
        self.check_count = 0
        self.last_check_time: Optional[datetime] = None
        
        # 線程鎖
        self._lock = threading.RLock()
        
        self.logger.info("🏥 健康監控器初始化完成")
    
    def start_monitoring(self):
        """開始健康監控"""
        with self._lock:
            if not self._monitoring:
                self._monitoring = True
                self._monitor_thread = threading.Thread(
                    target=self._monitoring_loop,
                    daemon=True
                )
                self._monitor_thread.start()
                self.logger.info("🏥 開始健康監控")
    
    def stop_monitoring(self):
        """停止健康監控"""
        with self._lock:
            if self._monitoring:
                self._monitoring = False
                if self._monitor_thread and self._monitor_thread.is_alive():
                    self._monitor_thread.join(timeout=2.0)
                self.logger.info("🏥 停止健康監控")
    
    def _monitoring_loop(self):
        """監控循環"""
        while self._monitoring:
            try:
                self.run_health_checks()
                time.sleep(self.check_interval)
            except Exception as e:
                self.logger.error(f"❌ 健康監控錯誤: {str(e)}")
                time.sleep(self.check_interval)
    
    def run_health_checks(self) -> Dict[str, HealthCheck]:
        """運行所有健康檢查"""
        start_time = time.time()
        
        with self._lock:
            self.check_count += 1
            self.last_check_time = datetime.now()
            
            # 執行各項檢查
            checks = {
                'cpu': self._check_cpu_usage(),
                'memory': self._check_memory_usage(),
                'disk': self._check_disk_usage(),
                'system': self._check_system_status()
            }
            
            # 更新健康檢查結果
            self.health_checks.update(checks)
            
            # 計算整體狀態
            self.overall_status = self._calculate_overall_status()
            
            check_time = (time.time() - start_time) * 1000
            self.logger.debug(f"🏥 健康檢查完成 ({check_time:.1f}ms)")
            
            return self.health_checks
    
    def _check_cpu_usage(self) -> HealthCheck:
        """檢查CPU使用率"""
        start_time = time.time()
        
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            duration_ms = (time.time() - start_time) * 1000
            
            if cpu_percent >= self.cpu_threshold:
                status = HealthStatus.CRITICAL
                message = f"CPU使用率過高: {cpu_percent:.1f}%"
            elif cpu_percent >= self.cpu_threshold * 0.8:
                status = HealthStatus.DEGRADED
                message = f"CPU使用率較高: {cpu_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"CPU使用率正常: {cpu_percent:.1f}%"
            
            return HealthCheck(
                name="cpu",
                status=status,
                message=message,
                timestamp=datetime.now(),
                duration_ms=duration_ms,
                metadata={
                    'cpu_percent': cpu_percent,
                    'cpu_count': psutil.cpu_count(),
                    'threshold': self.cpu_threshold
                }
            )
            
        except Exception as e:
            return HealthCheck(
                name="cpu",
                status=HealthStatus.UNHEALTHY,
                message=f"CPU檢查失敗: {str(e)}",
                timestamp=datetime.now(),
                duration_ms=(time.time() - start_time) * 1000
            )
    
    def _check_memory_usage(self) -> HealthCheck:
        """檢查記憶體使用率"""
        start_time = time.time()
        
        try:
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            duration_ms = (time.time() - start_time) * 1000
            
            if memory_percent >= self.memory_threshold:
                status = HealthStatus.CRITICAL
                message = f"記憶體使用率過高: {memory_percent:.1f}%"
            elif memory_percent >= self.memory_threshold * 0.8:
                status = HealthStatus.DEGRADED
                message = f"記憶體使用率較高: {memory_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"記憶體使用率正常: {memory_percent:.1f}%"
            
            return HealthCheck(
                name="memory",
                status=status,
                message=message,
                timestamp=datetime.now(),
                duration_ms=duration_ms,
                metadata={
                    'memory_percent': memory_percent,
                    'total_gb': memory.total / (1024**3),
                    'available_gb': memory.available / (1024**3),
                    'used_gb': memory.used / (1024**3),
                    'threshold': self.memory_threshold
                }
            )
            
        except Exception as e:
            return HealthCheck(
                name="memory",
                status=HealthStatus.UNHEALTHY,
                message=f"記憶體檢查失敗: {str(e)}",
                timestamp=datetime.now(),
                duration_ms=(time.time() - start_time) * 1000
            )
    
    def _check_disk_usage(self) -> HealthCheck:
        """檢查磁盤使用率"""
        start_time = time.time()
        
        try:
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            duration_ms = (time.time() - start_time) * 1000
            
            if disk_percent >= self.disk_threshold:
                status = HealthStatus.CRITICAL
                message = f"磁盤使用率過高: {disk_percent:.1f}%"
            elif disk_percent >= self.disk_threshold * 0.8:
                status = HealthStatus.DEGRADED
                message = f"磁盤使用率較高: {disk_percent:.1f}%"
            else:
                status = HealthStatus.HEALTHY
                message = f"磁盤使用率正常: {disk_percent:.1f}%"
            
            return HealthCheck(
                name="disk",
                status=status,
                message=message,
                timestamp=datetime.now(),
                duration_ms=duration_ms,
                metadata={
                    'disk_percent': disk_percent,
                    'total_gb': disk.total / (1024**3),
                    'free_gb': disk.free / (1024**3),
                    'used_gb': disk.used / (1024**3),
                    'threshold': self.disk_threshold
                }
            )
            
        except Exception as e:
            return HealthCheck(
                name="disk",
                status=HealthStatus.UNHEALTHY,
                message=f"磁盤檢查失敗: {str(e)}",
                timestamp=datetime.now(),
                duration_ms=(time.time() - start_time) * 1000
            )
    
    def _check_system_status(self) -> HealthCheck:
        """檢查系統整體狀態"""
        start_time = time.time()
        
        try:
            # 檢查系統負載
            load_avg = psutil.getloadavg() if hasattr(psutil, 'getloadavg') else (0, 0, 0)
            
            # 檢查進程數
            process_count = len(psutil.pids())
            
            # 檢查網絡連接
            network_connections = len(psutil.net_connections())
            
            # 檢查啟動時間
            boot_time = psutil.boot_time()
            uptime_seconds = time.time() - boot_time
            
            duration_ms = (time.time() - start_time) * 1000
            
            # 基於多個指標判斷系統狀態
            if process_count > 500 or network_connections > 1000:
                status = HealthStatus.DEGRADED
                message = "系統負載較高"
            else:
                status = HealthStatus.HEALTHY
                message = "系統狀態正常"
            
            return HealthCheck(
                name="system",
                status=status,
                message=message,
                timestamp=datetime.now(),
                duration_ms=duration_ms,
                metadata={
                    'load_avg': load_avg,
                    'process_count': process_count,
                    'network_connections': network_connections,
                    'uptime_seconds': uptime_seconds,
                    'uptime_hours': uptime_seconds / 3600
                }
            )
            
        except Exception as e:
            return HealthCheck(
                name="system",
                status=HealthStatus.UNHEALTHY,
                message=f"系統檢查失敗: {str(e)}",
                timestamp=datetime.now(),
                duration_ms=(time.time() - start_time) * 1000
            )
    
    def _calculate_overall_status(self) -> HealthStatus:
        """計算整體健康狀態"""
        if not self.health_checks:
            return HealthStatus.HEALTHY
        
        statuses = [check.status for check in self.health_checks.values()]
        
        # 如果有任何critical狀態，整體為critical
        if HealthStatus.CRITICAL in statuses:
            return HealthStatus.CRITICAL
        
        # 如果有任何unhealthy狀態，整體為unhealthy
        if HealthStatus.UNHEALTHY in statuses:
            return HealthStatus.UNHEALTHY
        
        # 如果有任何degraded狀態，整體為degraded
        if HealthStatus.DEGRADED in statuses:
            return HealthStatus.DEGRADED
        
        return HealthStatus.HEALTHY
    
    def get_health_status(self) -> Dict[str, Any]:
        """獲取健康狀態"""
        with self._lock:
            # 如果沒有進行過檢查，先執行一次
            if not self.health_checks:
                self.run_health_checks()
            
            uptime = datetime.now() - self.start_time
            
            return {
                'overall_status': self.overall_status.value,
                'timestamp': datetime.now().isoformat(),
                'uptime_seconds': uptime.total_seconds(),
                'uptime_hours': uptime.total_seconds() / 3600,
                'check_count': self.check_count,
                'last_check': self.last_check_time.isoformat() if self.last_check_time else None,
                'checks': {
                    name: {
                        'status': check.status.value,
                        'message': check.message,
                        'timestamp': check.timestamp.isoformat(),
                        'duration_ms': check.duration_ms,
                        'metadata': check.metadata
                    }
                    for name, check in self.health_checks.items()
                }
            }
    
    def get_system_info(self) -> Dict[str, Any]:
        """獲取系統信息"""
        try:
            # 基本系統信息
            cpu_count = psutil.cpu_count()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # 系統詳細信息
            system_info = {
                'cpu': {
                    'count': cpu_count,
                    'current_percent': psutil.cpu_percent(),
                    'frequency': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                },
                'memory': {
                    'total_gb': memory.total / (1024**3),
                    'available_gb': memory.available / (1024**3),
                    'used_gb': memory.used / (1024**3),
                    'percent': memory.percent
                },
                'disk': {
                    'total_gb': disk.total / (1024**3),
                    'free_gb': disk.free / (1024**3),
                    'used_gb': disk.used / (1024**3),
                    'percent': (disk.used / disk.total) * 100
                },
                'process': {
                    'count': len(psutil.pids()),
                    'current_pid': psutil.Process().pid
                }
            }
            
            # GPU信息
            try:
                import torch
                if torch.cuda.is_available():
                    system_info['gpu'] = {
                        'available': True,
                        'count': torch.cuda.device_count(),
                        'current_device': torch.cuda.current_device(),
                        'memory_allocated': torch.cuda.memory_allocated() / (1024**2),  # MB
                        'memory_reserved': torch.cuda.memory_reserved() / (1024**2)    # MB
                    }
                else:
                    system_info['gpu'] = {'available': False}
            except ImportError:
                system_info['gpu'] = {'available': False, 'torch_not_installed': True}
            
            return system_info
            
        except Exception as e:
            self.logger.error(f"❌ 系統信息獲取失敗: {str(e)}")
            return {'error': str(e)}
    
    def is_healthy(self) -> bool:
        """檢查系統是否健康"""
        return self.overall_status in [HealthStatus.HEALTHY, HealthStatus.DEGRADED]
    
    def set_thresholds(self, 
                      cpu_threshold: Optional[float] = None,
                      memory_threshold: Optional[float] = None,
                      disk_threshold: Optional[float] = None):
        """設置健康檢查閾值"""
        if cpu_threshold is not None:
            self.cpu_threshold = cpu_threshold
        if memory_threshold is not None:
            self.memory_threshold = memory_threshold
        if disk_threshold is not None:
            self.disk_threshold = disk_threshold
        
        self.logger.info(f"🏥 健康檢查閾值已更新: CPU={self.cpu_threshold}%, Memory={self.memory_threshold}%, Disk={self.disk_threshold}%")
    
    def cleanup(self):
        """清理健康監控器資源"""
        self.logger.debug("🧹 清理健康監控器資源")
        self.stop_monitoring()
        
        with self._lock:
            self.health_checks.clear()