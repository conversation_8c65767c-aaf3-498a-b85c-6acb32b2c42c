#!/usr/bin/env python3
"""
🔪 切片處理器
專門處理圖像切片邏輯，從原始 advanced_slice_inference.py 中提取
"""

import time
import logging
from typing import List, Tuple, Dict, Any, Optional
import numpy as np
import cv2
from dataclasses import dataclass

from ..core.base_inference import Detection
from ..config import SliceConfig


@dataclass
class SliceInfo:
    """切片信息類"""
    x_start: int
    y_start: int
    x_end: int  
    y_end: int
    slice_id: int
    overlap_area: float = 0.0


class SliceProcessor:
    """
    切片處理器
    
    負責：
    - 智能切片生成
    - 切片推理協調
    - ROI區域處理
    """
    
    def __init__(self, slice_config: SliceConfig, class_configs: Dict = None):
        """
        初始化切片處理器
        
        Args:
            slice_config: 切片配置
            class_configs: 類別配置字典 {class_id: ClassConfig}
        """
        self.config = slice_config
        self.class_configs = class_configs or {}
        self.logger = logging.getLogger(__name__)
        self.stats = {
            'total_slices_generated': 0,
            'total_slice_inferences': 0,
            'slice_generation_times': [],
            'slice_inference_times': []
        }
        # ROI-SAHI 支援
        self.image_processor = None  # 將由外部設定
    
    def set_image_processor(self, image_processor):
        """
        設定 ImageProcessor 以支援 ROI-SAHI 功能
        
        Args:
            image_processor: ImageProcessor 實例
        """
        self.image_processor = image_processor
    
    def process(self, image: np.ndarray, model_adapter, rois: Optional[List[Tuple[int, int, int, int]]] = None) -> List[Detection]:
        """
        執行切片處理
        
        Args:
            image: 輸入圖像
            model_adapter: 模型適配器
            rois: 感興趣區域列表
            
        Returns:
            List[Detection]: 切片推理結果
        """
        if not self.config.enabled:
            return []
        
        try:
            start_time = time.time()
            
            # 1. 生成切片
            slices = self._generate_slices(image, rois)
            
            # 2. 執行切片推理
            detections = self._execute_slice_inference(image, slices, model_adapter)
            
            processing_time = time.time() - start_time
            self.stats['slice_generation_times'].append(processing_time)
            
            self.logger.debug(f"✅ 切片處理完成：{len(slices)}個切片，{len(detections)}個檢測，耗時{processing_time:.3f}秒")
            
            return detections
            
        except Exception as e:
            self.logger.error(f"❌ 切片處理失敗: {str(e)}")
            return []
    
    def _generate_slices(self, image: np.ndarray, rois: Optional[List[Tuple[int, int, int, int]]]) -> List[SliceInfo]:
        """
        生成切片信息
        
        Args:
            image: 輸入圖像
            rois: ROI區域列表
            
        Returns:
            List[SliceInfo]: 切片信息列表
        """
        start_time = time.time()
        
        h, w = image.shape[:2]
        slices = []
        
        # 🎯 ROI-SAHI 整合: 如果有 ImageProcessor 且啟用 ROI，使用 ROI 切片
        if (self.image_processor and 
            hasattr(self.image_processor, 'roi_config') and 
            self.image_processor.roi_config.enabled and
            self.image_processor.roi_config.inference_mode == "sahi"):
            
            self.logger.info("🧩 使用ROI-SAHI模式生成切片")
            
            # 使用 ImageProcessor 的 ROI 切片方法
            roi_slices = self.image_processor.get_roi_sahi_slices(
                image_shape=(h, w),
                slice_height=self.config.height,
                slice_width=self.config.width,
                overlap_ratio=self.config.overlap_ratio
            )
            
            # 轉換為 SliceInfo 格式
            for slice_info in roi_slices:
                slice_obj = SliceInfo(
                    x_start=slice_info['x'],
                    y_start=slice_info['y'],
                    x_end=slice_info['x'] + slice_info['width'],
                    y_end=slice_info['y'] + slice_info['height'],
                    slice_id=slice_info['slice_id']
                )
                slices.append(slice_obj)
            
            generation_time = time.time() - start_time
            self.stats['total_slices_generated'] += len(slices)
            
            self.logger.info(f"🎯 ROI-SAHI切片生成：{len(slices)}個，耗時{generation_time:.3f}秒")
            
            return slices
        
        # 標準切片生成邏輯
        slice_id = 0
        
        # 如果沒有指定ROI，使用整張圖像
        if rois is None:
            rois = [(0, 0, w, h)]
        
        for roi_x1, roi_y1, roi_x2, roi_y2 in rois:
            # 確保ROI在圖像範圍內
            roi_x1 = max(0, roi_x1)
            roi_y1 = max(0, roi_y1)
            roi_x2 = min(w, roi_x2)
            roi_y2 = min(h, roi_y2)
            
            roi_width = roi_x2 - roi_x1
            roi_height = roi_y2 - roi_y1
            
            # 計算重疊像素
            overlap_width = int(self.config.width * self.config.overlap_ratio)
            overlap_height = int(self.config.height * self.config.overlap_ratio)
            
            # 計算步長
            step_width = self.config.width - overlap_width
            step_height = self.config.height - overlap_height
            
            # 生成切片座標
            y = roi_y1
            while y < roi_y2:
                x = roi_x1
                while x < roi_x2:
                    # 計算切片邊界
                    slice_x1 = x
                    slice_y1 = y
                    slice_x2 = min(x + self.config.width, roi_x2)
                    slice_y2 = min(y + self.config.height, roi_y2)
                    
                    # 檢查切片大小是否足夠
                    slice_width = slice_x2 - slice_x1
                    slice_height = slice_y2 - slice_y1
                    
                    if (slice_width >= self.config.min_slice_size and 
                        slice_height >= self.config.min_slice_size):
                        
                        slice_info = SliceInfo(
                            x_start=slice_x1,
                            y_start=slice_y1,
                            x_end=slice_x2,
                            y_end=slice_y2,
                            slice_id=slice_id
                        )
                        slices.append(slice_info)
                        slice_id += 1
                    
                    x += step_width
                    if x >= roi_x2:
                        break
                
                y += step_height
                if y >= roi_y2:
                    break
        
        generation_time = time.time() - start_time
        self.stats['total_slices_generated'] += len(slices)
        
        self.logger.debug(f"🔪 生成切片：{len(slices)}個，耗時{generation_time:.3f}秒")
        
        return slices
    
    def _execute_slice_inference(self, image: np.ndarray, slices: List[SliceInfo], model_adapter) -> List[Detection]:
        """
        執行切片推理
        
        Args:
            image: 原始圖像
            slices: 切片信息列表
            model_adapter: 模型適配器
            
        Returns:
            List[Detection]: 推理結果
        """
        all_detections = []
        inference_start = time.time()
        
        for i, slice_info in enumerate(slices):
            slice_image = None  # 🗑️ 每次迭代初始化
            slice_detections = None
            try:
                # 提取切片圖像
                slice_image = image[
                    slice_info.y_start:slice_info.y_end,
                    slice_info.x_start:slice_info.x_end
                ].copy()  # 🗑️ 使用copy()避免引用原始圖像
                
                if slice_image.size == 0:
                    continue
                
                # 🎯 計算SAHI置信度閾值 - 使用類別特定的sahi_confidence
                sahi_confidence = self._get_min_sahi_confidence()
                
                # 執行推理
                slice_detections = model_adapter.inference(
                    slice_image,
                    confidence=sahi_confidence,  # 使用類別特定的SAHI閾值
                    iou_threshold=0.45,
                    max_det=1000
                )
                
                # 轉換座標到原圖座標系
                for detection in slice_detections:
                    # 調整bbox座標
                    detection.bbox[0] += slice_info.x_start  # x1
                    detection.bbox[1] += slice_info.y_start  # y1  
                    detection.bbox[2] += slice_info.x_start  # x2
                    detection.bbox[3] += slice_info.y_start  # y2
                    
                    # 如果有mask，也需要調整
                    if detection.mask is not None:
                        # 創建原圖大小的mask
                        h, w = image.shape[:2]
                        full_mask = np.zeros((h, w), dtype=detection.mask.dtype)
                        
                        # 將切片mask放置到正確位置
                        # 確保mask尺寸與切片圖像尺寸匹配
                        # slice_image 的實際尺寸是 slice_info.x_end - slice_info.x_start, slice_info.y_end - slice_info.y_start
                        target_mask_h = slice_info.y_end - slice_info.y_start
                        target_mask_w = slice_info.x_end - slice_info.x_start
                        
                        if detection.mask.shape != target_mask_h or detection.mask.shape != target_mask_w:
                            detection.mask = cv2.resize(
                                detection.mask.astype(np.uint8),
                                (target_mask_w, target_mask_h),
                                interpolation=cv2.INTER_NEAREST
                            ).astype(detection.mask.dtype)
                        
                        mask_h, mask_w = detection.mask.shape
                        y_end = min(slice_info.y_start + mask_h, h)
                        x_end = min(slice_info.x_start + mask_w, w)
                        
                        full_mask[slice_info.y_start:y_end, slice_info.x_start:x_end] = \
                            detection.mask[:y_end-slice_info.y_start, :x_end-slice_info.x_start]
                        
                        detection.mask = full_mask
                
                all_detections.extend(slice_detections)
                
                # 進度報告
                if (i + 1) % 10 == 0 or i == len(slices) - 1:
                    self.logger.debug(f"🔄 切片推理進度: {i+1}/{len(slices)}")
                    
            except Exception as e:
                self.logger.warning(f"⚠️ 切片 {i} 推理失敗: {str(e)}")
                continue
            finally:
                # 🗑️ 明確清理每次迭代的臨時變數
                if slice_image is not None:
                    del slice_image
                if slice_detections is not None:
                    del slice_detections
                # 每10個切片強制垃圾回收
                if (i + 1) % 10 == 0:
                    import gc
                    gc.collect()
        
        inference_time = time.time() - inference_start
        self.stats['total_slice_inferences'] += len(slices)
        self.stats['slice_inference_times'].append(inference_time)
        
        self.logger.debug(f"🎯 切片推理完成：{len(all_detections)}個檢測，耗時{inference_time:.3f}秒")
        
        # 🗑️ 最終記憶體清理
        import gc
        gc.collect()
        
        return all_detections
    
    def generate_preview_image(self, image: np.ndarray, slices: List[SliceInfo]) -> np.ndarray:
        """
        生成切片預覽圖像
        
        Args:
            image: 原始圖像
            slices: 切片信息列表
            
        Returns:
            np.ndarray: 預覽圖像
        """
        preview_image = image.copy()
        
        # 繪製切片邊界
        for i, slice_info in enumerate(slices):
            color = (0, 255, 255)  # 青色
            thickness = 1
            
            cv2.rectangle(
                preview_image,
                (slice_info.x_start, slice_info.y_start),
                (slice_info.x_end, slice_info.y_end),
                color,
                thickness
            )
            
            # 添加切片編號
            text = f"S{slice_info.slice_id}"
            text_position = (slice_info.x_start + 5, slice_info.y_start + 20)
            cv2.putText(preview_image, text, text_position, cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return preview_image
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取切片處理統計信息"""
        avg_generation_time = (
            sum(self.stats['slice_generation_times']) / len(self.stats['slice_generation_times'])
            if self.stats['slice_generation_times'] else 0.0
        )
        
        avg_inference_time = (
            sum(self.stats['slice_inference_times']) / len(self.stats['slice_inference_times'])
            if self.stats['slice_inference_times'] else 0.0
        )
        
        return {
            'total_slices_generated': self.stats['total_slices_generated'],
            'total_slice_inferences': self.stats['total_slice_inferences'],
            'average_generation_time': avg_generation_time,
            'average_inference_time': avg_inference_time,
            'config': {
                'slice_size': f"{self.config.width}x{self.config.height}",
                'overlap_ratio': self.config.overlap_ratio,
                'min_slice_size': self.config.min_slice_size
            }
        }
    
    def reset_statistics(self):
        """重置統計信息"""
        self.stats = {
            'total_slices_generated': 0,
            'total_slice_inferences': 0,
            'slice_generation_times': [],
            'slice_inference_times': []
        }
    
    def _get_min_sahi_confidence(self) -> float:
        """
        獲取最低的SAHI置信度閾值
        
        Returns:
            float: 最低的SAHI置信度閾值
        """
        if not self.class_configs:
            return 0.05  # 默認閾值
        
        # 只考慮啟用的類別
        enabled_configs = [config for config in self.class_configs.values() if config.enabled]
        
        if not enabled_configs:
            return 0.05
        
        min_sahi_conf = min(config.sahi_confidence for config in enabled_configs)
        
        # 記錄使用的閾值
        self.logger.debug(f"🔪 SAHI使用最低置信度閾值: {min_sahi_conf:.3f}")
        
        return min_sahi_conf

    def cleanup(self):
        """清理切片處理器資源"""
        self.logger.debug("🧹 清理切片處理器資源")
        
        # 清理統計數據
        self.reset_statistics()
        
        # 🗑️ 清理其他引用
        self.image_processor = None
        self.class_configs = None
        
        # 強制垃圾回收
        import gc
        gc.collect()