#!/usr/bin/env python3
"""
🔧 SAHI融合修復驗證腳本
快速驗證SAHI融合策略是否正確配置和工作
"""

import sys
from pathlib import Path
import logging

# 添加路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """主驗證流程"""
    
    print("🔧 SAHI融合修復驗證")
    print("=" * 50)
    
    try:
        # 1. 檢查配置文件中的關鍵設定
        print("\n1. 📋 檢查關鍵配置...")
        
        # 讀取run_unified_yolo_ultimate.py中的關鍵參數
        config_file = current_dir / "run_unified_yolo_ultimate.py"
        if not config_file.exists():
            print("❌ 找不到配置文件: run_unified_yolo_ultimate.py")
            return False
        
        config_content = config_file.read_text(encoding='utf-8')
        
        # 檢查融合策略設定
        if 'fusion_strategy = "sahi_overlap_merge"' in config_content:
            print("✅ 融合策略: sahi_overlap_merge")
        else:
            print("❌ 融合策略未正確設定為 sahi_overlap_merge")
            return False
        
        # 檢查自適應融合設定
        if 'enable_adaptive_fusion = False' in config_content:
            print("✅ 自適應融合: 已禁用")
        else:
            print("❌ 自適應融合仍然啟用，這會覆蓋SAHI策略")
            return False
            
        # 檢查IoU閾值
        if 'sahi_merge_iou_threshold = 0.1' in config_content:
            print("✅ SAHI IoU閾值: 0.1")
        else:
            print("⚠️ SAHI IoU閾值可能不是預期的0.1")
        
        # 2. 測試配置生成
        print("\n2. 🏗️ 測試配置生成...")
        
        # 設定必要的變數來模擬配置生成
        fusion_strategy = "sahi_overlap_merge"
        enable_adaptive_fusion = False
        sahi_merge_iou_threshold = 0.1
        enable_mask_iou_calculation = True
        sahi_merge_confidence_strategy = "max"
        
        from inference_system.config.unified_config import (
            UnifiedConfig, FusionStrategy
        )
        
        config = UnifiedConfig()
        
        # 測試策略映射
        strategy_mapping = {
            "standard_nms": FusionStrategy.STANDARD_NMS,
            "soft_nms": FusionStrategy.SOFT_NMS,
            "diou_nms": FusionStrategy.DIOU_NMS,
            "weighted_boxes_fusion": FusionStrategy.WEIGHTED_BOXES_FUSION,
            "cluster_nms": FusionStrategy.CLUSTER_NMS,
            "largest_object": FusionStrategy.LARGEST_OBJECT,
            "no_fusion": FusionStrategy.NO_FUSION,
            "iou_cluster_merge": FusionStrategy.IOU_CLUSTER_MERGE,
            "matrix_nms": FusionStrategy.MATRIX_NMS,
            "fast_nms": FusionStrategy.FAST_NMS,
            "batched_nms": FusionStrategy.BATCHED_NMS,
            "sahi_overlap_merge": FusionStrategy.SAHI_OVERLAP_MERGE
        }
        
        if fusion_strategy in strategy_mapping:
            selected_strategy = strategy_mapping[fusion_strategy]
            config.processing.fusion.strategy = selected_strategy
            config.processing.fusion.enable_adaptive_fusion = enable_adaptive_fusion
            config.processing.fusion.sahi_merge_iou_threshold = sahi_merge_iou_threshold
            config.processing.fusion.enable_mask_iou_calculation = enable_mask_iou_calculation
            config.processing.fusion.sahi_merge_confidence_strategy = sahi_merge_confidence_strategy
            
            print(f"✅ 配置生成成功")
            print(f"   - 策略: {selected_strategy.value}")
            print(f"   - 自適應融合: {config.processing.fusion.enable_adaptive_fusion}")
            print(f"   - SAHI IoU閾值: {config.processing.fusion.sahi_merge_iou_threshold}")
        else:
            print("❌ 策略映射失敗")
            return False
        
        # 3. 測試融合引擎創建
        print("\n3. 🔧 測試融合引擎...")
        
        from inference_system.processing.fusion_engine import FusionEngine
        
        fusion_engine = FusionEngine(config.processing.fusion)
        
        # 檢查是否包含SAHI策略
        if FusionStrategy.SAHI_OVERLAP_MERGE in fusion_engine.strategies:
            print("✅ SAHI融合策略已註冊")
        else:
            print("❌ SAHI融合策略未註冊")
            return False
        
        # 4. 模擬重疊檢測測試
        print("\n4. 🧪 模擬重疊檢測測試...")
        
        from inference_system.core.base_inference import Detection
        
        # 創建重疊的Linear_crack檢測（模擬SAHI切片產生的重疊檢測）
        test_detections = [
            Detection(
                bbox=[100.0, 100.0, 200.0, 200.0],
                confidence=0.8,
                class_id=6,  # linear_crack
                class_name="linear_crack"
            ),
            Detection(
                bbox=[150.0, 150.0, 250.0, 250.0],  # 與第一個重疊
                confidence=0.7,
                class_id=6,  # 同類別
                class_name="linear_crack"
            ),
            Detection(
                bbox=[120.0, 120.0, 220.0, 220.0],  # 與前兩個都重疊
                confidence=0.6,
                class_id=6,  # 同類別
                class_name="linear_crack"
            ),
            Detection(
                bbox=[300.0, 300.0, 400.0, 400.0],  # 不重疊的框
                confidence=0.8,
                class_id=6,
                class_name="linear_crack"
            )
        ]
        
        print(f"   輸入檢測數: {len(test_detections)}")
        
        # 執行融合
        fused_detections = fusion_engine.fuse(test_detections)
        
        print(f"   融合後檢測數: {len(fused_detections)}")
        
        # 檢查結果
        if len(fused_detections) < len(test_detections):
            print("✅ SAHI融合測試成功：重疊檢測已被合併")
            reduction = len(test_detections) - len(fused_detections)
            print(f"   合併效果: 減少了{reduction}個重疊檢測")
        else:
            print("❌ SAHI融合測試失敗：重疊檢測未被合併")
            return False
        
        # 5. 最終驗證總結
        print("\n5. 📊 驗證總結...")
        print("✅ 所有驗證項目通過！")
        print("\n🎯 修復狀態: 完成")
        print("🔧 主要修復:")
        print("   - 禁用自適應融合策略（防止覆蓋SAHI策略）")
        print("   - 確保sahi_overlap_merge策略被正確使用")
        print("   - 添加詳細的調試日誌")
        print("\n💡 使用建議:")
        print("   1. 運行 run_unified_yolo_ultimate.py")
        print("   2. 查看日誌中的 '🎯 SAHI專用融合策略已啟用' 信息")
        print("   3. 檢查融合統計：'X → Y' (Y < X 表示成功合併)")
        print("   4. 觀察輸出圖像中重疊框是否顯著減少")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 驗證過程中出現錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 SAHI融合修復驗證 - 通過！")
        print("系統已就緒，可以正確合併重疊的Linear_crack檢測框")
    else:
        print("❌ SAHI融合修復驗證 - 失敗！")
        print("需要進一步檢查配置或代碼")
    
    print("\n按 Enter 鍵退出...")
    input()