#!/usr/bin/env python3
"""
🏗️ 基礎推理抽象類
統一推理接口，定義所有推理引擎的標準接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Union, Optional
import numpy as np
from pathlib import Path
from dataclasses import dataclass
from enum import Enum


class InferenceStatus(Enum):
    """推理狀態枚舉"""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"
    SKIPPED = "skipped"


@dataclass
class TimingInfo:
    """計時信息類"""
    total_time: float
    inference_time: float
    preprocessing_time: float = 0.0
    postprocessing_time: float = 0.0
    visualization_time: float = 0.0


@dataclass
class Detection:
    """檢測結果標準格式"""
    bbox: List[float]  # [x1, y1, x2, y2]
    confidence: float
    class_id: int
    class_name: str
    mask: Optional[np.ndarray] = None
    area: Optional[float] = None


@dataclass
class ConfusionMetrics:
    """混淆矩陣指標"""
    true_positive: int
    false_positive: int
    false_negative: int
    precision: float
    recall: float
    f1_score: float


@dataclass
class StandardResult:
    """標準化推理結果"""
    status: InferenceStatus
    detections: List[Detection]
    metrics: Optional[ConfusionMetrics]
    visualization: Optional[np.ndarray]
    timing: TimingInfo
    metadata: Dict[str, Any]


class BaseInference(ABC):
    """
    統一推理接口抽象基類
    
    所有推理引擎都必須繼承此類並實現其抽象方法
    這確保了統一的接口標準和行為一致性
    """
    
    def __init__(self, device: str = "auto"):
        """
        初始化基礎推理類
        
        Args:
            device: 運算設備 ("auto", "cpu", "cuda", "mps")
        """
        self.device = self._resolve_device(device)
        self.model = None
        self.config = None
        self._statistics = {
            'total_inferences': 0,
            'success_count': 0,
            'failure_count': 0,
            'average_inference_time': 0.0
        }
    
    @abstractmethod
    def predict(self, 
                image: Union[str, np.ndarray],
                output_dir: Optional[str] = None,
                gt_annotations: Optional[List[Dict]] = None,
                **kwargs) -> StandardResult:
        """
        統一的推理接口
        
        Args:
            image: 輸入圖像（路徑或numpy數組）
            output_dir: 輸出目錄
            gt_annotations: Ground Truth標註（用於計算指標）
            **kwargs: 其他參數
            
        Returns:
            StandardResult: 標準化推理結果
        """
        pass
    
    @abstractmethod
    def predict_batch(self, 
                     images: List[Union[str, np.ndarray]],
                     output_dir: Optional[str] = None,
                     gt_annotations: Optional[List[List[Dict]]] = None,
                     **kwargs) -> List[StandardResult]:
        """
        批次推理接口
        
        Args:
            images: 輸入圖像列表
            output_dir: 輸出目錄
            gt_annotations: GT標註列表
            **kwargs: 其他參數
            
        Returns:
            List[StandardResult]: 推理結果列表
        """
        pass
    
    def get_statistics(self) -> Dict[str, Any]:
        """獲取推理統計信息"""
        return self._statistics.copy()
    
    def reset_statistics(self):
        """重置統計信息"""
        self._statistics = {
            'total_inferences': 0,
            'success_count': 0,
            'failure_count': 0,
            'average_inference_time': 0.0
        }
    
    def _update_statistics(self, result: StandardResult):
        """更新統計信息"""
        self._statistics['total_inferences'] += 1
        
        if result.status == InferenceStatus.SUCCESS:
            self._statistics['success_count'] += 1
        else:
            self._statistics['failure_count'] += 1
        
        # 更新平均推理時間
        total_time = self._statistics['average_inference_time'] * (self._statistics['total_inferences'] - 1)
        total_time += result.timing.total_time
        self._statistics['average_inference_time'] = total_time / self._statistics['total_inferences']
    
    def _resolve_device(self, device: str) -> str:
        """解析運算設備"""
        if device == "auto":
            try:
                import torch
                if torch.cuda.is_available():
                    return "cuda"
                elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                    return "mps"
                else:
                    return "cpu"
            except ImportError:
                return "cpu"
        return device
    
    def _validate_image_input(self, image: Union[str, np.ndarray]) -> np.ndarray:
        """驗證並標準化圖像輸入"""
        if isinstance(image, str):
            # 檔案路徑
            image_path = Path(image)
            if not image_path.exists():
                raise FileNotFoundError(f"圖像檔案不存在: {image}")
            
            import cv2
            img = cv2.imread(str(image_path))
            if img is None:
                raise ValueError(f"無法載入圖像: {image}")
            return img
            
        elif isinstance(image, np.ndarray):
            # numpy數組
            if len(image.shape) != 3:
                raise ValueError(f"圖像維度錯誤，期望3維，實際{len(image.shape)}維")
            return image
        else:
            raise TypeError(f"不支援的圖像類型: {type(image)}")
    
    def _create_standard_result(self,
                              status: InferenceStatus,
                              detections: List[Detection],
                              timing: TimingInfo,
                              metrics: Optional[ConfusionMetrics] = None,
                              visualization: Optional[np.ndarray] = None,
                              metadata: Optional[Dict[str, Any]] = None) -> StandardResult:
        """創建標準化結果"""
        return StandardResult(
            status=status,
            detections=detections,
            metrics=metrics,
            visualization=visualization,
            timing=timing,
            metadata=metadata or {}
        )


class InferenceError(Exception):
    """推理相關異常基類"""
    def __init__(self, message: str, error_code: str = "UNKNOWN"):
        self.message = message
        self.error_code = error_code
        super().__init__(f"[{error_code}] {message}")


class ModelLoadError(InferenceError):
    """模型載入異常"""
    def __init__(self, message: str):
        super().__init__(message, "MODEL_LOAD_ERROR")


class InferenceProcessError(InferenceError):
    """推理處理異常"""
    def __init__(self, message: str):
        super().__init__(message, "INFERENCE_PROCESS_ERROR")


class ConfigurationError(InferenceError):
    """配置異常"""
    def __init__(self, message: str):
        super().__init__(message, "CONFIGURATION_ERROR")