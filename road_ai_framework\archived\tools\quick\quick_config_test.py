#!/usr/bin/env python3
"""
🧪 快速配置測試
測試新的配置方式是否正常工作
"""

def test_config_loading():
    """測試配置載入"""
    print("🔍 測試配置載入...")
    
    try:
        # 測試導入
        from high_accuracy_classifier import ClassifierConfig, ClassificationTrainer
        print("✅ 模組導入成功")
        
        # 測試配置創建
        config = ClassifierConfig(
            data_dir="./test_data",
            output_dir="./test_output",
            model_name="efficientnet_v2_l",
            batch_size=8,
            num_epochs=10,
            image_size=224
        )
        
        print("✅ 配置創建成功")
        print(f"   數據目錄: {config.data_dir}")
        print(f"   模型: {config.model_name}")
        print(f"   批次大小: {config.batch_size}")
        print(f"   訓練輪數: {config.num_epochs}")
        print(f"   圖像尺寸: {config.image_size}")
        
        # 測試配置轉換
        config_dict = config.to_dict()
        print("✅ 配置轉換成功")
        
        # 測試從字典創建
        config2 = ClassifierConfig.from_dict(config_dict)
        print("✅ 從字典創建配置成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置測試失敗: {e}")
        return False

def test_config_parameters():
    """測試配置參數"""
    print("\n🔍 測試配置參數...")
    
    try:
        # 讀取文件中的配置參數
        with open("high_accuracy_classifier.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 檢查配置參數是否存在
        required_params = [
            "DATA_DIR", "OUTPUT_DIR", "MODEL_NAME", "IMAGE_SIZE",
            "BATCH_SIZE", "NUM_EPOCHS", "LEARNING_RATE",
            "MIXED_PRECISION", "USE_ADVANCED_AUGMENTATION"
        ]
        
        found_params = []
        for param in required_params:
            if f"{param} = " in content:
                found_params.append(param)
        
        print(f"✅ 找到 {len(found_params)}/{len(required_params)} 個配置參數")
        
        missing_params = set(required_params) - set(found_params)
        if missing_params:
            print(f"⚠️ 缺少參數: {missing_params}")
        
        return len(found_params) == len(required_params)
        
    except Exception as e:
        print(f"❌ 參數測試失敗: {e}")
        return False

def test_directory_structure():
    """測試目錄結構檢查"""
    print("\n🔍 測試目錄結構檢查...")
    
    try:
        from pathlib import Path
        
        # 模擬目錄結構檢查邏輯
        test_data_dir = Path("./test_data")
        
        # 檢查基本邏輯
        exists = test_data_dir.exists()
        print(f"   測試數據目錄存在: {exists}")
        
        train_dir = test_data_dir / "train"
        val_dir = test_data_dir / "val"
        test_dir = test_data_dir / "test"
        
        print(f"   訓練目錄路徑: {train_dir}")
        print(f"   驗證目錄路徑: {val_dir}")
        print(f"   測試目錄路徑: {test_dir}")
        
        print("✅ 目錄結構檢查邏輯正常")
        return True
        
    except Exception as e:
        print(f"❌ 目錄結構測試失敗: {e}")
        return False

def show_usage_example():
    """顯示使用示例"""
    print("\n📋 使用示例:")
    print("""
    1. 📝 修改配置參數:
       打開 high_accuracy_classifier.py
       在 "🔧 配置參數區域" 修改:
       
       DATA_DIR = "./your_data"           # 你的數據路徑
       MODEL_NAME = "efficientnet_v2_l"   # 選擇模型
       BATCH_SIZE = 16                    # 批次大小
       NUM_EPOCHS = 100                   # 訓練輪數
       IMAGE_SIZE = 480                   # 圖像尺寸
    
    2. 🚀 直接運行:
       python high_accuracy_classifier.py
    
    3. 📊 查看輸出:
       - classifier_output/best_model.pth
       - classifier_output/training_curves.png
       - classifier_output/confusion_matrix.png
       - classifier_output/test_results.json
    """)

def main():
    """主函數"""
    print("🧪 快速配置測試")
    print("=" * 50)
    
    tests = [
        ("配置載入", test_config_loading),
        ("配置參數", test_config_parameters),
        ("目錄結構", test_directory_structure)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 測試結果:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 通過率: {passed}/{len(results)} ({passed/len(results)*100:.1f}%)")
    
    if passed == len(results):
        print("\n🎉 所有測試通過！新的配置方式已準備就緒")
        show_usage_example()
    else:
        print(f"\n⚠️ 有 {len(results)-passed} 個測試失敗")
        print("請檢查相關配置")

if __name__ == "__main__":
    main()