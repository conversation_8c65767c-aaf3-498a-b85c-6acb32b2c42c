# 🐳 統一YOLO推理系統 - Docker Compose配置
version: '3.8'

services:
  # 主要推理服務
  unified-yolo-api:
    build: 
      context: .
      dockerfile: Dockerfile
    image: unified-yolo:v3-api
    container_name: unified-yolo-api
    restart: unless-stopped
    
    ports:
      - "8000:8000"  # API服務端口
    
    volumes:
      - ./data/models:/app/data/models:ro    # 模型文件（只讀）
      - ./data/input:/app/data/input:ro      # 輸入數據（只讀）
      - ./data/output:/app/data/output       # 輸出結果
      - ./data/cache:/app/data/cache         # 緩存數據
      - ./data/logs:/app/data/logs           # 日誌文件
    
    environment:
      - MODEL_PATH=/app/data/models/best.pt
      - CUDA_VISIBLE_DEVICES=0
      - MAX_WORKERS=4
      - ENABLE_CACHING=true
      - LOG_LEVEL=INFO
    
    command: ["api"]
    
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    networks:
      - unified-yolo-network

  # Redis緩存服務（可選）
  redis-cache:
    image: redis:7-alpine
    container_name: unified-yolo-redis
    restart: unless-stopped
    
    ports:
      - "6379:6379"
    
    volumes:
      - redis-data:/data
    
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    
    networks:
      - unified-yolo-network

  # 監控服務（Prometheus + Grafana）
  prometheus:
    image: prom/prometheus:latest
    container_name: unified-yolo-prometheus
    restart: unless-stopped
    
    ports:
      - "9090:9090"
    
    volumes:
      - ./docker/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/usr/share/prometheus/console_libraries'
      - '--web.console.templates=/usr/share/prometheus/consoles'
      - '--web.enable-lifecycle'
    
    networks:
      - unified-yolo-network

  grafana:
    image: grafana/grafana:latest
    container_name: unified-yolo-grafana
    restart: unless-stopped
    
    ports:
      - "3000:3000"
    
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./docker/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    
    networks:
      - unified-yolo-network

  # Nginx反向代理（生產環境）
  nginx:
    image: nginx:alpine
    container_name: unified-yolo-nginx
    restart: unless-stopped
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro  # SSL證書
    
    depends_on:
      - unified-yolo-api
    
    networks:
      - unified-yolo-network

  # 批量處理工作節點
  batch-worker:
    build: 
      context: .
      dockerfile: Dockerfile
    image: unified-yolo:v3-worker
    
    volumes:
      - ./data/models:/app/data/models:ro
      - ./data/batch_input:/app/data/input:ro
      - ./data/batch_output:/app/data/output
      - ./data/cache:/app/data/cache
      - ./data/logs:/app/data/logs
    
    environment:
      - INPUT_PATH=/app/data/input
      - OUTPUT_PATH=/app/data/output
      - MODEL_PATH=/app/data/models/best.pt
      - CUDA_VISIBLE_DEVICES=1
      - MAX_WORKERS=8
      - ENABLE_CACHING=true
    
    command: ["batch"]
    
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
      replicas: 0  # 默認不啟動，需要時可擴展
    
    networks:
      - unified-yolo-network

# 數據卷
volumes:
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local

# 網絡
networks:
  unified-yolo-network:
    driver: bridge