#!/usr/bin/env python3
"""
簡化版模型標註工具
Simple Model Labeling Tool

專注於直接檢測模式的輕量級工具：
- 單模型或雙模型檢測
- 可自訂標籤類別 
- 直接圖像檢測模式
- 輸出labelme格式標註檔案
- 批次處理支援
- 選擇性類別保存
"""

import os
import json
import logging
import time
import base64
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict
import numpy as np
import torch
from ultralytics import YOLO
import cv2
import sys
from sahi import AutoDetectionModel
from sahi.predict import get_sliced_prediction
from PIL import Image
import colorsys

# 設置日誌
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('SimpleModelLabeling')

# 導入高級組件 (可選)
try:
    from utils.gpu_manager import get_gpu_manager, DeviceType
    from utils.performance_monitor import get_performance_monitor
    from utils.advanced_memory_manager import create_advanced_memory_manager, CachePolicy
    HAS_ADVANCED_UTILS = True
except ImportError:
    HAS_ADVANCED_UTILS = False

# 導入下世代組件 (2025年前沿技術)
try:
    from utils.next_gen_memory_manager import create_nextgen_memory_manager
    from utils.next_gen_performance_monitor import get_nextgen_performance_monitor
    HAS_NEXTGEN_COMPONENTS = True
except ImportError:
    HAS_NEXTGEN_COMPONENTS = False

# 基本圖像處理工具
class SimpleImageUtils:
    """簡化版圖像工具"""
    
    @staticmethod
    def cv_imread(image_path: str) -> Optional[np.ndarray]:
        """安全讀取圖像"""
        try:
            # 處理中文路徑
            image_path = str(image_path)
            if any(ord(char) > 127 for char in image_path):
                # 中文路徑處理
                with open(image_path, 'rb') as f:
                    data = np.frombuffer(f.read(), np.uint8)
                    image = cv2.imdecode(data, cv2.IMREAD_COLOR)
            else:
                image = cv2.imread(image_path)
            return image
        except Exception as e:
            logger.error(f"讀取圖像失敗 {image_path}: {e}")
            return None
    
    @staticmethod
    def cv_imwrite(image_path: str, image: np.ndarray, quality: int = 90) -> bool:
        """安全寫入圖像"""
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(image_path), exist_ok=True)
            
            # 設置質量參數
            if image_path.lower().endswith('.jpg') or image_path.lower().endswith('.jpeg'):
                encode_params = [cv2.IMWRITE_JPEG_QUALITY, quality]
            else:
                encode_params = []
            
            # 處理中文路徑
            if any(ord(char) > 127 for char in image_path):
                # 中文路徑處理
                _, encoded_img = cv2.imencode('.jpg', image, encode_params)
                with open(image_path, 'wb') as f:
                    f.write(encoded_img.tobytes())
            else:
                cv2.imwrite(image_path, image, encode_params)
            return True
        except Exception as e:
            logger.error(f"寫入圖像失敗 {image_path}: {e}")
            return False


class OutputFormat(Enum):
    """輸出格式枚舉"""
    BBOX = "bbox"  # 邊界框格式
    MASK = "mask"  # 遮罩格式


@dataclass
class ModelConfig:
    """模型配置"""
    primary_model_path: str
    secondary_model_path: Optional[str] = None
    confidence_threshold: float = 0.25
    iou_threshold: float = 0.5
    device: str = "auto"  # auto, cpu, cuda

    # 雙模型協同參數
    enable_consensus: bool = True  # 雙模型共識機制
    consensus_threshold: float = 0.3  # 共識閾值

    # 類別特定閾值
    class_thresholds: Optional[Dict[int, float]] = None  # {class_id: threshold}

    # SAHI 參數
    use_sahi: bool = False
    slice_height: int = 640
    slice_width: int = 640
    overlap_height_ratio: float = 0.2
    overlap_width_ratio: float = 0.2
    roi_ratio: Tuple[float, float, float, float] = (0.0, 0.0, 1.0, 1.0)  # (x1, y1, x2, y2)
    postprocess_type: str = "GREEDYNMM"
    postprocess_match_threshold: float = 0.1

    # 檢測合併參數
    enable_detection_merge: bool = True
    iou_merge_threshold: float = 0.3
    enable_class_filtering: bool = True


@dataclass
class ProcessingConfig:
    """處理配置"""
    output_format: OutputFormat = OutputFormat.BBOX  # 輸出格式：bbox 或 mask

    # 輸出設置
    output_quality: int = 80
    save_visualization: bool = True  # 是否生成可視化內容
    save_visualization_files: bool = True  # 是否保存可視化檔案
    include_base64: bool = True

    # Mask 相關參數
    mask_expansion: int = 5  # mask 膨脹像素數
    mask_fill_value: int = 255  # mask 填充值

    # 批次處理
    batch_size: int = 10  # 用於記憶體管理

    # 選擇性類別保存
    target_classes: Optional[List[int]] = None  # 指定要保存的類別ID，None表示保存全部
    skip_empty: bool = True  # 跳過空檢測結果

    # 增強可視化參數
    visualization_size: Tuple[int, int] = (1200, 900)  # 可視化圖像尺寸
    show_masks: bool = True  # 是否顯示遮罩
    mask_alpha: float = 0.35  # 遮罩透明度


@dataclass
class ClassMapping:
    """類別映射配置"""
    class_names: Dict[int, str]  # {class_id: class_name}

    @classmethod
    def from_string(cls, mapping_str: str) -> 'ClassMapping':
        """從字串創建類別映射: \"0:face,1:license_plate\" """
        class_names = {}
        try:
            for pair in mapping_str.split(','):
                if ':' in pair:
                    id_str, name = pair.split(':', 1)
                    class_id = int(id_str.strip())
                    class_name = name.strip()
                    class_names[class_id] = class_name
        except Exception as e:
            logger.error(f"解析類別映射失敗: {e}")
            raise ValueError(f"無效的類別映射格式: {mapping_str}")

        if not class_names:
            raise ValueError("類別映射不能為空")

        return cls(class_names=class_names)

    @classmethod
    def default(cls) -> 'ClassMapping':
        """預設類別映射"""
        return cls(class_names={0: 'face', 1: 'license_plate'})


class ModelManager:
    """模型管理器"""

    def __init__(self, config: ModelConfig):
        self.config = config
        self.primary_model = None
        self.secondary_model = None
        self.sahi_model = None
        self.device = self._get_device()

        # 用於 SAHI 可視化的色彩調色板
        self.color_palette = self._generate_color_palette()
        self.class_colors = {}

        self._load_models()

    def _get_device(self) -> str:
        """確定使用的設備"""
        if self.config.device == "auto":
            return "cuda" if torch.cuda.is_available() else "cpu"
        return self.config.device

    def _generate_color_palette(self, num_colors: int = 50) -> List[Tuple[int, int, int]]:
        """生成色彩調色板"""
        palette = []
        h = 0.0
        phi = 0.61803398875  # 黃金比例
        for _ in range(num_colors):
            h = (h + phi) % 1.0
            r, g, b = colorsys.hsv_to_rgb(h, 0.85, 0.95)
            # BGR 格式用於 OpenCV
            palette.append((int(b * 255), int(g * 255), int(r * 255)))
        return palette

    def _load_models(self):
        """載入模型"""
        # 載入主模型
        if not Path(self.config.primary_model_path).exists():
            raise FileNotFoundError(f"主模型檔案不存在: {self.config.primary_model_path}")

        logger.info(f"載入主模型: {self.config.primary_model_path}")
        self.primary_model = YOLO(self.config.primary_model_path)
        self.primary_model.to(self.device)

        # 載入副模型（可選）
        if self.config.secondary_model_path:
            if not Path(self.config.secondary_model_path).exists():
                logger.warning(f"副模型檔案不存在，忽略: {self.config.secondary_model_path}")
                self.config.secondary_model_path = None
            else:
                logger.info(f"載入副模型: {self.config.secondary_model_path}")
                self.secondary_model = YOLO(self.config.secondary_model_path)
                self.secondary_model.to(self.device)

        # 載入 SAHI 模型（可選）
        if self.config.use_sahi:
            logger.info(f"初始化 SAHI 模型")
            self.sahi_model = AutoDetectionModel.from_pretrained(
                model_type="ultralytics",
                model_path=self.config.primary_model_path,
                confidence_threshold=self.config.confidence_threshold,
                device=self.device
            )

        logger.info(f"模型載入完成，使用設備: {self.device}")

    def predict_single_model(self, image: np.ndarray, model: YOLO) -> List[Dict]:
        """單模型預測支援 mask 和 boxes"""
        predictions = []

        try:
            results = model.predict(
                source=image,
                conf=self.config.confidence_threshold,
                iou=self.config.iou_threshold,
                verbose=False
            )

            if results and len(results) > 0:
                result = results[0]
                if hasattr(result, 'boxes') and result.boxes is not None:
                    boxes = result.boxes
                    masks = getattr(result, 'masks', None)

                    for i in range(len(boxes)):
                        cls_id = int(boxes.cls[i].item())
                        conf = float(boxes.conf[i].item())
                        x1, y1, x2, y2 = boxes.xyxy[i].tolist()

                        pred_dict = {
                            'class_id': cls_id,
                            'confidence': conf,
                            'bbox': [x1, y1, x2, y2],
                            'area': (x2 - x1) * (y2 - y1),
                            'mask': None
                        }

                        # 如果有 mask 資訊
                        if masks is not None and i < len(masks.data):
                            mask = masks.data[i].cpu().numpy().astype(bool)
                            pred_dict['mask'] = mask

                        predictions.append(pred_dict)

        except Exception as e:
            logger.error(f"模型預測失敗: {e}")

        return predictions

    def predict_sahi(self, image: np.ndarray, **kwargs) -> List[Dict]:
        """使用 SAHI 進行切片預測"""
        if not self.config.use_sahi or not self.sahi_model:
            raise ValueError("SAHI 模式未啟用或未初始化")

        predictions = []

        try:
            # 轉換為 PIL 圖像
            if len(image.shape) == 3:
                pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            else:
                pil_image = Image.fromarray(image)

            # 計算 ROI 區域
            W, H = pil_image.size
            l, t, r, b = self.config.roi_ratio
            x1, y1, x2, y2 = int(W * l), int(H * t), int(W * r), int(H * b)
            roi = pil_image.crop((x1, y1, x2, y2))

            # SAHI 切片預測
            sahi_result = get_sliced_prediction(
                image=roi,
                detection_model=self.sahi_model,
                overlap_height_ratio=self.config.overlap_height_ratio,
                overlap_width_ratio=self.config.overlap_width_ratio,
                perform_standard_pred=True,
                postprocess_type=self.config.postprocess_type,
                postprocess_match_threshold=self.config.postprocess_match_threshold,
                auto_slice_resolution=True
            )

            # 轉換 SAHI 結果
            for obj_pred in sahi_result.object_prediction_list:
                bx1, by1, bx2, by2 = obj_pred.bbox.to_xyxy()

                # 轉換回原圖座標
                bbox = [bx1 + x1, by1 + y1, bx2 + x1, by2 + y1]

                pred_dict = {
                    'class_id': obj_pred.category.id,
                    'confidence': obj_pred.score.value,
                    'bbox': bbox,
                    'area': (bx2 - bx1) * (by2 - by1),
                    'mask': None
                }

                # 如果有 mask
                if obj_pred.mask is not None:
                    mask_roi = obj_pred.mask.bool_mask
                    # 將 ROI mask 轉換回全圖 mask
                    full_mask = np.zeros((H, W), dtype=bool)
                    full_mask[y1:y2, x1:x2] = mask_roi
                    pred_dict['mask'] = full_mask

                predictions.append(pred_dict)

        except Exception as e:
            logger.error(f"SAHI 預測失敗: {e}")

        return predictions

    def predict(self, image: np.ndarray, **kwargs) -> List[Dict]:
        """執行預測（支援 SAHI/單/雙模型）並包含全部高級功能"""

        # SAHI 模式
        if self.config.use_sahi:
            predictions = self.predict_sahi(image, **kwargs)
        else:
            # 標準模式
            predictions = self.predict_single_model(image, self.primary_model)

            # 如果有副模型，做雙模型協同
            if self.secondary_model:
                secondary_predictions = self.predict_single_model(image, self.secondary_model)

                if self.config.enable_consensus:
                    predictions = self._merge_predictions(predictions, secondary_predictions)
                else:
                    all_predictions = predictions + secondary_predictions
                    predictions = self._remove_duplicates(all_predictions)

        # 類別特定閥值過濾
        if self.config.class_thresholds:
            predictions = self._apply_class_thresholds(predictions)

        # 類別間過濾 (linear vs alligator, linear vs joint)
        if self.config.enable_class_filtering:
            predictions = self._apply_class_filtering(predictions)

        # 檢測合併
        if self.config.enable_detection_merge:
            predictions = self._merge_detections(predictions)

        return predictions

    def _merge_predictions(self, primary: List[Dict], secondary: List[Dict]) -> List[Dict]:
        """雙模型共識合併"""
        merged_predictions = []

        # 先加入主模型的高置信度預測
        for pred in primary:
            if pred['confidence'] >= self.config.consensus_threshold:
                merged_predictions.append(pred)

        # 檢查副模型預測是否與主模型一致
        for sec_pred in secondary:
            is_consensus = False

            for prim_pred in primary:
                if self._calculate_iou(sec_pred['bbox'], prim_pred['bbox']) > 0.3:
                    is_consensus = True
                    break

            # 如果副模型預測與主模型一致，且置信度足夠，加入結果
            if is_consensus and sec_pred['confidence'] >= self.config.consensus_threshold:
                # 避免重複
                if not any(self._calculate_iou(sec_pred['bbox'], pred['bbox']) > 0.5
                           for pred in merged_predictions):
                    merged_predictions.append(sec_pred)

        return merged_predictions

    def _remove_duplicates(self, predictions: List[Dict], iou_threshold: float = 0.5) -> List[Dict]:
        """移除重複預測"""
        if not predictions:
            return predictions

        # 按置信度排序
        predictions = sorted(predictions, key=lambda x: x['confidence'], reverse=True)

        filtered_predictions = []
        for pred in predictions:
            is_duplicate = False
            for filtered_pred in filtered_predictions:
                if self._calculate_iou(pred['bbox'], filtered_pred['bbox']) > iou_threshold:
                    is_duplicate = True
                    break

            if not is_duplicate:
                filtered_predictions.append(pred)

        return filtered_predictions

    def _calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """計算IoU"""
        x1_1, y1_1, x2_1, y2_1 = box1
        x1_2, y1_2, x2_2, y2_2 = box2

        # 計算交集
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)

        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0

        intersection = (x2_i - x1_i) * (y2_i - y1_i)

        # 計算聯集
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    def _apply_class_thresholds(self, predictions: List[Dict]) -> List[Dict]:
        """應用類別特定闾值"""
        filtered_predictions = []

        for pred in predictions:
            cls_id = pred['class_id']
            confidence = pred['confidence']
            threshold = self.config.class_thresholds.get(cls_id, self.config.confidence_threshold)

            if confidence >= threshold:
                filtered_predictions.append(pred)

        return filtered_predictions

    def _apply_class_filtering(self, predictions: List[Dict]) -> List[Dict]:
        """應用類別間過濾 (linear vs alligator, linear vs joint)"""
        # 獲取類別名稱
        names = getattr(self.primary_model, 'names', {})

        # Step 1: linear_crack vs Alligator_crack (面積小者移除)
        filtered_predictions = []
        keep_indices = list(range(len(predictions)))

        for i, pred_i in enumerate(predictions):
            if i not in keep_indices:
                continue

            cls_i = pred_i['class_id']
            name_i = names.get(cls_i, str(cls_i)).split('_')[0].lower()

            if name_i == 'linear':
                box_i = pred_i['bbox']
                area_i = pred_i['area']

                for j, pred_j in enumerate(predictions):
                    if j <= i or j not in keep_indices:
                        continue

                    cls_j = pred_j['class_id']
                    name_j = names.get(cls_j, str(cls_j)).split('_')[0].lower()

                    if name_j == 'alligator':
                        box_j = pred_j['bbox']
                        area_j = pred_j['area']
                        iou = self._calculate_iou(box_i, box_j)

                        if iou > 0.0:
                            # 移除面積較小者
                            if area_j < area_i:
                                keep_indices.remove(j)
                            else:
                                keep_indices.remove(i)
                                break

        # 保留第一步過濾後的結果
        step1_predictions = [predictions[i] for i in keep_indices]

        # Step 2: linear_crack vs joint (mask 或 IoU 覆蓋 > 0.3 時刪 linear)
        final_keep_indices = list(range(len(step1_predictions)))

        for i, pred_i in enumerate(step1_predictions):
            if i not in final_keep_indices:
                continue

            cls_i = pred_i['class_id']
            name_i = names.get(cls_i, str(cls_i)).split('_')[0].lower()

            if name_i == 'linear':
                for j, pred_j in enumerate(step1_predictions):
                    if j == i or j not in final_keep_indices:
                        continue

                    cls_j = pred_j['class_id']
                    name_j = names.get(cls_j, str(cls_j)).split('_')[0].lower()

                    if name_j == 'joint':
                        # 如果有 mask，使用 mask 計算覆蓋率
                        if pred_i['mask'] is not None and pred_j['mask'] is not None:
                            mask_i = pred_i['mask']
                            mask_j = pred_j['mask']
                            inter = np.logical_and(mask_i, mask_j).sum()
                            total_linear = mask_i.sum()

                            if total_linear > 0 and (inter / total_linear) > 0.3:
                                final_keep_indices.remove(i)
                                break
                        else:
                            # 使用 IoU 近似
                            iou = self._calculate_iou(pred_i['bbox'], pred_j['bbox'])
                            if iou > 0.3:
                                final_keep_indices.remove(i)
                                break

        return [step1_predictions[i] for i in final_keep_indices]

    def _merge_detections(self, predictions: List[Dict]) -> List[Dict]:
        """合併相同類別的重疊檢測"""
        if not predictions:
            return predictions

        # 按類別分組
        class_groups = defaultdict(list)
        for i, pred in enumerate(predictions):
            class_groups[pred['class_id']].append((i, pred))

        merged_predictions = []
        used_indices = set()

        # 對每個類別獨立處理
        for cls_id, group in class_groups.items():
            indices = [item[0] for item in group]
            preds = [item[1] for item in group]

            i = 0
            while i < len(indices):
                idx_i = indices[i]
                if idx_i in used_indices:
                    i += 1
                    continue

                pred_i = preds[i]
                merged_box = pred_i['bbox'].copy()
                merged_mask = pred_i['mask']
                best_conf = pred_i['confidence']

                used_indices.add(idx_i)

                # 找出與當前檢測重疊的其他檢測
                for j in range(i + 1, len(indices)):
                    idx_j = indices[j]
                    if idx_j in used_indices:
                        continue

                    pred_j = preds[j]
                    iou = self._calculate_iou(merged_box, pred_j['bbox'])

                    if iou > self.config.iou_merge_threshold:
                        used_indices.add(idx_j)

                        # 合併 bbox
                        merged_box[0] = min(merged_box[0], pred_j['bbox'][0])
                        merged_box[1] = min(merged_box[1], pred_j['bbox'][1])
                        merged_box[2] = max(merged_box[2], pred_j['bbox'][2])
                        merged_box[3] = max(merged_box[3], pred_j['bbox'][3])

                        # 合併置信度
                        best_conf = max(best_conf, pred_j['confidence'])

                        # 合併 mask
                        if merged_mask is not None and pred_j['mask'] is not None:
                            merged_mask = np.logical_or(merged_mask, pred_j['mask'])
                        elif merged_mask is None and pred_j['mask'] is not None:
                            merged_mask = pred_j['mask'].copy()

                # 添加合併後的檢測
                merged_pred = {
                    'class_id': cls_id,
                    'confidence': best_conf,
                    'bbox': merged_box,
                    'area': (merged_box[2] - merged_box[0]) * (merged_box[3] - merged_box[1]),
                    'mask': merged_mask
                }
                merged_predictions.append(merged_pred)

                i += 1

        return merged_predictions


class SimpleModelLabelingTool:
    """簡化版模型標註工具"""

    def __init__(self,
                 model_config: ModelConfig,
                 processing_config: ProcessingConfig,
                 class_mapping: ClassMapping):
        """初始化工具"""
        self.model_config = model_config
        self.processing_config = processing_config
        self.class_mapping = class_mapping

        # 創建核心組件
        self.model_manager = ModelManager(model_config)
        self.image_utils = SimpleImageUtils()

        # 高級組件初始化
        self.gpu_manager = None
        self.performance_monitor = None
        self.advanced_memory_manager = None
        
        if HAS_ADVANCED_UTILS:
            try:
                # 初始化GPU管理器
                self.gpu_manager = get_gpu_manager()
                best_device = self.gpu_manager.get_best_device(preferred_type=DeviceType.CUDA)
                if best_device:
                    logger.info(f"GPU管理器: 選擇最佳設備 {best_device.name} ({best_device.total_memory}MB)")
                
                # 初始化性能監控器
                self.performance_monitor = get_performance_monitor()
                self.performance_monitor.start_monitoring()
                
                # 初始化高級記憶體管理器
                self.advanced_memory_manager = create_advanced_memory_manager(
                    max_memory_mb=1536,  # 簡化工具需要較少記憶體
                    cache_policy=CachePolicy.LRU,
                    enable_streaming=False,
                    chunk_size_mb=128
                )
                
                logger.info("高級組件初始化完成")
                
                # 初始化下世代組件 (2025年前沿技術)
                if HAS_NEXTGEN_COMPONENTS:
                    try:
                        # 下世代記憶體管理器 (PyTorch 2025)
                        self.nextgen_memory_manager = create_nextgen_memory_manager(
                            pytorch_2025_integration=True,
                            ai_driven_cache=True,
                            unified_memory_support=True
                        )
                        
                        # 下世代性能監控器 (Scalene + AI預測)
                        self.nextgen_performance_monitor = get_nextgen_performance_monitor()
                        if self.nextgen_performance_monitor:
                            self.nextgen_performance_monitor.enable_ai_prediction(True)
                        
                        logger.info("簡化工具下世代組件初始化完成")
                        
                    except Exception as e:
                        logger.warning(f"下世代組件初始化失敗: {e}")
                
            except Exception as e:
                logger.warning(f"高級組件初始化失敗: {e}")

        logger.info("簡化版模型標註工具初始化完成")
        logger.info(f"主模型: {model_config.primary_model_path}")
        if model_config.secondary_model_path:
            logger.info(f"副模型: {model_config.secondary_model_path}")
        logger.info(f"類別映射: {class_mapping.class_names}")

    def _image_to_base64(self, image_path: str) -> Optional[str]:
        """圖像轉base64"""
        try:
            with open(image_path, 'rb') as f:
                return base64.b64encode(f.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"轉換base64失敗 {image_path}: {e}")
            return None

    def _generate_mask_from_bbox(self, bbox: List[float], img_width: int, img_height: int) -> List[List[float]]:
        """從邊界框生成mask多邊形點"""
        x1, y1, x2, y2 = bbox

        # 應用膨脹
        expansion = self.processing_config.mask_expansion
        x1 = max(0, x1 - expansion)
        y1 = max(0, y1 - expansion)
        x2 = min(img_width, x2 + expansion)
        y2 = min(img_height, y2 + expansion)

        # 生成矩形mask的四個角點
        points = [
            [float(x1), float(y1)],  # 左上
            [float(x2), float(y1)],  # 右上
            [float(x2), float(y2)],  # 右下
            [float(x1), float(y2)]   # 左下
        ]

        return points

    def _mask_to_polygon(self, mask: np.ndarray) -> List[List[float]]:
        """將 mask 轉換為多邊形點列表"""
        try:
            # 使用 cv2.findContours 找到輪廓
            if mask.dtype != np.uint8:
                mask_uint8 = (mask * 255).astype(np.uint8)
            else:
                mask_uint8 = mask.astype(np.uint8)

            contours, _ = cv2.findContours(mask_uint8, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return []

            # 取最大的輪廓
            largest_contour = max(contours, key=cv2.contourArea)

            # 簡化輪廓，減少點數
            epsilon = 0.005 * cv2.arcLength(largest_contour, True)
            simplified_contour = cv2.approxPolyDP(largest_contour, epsilon, True)

            # 轉換為點列表
            points = []
            for point in simplified_contour:
                x, y = point[0]
                points.append([float(x), float(y)])

            return points

        except Exception as e:
            logger.warning(f"Mask 轉換多邊形失敗: {e}, 使用 bbox 替代")
            return []

    def _save_labelme_annotation(self, predictions: List[Dict],
                                 output_path: str, img_width: int, img_height: int,
                                 image_path: str = "") -> None:
        """保存labelme格式標註（支援真實 mask 或 bbox）"""
        # 準備shapes
        shapes = []
        for pred in predictions:
            x1, y1, x2, y2 = pred['bbox']
            class_name = self.class_mapping.class_names.get(
                pred['class_id'], f'class_{pred["class_id"]}')

            if self.processing_config.output_format == OutputFormat.BBOX:
                # 邊界框格式
                shape = {
                    "label": class_name,
                    "points": [[float(x1), float(y1)], [float(x2), float(y2)]],
                    "group_id": None,
                    "shape_type": "rectangle",
                    "flags": {}
                }
            else:  # MASK 格式
                # 儘量使用真實 mask，如果沒有則使用 bbox 生成
                mask_points = []

                if pred.get('mask') is not None:
                    # 使用真實 mask
                    mask_points = self._mask_to_polygon(pred['mask'])

                # 如果 mask 轉換失敗或無 mask，使用 bbox 生成
                if not mask_points:
                    mask_points = self._generate_mask_from_bbox(
                        pred['bbox'], img_width, img_height)

                shape = {
                    "label": class_name,
                    "points": mask_points,
                    "group_id": None,
                    "shape_type": "polygon",
                    "flags": {
                        # 標記是否為真實 mask
                        "real_mask": pred.get('mask') is not None
                    }
                }

            shapes.append(shape)

        # 準備base64數據
        image_data = None
        if self.processing_config.include_base64 and os.path.exists(image_path):
            image_data = self._image_to_base64(image_path)

        # 創建labelme標註數據
        labelme_data = {
            "version": "5.0.1",
            "flags": {},
            "shapes": shapes,
            "imagePath": os.path.basename(image_path) if image_path else "",
            "imageData": image_data,
            "imageHeight": img_height,
            "imageWidth": img_width
        }

        # 保存檔案
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(labelme_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存標註檔案失敗 {output_path}: {e}")

    def _get_class_color(self, class_id: int) -> Tuple[int, int, int]:
        """獲取類別色彩"""
        if class_id not in self.model_manager.class_colors:
            color_idx = len(self.model_manager.class_colors) % len(
                self.model_manager.color_palette)
            self.model_manager.class_colors[class_id] = self.model_manager.color_palette[color_idx]
        return self.model_manager.class_colors[class_id]

    def _overlay_mask(self, image: np.ndarray, mask: np.ndarray, color: Tuple[int, int, int], alpha: float = 0.35) -> np.ndarray:
        """遮罩覆蓋"""
        for c in range(3):
            image[..., c] = np.where(
                mask, image[..., c] * (1 - alpha) + color[c] * alpha, image[..., c])
        return image

    def _filter_predictions_by_class(self, predictions: List[Dict]) -> List[Dict]:
        """根據目標類別過濾預測結果 - 修改版：如果有target_classes中的任一類別存在，則保存所有類別"""
        if self.processing_config.target_classes is None:
            return predictions

        # 檢查是否存在target_classes中的任一類別
        has_target_class = any(pred['class_id'] in self.processing_config.target_classes 
                              for pred in predictions)
        
        if has_target_class:
            # 如果有target_classes中的類別，保存所有預測結果
            logger.info(f"  檢測到目標類別，保存所有 {len(predictions)} 個檢測")
            return predictions
        else:
            # 如果沒有target_classes中的類別，返回空結果或只保存target_classes
            logger.info(f"  未檢測到目標類別，跳過此圖像")
            return []  # 或者保持原邏輯：return [pred for pred in predictions if pred['class_id'] in self.processing_config.target_classes]

    def _create_visualization(self, image: np.ndarray, predictions: List[Dict], output_path: str) -> None:
        """創建可視化"""
        if not predictions:
            return

        vis_image = image.copy()

        # 調整到指定尺寸
        target_width, target_height = self.processing_config.visualization_size
        if vis_image.shape[:2] != (target_height, target_width):
            vis_image = cv2.resize(vis_image, (target_width, target_height))

            # 調整預測結果的座標
            h_scale = target_height / image.shape[0]
            w_scale = target_width / image.shape[1]

            scaled_predictions = []
            for pred in predictions:
                scaled_pred = pred.copy()
                x1, y1, x2, y2 = pred['bbox']
                scaled_pred['bbox'] = [x1 * w_scale, y1 * h_scale, x2 * w_scale, y2 * h_scale]

                # 調整 mask
                if pred['mask'] is not None:
                    scaled_mask = cv2.resize(pred['mask'].astype(np.uint8),
                                             (target_width, target_height),
                                             interpolation=cv2.INTER_NEAREST).astype(bool)
                    scaled_pred['mask'] = scaled_mask

                scaled_predictions.append(scaled_pred)
        else:
            scaled_predictions = predictions

        # 繪製檢測結果
        for pred in scaled_predictions:
            x1, y1, x2, y2 = [int(coord) for coord in pred['bbox']]
            class_id = pred['class_id']
            class_name = self.class_mapping.class_names.get(class_id, f'class_{class_id}')
            confidence = pred['confidence']
            color = self._get_class_color(class_id)

            # 只取英文部分
            display_name = class_name.split('_')[0] if '_' in class_name else class_name

            # 繪製 mask
            if self.processing_config.show_masks and pred['mask'] is not None:
                self._overlay_mask(vis_image, pred['mask'], color, self.processing_config.mask_alpha)

            # 繪製邊界框
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), color, 2)

            # 添加標籤
            label = f"{display_name} {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_DUPLEX, 1.4, 3)[0]

            # 確保標籤在圖像範圍內
            label_y = max(y1 - 12, label_size[1])

            # 繪製文字（增強效果）
            cv2.putText(vis_image, label, (x1, label_y),
                        cv2.FONT_HERSHEY_DUPLEX, 1.4, color, 3, cv2.LINE_AA)

        # 只有當 save_visualization_files 為 True 時才保存檔案
        if self.processing_config.save_visualization_files:
            self.image_utils.cv_imwrite(output_path, vis_image, self.processing_config.output_quality)

    def process_single_image(self, image_path: str, output_dir: str) -> Dict:
        """處理單張圖像"""
        logger.info(f"直接檢測模式處理: {image_path}")

        # 讀取圖像
        image = self.image_utils.cv_imread(image_path)
        if image is None:
            raise ValueError(f"無法讀取圖像: {image_path}")

        img_height, img_width = image.shape[:2]
        base_name = Path(image_path).stem

        # 執行預測
        start_time = time.time()
        predictions = self.model_manager.predict(image)
        prediction_time = time.time() - start_time

        logger.info(f"  檢測到 {len(predictions)} 個目標，耗時 {prediction_time:.2f}s")

        # 過濾目標類別
        filtered_predictions = self._filter_predictions_by_class(predictions)

        # 如果啟用了跳過空結果且沒有符合的檢測
        if self.processing_config.skip_empty and not filtered_predictions:
            logger.info(f"  跳過空結果: {image_path}")
            return {
                'mode': 'direct',
                'input_path': image_path,
                'skipped': True,
                'reason': 'no_target_detections',
                'detections_count': 0,
                'processing_time': prediction_time
            }

        # 創建輸出目錄
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 複製原始圖像到輸出目錄
        output_image_path = output_dir / f"{base_name}.jpg"
        self.image_utils.cv_imwrite(str(output_image_path), image, self.processing_config.output_quality)

        # 保存標註（使用過濾後的結果）
        json_path = output_dir / f"{base_name}.json"
        self._save_labelme_annotation(
            filtered_predictions, str(json_path), img_width, img_height, str(output_image_path))

        # 創建可視化
        vis_path = None
        if self.processing_config.save_visualization and filtered_predictions:
            vis_path = output_dir / f"{base_name}_vis.jpg"
            self._create_visualization(image, filtered_predictions, str(vis_path))
            # 如果沒有實際保存檔案，則不返回路徑
            if not self.processing_config.save_visualization_files:
                vis_path = None

        return {
            'mode': 'direct',
            'input_path': image_path,
            'output_image': str(output_image_path),
            'output_json': str(json_path),
            'visualization': str(vis_path) if vis_path else None,
            'detections_count': len(filtered_predictions),
            'processing_time': prediction_time
        }

    def process_batch(self, input_path: str, output_dir: str) -> List[Dict]:
        """批次處理"""
        input_path = Path(input_path)

        if input_path.is_file():
            # 單個檔案
            return [self.process_single_image(str(input_path), output_dir)]

        elif input_path.is_dir():
            # 目錄批次處理
            results = []
            image_extensions = ['.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG']

            image_files = []
            for ext in image_extensions:
                image_files.extend(input_path.glob(f"*{ext}"))

            if not image_files:
                logger.warning(f"在目錄中未找到圖像檔案: {input_path}")
                return results

            logger.info(f"開始批次處理 {len(image_files)} 個檔案")

            for i, image_file in enumerate(image_files, 1):
                try:
                    logger.info(f"處理 [{i}/{len(image_files)}]: {image_file.name}")
                    result = self.process_single_image(str(image_file), output_dir)
                    results.append(result)

                except Exception as e:
                    logger.error(f"處理失敗 {image_file.name}: {e}")
                    continue

            # 統計結果
            successful_count = len(results)
            total_detections = sum(r.get('detections_count', 0) for r in results)
            total_time = sum(r.get('processing_time', 0) for r in results)

            logger.info(f"批次處理完成: {successful_count}/{len(image_files)} 成功")
            logger.info(f"總檢測數: {total_detections}, 總耗時: {total_time:.2f}s")

            return results

        else:
            raise ValueError(f"無效的輸入路徑: {input_path}")


def create_simple_tool_with_params(**params) -> SimpleModelLabelingTool:
    """使用指定參數創建簡化工具"""
    
    # 模型配置
    model_config = ModelConfig(
        primary_model_path=params.get('model_path', 'model/best.pt'),
        secondary_model_path=params.get('secondary_model_path', None),
        confidence_threshold=params.get('conf', 0.25),
        iou_threshold=params.get('iou', 0.5),
        use_sahi=params.get('use_sahi', False),
        class_thresholds=params.get('class_thresholds', None),
        enable_detection_merge=params.get('enable_merge', True),
        enable_class_filtering=params.get('enable_class_filtering', True)
    )
    
    # 處理配置
    processing_config = ProcessingConfig(
        output_format=OutputFormat.MASK if params.get('save_masks', False) else OutputFormat.BBOX,
        target_classes=params.get('target_classes', None),
        skip_empty=params.get('skip_empty', True),
        save_visualization=params.get('save_vis', True),
        save_visualization_files=params.get('save_vis_files', True),
        show_masks=params.get('show_masks', True),
        visualization_size=params.get('vis_size', (1200, 900))
    )
    
    # 類別映射
    class_mapping_str = params.get('class_mapping', "0:face,1:license_plate")
    class_mapping = ClassMapping.from_string(class_mapping_str)
    
    return SimpleModelLabelingTool(model_config, processing_config, class_mapping)


def main():
    """主函數"""
    print("=== 簡化版模型標註工具 ===")
    print("專注於直接檢測模式的輕量級工具")
    
    # 示例配置
    params = {
        'model_path': 'model/best.pt',
        'conf': 0.25,
        'target_classes': [0, 1],  # face, license_plate
        'save_masks': True,
        'class_mapping': "0:face,1:license_plate"
    }
    
    try:
        tool = create_simple_tool_with_params(**params)
        print("工具初始化成功")
        
        # 這裡可以添加測試代碼
        # results = tool.process_batch('input_folder', 'output_folder')
        
    except Exception as e:
        print(f"工具初始化失敗: {e}")


if __name__ == "__main__":
    main()