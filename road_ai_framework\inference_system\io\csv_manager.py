#!/usr/bin/env python3
"""
📊 CSV統計管理器
處理CSV報告生成和統計數據管理，從原始 unified_yolo_inference.py 中提取
"""

import csv
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

from ..core.base_inference import Detection, ConfusionMetrics
from ..config import UnifiedConfig


class CSVManager:
    """
    CSV統計管理器
    
    功能：
    - 檢測結果統計
    - 類別統計
    - 性能統計  
    - CSV報告生成
    """
    
    def __init__(self, config: UnifiedConfig):
        """
        初始化CSV管理器
        
        Args:
            config: 統一配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 統計數據
        self.stats = {
            'total_images': 0,
            'total_detections': 0,
            'class_counts': {},
            'processing_times': [],
            'image_records': []
        }
        
        # CSV文件路徑
        self.csv_dir = None
        if config.output_path:
            self.csv_dir = Path(config.output_path) / config.output.csv_output_dirname
            self.csv_dir.mkdir(parents=True, exist_ok=True)
    
    def add_image_result(self,
                        image_name: str,
                        detections: List[Detection],
                        processing_time: float,
                        metrics: Optional[ConfusionMetrics] = None):
        """
        添加單張圖像的處理結果
        
        Args:
            image_name: 圖像名稱
            detections: 檢測結果
            processing_time: 處理時間
            metrics: 混淆矩陣指標
        """
        try:
            # 更新總體統計
            self.stats['total_images'] += 1
            self.stats['total_detections'] += len(detections)
            self.stats['processing_times'].append(processing_time)
            
            # 更新類別統計
            for detection in detections:
                class_name = detection.class_name
                if class_name not in self.stats['class_counts']:
                    self.stats['class_counts'][class_name] = 0
                self.stats['class_counts'][class_name] += 1
            
            # 記錄詳細信息
            image_record = {
                'image_name': image_name,
                'detection_count': len(detections),
                'processing_time': processing_time,
                'timestamp': datetime.now().isoformat(),
                'detections': [
                    {
                        'class_name': det.class_name,
                        'confidence': det.confidence,
                        'bbox': det.bbox,
                        'area': det.area
                    }
                    for det in detections
                ]
            }
            
            # 添加指標信息
            if metrics:
                image_record.update({
                    'tp': metrics.true_positive,
                    'fp': metrics.false_positive,
                    'fn': metrics.false_negative,
                    'precision': metrics.precision,
                    'recall': metrics.recall,
                    'f1_score': metrics.f1_score
                })
            
            self.stats['image_records'].append(image_record)
            
            self.logger.debug(f"📊 添加圖像統計: {image_name}, {len(detections)}個檢測")
            
        except Exception as e:
            self.logger.error(f"❌ 添加圖像結果失敗: {str(e)}")
    
    def update_statistics(self,
                         image_path: str,
                         detections: List[Detection],
                         timing_info: Any,
                         confusion_metrics: Optional[ConfusionMetrics] = None):
        """
        更新統計信息（兼容接口）
        
        Args:
            image_path: 圖像路徑
            detections: 檢測結果
            timing_info: 計時信息對象
            confusion_metrics: 混淆矩陣指標
        """
        try:
            from pathlib import Path
            image_name = Path(image_path).name
            
            # 從timing_info提取總處理時間
            processing_time = getattr(timing_info, 'total_time', 0.0)
            
            # 調用原有的添加方法
            self.add_image_result(
                image_name=image_name,
                detections=detections,
                processing_time=processing_time,
                metrics=confusion_metrics
            )
            
        except Exception as e:
            self.logger.error(f"❌ 更新統計信息失敗: {str(e)}")
    
    def save_final_statistics(self):
        """保存最終統計結果"""
        try:
            if self.stats['total_images'] > 0:
                summary_path = self.generate_summary_csv()
                class_stats_path = self.generate_class_statistics_csv()
                
                self.logger.info("✅ 最終統計結果已保存")
                if summary_path:
                    self.logger.info(f"   摘要報告: {summary_path}")
                if class_stats_path:
                    self.logger.info(f"   類別統計: {class_stats_path}")
            else:
                self.logger.warning("⚠️ 沒有統計數據可保存")
                
        except Exception as e:
            self.logger.error(f"❌ 保存最終統計失敗: {str(e)}")
    
    def generate_summary_csv(self) -> Optional[str]:
        """
        生成摘要CSV報告
        
        Returns:
            str: CSV文件路徑
        """
        if not self.csv_dir:
            return None
        
        try:
            csv_path = self.csv_dir / "detection_summary.csv"
            
            with open(csv_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 寫入標題
                headers = [
                    '圖像名稱', '檢測數量', '處理時間(秒)', '時間戳'
                ]
                
                # 如果有混淆矩陣數據，添加相關列
                if any('tp' in record for record in self.stats['image_records']):
                    headers.extend(['TP', 'FP', 'FN', 'Precision', 'Recall', 'F1'])
                
                writer.writerow(headers)
                
                # 寫入數據
                for record in self.stats['image_records']:
                    row = [
                        record['image_name'],
                        record['detection_count'],
                        f"{record['processing_time']:.3f}",
                        record['timestamp']
                    ]
                    
                    # 添加指標數據
                    if 'tp' in record:
                        row.extend([
                            record['tp'],
                            record['fp'], 
                            record['fn'],
                            f"{record['precision']:.3f}",
                            f"{record['recall']:.3f}",
                            f"{record['f1_score']:.3f}"
                        ])
                    
                    writer.writerow(row)
            
            self.logger.info(f"✅ 摘要CSV報告生成: {csv_path}")
            return str(csv_path)
            
        except Exception as e:
            self.logger.error(f"❌ CSV報告生成失敗: {str(e)}")
            return None
    
    def generate_class_statistics_csv(self) -> Optional[str]:
        """
        生成類別統計CSV
        
        Returns:
            str: CSV文件路徑
        """
        if not self.csv_dir or not self.stats['class_counts']:
            return None
        
        try:
            csv_path = self.csv_dir / "class_statistics.csv"
            
            with open(csv_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 標題
                writer.writerow(['類別名稱', '檢測數量', '佔比(%)'])
                
                total_detections = sum(self.stats['class_counts'].values())
                
                # 按檢測數量排序
                sorted_classes = sorted(
                    self.stats['class_counts'].items(),
                    key=lambda x: x[1],
                    reverse=True
                )
                
                for class_name, count in sorted_classes:
                    percentage = (count / total_detections * 100) if total_detections > 0 else 0
                    writer.writerow([class_name, count, f"{percentage:.1f}"])
                
                # 總計行
                writer.writerow(['總計', total_detections, '100.0'])
            
            self.logger.info(f"✅ 類別統計CSV生成: {csv_path}")
            return str(csv_path)
            
        except Exception as e:
            self.logger.error(f"❌ 類別統計CSV生成失敗: {str(e)}")
            return None
    
    def get_statistics_summary(self) -> Dict[str, Any]:
        """獲取統計摘要"""
        avg_processing_time = (
            sum(self.stats['processing_times']) / len(self.stats['processing_times'])
            if self.stats['processing_times'] else 0.0
        )
        
        avg_detections_per_image = (
            self.stats['total_detections'] / self.stats['total_images']
            if self.stats['total_images'] > 0 else 0.0
        )
        
        return {
            'total_images': self.stats['total_images'],
            'total_detections': self.stats['total_detections'],
            'average_processing_time': avg_processing_time,
            'average_detections_per_image': avg_detections_per_image,
            'class_distribution': self.stats['class_counts'].copy(),
            'unique_classes': len(self.stats['class_counts'])
        }
    
    def reset_statistics(self):
        """重置統計數據"""
        self.stats = {
            'total_images': 0,
            'total_detections': 0,
            'class_counts': {},
            'processing_times': [],
            'image_records': []
        }
        
        self.logger.debug("📊 CSV統計數據已重置")
    
    def cleanup(self):
        """清理CSV管理器資源"""
        self.logger.debug("🧹 清理CSV管理器資源")
        # 可以在此處執行最終報告生成
        if self.stats['total_images'] > 0:
            self.generate_summary_csv()
            self.generate_class_statistics_csv()