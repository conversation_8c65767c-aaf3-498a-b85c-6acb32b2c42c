#!/usr/bin/env python3
"""
🧪 新架構功能測試腳本
快速測試重構後的統一YOLO推理系統功能
"""

import logging
import sys
from pathlib import Path

# 設置路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_basic_imports():
    """測試基本導入功能"""
    print("🧪 測試基本導入...")
    
    try:
        # 測試核心模組導入
        from inference_system.core import BaseInference, UnifiedInferenceEngine, ModelAdapter
        print("✅ 核心模組導入成功")
        
        # 測試配置系統導入
        from inference_system.config import UnifiedConfig, ModelConfig, SAHIConfig
        print("✅ 配置系統導入成功")
        
        # 測試處理模組導入
        from inference_system.processing import SliceProcessor, FusionEngine, PostProcessor
        print("✅ 處理模組導入成功")
        
        # 測試性能模組導入
        from inference_system.performance import ConcurrentProcessor, CacheManager, PerformanceMonitor
        print("✅ 性能模組導入成功")
        
        # 測試主API導入
        from inference_system import create_inference_system, UnifiedYOLOInference
        print("✅ 主API導入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {str(e)}")
        return False

def test_config_creation():
    """測試配置創建"""
    print("\n🧪 測試配置創建...")
    
    try:
        from inference_system.config import UnifiedConfig, ClassConfig
        
        # 創建基本配置
        config = UnifiedConfig()
        print("✅ 基本配置創建成功")
        
        # 添加類別配置
        config.classes[2] = ClassConfig(
            id=2,
            name="linear_crack",
            display_name="縱向裂縫",
            color=[0, 0, 255],
            confidence_threshold=0.2,
            sahi_confidence_threshold=0.08,
            enabled=True
        )
        print("✅ 類別配置添加成功")
        
        # 測試配置驗證
        errors = config.validate()
        if not any(errors.values()):
            print("✅ 配置驗證通過")
        else:
            print(f"⚠️ 配置驗證警告: {errors}")
            
        return True
        
    except Exception as e:
        print(f"❌ 配置創建失敗: {str(e)}")
        return False

def test_performance_modules():
    """測試性能模組"""
    print("\n🧪 測試性能模組...")
    
    try:
        from inference_system.performance import CacheManager, PerformanceMonitor, MemoryOptimizer
        from inference_system.config import UnifiedConfig
        
        config = UnifiedConfig()
        
        # 測試緩存管理器
        cache_manager = CacheManager(config, cache_dir="./test_cache")
        cache_key = cache_manager.get_cache_key("test_image.jpg")
        print(f"✅ 緩存管理器創建成功，生成cache key: {cache_key[:8]}...")
        
        # 測試性能監控器
        perf_monitor = PerformanceMonitor(config)
        system_resources = perf_monitor.system_resources
        print(f"✅ 性能監控器創建成功，CPU核心: {system_resources.cpu_count}")
        
        # 測試記憶體優化器
        memory_optimizer = MemoryOptimizer(config)
        memory_snapshot = memory_optimizer.get_memory_snapshot()
        print(f"✅ 記憶體優化器創建成功，當前記憶體使用: {memory_snapshot.memory_percent:.1f}%")
        
        # 清理資源
        cache_manager.cleanup()
        perf_monitor.cleanup()
        memory_optimizer.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ 性能模組測試失敗: {str(e)}")
        return False

def test_mock_inference():
    """測試模擬推理（不需要實際模型）"""
    print("\n🧪 測試模擬推理...")
    
    try:
        from inference_system.config import UnifiedConfig, ClassConfig
        from inference_system.core.base_inference import Detection
        from unittest.mock import Mock
        
        # 創建配置
        config = UnifiedConfig()
        config.classes[2] = ClassConfig(
            id=2,
            name="linear_crack",
            display_name="縱向裂縫",
            color=[0, 0, 255],
            confidence_threshold=0.2,
            sahi_confidence_threshold=0.08,
            enabled=True
        )
        
        # 創建模擬檢測結果
        mock_detection = Detection(
            bbox=[100, 100, 200, 200],
            confidence=0.85,
            class_id=2,
            class_name="linear_crack",
            area=10000.0
        )
        
        print(f"✅ 模擬檢測結果創建成功: {mock_detection.class_name} (confidence: {mock_detection.confidence})")
        
        # 測試後處理器
        from inference_system.processing import PostProcessor
        post_processor = PostProcessor(config)
        
        # 模擬智能過濾
        filtered_detections = post_processor._intelligent_filtering([mock_detection])
        print(f"✅ 智能過濾完成，結果數量: {len(filtered_detections)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模擬推理測試失敗: {str(e)}")
        return False

def test_architecture_structure():
    """測試架構結構完整性"""
    print("\n🧪 測試架構結構...")
    
    expected_modules = [
        "inference_system.core",
        "inference_system.config", 
        "inference_system.processing",
        "inference_system.visualization",
        "inference_system.io",
        "inference_system.utils",
        "inference_system.performance",
        "inference_system.tests"
    ]
    
    success_count = 0
    for module_name in expected_modules:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module_name}: {str(e)}")
    
    print(f"\n📊 架構完整性: {success_count}/{len(expected_modules)} ({success_count/len(expected_modules)*100:.1f}%)")
    
    return success_count == len(expected_modules)

def main():
    """主測試函數"""
    print("🚀 統一YOLO推理系統 - 新架構測試")
    print("=" * 70)
    
    # 設置日誌
    logging.basicConfig(level=logging.WARNING)  # 只顯示警告和錯誤
    
    test_results = []
    
    # 執行各項測試
    test_results.append(("基本導入測試", test_basic_imports()))
    test_results.append(("配置創建測試", test_config_creation()))
    test_results.append(("性能模組測試", test_performance_modules()))
    test_results.append(("模擬推理測試", test_mock_inference()))
    test_results.append(("架構結構測試", test_architecture_structure()))
    
    # 統計結果
    passed_count = sum(1 for _, result in test_results if result)
    total_count = len(test_results)
    
    print("\n" + "=" * 70)
    print("📊 測試結果統計:")
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 總體結果: {passed_count}/{total_count} 通過 ({passed_count/total_count*100:.1f}%)")
    
    if passed_count == total_count:
        print("🎉 所有測試通過！新架構準備就緒")
        print("\n💡 下一步:")
        print("   1. 運行 run_unified_yolo_new.py 進行實際推理測試")
        print("   2. 查看 MIGRATION_GUIDE.md 了解遷移步驟")
        print("   3. 參考 examples/ 目錄中的使用示例")
    else:
        print("⚠️ 部分測試失敗，請檢查相關模組")
        print("\n🔧 故障排除:")
        print("   1. 確認所有依賴包已安裝")
        print("   2. 檢查Python環境配置")
        print("   3. 查看具體錯誤信息進行修復")

if __name__ == "__main__":
    main()