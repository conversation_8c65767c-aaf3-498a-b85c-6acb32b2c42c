from PIL import Image
import os

def resize_image(input_path, output_path, width, height):
    """
    調整圖像尺寸並保存
    
    參數:
        input_path (str): 輸入圖像路徑
        output_path (str): 輸出圖像路徑
        width (int): 目標寬度
        height (int): 目標高度
    """
    try:
        # 打開原始圖像
        with Image.open(input_path) as img:
            # 調整尺寸
            resized_img = img.resize((width, height), Image.LANCZOS)
            
            # 保存圖像
            resized_img.save(output_path)
            print(f"圖像已成功調整尺寸並保存到: {output_path}")
            
    except FileNotFoundError:
        print(f"錯誤: 找不到輸入文件 {input_path}")
    except Exception as e:
        print(f"發生錯誤: {str(e)}")

if __name__ == "__main__":
    # 獲取用戶輸入
    input_path = input("請輸入原始圖像路徑: ")
    output_path = input("請輸入輸出圖像路徑: ")
    width = int(input("請輸入目標寬度(像素): "))
    height = int(input("請輸入目標高度(像素): "))
    
    # 檢查輸入文件是否存在
    if not os.path.isfile(input_path):
        print(f"錯誤: 文件 {input_path} 不存在")
    else:
        resize_image(input_path, output_path, width, height)