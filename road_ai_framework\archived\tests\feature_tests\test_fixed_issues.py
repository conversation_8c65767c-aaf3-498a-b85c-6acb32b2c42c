#!/usr/bin/env python3
"""
🧪 測試修復後的問題
測試標題間距、GT/pred顏色一致性和中文字體問題的修復
"""

import os
import sys
from pathlib import Path

# 使用統一導入管理
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_fixes():
    """測試修復效果"""
    print("🧪 測試修復後的問題")
    print("=" * 70)
    print("🎨 測試標題間距修復")
    print("🌈 測試GT/pred顏色一致性")
    print("📝 測試中文字體問題修復")
    print("=" * 70)
    
    try:
        # 導入必要模組
        from core.import_helper import setup_project_paths
        setup_project_paths()
        
        # 測試用配置
        test_config = {
            'input_path': '/mnt/d/image/5_test_image/test_2_org/BMP-2291_20241126_130131461.jpg',
            'output_path': '/mnt/d/image/5_test_image/test_2_out_fixed',
            'labelme_dir': '/mnt/d/image/5_test_image/test_2_org',
            'model_path': '/mnt/d/4_road_crack/best.pt'
        }
        
        # 檢查必要文件
        print("🔍 檢查測試文件...")
        if not Path(test_config['input_path']).exists():
            print(f"❌ 測試圖像不存在: {test_config['input_path']}")
            print("💡 請修改test_config中的路徑")
            return False
        
        print("✅ 測試配置檢查通過")
        
        # 創建配置管理器
        print("\\n🔧 創建測試配置...")
        from models.inference.config_manager import UnifiedYOLOConfigManager
        
        config_manager = UnifiedYOLOConfigManager()
        config_manager.model.segmentation_model_path = test_config['model_path']
        config_manager.model.device = "cuda"
        config_manager.paths.labelme_dir = test_config['labelme_dir']
        
        # 設置類別配置（確保patch類別存在）
        config_manager.classes.clear()
        class_configs = {
            0: ["expansion_joint", "expansion_joint", [255, 0, 0], 0.1, 0.15, True],
            1: ["joint", "joint", [0, 255, 0], 0.25, 0.1, True],
            2: ["linear_crack", "linear_crack", [0, 0, 255], 0.1, 0.08, True],
            3: ["Alligator_crack", "Alligator_crack", [255, 255, 0], 0.1, 0.15, True],
            4: ["potholes", "potholes", [255, 0, 255], 0.1, 0.2, True],
            5: ["patch", "patch", [0, 255, 255], 0.1, 0.18, True],  # 確保patch類別存在
            6: ["manhole", "manhole", [128, 0, 128], 0.1, 0.25, True],
        }
        
        for class_id, (name, display_name, color, conf, sahi_conf, enabled) in class_configs.items():
            config_manager.add_class_config(
                class_id=class_id,
                name=name,
                display_name=display_name,
                color=color,
                confidence=conf,
                sahi_confidence=sahi_conf,
                enabled=enabled
            )
        
        print(f"✅ 配置了 {len(class_configs)} 個類別")
        
        # 創建推理引擎
        print("\\n🚀 初始化測試推理引擎...")
        from models.inference.unified_yolo_inference import UnifiedYOLOInference
        
        inference = UnifiedYOLOInference()
        inference.config_manager = config_manager
        
        # 創建輸出目錄
        output_path = Path(test_config['output_path'])
        output_path.mkdir(parents=True, exist_ok=True)
        (output_path / "images").mkdir(exist_ok=True)
        (output_path / "reports").mkdir(exist_ok=True)
        
        print("✅ 測試環境準備完成")
        
        # 執行測試推理（只測試三視圖生成，避免完整推理）
        print(f"\\n🎯 執行修復測試...")
        print(f"📁 測試圖像: {test_config['input_path']}")
        print(f"📁 GT目錄: {test_config['labelme_dir']}")
        print(f"📁 輸出目錄: {test_config['output_path']}")
        
        try:
            inference._load_model()
            
            # 重新初始化組件
            from models.inference.unified_yolo_inference import FontManager, ThreeViewGenerator
            
            inference.font_manager = FontManager(
                font_size=1.0,
                font_thickness=2,
                font_scale=1.0
            )
            
            inference.three_view_generator = ThreeViewGenerator(
                font_manager=inference.font_manager,
                config_manager=config_manager,
                layout="horizontal",
                spacing=10
            )
            
            # 執行推理並生成三視圖
            result = inference.predict_single_image(
                test_config['input_path'], 
                test_config['output_path']
            )
            
            # 檢查結果
            image_name = Path(test_config['input_path']).stem
            three_view_file = output_path / "images" / f"{image_name}_three_view.jpg"
            
            print(f"\\n✅ 測試完成! 檢測到 {len(result['detections'])} 個目標")
            
            if three_view_file.exists():
                print(f"✅ 三視圖生成成功: {three_view_file}")
                print("\\n🎯 請檢查以下修復效果:")
                print("  1. 📏 標題行間距是否足夠（不再擠在一起）")
                print("  2. 🎨 GT與pred顏色是否一致（相同類別相同顏色）")
                print("  3. 📝 中文字符是否正確顯示（不再是問號）")
            else:
                print("❌ 三視圖生成失敗")
                
            # 顯示檢測結果
            if result['detections']:
                print(f"\\n🎯 檢測結果:")
                for det in result['detections']:
                    class_name = det['class_name']
                    confidence = det['confidence']
                    print(f"  • {class_name}: {confidence:.3f}")
                    
        except Exception as e:
            print(f"💥 推理測試失敗: {e}")
            import traceback
            traceback.print_exc()
            return False
            
        return True
        
    except Exception as e:
        print(f"\\n💥 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函數"""
    print("🚀 修復問題測試套件")
    print("測試標題間距、GT/pred顏色一致性和中文字體修復")
    print("=" * 70)
    
    # 執行修復測試
    success = test_fixes()
    
    if success:
        print("\\n✅ 修復測試完成!")
        print("\\n📋 修復內容說明:")
        print("  📏 標題間距修復: 增加行間距從5到15，標題高度從20到40")
        print("  🎨 GT顏色一致性: 添加調試信息，確保GT使用正確類別顏色")
        print("  📝 中文字體處理: 將中文標題轉換為英文避免問號問題")
        
        print("\\n🎯 修復效果驗證:")
        print("  1. 查看生成的三視圖文件")
        print("  2. 確認標題文字不再擁擠")
        print("  3. 確認GT與pred使用相同類別顏色")
        print("  4. 確認調試信息輸出正確的顏色匹配過程")
        
    else:
        print("\\n❌ 修復測試失敗，請檢查環境和配置")
        
    print("\\n💡 注意事項:")
    print("  - 如果仍有問題，請檢查控制台的調試信息")
    print("  - GT顏色調試信息會顯示類別匹配過程")
    print("  - 確保patch類別在配置中存在且啟用")
    
    return success


if __name__ == "__main__":
    main()