#!/usr/bin/env python3
"""
🔄 並發處理器
支援多線程和多進程的並發推理處理
"""

import logging
import threading
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Callable, Union
from pathlib import Path
from queue import Queue
import time

from ..config import UnifiedConfig
from ..core.base_inference import Detection, TimingInfo


class ConcurrentProcessor:
    """
    並發處理器
    
    特色功能:
    - 智能負載均衡
    - 動態線程池調整
    - 進度追蹤和監控
    - 錯誤恢復機制
    """
    
    def __init__(self, 
                 config: UnifiedConfig,
                 max_workers: Optional[int] = None,
                 use_processes: bool = False):
        """
        初始化並發處理器
        
        Args:
            config: 統一配置
            max_workers: 最大工作線程/進程數
            use_processes: 是否使用多進程（CPU密集型任務推薦）
        """
        self.config = config
        self.max_workers = max_workers or min(8, (mp.cpu_count() or 1) + 4)
        self.use_processes = use_processes
        self.logger = logging.getLogger(__name__)
        
        # 性能統計
        self.processed_count = 0
        self.total_processing_time = 0.0
        self.start_time = None
        
        # 進度追蹤
        self.progress_queue = Queue()
        self.error_queue = Queue()
        
        self.logger.info(f"⚡ 並發處理器初始化完成")
        self.logger.info(f"   最大工作數: {self.max_workers}")
        self.logger.info(f"   處理模式: {'多進程' if use_processes else '多線程'}")
    
    def process_batch_concurrent(self, 
                                image_paths: List[str],
                                inference_func: Callable,
                                progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """
        並發批量處理圖像
        
        Args:
            image_paths: 圖像路徑列表
            inference_func: 推理函數
            progress_callback: 進度回調函數
            
        Returns:
            List[Dict]: 處理結果列表
        """
        self.start_time = time.time()
        results = []
        
        try:
            executor_class = ProcessPoolExecutor if self.use_processes else ThreadPoolExecutor
            
            with executor_class(max_workers=self.max_workers) as executor:
                # 提交所有任務
                future_to_path = {
                    executor.submit(self._safe_inference_wrapper, inference_func, path): path
                    for path in image_paths
                }
                
                # 收集結果
                for future in as_completed(future_to_path):
                    image_path = future_to_path[future]
                    
                    try:
                        result = future.result()
                        result['image_path'] = image_path
                        results.append(result)
                        
                        self.processed_count += 1
                        
                        # 調用進度回調
                        if progress_callback:
                            progress_callback(self.processed_count, len(image_paths), result)
                        
                        # 記錄進度
                        if self.processed_count % 10 == 0:
                            self._log_progress(len(image_paths))
                            
                    except Exception as e:
                        self.logger.error(f"❌ 處理失敗 {image_path}: {str(e)}")
                        results.append({
                            'image_path': image_path,
                            'success': False,
                            'error': str(e)
                        })
        
        except Exception as e:
            self.logger.error(f"❌ 並發處理失敗: {str(e)}")
            raise
        
        self._log_final_statistics(len(image_paths))
        return results
    
    def _safe_inference_wrapper(self, inference_func: Callable, image_path: str) -> Dict[str, Any]:
        """
        安全的推理包裝器，處理異常和超時
        
        Args:
            inference_func: 推理函數
            image_path: 圖像路徑
            
        Returns:
            Dict: 推理結果
        """
        start_time = time.time()
        
        try:
            # 執行推理
            result = inference_func(image_path)
            
            # 添加時間信息
            processing_time = time.time() - start_time
            if isinstance(result, dict):
                result['processing_time'] = processing_time
            
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.warning(f"⚠️ 推理失敗 {Path(image_path).name}: {str(e)}")
            
            return {
                'success': False,
                'error': str(e),
                'processing_time': processing_time,
                'detections': []
            }
    
    def process_with_load_balancing(self, 
                                  image_paths: List[str],
                                  inference_func: Callable,
                                  batch_size: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        負載均衡批量處理
        
        根據系統負載動態調整並發數量
        
        Args:
            image_paths: 圖像路徑列表
            inference_func: 推理函數
            batch_size: 批次大小
            
        Returns:
            List[Dict]: 處理結果列表
        """
        if batch_size is None:
            batch_size = max(1, len(image_paths) // self.max_workers)
        
        results = []
        
        # 分批處理，避免記憶體過載
        for i in range(0, len(image_paths), batch_size):
            batch_paths = image_paths[i:i + batch_size]
            
            self.logger.info(f"📦 處理批次 {i//batch_size + 1}: {len(batch_paths)}張圖像")
            
            batch_results = self.process_batch_concurrent(
                batch_paths, 
                inference_func,
                self._create_batch_progress_callback(i, len(image_paths))
            )
            
            results.extend(batch_results)
            
            # 批次間記憶體清理
            self._cleanup_batch_memory()
        
        return results
    
    def _create_batch_progress_callback(self, batch_start: int, total_count: int) -> Callable:
        """創建批次進度回調函數"""
        def progress_callback(current: int, batch_total: int, result: Dict[str, Any]):
            global_current = batch_start + current
            progress_percent = (global_current / total_count) * 100
            
            self.logger.info(f"📈 整體進度: {global_current}/{total_count} ({progress_percent:.1f}%)")
            
            # 簡單的性能統計
            if result.get('success', False) and 'processing_time' in result:
                self.total_processing_time += result['processing_time']
        
        return progress_callback
    
    def _log_progress(self, total_count: int):
        """記錄處理進度"""
        if self.start_time:
            elapsed_time = time.time() - self.start_time
            progress_percent = (self.processed_count / total_count) * 100
            avg_time = elapsed_time / self.processed_count if self.processed_count > 0 else 0
            
            estimated_remaining = (total_count - self.processed_count) * avg_time
            
            self.logger.info(f"📊 並發處理進度: {self.processed_count}/{total_count} ({progress_percent:.1f}%)")
            self.logger.info(f"⏱️  平均處理時間: {avg_time:.2f}秒/張")
            self.logger.info(f"⏳ 預估剩餘時間: {estimated_remaining:.1f}秒")
    
    def _log_final_statistics(self, total_count: int):
        """記錄最終統計信息"""
        if self.start_time:
            total_elapsed = time.time() - self.start_time
            avg_time = total_elapsed / total_count if total_count > 0 else 0
            throughput = total_count / total_elapsed if total_elapsed > 0 else 0
            
            self.logger.info(f"🎉 並發處理完成統計:")
            self.logger.info(f"   📊 處理總數: {total_count}張")
            self.logger.info(f"   ⏱️  總耗時: {total_elapsed:.2f}秒")
            self.logger.info(f"   📈 平均耗時: {avg_time:.2f}秒/張") 
            self.logger.info(f"   🚀 處理速度: {throughput:.2f}張/秒")
            self.logger.info(f"   ⚡ 並發效率: {self.max_workers}線程並發")
    
    def _cleanup_batch_memory(self):
        """批次間記憶體清理"""
        import gc
        gc.collect()
        
        # 如果使用GPU，清理GPU記憶體
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        except ImportError:
            pass
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """獲取性能統計信息"""
        elapsed_time = time.time() - self.start_time if self.start_time else 0
        
        return {
            'processed_count': self.processed_count,
            'total_elapsed_time': elapsed_time,
            'average_processing_time': elapsed_time / self.processed_count if self.processed_count > 0 else 0,
            'throughput': self.processed_count / elapsed_time if elapsed_time > 0 else 0,
            'max_workers': self.max_workers,
            'processing_mode': 'multiprocessing' if self.use_processes else 'multithreading'
        }
    
    def cleanup(self):
        """清理並發處理器資源"""
        self.logger.debug("🧹 清理並發處理器資源")
        
        # 清空隊列
        while not self.progress_queue.empty():
            try:
                self.progress_queue.get_nowait()
            except:
                break
        
        while not self.error_queue.empty():
            try:
                self.error_queue.get_nowait()
            except:
                break