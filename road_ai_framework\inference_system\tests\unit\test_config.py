#!/usr/bin/env python3
"""
🧪 配置系統單元測試
"""

import unittest
import tempfile
import yaml
from pathlib import Path

from ...config import (
    UnifiedConfig, ModelConfig, SliceConfig, FusionConfig,
    FilteringConfig, ClassConfig, VisualizationConfig, OutputConfig,
    ProcessingConfig, ConfigManager, FusionStrategy, create_default_config
)
from ..test_base import BaseTestCase


class TestUnifiedConfig(BaseTestCase):
    """統一配置類測試"""
    
    def test_default_config_creation(self):
        """測試默認配置創建"""
        config = create_default_config()
        
        self.assertIsInstance(config, UnifiedConfig)
        self.assertIsInstance(config.model, ModelConfig)
        self.assertIsInstance(config.processing, ProcessingConfig)
        self.assertIsInstance(config.visualization, VisualizationConfig)
        self.assertIsInstance(config.output, OutputConfig)
        
        # 檢查默認類別配置
        self.assertIn(2, config.classes)  # linear_crack
        self.assertIn(3, config.classes)  # Alligator_crack
    
    def test_yaml_roundtrip(self):
        """測試YAML序列化和反序列化"""
        # 創建配置
        config = create_default_config()
        config.model.segmentation_model_path = "/test/model.pt"
        config.input_path = "/test/input"
        config.output_path = "/test/output"
        
        # 保存到YAML
        yaml_path = self.temp_path / "test_config.yaml"
        config.to_yaml(str(yaml_path))
        
        # 從YAML載入
        loaded_config = UnifiedConfig.from_yaml(str(yaml_path))
        
        # 驗證關鍵字段
        self.assertEqual(loaded_config.model.segmentation_model_path, "/test/model.pt")
        self.assertEqual(loaded_config.input_path, "/test/input")
        self.assertEqual(loaded_config.output_path, "/test/output")
        self.assertEqual(len(loaded_config.classes), len(config.classes))
    
    def test_config_validation(self):
        """測試配置驗證"""
        config = create_default_config()
        
        # 有效配置應該通過驗證
        errors = config.validate()
        self.assertEqual(len(errors), 0)
        
        # 無效配置應該失敗
        config.model.segmentation_model_path = "/nonexistent/model.pt"
        errors = config.validate()
        self.assertGreater(len(errors), 0)
    
    def test_class_config_operations(self):
        """測試類別配置操作"""
        config = create_default_config()
        
        # 獲取類別配置
        linear_crack = config.get_class_config(2)
        self.assertIsNotNone(linear_crack)
        self.assertEqual(linear_crack.name, "linear_crack")
        
        # 添加新類別
        new_class = ClassConfig(
            name="test_class",
            display_name="測試類別",
            color=[255, 0, 0],
            confidence=0.5,
            sahi_confidence=0.3
        )
        config.add_class_config(99, new_class)
        
        # 驗證添加成功
        self.assertIn(99, config.classes)
        self.assertEqual(config.classes[99].name, "test_class")
        
        # 測試啟用類別過濾
        enabled_classes = config.get_enabled_classes()
        self.assertIn(2, enabled_classes)  # linear_crack應該啟用
        
        # 排除類別
        config.excluded_class_ids = [2]
        enabled_classes = config.get_enabled_classes()
        self.assertNotIn(2, enabled_classes)  # linear_crack應該被排除


class TestConfigManager(BaseTestCase):
    """配置管理器測試"""
    
    def test_config_manager_creation(self):
        """測試配置管理器創建"""
        manager = ConfigManager()
        self.assertIsInstance(manager.config, UnifiedConfig)
    
    def test_yaml_loading(self):
        """測試YAML載入"""
        # 創建測試YAML
        test_config = {
            'model': {
                'segmentation_model_path': '/test/model.pt',
                'device': 'cpu'
            },
            'processing': {
                'slice': {
                    'enabled': True,
                    'height': 640,
                    'width': 640
                }
            }
        }
        
        yaml_path = self.temp_path / "test.yaml"
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(test_config, f)
        
        # 載入配置
        manager = ConfigManager()
        config = manager.load_from_yaml(str(yaml_path))
        
        self.assertEqual(config.model.segmentation_model_path, '/test/model.pt')
        self.assertEqual(config.model.device, 'cpu')
        self.assertTrue(config.processing.slice.enabled)
    
    def test_config_validation(self):
        """測試配置驗證"""
        manager = ConfigManager()
        
        # 默認配置應該通過驗證（除了模型路徑）
        # 注意：默認配置的模型路徑為空，會有驗證錯誤
        # 這是預期行為
        result = manager.validate_config()
        # 不檢查結果，因為默認配置沒有模型路徑
    
    def test_config_history(self):
        """測試配置變更歷史"""
        manager = ConfigManager()
        
        # 初始歷史應該為空
        history = manager.get_config_history()
        self.assertEqual(len(history), 0)
        
        # 載入配置應該記錄歷史
        test_config = {'model': {'device': 'cpu'}}
        manager.load_from_dict(test_config)
        
        history = manager.get_config_history()
        self.assertGreater(len(history), 0)
        self.assertEqual(history[-1]['action'], 'load_dict')


class TestFusionStrategy(BaseTestCase):
    """融合策略測試"""
    
    def test_fusion_strategy_enum(self):
        """測試融合策略枚舉"""
        # 檢查所有策略值
        strategies = [
            FusionStrategy.STANDARD_NMS,
            FusionStrategy.SOFT_NMS,
            FusionStrategy.WEIGHTED_BOXES_FUSION,
            FusionStrategy.DIOU_NMS,
            FusionStrategy.CLUSTER_NMS,
            FusionStrategy.LARGEST_OBJECT
        ]
        
        for strategy in strategies:
            self.assertIsInstance(strategy.value, str)
        
        # 檢查默認策略
        fusion_config = FusionConfig()
        self.assertEqual(fusion_config.strategy, FusionStrategy.LARGEST_OBJECT)


if __name__ == "__main__":
    unittest.main()