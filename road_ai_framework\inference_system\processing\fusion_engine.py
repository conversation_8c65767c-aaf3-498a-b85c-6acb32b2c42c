#!/usr/bin/env python3
"""
🔀 物件融合引擎
整合6種融合策略，從原始 advanced_slice_inference.py 中提取並優化
"""

import logging
from typing import List, Dict, Any, Tuple, Optional
import numpy as np

from ..core.base_inference import Detection
from ..config import FusionConfig, FusionStrategy


class FusionEngine:
    """
    物件融合引擎

    支援融合策略：
    - Standard NMS: 經典非極大值抑制
    - Soft-NMS: 軟抑制，避免過度移除  
    - Weighted Boxes Fusion: 加權框融合
    - DIoU-NMS: 距離IoU增強NMS
    - Cluster-NMS: 聚類NMS，適合擁擠場景
    - Largest Object: 保留最大物件策略
    """

    def __init__(self, fusion_config: FusionConfig):
        """
        初始化融合引擎

        Args:
            fusion_config: 融合配置
        """
        self.config = fusion_config
        self.logger = logging.getLogger(__name__)

        # 簡化策略映射 - 只保留三種核心策略
        self.strategies = {
            FusionStrategy.STANDARD_NMS: self._standard_nms,
            FusionStrategy.LARGEST_OBJECT: self._largest_object_strategy,
            FusionStrategy.NO_FUSION: self._no_fusion
        }

        # 🔀 自適應融合策略選擇參數
        self.adaptive_thresholds = {
            'high_density_threshold': 20,    # 高密度檢測閾值
            'large_object_ratio': 0.3,       # 大物件比例閾值
            'overlap_ratio_threshold': 0.7   # 高重疊比例閾值
        }

        self.stats = {
            'total_fusions': 0,
            'input_detections': 0,
            'output_detections': 0,
            'fusion_times': [],
            'strategy_usage': {},  # 記錄策略使用頻率
            'adaptive_selections': 0  # 自適應選擇次數
        }

    def fuse(self, detections: List[Detection]) -> List[Detection]:
        """
        融合檢測結果

        Args:
            detections: 原始檢測結果列表

        Returns:
            List[Detection]: 融合後的檢測結果
        """
        if not detections:
            return []

        import time
        start_time = time.time()

        selected_strategy = self.config.strategy

        # 如果選擇了 'no_fusion' 策略，使用專門的方法處理
        if selected_strategy == FusionStrategy.NO_FUSION:
            no_fusion_result = self._no_fusion(self._detections_to_internal(detections))
            fused_detections = self._internal_to_detections(no_fusion_result)
            
            # 更新統計
            fusion_time = time.time() - start_time
            self.stats['total_fusions'] += 1
            self.stats['input_detections'] += len(detections)
            self.stats['output_detections'] += len(fused_detections)
            self.stats['fusion_times'].append(fusion_time)
            strategy_name = selected_strategy.value if hasattr(selected_strategy, 'value') else str(selected_strategy)
            self.stats['strategy_usage'][strategy_name] = self.stats['strategy_usage'].get(strategy_name, 0) + 1
            
            self.logger.debug(f"🔀 無融合處理完成：{len(detections)} → {len(fused_detections)}，耗時：{fusion_time:.3f}秒")
            return fused_detections

        # 轉換為內部格式
        internal_detections = self._detections_to_internal(detections)

        # 直接使用配置的策略，無需自適應選擇

        # 執行融合策略
        strategy_func = self.strategies.get(
            selected_strategy, self._standard_nms)
        fused_internal = strategy_func(internal_detections)

        # 更新策略使用統計
        strategy_name = selected_strategy.value if hasattr(
            selected_strategy, 'value') else str(selected_strategy)
        self.stats['strategy_usage'][strategy_name] = self.stats['strategy_usage'].get(
            strategy_name, 0) + 1

        # 轉換回Detection格式
        fused_detections = self._internal_to_detections(fused_internal)

        # 更新統計
        fusion_time = time.time() - start_time
        self.stats['total_fusions'] += 1
        self.stats['input_detections'] += len(detections)
        self.stats['output_detections'] += len(fused_detections)
        self.stats['fusion_times'].append(fusion_time)

        self.logger.debug(
            f"🔀 物件融合完成：{len(detections)} → {len(fused_detections)}，策略：{strategy_name}，耗時：{fusion_time:.3f}秒")

        return fused_detections

    def _detections_to_internal(self, detections: List[Detection]) -> List[Dict[str, Any]]:
        """轉換Detection對象為內部字典格式"""
        internal = []
        for det in detections:
            internal.append({
                'bbox': det.bbox,
                'confidence': det.confidence,
                'class_id': det.class_id,
                'class_name': det.class_name,
                'mask': det.mask,
                'area': det.area
            })
        return internal

    def _internal_to_detections(self, internal_detections: List[Dict[str, Any]]) -> List[Detection]:
        """轉換內部字典格式為Detection對象"""
        detections = []
        for det in internal_detections:
            detection = Detection(
                bbox=det['bbox'],
                confidence=det['confidence'],
                class_id=det['class_id'],
                class_name=det['class_name'],
                mask=det.get('mask'),
                area=det.get('area')
            )
            detections.append(detection)
        return detections

    def _select_adaptive_strategy(self, detections: List[Dict[str, Any]]) -> FusionStrategy:
        """
        🔀 自適應策略選擇：根據檢測場景特徵選擇最佳融合策略

        Args:
            detections: 檢測結果列表

        Returns:
            FusionStrategy: 選擇的融合策略
        """
        if not detections:
            return FusionStrategy.STANDARD_NMS

        # 分析檢測場景特徵
        detection_count = len(detections)

        # 計算平均檢測框面積
        total_area = sum(self._calculate_bbox_area(
            det['bbox']) for det in detections)
        avg_area = total_area / detection_count

        # 計算重疊程度
        overlap_count = 0
        total_pairs = 0
        for i, det1 in enumerate(detections):
            for j, det2 in enumerate(detections[i+1:], i+1):
                if det1['class_id'] == det2['class_id']:  # 同類別才考慮重疊
                    iou = self._calculate_iou(det1['bbox'], det2['bbox'])
                    if iou > 0.1:  # 有一定重疊
                        overlap_count += 1
                    total_pairs += 1

        overlap_ratio = overlap_count / max(total_pairs, 1)

        # 計算大物件比例
        image_area = 640 * 640  # 假設標準圖像尺寸
        large_objects = sum(1 for det in detections if self._calculate_bbox_area(
            det['bbox']) > image_area * 0.01)
        large_object_ratio = large_objects / detection_count

        # 策略選擇邏輯
        self.logger.debug(
            f"🔀 場景分析: 檢測數={detection_count}, 重疊率={overlap_ratio:.2f}, 大物件率={large_object_ratio:.2f}")

        # 1. 高密度場景 -> Cluster NMS或Soft NMS
        if detection_count > self.adaptive_thresholds['high_density_threshold']:
            if overlap_ratio > self.adaptive_thresholds['overlap_ratio_threshold']:
                self.logger.debug("   選擇: SOFT_NMS (高密度+高重疊)")
                return FusionStrategy.SOFT_NMS
            else:
                self.logger.debug("   選擇: CLUSTER_NMS (高密度)")
                return FusionStrategy.CLUSTER_NMS

        # 2. 大物件為主 -> Largest Object或WBF
        if large_object_ratio > self.adaptive_thresholds['large_object_ratio']:
            if overlap_ratio > 0.5:
                self.logger.debug("   選擇: WEIGHTED_BOXES_FUSION (大物件+重疊)")
                return FusionStrategy.WEIGHTED_BOXES_FUSION
            else:
                self.logger.debug("   選擇: LARGEST_OBJECT (大物件主導)")
                return FusionStrategy.LARGEST_OBJECT

        # 3. 中等重疊場景 -> DIoU NMS
        if overlap_ratio > 0.3:
            self.logger.debug("   選擇: DIOU_NMS (中等重疊)")
            return FusionStrategy.DIOU_NMS

        # 4. 默認場景 -> Standard NMS
        self.logger.debug("   選擇: STANDARD_NMS (標準場景)")
        return FusionStrategy.STANDARD_NMS

    def _standard_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """標準NMS實現"""
        if not detections:
            return []

        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        final_detections = []
        for class_id, class_dets in class_groups.items():
            # 按置信度排序
            class_dets.sort(key=lambda x: x['confidence'], reverse=True)

            keep = []
            while class_dets:
                current = class_dets.pop(0)
                keep.append(current)

                # 移除與當前框重疊度高的框
                remaining = []
                for det in class_dets:
                    iou = self._calculate_iou(current['bbox'], det['bbox'])
                    if iou < self.config.iou_threshold:
                        remaining.append(det)
                class_dets = remaining

            final_detections.extend(keep)

        return final_detections

    def _soft_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Soft-NMS實現"""
        # 簡化實現：降低重疊框的置信度而非移除
        if not detections:
            return []

        result = []
        remaining = detections[:]  # 使用列表切片複製

        while remaining:
            # 找到最高置信度的檢測
            max_idx = max(range(len(remaining)),
                          key=lambda i: remaining[i]['confidence'])
            current = remaining.pop(max_idx)
            result.append(current)

            # 降低重疊框的置信度
            for det in remaining:
                iou = self._calculate_iou(current['bbox'], det['bbox'])
                if iou > self.config.iou_threshold:
                    # 使用Gaussian衰減
                    decay = np.exp(-(iou * iou) / self.config.soft_nms_sigma)
                    det['confidence'] *= decay

        # 過濾低置信度檢測
        result = [det for det in result if det['confidence']
                  >= self.config.confidence_threshold]

        return result

    def _weighted_boxes_fusion(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """加權框融合實現"""
        try:
            # 嘗試使用ensemble-boxes庫
            from ensemble_boxes import weighted_boxes_fusion

            # 按類別分組
            class_groups = {}
            for det in detections:
                class_id = det['class_id']
                if class_id not in class_groups:
                    class_groups[class_id] = []
                class_groups[class_id].append(det)

            final_detections = []

            for class_id, class_dets in class_groups.items():
                if len(class_dets) <= 1:
                    final_detections.extend(class_dets)
                    continue

                # 準備WBF輸入（假設1000x1000圖像進行歸一化）
                boxes_list = []
                scores_list = []
                labels_list = []

                for det in class_dets:
                    bbox = det['bbox']
                    # 歸一化座標
                    boxes_list.append(
                        [bbox[0]/1000, bbox[1]/1000, bbox[2]/1000, bbox[3]/1000])
                    scores_list.append(det['confidence'])
                    labels_list.append(0)  # WBF內部用同一label

                # 執行WBF
                fused_boxes, fused_scores, fused_labels = weighted_boxes_fusion(
                    [boxes_list], [scores_list], [labels_list],
                    weights=[1.0],
                    iou_thr=self.config.iou_threshold,
                    skip_box_thr=self.config.confidence_threshold
                )

                # 轉換回原格式
                for box, score in zip(fused_boxes, fused_scores):
                    if score >= self.config.confidence_threshold:
                        final_detections.append({
                            'bbox': [box[0]*1000, box[1]*1000, box[2]*1000, box[3]*1000],
                            'confidence': float(score),
                            'class_id': class_id,
                            'class_name': class_dets[0]['class_name'],
                            'mask': None,
                            'area': (box[2]-box[0]) * (box[3]-box[1]) * 1000000
                        })

            return final_detections

        except ImportError:
            self.logger.warning("ensemble-boxes未安裝，回退到Standard NMS")
            return self._standard_nms(detections)
        except Exception as e:
            self.logger.error(f"WBF融合失敗: {str(e)}，回退到Standard NMS")
            return self._standard_nms(detections)

    def _largest_object_strategy(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """保留最大物件策略"""
        if not detections:
            return []

        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        final_detections = []

        for class_id, class_dets in class_groups.items():
            # 計算每個檢測的面積
            for det in class_dets:
                if det.get('area') is None:
                    bbox = det['bbox']
                    det['area'] = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])

            # 按面積分組重疊檢測
            groups = []
            for det in class_dets:
                added_to_group = False
                for group in groups:
                    # 檢查與群組中任何檢測的重疊
                    for group_det in group:
                        iou = self._calculate_iou(
                            det['bbox'], group_det['bbox'])
                        if iou > self.config.iou_threshold:
                            group.append(det)
                            added_to_group = True
                            break
                    if added_to_group:
                        break

                if not added_to_group:
                    groups.append([det])

            # 每組保留面積最大的檢測
            for group in groups:
                largest_det = max(group, key=lambda x: x['area'])
                final_detections.append(largest_det)

        return final_detections

    def _no_fusion(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """無融合策略 - 直接返回原始檢測結果"""
        self.logger.debug("🔀 無融合策略：直接返回原始檢測")
        return detections

    def _diou_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """DIoU-NMS實現"""
        # 簡化實現：使用DIoU而非IoU進行NMS
        if not detections:
            return []

        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        final_detections = []
        for class_id, class_dets in class_groups.items():
            # 按置信度排序
            class_dets.sort(key=lambda x: x['confidence'], reverse=True)

            keep = []
            while class_dets:
                current = class_dets.pop(0)
                keep.append(current)

                # 使用DIoU計算重疊度
                remaining = []
                for det in class_dets:
                    diou = self._calculate_diou(current['bbox'], det['bbox'])
                    if diou < self.config.iou_threshold:
                        remaining.append(det)
                class_dets = remaining

            final_detections.extend(keep)

        return final_detections

    def _cluster_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        聚類NMS實現：基於空間聚類的NMS，適用於高密度檢測場景
        """
        if not detections:
            return []

        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)

        final_detections = []

        for class_id, class_dets in class_groups.items():
            if len(class_dets) <= 1:
                final_detections.extend(class_dets)
                continue

            # 基於中心點距離的簡單聚類
            clusters = []

            for det in class_dets:
                # 計算檢測框中心點
                bbox = det['bbox']
                center = [(bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2]
                det['center'] = center

                # 尋找最近的聚類
                best_cluster = None
                min_distance = float('inf')

                for cluster in clusters:
                    # 計算到聚類中心的距離
                    cluster_center = cluster['center']
                    distance = ((center[0] - cluster_center[0]) **
                                2 + (center[1] - cluster_center[1])**2)**0.5

                    if distance < min_distance and distance < 50:  # 距離閾值
                        min_distance = distance
                        best_cluster = cluster

                if best_cluster:
                    best_cluster['detections'].append(det)
                    # 更新聚類中心（加權平均）
                    total_conf = sum(d['confidence']
                                     for d in best_cluster['detections'])
                    weighted_x = sum(d['center'][0] * d['confidence']
                                     for d in best_cluster['detections']) / total_conf
                    weighted_y = sum(d['center'][1] * d['confidence']
                                     for d in best_cluster['detections']) / total_conf
                    best_cluster['center'] = [weighted_x, weighted_y]
                else:
                    # 創建新聚類
                    clusters.append({
                        'center': center,
                        'detections': [det]
                    })

            # 從每個聚類中選擇最佳檢測
            for cluster in clusters:
                cluster_dets = cluster['detections']

                if len(cluster_dets) == 1:
                    final_detections.extend(cluster_dets)
                else:
                    # 應用標準NMS到聚類內的檢測
                    cluster_dets.sort(
                        key=lambda x: x['confidence'], reverse=True)
                    keep = []

                    while cluster_dets:
                        current = cluster_dets.pop(0)
                        keep.append(current)

                        # 移除與當前框重疊度高的框
                        remaining = []
                        for det in cluster_dets:
                            iou = self._calculate_iou(
                                current['bbox'], det['bbox'])
                            if iou < self.config.iou_threshold:
                                remaining.append(det)
                        cluster_dets = remaining

                    final_detections.extend(keep)

        return final_detections

    def _iou_cluster_merge(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        IoU聚類合併：將IoU大於0.1的同類別bbox全部視為統一一個類別
        
        Args:
            detections: 檢測結果列表
            
        Returns:
            List[Dict]: 合併後的檢測結果
        """
        if not detections:
            return []
        
        self.logger.debug(f"🔗 開始IoU聚類合併: {len(detections)} 個檢測")
        
        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)
        
        final_detections = []
        
        for class_id, class_dets in class_groups.items():
            if len(class_dets) <= 1:
                final_detections.extend(class_dets)
                continue
            
            # 為每個檢測分配唯一ID
            for i, det in enumerate(class_dets):
                det['temp_id'] = i
            
            # 建立IoU連接圖
            connections = {}  # temp_id -> [connected_temp_ids]
            
            for i, det1 in enumerate(class_dets):
                temp_id1 = det1['temp_id']
                connections[temp_id1] = []
                
                for j, det2 in enumerate(class_dets[i+1:], i+1):
                    temp_id2 = det2['temp_id']
                    
                    iou = self._calculate_iou(det1['bbox'], det2['bbox'])
                    if iou > 0.1:  # IoU閾值設定為0.1
                        connections[temp_id1].append(temp_id2)
                        if temp_id2 not in connections:
                            connections[temp_id2] = []
                        connections[temp_id2].append(temp_id1)
            
            # 使用深度優先搜尋找到連通分量
            visited = set()
            clusters = []
            
            for temp_id in range(len(class_dets)):
                if temp_id not in visited:
                    cluster = []
                    self._dfs_cluster(temp_id, connections, visited, cluster)
                    clusters.append(cluster)
            
            # 合併每個cluster中的檢測
            for cluster_ids in clusters:
                if len(cluster_ids) == 1:
                    # 單個檢測，直接添加
                    det_idx = cluster_ids[0]
                    final_detections.append(class_dets[det_idx])
                else:
                    # 多個檢測，需要合併
                    cluster_detections = [class_dets[idx] for idx in cluster_ids]
                    merged_detection = self._merge_iou_cluster_detections(cluster_detections)
                    final_detections.append(merged_detection)
                    
                    self.logger.debug(f"   類別{class_id}: 合併{len(cluster_detections)}個檢測為1個")
        
        self.logger.debug(f"🔗 IoU聚類合併完成: {len(detections)} → {len(final_detections)} 個檢測")
        return final_detections
    
    def _dfs_cluster(self, temp_id: int, connections: Dict[int, List[int]], 
                    visited: set, cluster: List[int]):
        """
        深度優先搜尋找到連通的檢測組
        
        Args:
            temp_id: 當前檢測的臨時ID
            connections: 連接關係字典
            visited: 已訪問的節點集合
            cluster: 當前cluster的檢測ID列表
        """
        visited.add(temp_id)
        cluster.append(temp_id)
        
        # 遍歷所有連接的檢測
        for connected_id in connections.get(temp_id, []):
            if connected_id not in visited:
                self._dfs_cluster(connected_id, connections, visited, cluster)
    
    def _merge_iou_cluster_detections(self, detections: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        合併IoU cluster中的多個檢測
        
        Args:
            detections: 需要合併的檢測列表
            
        Returns:
            Dict: 合併後的檢測
        """
        if len(detections) == 1:
            return detections[0]
        
        # 計算合併後的邊界框（包圍所有檢測框）
        all_bboxes = [det['bbox'] for det in detections]
        x_min = min(bbox[0] for bbox in all_bboxes)
        y_min = min(bbox[1] for bbox in all_bboxes)
        x_max = max(bbox[2] for bbox in all_bboxes)
        y_max = max(bbox[3] for bbox in all_bboxes)
        
        merged_bbox = [x_min, y_min, x_max, y_max]
        
        # 計算加權置信度（面積加權）
        total_area = 0
        weighted_confidence_sum = 0
        
        for det in detections:
            bbox_area = self._calculate_bbox_area(det['bbox'])
            total_area += bbox_area
            weighted_confidence_sum += det['confidence'] * bbox_area
        
        merged_confidence = weighted_confidence_sum / max(total_area, 1e-6)
        merged_confidence = min(merged_confidence, 1.0)  # 限制在1.0以下
        
        # 使用最高置信度檢測的類別信息
        best_detection = max(detections, key=lambda x: x['confidence'])
        
        # 創建合併後的檢測
        merged_detection = {
            'bbox': merged_bbox,
            'confidence': merged_confidence,
            'class_id': best_detection['class_id'],
            'class_name': best_detection['class_name'],
            'area': self._calculate_bbox_area(merged_bbox),
            'merged_from_count': len(detections),
            'original_confidences': [det['confidence'] for det in detections],
            'merge_method': 'iou_cluster'
        }
        
        # 處理mask合併（如果存在）
        masks = [det.get('mask') for det in detections if det.get('mask') is not None]
        if masks:
            try:
                # 創建合併mask: 取所有mask的並集
                merged_mask = masks[0].copy().astype(bool)
                for mask in masks[1:]:
                    if mask.shape == merged_mask.shape:
                        merged_mask = np.logical_or(merged_mask, mask.astype(bool))
                
                merged_detection['mask'] = merged_mask
            except Exception as e:
                self.logger.warning(f"IoU cluster mask合併失敗: {e}")
                merged_detection['mask'] = None
        else:
            merged_detection['mask'] = None
        
        return merged_detection

    def _matrix_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Matrix NMS 實現 (2020年方法)
        基於矩陣運算的並行NMS，使用衰減因子而非硬刪除
        
        Reference: "SOLOv2: Dynamic and Fast Instance Segmentation"
        """
        if not detections:
            return []
        
        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)
        
        final_detections = []
        
        for class_id, class_dets in class_groups.items():
            if len(class_dets) <= 1:
                final_detections.extend(class_dets)
                continue
            
            # 按置信度排序
            class_dets.sort(key=lambda x: x['confidence'], reverse=True)
            
            n = len(class_dets)
            if n == 0:
                continue
                
            # 計算所有框的IoU矩陣
            iou_matrix = np.zeros((n, n))
            for i in range(n):
                for j in range(i + 1, n):
                    iou = self._calculate_iou(class_dets[i]['bbox'], class_dets[j]['bbox'])
                    iou_matrix[i][j] = iou
                    iou_matrix[j][i] = iou  # 對稱矩陣
            
            # Matrix NMS 衰減函數
            decay_matrix = np.ones((n, n))
            
            # 對於每個檢測，計算來自更高置信度檢測的衰減
            for i in range(n):
                for j in range(i):
                    # j < i 意味著 class_dets[j] 有更高置信度
                    if iou_matrix[i][j] > self.config.iou_threshold:
                        # 計算衰減權重：基於IoU的線性衰減
                        decay = 1.0 - iou_matrix[i][j]
                        decay_matrix[i][j] = decay
            
            # 計算最終的衰減權重（取最小值）
            final_weights = np.ones(n)
            for i in range(n):
                # 計算來自所有更高置信度檢測的累積衰減
                decay_factors = []
                for j in range(i):
                    if iou_matrix[i][j] > self.config.iou_threshold:
                        decay_factors.append(decay_matrix[i][j])
                
                if decay_factors:
                    # 使用最小衰減因子（最嚴格的抑制）
                    final_weights[i] = min(decay_factors)
            
            # 應用權重並過濾
            for i, det in enumerate(class_dets):
                adjusted_confidence = det['confidence'] * final_weights[i]
                
                if adjusted_confidence >= self.config.confidence_threshold:
                    # 更新調整後的置信度
                    adjusted_det = det.copy()
                    adjusted_det['confidence'] = adjusted_confidence
                    adjusted_det['matrix_nms_weight'] = final_weights[i]
                    final_detections.append(adjusted_det)
        
        self.logger.debug(f"🔀 Matrix NMS: {len(detections)} → {len(final_detections)} 檢測")
        return final_detections
    
    def _fast_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Fast NMS 實現
        並行化的NMS實現，提升計算效率
        
        Key optimization: 避免循環依賴，使用矩陣運算
        """
        if not detections:
            return []
        
        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)
        
        final_detections = []
        
        for class_id, class_dets in class_groups.items():
            if len(class_dets) <= 1:
                final_detections.extend(class_dets)
                continue
            
            # 按置信度排序
            class_dets.sort(key=lambda x: x['confidence'], reverse=True)
            
            n = len(class_dets)
            
            # 預先計算所有IoU（矩陣運算）
            bboxes = np.array([det['bbox'] for det in class_dets])
            confidences = np.array([det['confidence'] for det in class_dets])
            
            # 計算IoU矩陣（向量化）
            iou_matrix = self._compute_iou_matrix_vectorized(bboxes)
            
            # Fast NMS: 一次性確定保留的檢測
            keep_mask = np.ones(n, dtype=bool)
            
            for i in range(n):
                if not keep_mask[i]:
                    continue
                    
                # 找到與當前檢測IoU > threshold的所有檢測
                overlapping = (iou_matrix[i] > self.config.iou_threshold) & (np.arange(n) > i)
                
                # 移除置信度較低的重疊檢測
                keep_mask[overlapping] = False
            
            # 收集保留的檢測
            for i in range(n):
                if keep_mask[i] and confidences[i] >= self.config.confidence_threshold:
                    final_detections.append(class_dets[i])
        
        self.logger.debug(f"🔀 Fast NMS: {len(detections)} → {len(final_detections)} 檢測")
        return final_detections
    
    def _batched_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Batched NMS 實現
        批量處理優化的NMS，適合大規模檢測結果
        
        Key features:
        - 批量處理多個類別
        - 記憶體效率優化
        - 適合GPU並行計算
        """
        if not detections:
            return []
        
        # 按類別分組並收集統計信息
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)
        
        # 批量配置
        batch_size = 32  # 每批處理的最大檢測數
        final_detections = []
        
        for class_id, class_dets in class_groups.items():
            if len(class_dets) <= 1:
                final_detections.extend(class_dets)
                continue
            
            # 按置信度排序
            class_dets.sort(key=lambda x: x['confidence'], reverse=True)
            
            # 分批處理
            for batch_start in range(0, len(class_dets), batch_size):
                batch_end = min(batch_start + batch_size, len(class_dets))
                batch_dets = class_dets[batch_start:batch_end]
                
                if len(batch_dets) == 1:
                    final_detections.extend(batch_dets)
                    continue
                
                # 對當前批次執行標準NMS
                batch_result = self._batch_standard_nms(batch_dets)
                final_detections.extend(batch_result)
                
                # 檢查是否需要與之前的結果進行去重
                if batch_start > 0 and batch_result:
                    # 與已處理的結果進行交叉NMS
                    final_detections = self._cross_batch_nms(
                        final_detections, batch_result, class_id
                    )
        
        self.logger.debug(f"🔀 Batched NMS: {len(detections)} → {len(final_detections)} 檢測")
        return final_detections
    
    def _compute_iou_matrix_vectorized(self, bboxes: np.ndarray) -> np.ndarray:
        """
        向量化計算IoU矩陣
        
        Args:
            bboxes: shape (N, 4) 的邊界框陣列
            
        Returns:
            np.ndarray: shape (N, N) 的IoU矩陣
        """
        n = len(bboxes)
        iou_matrix = np.zeros((n, n))
        
        # 展開座標
        x1 = bboxes[:, 0][:, None]  # (N, 1)
        y1 = bboxes[:, 1][:, None]  # (N, 1)
        x2 = bboxes[:, 2][:, None]  # (N, 1)
        y2 = bboxes[:, 3][:, None]  # (N, 1)
        
        # 廣播計算交集
        inter_x1 = np.maximum(x1, bboxes[:, 0])  # (N, N)
        inter_y1 = np.maximum(y1, bboxes[:, 1])  # (N, N)
        inter_x2 = np.minimum(x2, bboxes[:, 2])  # (N, N)
        inter_y2 = np.minimum(y2, bboxes[:, 3])  # (N, N)
        
        # 計算交集面積
        inter_area = np.maximum(0, inter_x2 - inter_x1) * np.maximum(0, inter_y2 - inter_y1)
        
        # 計算各框面積
        areas = (x2.flatten() - x1.flatten()) * (y2.flatten() - y1.flatten())
        areas_matrix = areas[:, None] + areas - inter_area
        
        # 避免除零
        iou_matrix = inter_area / np.maximum(areas_matrix, 1e-8)
        
        return iou_matrix
    
    def _batch_standard_nms(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        對一個批次執行標準NMS
        
        Args:
            detections: 單一批次的檢測結果
            
        Returns:
            List: NMS後的檢測結果
        """
        if len(detections) <= 1:
            return [det for det in detections if det['confidence'] >= self.config.confidence_threshold]
        
        # 轉換為矩陣格式
        bboxes = np.array([det['bbox'] for det in detections])
        confidences = np.array([det['confidence'] for det in detections])
        
        # 按置信度排序（應該已經排序，但確保一致性）
        sorted_indices = np.argsort(confidences)[::-1]
        
        keep_indices = []
        
        for i, idx in enumerate(sorted_indices):
            if confidences[idx] < self.config.confidence_threshold:
                break
                
            current_bbox = bboxes[idx]
            should_keep = True
            
            # 檢查與已保留檢測的重疊
            for keep_idx in keep_indices:
                keep_bbox = bboxes[keep_idx]
                iou = self._calculate_iou(current_bbox.tolist(), keep_bbox.tolist())
                
                if iou > self.config.iou_threshold:
                    should_keep = False
                    break
            
            if should_keep:
                keep_indices.append(idx)
        
        return [detections[idx] for idx in keep_indices]
    
    def _cross_batch_nms(self, all_detections: List[Dict[str, Any]], 
                        new_batch: List[Dict[str, Any]], 
                        class_id: int) -> List[Dict[str, Any]]:
        """
        在批次間執行交叉NMS去重
        
        Args:
            all_detections: 所有已處理的檢測結果
            new_batch: 新批次的檢測結果
            class_id: 當前處理的類別ID
            
        Returns:
            List: 去重後的檢測結果
        """
        if not new_batch:
            return all_detections
        
        # 提取同類別的已有檢測
        existing_same_class = [det for det in all_detections if det['class_id'] == class_id]
        other_class_detections = [det for det in all_detections if det['class_id'] != class_id]
        
        # 對新批次與現有同類別檢測進行NMS
        combined = existing_same_class + new_batch
        if len(combined) <= 1:
            return all_detections
        
        # 執行NMS
        combined.sort(key=lambda x: x['confidence'], reverse=True)
        nms_result = self._batch_standard_nms(combined)
        
        # 合併結果
        return other_class_detections + nms_result

    def _calculate_iou(self, box1: List[float], box2: List[float]) -> float:
        """計算IoU"""
        x1_max = max(box1[0], box2[0])
        y1_max = max(box1[1], box2[1])
        x2_min = min(box1[2], box2[2])
        y2_min = min(box1[3], box2[3])

        if x2_min <= x1_max or y2_min <= y1_max:
            return 0.0

        intersection = (x2_min - x1_max) * (y2_min - y1_max)
        area1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
        area2 = (box2[2] - box2[0]) * (box2[3] - box2[1])
        union = area1 + area2 - intersection

        return intersection / union if union > 0 else 0.0

    def _calculate_diou(self, box1: List[float], box2: List[float]) -> float:
        """計算DIoU (Distance IoU)"""
        # 基礎IoU
        iou = self._calculate_iou(box1, box2)

        # 計算中心點距離
        center1 = [(box1[0] + box1[2]) / 2, (box1[1] + box1[3]) / 2]
        center2 = [(box2[0] + box2[2]) / 2, (box2[1] + box2[3]) / 2]
        center_dist_sq = (center1[0] - center2[0])**2 + \
            (center1[1] - center2[1])**2

        # 計算對角線距離
        c_x1 = min(box1[0], box2[0])
        c_y1 = min(box1[1], box2[1])
        c_x2 = max(box1[2], box2[2])
        c_y2 = max(box1[3], box2[3])
        diagonal_dist_sq = (c_x2 - c_x1)**2 + (c_y2 - c_y1)**2

        # DIoU
        if diagonal_dist_sq == 0:
            return iou

        diou = iou - center_dist_sq / diagonal_dist_sq
        return diou

    def _calculate_bbox_area(self, bbox: List[float]) -> float:
        """計算bbox面積"""
        return (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])

    def get_statistics(self) -> Dict[str, Any]:
        """獲取融合統計信息"""
        avg_fusion_time = (
            sum(self.stats['fusion_times']) / len(self.stats['fusion_times'])
            if self.stats['fusion_times'] else 0.0
        )

        reduction_ratio = (
            1.0 - (self.stats['output_detections'] /
                   max(self.stats['input_detections'], 1))
            if self.stats['input_detections'] > 0 else 0.0
        )

        return {
            'total_fusions': self.stats['total_fusions'],
            'input_detections': self.stats['input_detections'],
            'output_detections': self.stats['output_detections'],
            'reduction_ratio': reduction_ratio,
            'average_fusion_time': avg_fusion_time,
            'configured_strategy': self.config.strategy.value,
            'strategy_usage': self.stats['strategy_usage'],  # 實際使用的策略統計
            'adaptive_selections': self.stats['adaptive_selections'],
            'config': {
                'iou_threshold': self.config.iou_threshold,
                'confidence_threshold': self.config.confidence_threshold,
                'soft_nms_sigma': self.config.soft_nms_sigma,
                'enable_adaptive_fusion': getattr(self.config, 'enable_adaptive_fusion', False)
            }
        }

    def reset_statistics(self):
        """重置統計信息"""
        self.stats = {
            'total_fusions': 0,
            'input_detections': 0,
            'output_detections': 0,
            'fusion_times': [],
            'strategy_usage': {},
            'adaptive_selections': 0
        }

    def _sahi_overlap_merge(self, detections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        SAHI專用重疊合併策略
        
        特點：
        - 檢測IoU > 0.1的同類別物件進行合併
        - 支援mask與mask的IoU計算
        - 支援box與box的IoU計算  
        - 保持較高的召回率，適合SAHI場景
        
        Args:
            detections: 檢測結果列表
            
        Returns:
            List[Dict]: 合併後的檢測結果
        """
        if not detections:
            return []
        
        self.logger.debug(f"🎯 開始SAHI重疊合併: {len(detections)} 個檢測")
        
        # 獲取配置參數
        merge_threshold = getattr(self.config, 'sahi_merge_iou_threshold', 0.1)
        enable_mask_iou = getattr(self.config, 'enable_mask_iou_calculation', True)
        confidence_strategy = getattr(self.config, 'sahi_merge_confidence_strategy', 'max')
        
        # 按類別分組
        class_groups = {}
        for det in detections:
            class_id = det['class_id']
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(det)
        
        final_detections = []
        merge_count = 0
        
        for class_id, class_dets in class_groups.items():
            if len(class_dets) <= 1:
                final_detections.extend(class_dets)
                continue
            
            # 為每個檢測分配唯一ID
            for i, det in enumerate(class_dets):
                det['temp_id'] = i
            
            # 建立IoU連接圖  
            connections = {}  # temp_id -> [connected_temp_ids]
            
            for i, det1 in enumerate(class_dets):
                temp_id1 = det1['temp_id']
                connections[temp_id1] = []
                
                for j, det2 in enumerate(class_dets[i+1:], i+1):
                    temp_id2 = det2['temp_id']
                    
                    # 計算IoU（支持mask和box）
                    iou = self._calculate_flexible_iou(det1, det2, enable_mask_iou)
                    
                    if iou > merge_threshold:
                        connections[temp_id1].append(temp_id2)
                        if temp_id2 not in connections:
                            connections[temp_id2] = []
                        connections[temp_id2].append(temp_id1)
                        
                        self.logger.debug(f"   類別{class_id}: 檢測{temp_id1}與{temp_id2} IoU={iou:.3f} > {merge_threshold} → 連接")
            
            # 使用深度優先搜尋找到連通分量
            visited = set()
            merge_groups = []
            
            for temp_id in range(len(class_dets)):
                if temp_id not in visited:
                    group = []
                    self._dfs_merge_group(temp_id, connections, visited, group)
                    merge_groups.append(group)
            
            # 合併每個group中的檢測
            for group_ids in merge_groups:
                if len(group_ids) == 1:
                    # 單個檢測，直接添加
                    det_idx = group_ids[0]
                    final_detections.append(class_dets[det_idx])
                else:
                    # 多個檢測，需要合併
                    group_detections = [class_dets[idx] for idx in group_ids]
                    merged_detection = self._merge_sahi_detections(
                        group_detections, confidence_strategy
                    )
                    final_detections.append(merged_detection)
                    merge_count += len(group_detections) - 1
                    
                    self.logger.debug(f"   類別{class_id}: 合併{len(group_detections)}個檢測為1個")
        
        self.logger.debug(f"🎯 SAHI重疊合併完成: {len(detections)} → {len(final_detections)} 個檢測 (合併{merge_count}次)")
        return final_detections
    
    def _calculate_flexible_iou(self, det1: Dict[str, Any], det2: Dict[str, Any], 
                               enable_mask_iou: bool = True) -> float:
        """
        靈活的IoU計算，支持mask和box
        
        Args:
            det1: 第一個檢測
            det2: 第二個檢測
            enable_mask_iou: 是否啟用mask IoU計算
            
        Returns:
            float: IoU值
        """
        # 如果兩者都有mask且啟用mask IoU，優先使用mask IoU
        if (enable_mask_iou and 
            det1.get('mask') is not None and 
            det2.get('mask') is not None):
            try:
                mask_iou = self._calculate_mask_iou(det1['mask'], det2['mask'])
                return mask_iou
            except Exception as e:
                self.logger.warning(f"Mask IoU計算失敗，回退到Box IoU: {e}")
        
        # 使用box IoU
        return self._calculate_iou(det1['bbox'], det2['bbox'])
    
    def _calculate_mask_iou(self, mask1: np.ndarray, mask2: np.ndarray) -> float:
        """
        計算兩個mask的IoU
        
        Args:
            mask1: 第一個mask
            mask2: 第二個mask
            
        Returns:
            float: IoU值
        """
        try:
            # 確保mask是布爾類型
            mask1 = mask1.astype(bool)
            mask2 = mask2.astype(bool)
            
            # 確保mask尺寸相同
            if mask1.shape != mask2.shape:
                # 如果尺寸不同，調整到相同尺寸
                import cv2
                if mask1.size > mask2.size:
                    mask2 = cv2.resize(mask2.astype(np.uint8), 
                                     (mask1.shape[1], mask1.shape[0]), 
                                     interpolation=cv2.INTER_NEAREST).astype(bool)
                else:
                    mask1 = cv2.resize(mask1.astype(np.uint8), 
                                     (mask2.shape[1], mask2.shape[0]), 
                                     interpolation=cv2.INTER_NEAREST).astype(bool)
            
            # 計算交集和並集
            intersection = np.logical_and(mask1, mask2)
            union = np.logical_or(mask1, mask2)
            
            intersection_area = np.sum(intersection)
            union_area = np.sum(union)
            
            if union_area == 0:
                return 0.0
            
            iou = intersection_area / union_area
            return float(iou)
            
        except Exception as e:
            self.logger.warning(f"Mask IoU計算異常: {e}")
            return 0.0
    
    def _dfs_merge_group(self, temp_id: int, connections: Dict[int, List[int]], 
                        visited: set, group: List[int]):
        """
        深度優先搜尋找到合併組
        
        Args:
            temp_id: 當前檢測的臨時ID
            connections: 連接關係字典
            visited: 已訪問的節點集合
            group: 當前合併組的檢測ID列表
        """
        visited.add(temp_id)
        group.append(temp_id)
        
        # 遍歷所有連接的檢測
        for connected_id in connections.get(temp_id, []):
            if connected_id not in visited:
                self._dfs_merge_group(connected_id, connections, visited, group)
    
    def _merge_sahi_detections(self, detections: List[Dict[str, Any]], 
                              confidence_strategy: str = "max") -> Dict[str, Any]:
        """
        合併SAHI檢測結果
        
        Args:
            detections: 需要合併的檢測列表
            confidence_strategy: 置信度合併策略 ("max", "avg", "weighted_avg")
            
        Returns:
            Dict: 合併後的檢測
        """
        if len(detections) == 1:
            return detections[0]
        
        # 計算合併後的邊界框（包圍所有檢測框）
        all_bboxes = [det['bbox'] for det in detections]
        x_min = min(bbox[0] for bbox in all_bboxes)
        y_min = min(bbox[1] for bbox in all_bboxes)
        x_max = max(bbox[2] for bbox in all_bboxes)
        y_max = max(bbox[3] for bbox in all_bboxes)
        
        merged_bbox = [x_min, y_min, x_max, y_max]
        
        # 根據策略計算合併置信度
        if confidence_strategy == "max":
            merged_confidence = max(det['confidence'] for det in detections)
        elif confidence_strategy == "avg":
            merged_confidence = sum(det['confidence'] for det in detections) / len(detections)
        elif confidence_strategy == "weighted_avg":
            # 按面積加權平均
            total_area = 0
            weighted_confidence_sum = 0
            
            for det in detections:
                bbox_area = self._calculate_bbox_area(det['bbox'])
                total_area += bbox_area
                weighted_confidence_sum += det['confidence'] * bbox_area
            
            merged_confidence = weighted_confidence_sum / max(total_area, 1e-6)
        else:
            merged_confidence = max(det['confidence'] for det in detections)
        
        # 確保置信度在合理範圍內
        merged_confidence = min(max(merged_confidence, 0.0), 1.0)
        
        # 使用最高置信度檢測的類別信息
        best_detection = max(detections, key=lambda x: x['confidence'])
        
        # 創建合併後的檢測
        merged_detection = {
            'bbox': merged_bbox,
            'confidence': merged_confidence,
            'class_id': best_detection['class_id'],
            'class_name': best_detection['class_name'],
            'area': self._calculate_bbox_area(merged_bbox),
            'merged_from_count': len(detections),
            'original_confidences': [det['confidence'] for det in detections],
            'merge_method': 'sahi_overlap_merge',
            'merge_strategy': confidence_strategy
        }
        
        # 處理mask合併（如果存在）
        masks = [det.get('mask') for det in detections if det.get('mask') is not None]
        if masks:
            try:
                merged_mask = self._merge_sahi_masks(masks)
                merged_detection['mask'] = merged_mask
            except Exception as e:
                self.logger.warning(f"SAHI mask合併失敗: {e}")
                merged_detection['mask'] = None
        else:
            merged_detection['mask'] = None
        
        return merged_detection
    
    def _merge_sahi_masks(self, masks: List[np.ndarray]) -> Optional[np.ndarray]:
        """
        合併多個SAHI masks
        
        Args:
            masks: mask列表
            
        Returns:
            Optional[np.ndarray]: 合併後的mask
        """
        if not masks:
            return None
        
        try:
            # 找到最大的mask尺寸
            max_h = max(mask.shape[0] for mask in masks)
            max_w = max(mask.shape[1] for mask in masks)
            
            # 將所有mask調整到相同尺寸並合併
            merged_mask = np.zeros((max_h, max_w), dtype=bool)
            
            for mask in masks:
                if mask.shape != (max_h, max_w):
                    import cv2
                    resized_mask = cv2.resize(
                        mask.astype(np.uint8), 
                        (max_w, max_h), 
                        interpolation=cv2.INTER_NEAREST
                    ).astype(bool)
                else:
                    resized_mask = mask.astype(bool)
                
                # 取並集
                merged_mask = np.logical_or(merged_mask, resized_mask)
            
            return merged_mask
            
        except Exception as e:
            self.logger.warning(f"SAHI masks合併失敗: {e}")
            return masks[0] if masks else None

    def cleanup(self):
        """清理融合引擎資源"""
        self.logger.debug("🧹 清理融合引擎資源")
        self.reset_statistics()
