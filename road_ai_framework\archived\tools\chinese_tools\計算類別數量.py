import os
import json
from collections import defaultdict


def analyze_dataset_classes(dataset_folder):
    """
    分析數據集資料夾，計算每個類別的數量，
    並單獨計算包含多個物件（多個標註）的影像中的類別數量。

    Args:
        dataset_folder (str): 包含 image 和 json 檔案的資料夾路徑。

    Returns:
        tuple: 包含兩個字典的元組。
               第一個字典: 標準的類別數量統計 (all_class_stats)。
               第二個字典: 多物件影像中的類別數量統計 (multi_object_stats)。
    """
    # 儲存所有影像中的類別統計
    all_class_stats = defaultdict(lambda: {"total": 0})

    # 儲存包含多個物件的影像中的類別統計
    # 這裡的「多物件」是指一個 JSON 檔案中包含的 'shapes' 數量大於 1
    multi_object_stats = defaultdict(lambda: {"total": 0})

    if not os.path.isdir(dataset_folder):
        print(f"警告: 資料夾 '{dataset_folder}' 不存在。")
        return all_class_stats, multi_object_stats

    print(f"正在掃描 '{dataset_folder}' 資料夾...")

    for filename in os.listdir(dataset_folder):
        if filename.lower().endswith(".json"):
            json_filepath = os.path.join(dataset_folder, filename)
            try:
                with open(json_filepath, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)

                    # 用於追蹤當前 JSON 檔案中所有物件的類別
                    current_file_labels = []

                    # 檢查 'shapes' 鍵是否存在且為列表
                    if "shapes" in json_data and isinstance(json_data["shapes"], list):
                        for shape in json_data["shapes"]:
                            # 從每個 shape 中獲取 label
                            category = shape.get("label")
                            if category:
                                # 執行所有類別的標準統計
                                all_class_stats[category]["total"] += 1
                                current_file_labels.append(category)
                            else:
                                print(
                                    f"警告: 檔案 '{json_filepath}' 中的一個物件未找到 'label' 鍵。")

                        # 判斷是否為多物件（多個標註）影像
                        # 如果一個檔案中包含的物件數量大於 1，則將其所有物件的類別計入 multi_object_stats
                        if len(current_file_labels) > 1:
                            for category in current_file_labels:
                                multi_object_stats[category]["total"] += 1

                    else:
                        print(
                            f"警告: 檔案 '{json_filepath}' 中未找到 'shapes' 列表或其格式不正確。")

            except json.JSONDecodeError:
                print(f"錯誤: 無法解析 JSON 檔案 '{json_filepath}'。請檢查其有效性。")
            except Exception as e:
                print(f"處理檔案 '{json_filepath}' 時發生未預期錯誤: {e}")

    return all_class_stats, multi_object_stats


def print_class_statistics(class_counts, title="類別統計結果"):
    """
    列印類別統計結果。

    Args:
        class_counts (dict): 包含類別統計數據的字典。
        title (str): 輸出標題。
    """
    print(f"\n--- {title} ---")
    if not class_counts:
        print("未找到任何類別資料。請檢查路徑和 JSON 檔案內容。")
        return

    # 對類別名稱進行排序以保持輸出一致性
    for category in sorted(class_counts.keys()):
        counts = class_counts[category]
        print(
            f"{category}: 總計={counts['total']}")
    print("--------------------\n")


# --- 使用範例 ---
if __name__ == "__main__":
    # >>> 請務必將以下路徑替換為你的數據集資料夾的實際路徑 <<<
    # 範例: dataset_root = "C:/Users/<USER>/Documents/MyDataset"
    # 或者如果你是 Windows 系統，也可以使用原始字串 (raw string) 來避免反斜線問題:
    # dataset_root = r"D:\MyDataset\object_detect"

    # 目前設定為你提供的範例路徑，請根據你的實際情況修改
    # 注意：現在直接指向包含 image 和 json 檔案的資料夾，不需要 train/val/test 子資料夾
    dataset_root = r"\\192.168.1.46\RD_Universe\專案\2025_3.道路破損辨識\1.專案執行\第5次_1800張_全資料\0_原檔"

    # 檢查指定的數據集資料夾是否存在
    if not os.path.isdir(dataset_root):
        print(f"錯誤: 指定的數據集資料夾 '{dataset_root}' 不存在。請檢查路徑是否正確。")
    else:
        # 運行分析函式，接收標準統計和多物件統計
        all_class_stats, multi_object_stats = analyze_dataset_classes(
            dataset_root)

        # 打印標準的類別統計結果
        print_class_statistics(all_class_stats, "所有影像中的類別統計")

        # 打印多物件影像中的類別統計結果
        # 這裡的「多物件」是指一個 JSON 檔案中包含的標註 (shapes) 數量大於 1
        print_class_statistics(multi_object_stats, "多物件影像中的類別統計")
