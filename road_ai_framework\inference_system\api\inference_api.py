#!/usr/bin/env python3
"""
🌐 推理API服務
提供RESTful API接口，支援微服務部署
"""

import logging
import time
import traceback
from typing import Dict, List, Any, Optional
from pathlib import Path
import base64
import io
import uuid
from datetime import datetime

try:
    from fastapi import FastAPI, HTTPException, File, UploadFile, BackgroundTasks
    from fastapi.responses import JSONResponse, FileResponse
    from fastapi.middleware.cors import CORSMiddleware
    from pydantic import BaseModel, Field
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False

from PIL import Image
import numpy as np

from ..config import UnifiedConfig
from ..main import UnifiedYOLOInference
from ..performance import PerformanceMonitor
from .health_monitor import HealthMonitor


# API數據模型
class InferenceRequest(BaseModel):
    """推理請求模型"""
    image_base64: Optional[str] = Field(None, description="Base64編碼的圖像")
    image_url: Optional[str] = Field(None, description="圖像URL")
    enable_visualization: bool = Field(True, description="是否生成視覺化結果")
    enable_caching: bool = Field(True, description="是否啟用緩存")
    confidence_threshold: Optional[float] = Field(None, description="置信度閾值")
    classes_filter: Optional[List[int]] = Field(None, description="類別過濾器")


class BatchInferenceRequest(BaseModel):
    """批量推理請求模型"""
    images: List[InferenceRequest] = Field(..., description="圖像列表")
    enable_concurrent: bool = Field(True, description="是否啟用並發處理")
    max_workers: Optional[int] = Field(None, description="最大工作線程數")


class InferenceResponse(BaseModel):
    """推理響應模型"""
    success: bool = Field(..., description="處理是否成功")
    request_id: str = Field(..., description="請求ID")
    processing_time: float = Field(..., description="處理時間(秒)")
    detections: List[Dict[str, Any]] = Field(default_factory=list, description="檢測結果")
    visualization_url: Optional[str] = Field(None, description="視覺化結果URL")
    statistics: Dict[str, Any] = Field(default_factory=dict, description="統計信息")
    error: Optional[str] = Field(None, description="錯誤信息")


class HealthResponse(BaseModel):
    """健康檢查響應模型"""
    status: str = Field(..., description="服務狀態")
    timestamp: str = Field(..., description="檢查時間")
    version: str = Field(..., description="系統版本")
    uptime_seconds: float = Field(..., description="運行時間")
    system_info: Dict[str, Any] = Field(default_factory=dict, description="系統信息")


class InferenceAPI:
    """
    推理API服務類
    
    提供完整的RESTful API接口
    """
    
    def __init__(self, config: UnifiedConfig, enable_monitoring: bool = True):
        """
        初始化API服務
        
        Args:
            config: 統一配置
            enable_monitoring: 是否啟用監控
        """
        if not FASTAPI_AVAILABLE:
            raise ImportError("FastAPI未安裝，請執行: pip install fastapi uvicorn")
        
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 核心組件
        self.inference_system = UnifiedYOLOInference(config)
        
        # 監控組件
        self.enable_monitoring = enable_monitoring
        self.performance_monitor = None
        self.health_monitor = None
        
        if enable_monitoring:
            self.performance_monitor = PerformanceMonitor(config)
            self.health_monitor = HealthMonitor()
        
        # API統計
        self.start_time = time.time()
        self.request_count = 0
        self.successful_requests = 0
        self.failed_requests = 0
        
        # 臨時文件管理
        self.temp_dir = Path(config.output_dir) / "api_temp"
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("🌐 推理API服務初始化完成")
    
    def create_app(self) -> FastAPI:
        """創建FastAPI應用"""
        app = FastAPI(
            title="統一YOLO推理API",
            description="企業級道路基礎設施AI檢測API服務",
            version="3.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        # CORS中間件
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # 註冊路由
        self._register_routes(app)
        
        return app
    
    def _register_routes(self, app: FastAPI):
        """註冊API路由"""
        
        @app.get("/", response_model=Dict[str, str])
        async def root():
            """根路由"""
            return {
                "service": "統一YOLO推理API",
                "version": "3.0.0",
                "status": "running",
                "docs": "/docs"
            }
        
        @app.get("/health", response_model=HealthResponse)
        async def health_check():
            """健康檢查"""
            try:
                uptime = time.time() - self.start_time
                
                # 系統信息
                system_info = {}
                if self.health_monitor:
                    system_info = self.health_monitor.get_system_info()
                
                return HealthResponse(
                    status="healthy",
                    timestamp=datetime.now().isoformat(),
                    version="3.0.0",
                    uptime_seconds=uptime,
                    system_info=system_info
                )
                
            except Exception as e:
                self.logger.error(f"❌ 健康檢查失敗: {str(e)}")
                raise HTTPException(status_code=500, detail="健康檢查失敗")
        
        @app.post("/inference", response_model=InferenceResponse)
        async def single_inference(request: InferenceRequest):
            """單張圖像推理"""
            request_id = str(uuid.uuid4())
            start_time = time.time()
            
            try:
                self.request_count += 1
                self.logger.info(f"🌐 API請求開始: {request_id}")
                
                # 處理輸入圖像
                image_path = await self._process_input_image(request, request_id)
                
                # 執行推理
                result = self.inference_system.process_single_image(
                    image_path,
                    enable_visualization=request.enable_visualization
                )
                
                # 生成響應
                processing_time = time.time() - start_time
                
                if result.get('success', False):
                    self.successful_requests += 1
                    
                    # 處理視覺化結果
                    visualization_url = None
                    if request.enable_visualization and 'visualization_paths' in result:
                        visualization_url = self._save_visualization(
                            result['visualization_paths'], 
                            request_id
                        )
                    
                    response = InferenceResponse(
                        success=True,
                        request_id=request_id,
                        processing_time=processing_time,
                        detections=result.get('detections', []),
                        visualization_url=visualization_url,
                        statistics=result.get('detection_statistics', {})
                    )
                    
                    self.logger.info(f"✅ API請求成功: {request_id} ({processing_time:.2f}s)")
                    return response
                    
                else:
                    self.failed_requests += 1
                    self.logger.warning(f"⚠️ 推理失敗: {request_id}")
                    
                    return InferenceResponse(
                        success=False,
                        request_id=request_id,
                        processing_time=processing_time,
                        error=result.get('error', '推理失敗')
                    )
                
            except Exception as e:
                self.failed_requests += 1
                processing_time = time.time() - start_time
                error_msg = str(e)
                
                self.logger.error(f"❌ API請求失敗: {request_id} - {error_msg}")
                self.logger.debug(traceback.format_exc())
                
                return InferenceResponse(
                    success=False,
                    request_id=request_id,
                    processing_time=processing_time,
                    error=error_msg
                )
            
            finally:
                # 清理臨時文件
                self._cleanup_temp_files(request_id)
        
        @app.post("/batch_inference")
        async def batch_inference(request: BatchInferenceRequest):
            """批量推理"""
            request_id = str(uuid.uuid4())
            start_time = time.time()
            
            try:
                self.logger.info(f"🌐 批量API請求開始: {request_id} ({len(request.images)}張圖像)")
                
                results = []
                
                for i, image_request in enumerate(request.images):
                    sub_request_id = f"{request_id}_{i}"
                    
                    try:
                        # 處理單張圖像
                        image_path = await self._process_input_image(image_request, sub_request_id)
                        result = self.inference_system.process_single_image(image_path)
                        
                        results.append({
                            'index': i,
                            'success': result.get('success', False),
                            'detections': result.get('detections', []),
                            'error': result.get('error')
                        })
                        
                    except Exception as e:
                        results.append({
                            'index': i,
                            'success': False,
                            'error': str(e)
                        })
                
                processing_time = time.time() - start_time
                successful_count = sum(1 for r in results if r['success'])
                
                self.logger.info(f"✅ 批量API請求完成: {request_id} ({successful_count}/{len(results)}成功)")
                
                return {
                    'success': True,
                    'request_id': request_id,
                    'processing_time': processing_time,
                    'total_images': len(request.images),
                    'successful_images': successful_count,
                    'failed_images': len(results) - successful_count,
                    'results': results
                }
                
            except Exception as e:
                processing_time = time.time() - start_time
                self.logger.error(f"❌ 批量API請求失敗: {request_id} - {str(e)}")
                
                return {
                    'success': False,
                    'request_id': request_id,
                    'processing_time': processing_time,
                    'error': str(e)
                }
        
        @app.get("/statistics")
        async def get_statistics():
            """獲取API統計信息"""
            uptime = time.time() - self.start_time
            
            stats = {
                'uptime_seconds': uptime,
                'total_requests': self.request_count,
                'successful_requests': self.successful_requests,
                'failed_requests': self.failed_requests,
                'success_rate': (self.successful_requests / max(1, self.request_count)) * 100,
                'requests_per_second': self.request_count / max(1, uptime)
            }
            
            # 性能監控統計
            if self.performance_monitor:
                perf_stats = self.performance_monitor.get_performance_summary()
                stats['performance'] = perf_stats
            
            return stats
        
        @app.get("/visualization/{file_name}")
        async def get_visualization(file_name: str):
            """獲取視覺化結果文件"""
            file_path = self.temp_dir / "visualizations" / file_name
            
            if not file_path.exists():
                raise HTTPException(status_code=404, detail="視覺化文件不存在")
            
            return FileResponse(
                path=str(file_path),
                media_type='image/jpeg',
                filename=file_name
            )
    
    async def _process_input_image(self, request: InferenceRequest, request_id: str) -> str:
        """處理輸入圖像"""
        
        if request.image_base64:
            # 處理base64圖像
            try:
                image_data = base64.b64decode(request.image_base64)
                image = Image.open(io.BytesIO(image_data))
                
                # 保存臨時文件
                temp_file = self.temp_dir / f"{request_id}.jpg"
                image.save(temp_file, format='JPEG')
                
                return str(temp_file)
                
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"無效的base64圖像: {str(e)}")
        
        elif request.image_url:
            # 處理圖像URL
            try:
                import requests
                response = requests.get(request.image_url, timeout=30)
                response.raise_for_status()
                
                image = Image.open(io.BytesIO(response.content))
                
                # 保存臨時文件
                temp_file = self.temp_dir / f"{request_id}.jpg"
                image.save(temp_file, format='JPEG')
                
                return str(temp_file)
                
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"無法下載圖像: {str(e)}")
        
        else:
            raise HTTPException(status_code=400, detail="必須提供image_base64或image_url")
    
    def _save_visualization(self, visualization_paths: Dict[str, str], request_id: str) -> Optional[str]:
        """保存視覺化結果並返回訪問URL"""
        try:
            viz_dir = self.temp_dir / "visualizations"
            viz_dir.mkdir(exist_ok=True)
            
            # 選擇主要的視覺化結果
            if 'three_view_path' in visualization_paths:
                source_file = Path(visualization_paths['three_view_path'])
                target_file = viz_dir / f"{request_id}_three_view.jpg"
                
                if source_file.exists():
                    import shutil
                    shutil.copy2(source_file, target_file)
                    return f"/visualization/{target_file.name}"
            
            return None
            
        except Exception as e:
            self.logger.warning(f"⚠️ 視覺化結果保存失敗: {str(e)}")
            return None
    
    def _cleanup_temp_files(self, request_id: str):
        """清理臨時文件"""
        try:
            # 清理輸入圖像
            temp_file = self.temp_dir / f"{request_id}.jpg"
            if temp_file.exists():
                temp_file.unlink()
            
            # 清理視覺化文件（保留一段時間供下載）
            # 這裡可以實現定期清理機制
            
        except Exception as e:
            self.logger.debug(f"臨時文件清理失敗: {str(e)}")
    
    def start_monitoring(self):
        """啟動監控"""
        if self.performance_monitor:
            self.performance_monitor.start_monitoring()
        
        if self.health_monitor:
            self.health_monitor.start_monitoring()
    
    def stop_monitoring(self):
        """停止監控"""
        if self.performance_monitor:
            self.performance_monitor.stop_monitoring()
        
        if self.health_monitor:
            self.health_monitor.stop_monitoring()
    
    def cleanup(self):
        """清理API服務資源"""
        self.logger.info("🧹 清理API服務資源")
        
        self.stop_monitoring()
        
        if self.inference_system:
            self.inference_system.cleanup()


def create_api_app(config: UnifiedConfig) -> FastAPI:
    """
    創建API應用的工廠函數
    
    Args:
        config: 統一配置
        
    Returns:
        FastAPI: 配置好的API應用
    """
    api_service = InferenceAPI(config)
    app = api_service.create_app()
    
    # 將API服務實例附加到app以便後續使用
    app.api_service = api_service
    
    return app


def run_api_server(config: UnifiedConfig, 
                  host: str = "0.0.0.0", 
                  port: int = 8000,
                  workers: int = 1):
    """
    運行API服務器
    
    Args:
        config: 統一配置
        host: 主機地址
        port: 端口號
        workers: 工作進程數
    """
    if not FASTAPI_AVAILABLE:
        raise ImportError("FastAPI未安裝，請執行: pip install fastapi uvicorn")
    
    app = create_api_app(config)
    
    # 啟動監控
    if hasattr(app, 'api_service'):
        app.api_service.start_monitoring()
    
    try:
        uvicorn.run(
            app,
            host=host,
            port=port,
            workers=workers,
            log_level="info"
        )
    finally:
        # 清理資源
        if hasattr(app, 'api_service'):
            app.api_service.cleanup()