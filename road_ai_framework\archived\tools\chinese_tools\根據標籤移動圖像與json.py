import os
import json
import shutil

def scan_labels(input_dir):
    """掃描所有 JSON 檔並收集出現過的標籤"""
    labels_set = set()
    for root, _, files in os.walk(input_dir):
        for file in files:
            if file.endswith(".json"):
                json_path = os.path.join(root, file)
                try:
                    with open(json_path, "r", encoding="utf-8") as f:
                        data = json.load(f)
                        if "shapes" in data:
                            for shape in data["shapes"]:
                                if "label" in shape:
                                    labels_set.add(shape["label"])
                except Exception as e:
                    print(f"讀取 {json_path} 時發生錯誤: {e}")
    return sorted(labels_set)

def move_files_by_label(input_dir, output_dir, target_labels):
    """移動包含指定標籤的 JSON 與對應圖片"""
    os.makedirs(output_dir, exist_ok=True)

    for root, _, files in os.walk(input_dir):
        for file in files:
            if file.endswith(".json"):
                json_path = os.path.join(root, file)
                try:
                    with open(json_path, "r", encoding="utf-8") as f:
                        data = json.load(f)
                        file_labels = set()
                        if "shapes" in data:
                            file_labels = {shape["label"] for shape in data["shapes"] if "label" in shape}

                        # 如果有符合標籤
                        if file_labels & set(target_labels):
                            # 複製 JSON
                            shutil.copy2(json_path, os.path.join(output_dir, file))

                            # 複製對應圖片（同名 jpg/png/jpeg）
                            base_name = os.path.splitext(file)[0]
                            for ext in [".jpg", ".jpeg", ".png"]:
                                img_path = os.path.join(root, base_name + ext)
                                if os.path.exists(img_path):
                                    shutil.copy2(img_path, os.path.join(output_dir, base_name + ext))
                except Exception as e:
                    print(f"處理 {json_path} 時發生錯誤: {e}")

if __name__ == "__main__":
    input_dir = input("輸入資料夾路徑: ").strip()
    output_dir = os.path.join(input_dir, "output")

    # 掃描標籤
    labels = scan_labels(input_dir)
    print("\n資料夾中找到以下標籤：")
    for i, label in enumerate(labels, 1):
        print(f"{i}. {label}")

    # 選擇要移動的標籤
    selected = input("\n輸入要移動的標籤編號（可用逗號分隔）: ").strip()
    selected_indices = [int(x) for x in selected.split(",") if x.strip().isdigit()]
    target_labels = [labels[i - 1] for i in selected_indices if 1 <= i <= len(labels)]

    print(f"\n將移動標籤 {target_labels} 的檔案到 {output_dir}\n")
    move_files_by_label(input_dir, output_dir, target_labels)
    print("完成！")
