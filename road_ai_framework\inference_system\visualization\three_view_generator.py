#!/usr/bin/env python3
"""
🎨 三視圖生成器
生成原圖/GT/預測的三視圖對比，從原始 unified_yolo_inference.py 中提取
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import cv2

from ..core.base_inference import Detection
from ..config import UnifiedConfig, VisualizationConfig
from .font_manager import FontManager


class ThreeViewGenerator:
    """
    三視圖生成器

    功能：
    - 原圖/GT/預測三視圖對比
    - 水平/垂直佈局支援
    - GT和預測顏色一致性
    - 排除類別支援
    """

    def __init__(self,
                 font_manager: FontManager,
                 config: UnifiedConfig,
                 layout: str = "horizontal",
                 spacing: int = 10,
                 excluded_classes: Optional[List] = None):
        """
        初始化三視圖生成器

        Args:
            font_manager: 字體管理器
            config: 統一配置
            layout: 佈局方式 ("horizontal" 或 "vertical")
            spacing: 視圖間距
            excluded_classes: 排除的類別列表
        """
        self.font_manager = font_manager
        self.config = config
        self.layout = layout
        self.spacing = spacing
        self.excluded_classes = excluded_classes or []
        self.logger = logging.getLogger(__name__)

        # 視覺化配置
        self.viz_config = config.visualization

    def generate_prediction_only(self,
                                 original_image: np.ndarray,
                                 detections: List[Detection],
                                 image_name: str = "image") -> np.ndarray:
        """
        生成單純預測結果圖像（不包含原圖或GT）

        Args:
            original_image: 原始圖像
            detections: 預測檢測結果
            image_name: 圖像名稱

        Returns:
            np.ndarray: 預測結果圖像
        """
        try:
            # 創建預測視圖
            pred_view = original_image.copy()

            # 繪製預測結果
            pred_view = self._draw_predictions(pred_view, detections)

            # 添加標題（使用字體管理器）
            title_text = f"Prediction Results - {len(detections)} detections"
            pred_view = self.font_manager.apply_text_style(
                pred_view, title_text, (10, 30), (255, 255, 255), (0, 0, 0)
            )

            # 🖼️ 應用輸出圖像縮放
            pred_view = self._apply_output_scaling(pred_view)

            self.logger.debug(f"🎨 單純預測圖像已生成: {image_name}")
            return pred_view

        except Exception as e:
            self.logger.error(f"❌ 生成單純預測圖像失敗: {e}")
            # 返回原圖作為備用
            return original_image.copy()

    def generate(self,
                 original_image: np.ndarray,
                 detections: List[Detection],
                 gt_annotations: Optional[List[Dict[str, Any]]] = None,
                 output_dir: Optional[str] = None,
                 image_name: str = "image") -> np.ndarray:
        """
        生成三視圖

        Args:
            original_image: 原始圖像
            detections: 預測檢測結果
            gt_annotations: GT標註
            output_dir: 輸出目錄
            image_name: 圖像名稱

        Returns:
            np.ndarray: 三視圖合成圖像
        """
        try:
            # 1. 準備三個視圖
            original_view = original_image.copy()
            gt_view = original_image.copy() if gt_annotations else None
            pred_view = original_image.copy()

            # 2. 繪製GT標註
            if gt_view is not None and gt_annotations:
                gt_view = self._draw_gt_annotations(gt_view, gt_annotations)

            # 3. 繪製預測結果
            pred_view = self._draw_predictions(pred_view, detections)

            # 4. 合成三視圖
            if gt_view is not None:
                three_view = self._compose_three_views(
                    original_view, gt_view, pred_view)
            else:
                # 只有兩視圖：原圖和預測
                three_view = self._compose_two_views(original_view, pred_view)

            # 5. 添加標題
            three_view = self._add_view_titles(three_view, gt_view is not None)

            # 6. 🖼️ 應用輸出圖像縮放
            three_view = self._apply_output_scaling(three_view)

            self.logger.debug(f"🎨 三視圖生成完成：{three_view.shape}")

            return three_view

        except Exception as e:
            self.logger.error(f"❌ 三視圖生成失敗: {str(e)}")
            return original_image

    def _draw_gt_annotations(self, image: np.ndarray, gt_annotations: List[Dict[str, Any]]) -> np.ndarray:
        """繪製GT標註"""
        result_image = image.copy()

        for gt in gt_annotations:
            try:
                # 檢查是否為排除類別
                class_id = gt.get('class_id', -1)
                class_name = gt.get('class_name', '')

                if self._is_excluded_class(class_id, class_name):
                    continue

                # 獲取顏色（與預測保持一致）
                color = self._get_class_color(class_id)

                # 繪製bbox
                bbox = gt.get('bbox', [])
                if len(bbox) >= 4:
                    cv2.rectangle(
                        result_image,
                        (int(bbox[0]), int(bbox[1])),
                        (int(bbox[2]), int(bbox[3])),
                        color,
                        self.viz_config.line_thickness
                    )

                    # 繪製標籤
                    label = f"GT:{class_name}"
                    label_pos = (int(bbox[0]), int(bbox[1]) - 5)
                    result_image = self.font_manager.apply_text_style(
                        result_image, label, label_pos, color, (0, 0, 0)
                    )

                # 繪製mask（如果有）
                if 'mask' in gt and gt['mask'] is not None:
                    self._draw_mask(result_image, gt['mask'], color)

            except Exception as e:
                self.logger.warning(f"⚠️ GT標註繪製失敗: {str(e)}")
                continue

        return result_image

    def _draw_predictions(self, image: np.ndarray, detections: List[Detection]) -> np.ndarray:
        """繪製預測結果"""
        result_image = image.copy()

        for detection in detections:
            try:
                # 檢查是否為排除類別
                if self._is_excluded_class(detection.class_id, detection.class_name):
                    continue

                # 獲取類別顏色
                color = self._get_class_color(detection.class_id)

                # 繪製bbox
                cv2.rectangle(
                    result_image,
                    (int(detection.bbox[0]), int(detection.bbox[1])),
                    (int(detection.bbox[2]), int(detection.bbox[3])),
                    color,
                    self.viz_config.line_thickness
                )

                # 繪製標籤和置信度
                label = f"{detection.class_name}:{detection.confidence:.2f}"
                label_pos = (int(detection.bbox[0]), int(
                    detection.bbox[1]) - 5)
                result_image = self.font_manager.apply_text_style(
                    result_image, label, label_pos, color, (0, 0, 0)
                )

                # 繪製mask（如果有）
                if detection.mask is not None:
                    self._draw_mask(result_image, detection.mask, color)

            except Exception as e:
                self.logger.warning(f"⚠️ 預測結果繪製失敗: {str(e)}")
                continue

        return result_image

    def _draw_mask(self, image: np.ndarray, mask: np.ndarray, color: Tuple[int, int, int]):
        """繪製mask"""
        try:
            if self.viz_config.mask_render_mode == "outline_only":
                # 只繪製邊框
                contours, _ = cv2.findContours(
                    mask.astype(
                        np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
                )
                cv2.drawContours(image, contours, -1, color,
                                 self.viz_config.line_thickness)
            else:
                # 填充mask
                mask_overlay = image.copy()
                mask_overlay[mask > 0] = color
                cv2.addWeighted(image, 1 - self.viz_config.fill_alpha,
                                mask_overlay, self.viz_config.fill_alpha, 0, image)
        except Exception as e:
            self.logger.warning(f"⚠️ Mask繪製失敗: {str(e)}")

    def _compose_three_views(self, original: np.ndarray, gt: np.ndarray, pred: np.ndarray) -> np.ndarray:
        """合成三視圖"""
        if self.layout == "horizontal":
            return self._horizontal_layout([original, gt, pred])
        else:
            return self._vertical_layout([original, gt, pred])

    def _compose_two_views(self, original: np.ndarray, pred: np.ndarray) -> np.ndarray:
        """合成兩視圖（原圖+預測）"""
        if self.layout == "horizontal":
            return self._horizontal_layout([original, pred])
        else:
            return self._vertical_layout([original, pred])

    def _horizontal_layout(self, images: List[np.ndarray]) -> np.ndarray:
        """水平佈局"""
        # 確保所有圖像高度一致
        min_height = min(img.shape[0] for img in images)
        resized_images = []

        for img in images:
            if img.shape[0] != min_height:
                aspect_ratio = img.shape[1] / img.shape[0]
                new_width = int(min_height * aspect_ratio)
                resized_img = cv2.resize(img, (new_width, min_height))
                resized_images.append(resized_img)
            else:
                resized_images.append(img)

        # 水平拼接
        return np.hstack([
            np.concatenate(
                [img, np.full((img.shape[0], self.spacing, 3), 255, dtype=np.uint8)], axis=1)
            if i < len(resized_images) - 1 else img
            for i, img in enumerate(resized_images)
        ])

    def _vertical_layout(self, images: List[np.ndarray]) -> np.ndarray:
        """垂直佈局"""
        # 確保所有圖像寬度一致
        min_width = min(img.shape[1] for img in images)
        resized_images = []

        for img in images:
            if img.shape[1] != min_width:
                aspect_ratio = img.shape[0] / img.shape[1]
                new_height = int(min_width * aspect_ratio)
                resized_img = cv2.resize(img, (min_width, new_height))
                resized_images.append(resized_img)
            else:
                resized_images.append(img)

        # 垂直拼接
        return np.vstack([
            np.concatenate(
                [img, np.full((self.spacing, img.shape[1], 3), 255, dtype=np.uint8)], axis=0)
            if i < len(resized_images) - 1 else img
            for i, img in enumerate(resized_images)
        ])

    def _add_view_titles(self, image: np.ndarray, has_gt: bool) -> np.ndarray:
        """添加視圖標題"""
        try:
            if has_gt:
                titles = ["原圖", "GT標註", "預測結果"]
            else:
                titles = ["原圖", "預測結果"]

            # 計算每個視圖的寬度
            if self.layout == "horizontal":
                view_width = (
                    image.shape[1] - (len(titles) - 1) * self.spacing) // len(titles)

                for i, title in enumerate(titles):
                    x_pos = i * (view_width + self.spacing) + \
                        view_width // 2 - 50
                    y_pos = 30

                    image = self.font_manager.apply_text_style(
                        image, title, (x_pos, y_pos),
                        (0, 0, 255), (255, 255, 255)
                    )
            else:
                view_height = (
                    image.shape[0] - (len(titles) - 1) * self.spacing) // len(titles)

                for i, title in enumerate(titles):
                    x_pos = 10
                    y_pos = i * (view_height + self.spacing) + 30

                    image = self.font_manager.apply_text_style(
                        image, title, (x_pos, y_pos),
                        (0, 0, 255), (255, 255, 255)
                    )

        except Exception as e:
            self.logger.warning(f"⚠️ 標題添加失敗: {str(e)}")

        return image

    def _get_class_color(self, class_id: int) -> Tuple[int, int, int]:
        """獲取類別顏色"""
        class_config = self.config.get_class_config(class_id)
        if class_config:
            # BGR格式
            return tuple(reversed(class_config.color))
        else:
            # 默認顏色
            return (0, 255, 0)

    def _is_excluded_class(self, class_id: int, class_name: str) -> bool:
        """檢查是否為排除類別"""
        return (class_id in self.excluded_classes or
                class_name in self.excluded_classes)

    def _apply_output_scaling(self, image: np.ndarray) -> np.ndarray:
        """
        🖼️ 應用輸出圖像縮放

        Args:
            image: 輸入圖像

        Returns:
            np.ndarray: 縮放後的圖像
        """
        try:
            scale = getattr(self.viz_config, 'output_image_scale', 1.0)

            if scale == 1.0:
                return image

            h, w = image.shape[:2]
            new_w = int(w * scale)
            new_h = int(h * scale)

            # 確保尺寸至少為1
            new_w = max(1, new_w)
            new_h = max(1, new_h)

            scaled_image = cv2.resize(
                image, (new_w, new_h), interpolation=cv2.INTER_AREA)

            self.logger.debug(
                f"🖼️ 輸出圖像縮放: {w}x{h} → {new_w}x{new_h} (比例: {scale:.2f})")

            return scaled_image

        except Exception as e:
            self.logger.warning(f"⚠️ 輸出圖像縮放失敗: {str(e)}")
            return image

    def cleanup(self):
        """清理三視圖生成器資源"""
        self.logger.debug("🧹 清理三視圖生成器資源")
