# 圖像樣本抽取工具 - EXE打包指南

## 🚀 快速開始

### 方法1: 自動打包（推薦）
```bash
# 1. 直接運行打包腳本
python build_exe.py

# 2. 或使用批次文件（Windows）
build.bat
```

### 方法2: 手動打包
```bash
# 1. 安裝必要套件
pip install PyQt6 pyinstaller

# 2. 執行打包命令
pyinstaller --clean --noconfirm image_sample_extractor.spec
```

## 📦 輸出結果

打包成功後會在 `dist/` 資料夾中生成：
- `圖像樣本抽取工具.exe` - 主程式（約 50-100MB）

## ⚙️ 自定義配置

### 修改應用程式圖標
1. 準備 `.ico` 格式的圖標文件
2. 編輯 `image_sample_extractor.spec`
3. 修改 `icon=None` 為 `icon='your_icon.ico'`

### 修改應用程式名稱
編輯 `image_sample_extractor.spec` 中的 `name='圖像樣本抽取工具'`

### 減小文件大小
在 spec 文件中添加排除項：
```python
excludes=[
    'matplotlib',
    'numpy',
    'scipy',
    'pandas',
    'tkinter'
]
```

## 🛠️ 疑難排解

### 常見問題1: 缺少模組
**錯誤**: `ModuleNotFoundError: No module named 'XXX'`

**解決方案**: 在 spec 文件的 `hiddenimports` 中添加缺少的模組：
```python
hiddenimports=[
    'PyQt6.QtCore',
    'PyQt6.QtGui', 
    'PyQt6.QtWidgets',
    'json',
    'pathlib',
    'shutil',
    'random',
    'XXX'  # 添加缺少的模組
]
```

### 常見問題2: exe文件太大
**解決方案**:
1. 使用 `--exclude-module` 排除不需要的套件
2. 使用 UPX 壓縮（已在 spec 中啟用）
3. 考慮使用 `--onedir` 模式而非 `--onefile`

### 常見問題3: 運行時錯誤
**解決方案**:
1. 暫時設定 `console=True` 查看錯誤信息
2. 檢查路徑問題（使用絕對路徑）
3. 確保所有資源文件都包含在打包中

### 常見問題4: PyQt6相關錯誤
**解決方案**:
```bash
# 重新安裝PyQt6
pip uninstall PyQt6
pip install PyQt6

# 或嘗試不同版本
pip install PyQt6==6.5.0
```

## 📋 完整依賴列表

```txt
PyQt6>=6.0.0
pyinstaller>=5.0.0
pathlib2>=2.3.0
Pillow>=8.0.0
```

## 🎯 打包命令詳解

### 基本命令
```bash
pyinstaller image_sample_extractor.py
```

### 進階命令
```bash
pyinstaller \
    --onefile \                    # 打包成單一exe文件
    --windowed \                   # 隱藏控制台窗口
    --name="圖像樣本抽取工具" \     # 自定義文件名
    --icon=icon.ico \              # 添加圖標
    --clean \                      # 清理暫存文件
    --noconfirm \                  # 不確認覆蓋
    image_sample_extractor.py
```

## 🚦 測試打包結果

1. **功能測試**: 在目標機器上測試所有功能
2. **路徑測試**: 確保文件選擇和保存正常
3. **權限測試**: 確保有文件讀寫權限
4. **相容性測試**: 在不同Windows版本上測試

## 📄 授權和分發

- EXE文件可獨立運行，無需安裝Python
- 建議包含使用說明文檔
- 檔案大小約 50-100MB
- 支援 Windows 7/10/11

## 🔧 開發者選項

### 調試模式
設定 `debug=True` 獲得詳細錯誤信息

### 保留控制台
設定 `console=True` 顯示調試輸出

### 分離模式
使用 `--onedir` 創建資料夾版本（啟動較快，但檔案較多）