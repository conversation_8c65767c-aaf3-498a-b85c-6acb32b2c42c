"""
🏗️ 核心推理系統模組
提供統一的推理接口和基礎設施
"""

from .base_inference import (
    BaseInference,
    StandardResult, 
    Detection,
    ConfusionMetrics,
    TimingInfo,
    InferenceStatus,
    InferenceError,
    ModelLoadError,
    InferenceProcessError,
    ConfigurationError
)

from .inference_engine import UnifiedInferenceEngine
from .model_adapter import ModelAdapter

__all__ = [
    "BaseInference",
    "StandardResult",
    "Detection", 
    "ConfusionMetrics",
    "TimingInfo",
    "InferenceStatus",
    "InferenceError",
    "ModelLoadError", 
    "InferenceProcessError",
    "ConfigurationError",
    "UnifiedInferenceEngine",
    "ModelAdapter"
]