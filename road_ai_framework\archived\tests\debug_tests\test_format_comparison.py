#!/usr/bin/env python3
"""
🧪 測試格式對比功能
驗證修改後的三視圖格式是否與之前版本匹配
"""

import os
import sys
from pathlib import Path

# 使用統一導入管理
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.import_helper import setup_project_paths
setup_project_paths()

def test_format_comparison():
    """測試格式對比"""
    print("🧪 測試格式對比")
    print("=" * 70)
    print("🎨 驗證GT與pred顏色一致性")
    print("📊 驗證P、R、F1、TP/FP/FN統計顯示")
    print("=" * 70)
    
    # 測試用配置
    test_config = {
        'input_path': '/mnt/d/image/5_test_image/test_2_org/BMP-2291_20241127_092702221.jpg',
        'output_path': '/mnt/d/image/5_test_image/test_2_out_format_test',
        'labelme_dir': '/mnt/d/image/5_test_image/test_2_org',
        'model_path': '/mnt/d/4_road_crack/best.pt'
    }
    
    # 檢查必要文件
    print("🔍 檢查測試文件...")
    for key, path in test_config.items():
        if not Path(path).exists() and key in ['input_path', 'model_path']:
            print(f"❌ 缺少必要文件: {key} -> {path}")
            print("💡 請修改test_config中的路徑指向您的實際文件")
            return False
    
    print("✅ 測試配置檢查通過")
    
    try:
        # 創建配置管理器
        print("\\n🔧 創建格式對比配置...")
        from models.inference.config_manager import UnifiedYOLOConfigManager
        
        config_manager = UnifiedYOLOConfigManager()
        config_manager.model.segmentation_model_path = test_config['model_path']
        config_manager.model.device = "cuda"
        config_manager.paths.labelme_dir = test_config['labelme_dir']
        
        # 設置類別配置（與之前一致）
        config_manager.classes.clear()
        class_configs = {
            0: ["expansion_joint", "expansion_joint", [255, 0, 0], 0.1, 0.15, True],
            1: ["joint", "joint", [0, 255, 0], 0.25, 0.1, True],
            2: ["linear_crack", "linear_crack", [0, 0, 255], 0.1, 0.08, True],
            3: ["Alligator_crack", "Alligator_crack", [255, 255, 0], 0.1, 0.15, True],
            4: ["potholes", "potholes", [255, 0, 255], 0.1, 0.2, True],
        }
        
        for class_id, (name, display_name, color, conf, sahi_conf, enabled) in class_configs.items():
            config_manager.add_class_config(
                class_id=class_id,
                name=name,
                display_name=display_name,
                color=color,
                confidence=conf,
                sahi_confidence=sahi_conf,
                enabled=enabled
            )
        
        # 創建推理引擎
        print("🚀 初始化格式對比推理引擎...")
        from models.inference.unified_yolo_inference import UnifiedYOLOInference
        
        inference = UnifiedYOLOInference()
        inference.config_manager = config_manager
        inference._load_model()
        
        # 重新初始化組件
        from models.inference.unified_yolo_inference import FontManager, ThreeViewGenerator, IntelligentFilter, SAHIEnhanced
        
        inference.font_manager = FontManager(
            font_size=1.0,
            font_thickness=2,
            font_scale=1.0
        )
        
        # 使用修改後的ThreeViewGenerator
        inference.three_view_generator = ThreeViewGenerator(
            font_manager=inference.font_manager,
            config_manager=config_manager,
            layout="horizontal",
            spacing=10
        )
        
        inference.intelligent_filter = IntelligentFilter(config_manager)
        inference.sahi_enhanced = SAHIEnhanced(config_manager)
        
        print("✅ 格式對比組件初始化完成")
        
        # 創建輸出目錄
        output_path = Path(test_config['output_path'])
        output_path.mkdir(parents=True, exist_ok=True)
        (output_path / "images").mkdir(exist_ok=True)
        (output_path / "reports").mkdir(exist_ok=True)
        
        # 執行格式對比推理
        print(f"\\n🎯 執行格式對比推理...")
        print(f"📁 測試圖像: {test_config['input_path']}")
        print(f"📁 GT目錄: {test_config['labelme_dir']}")
        print(f"📁 輸出目錄: {test_config['output_path']}")
        
        result = inference.predict_single_image(
            test_config['input_path'], 
            test_config['output_path']
        )
        
        # 驗證格式對比功能
        print(f"\\n✅ 格式對比推理完成! 檢測到 {len(result['detections'])} 個目標")
        
        # 檢查生成的文件
        image_name = Path(test_config['input_path']).stem
        images_dir = output_path / "images"
        
        # 檢查三視圖 (格式對比版本)
        three_view_file = images_dir / f"{image_name}_three_view.jpg"
        format_test = three_view_file.exists()
        
        # 顯示格式對比測試結果
        print(f"\\n🧪 格式對比測試結果:")
        print(f"  🎨 GT顏色一致性: {'✅ 通過' if format_test else '❌ 失敗'}")
        print(f"  📊 P/R/F1統計顯示: {'✅ 通過' if format_test else '❌ 失敗'}")
        print(f"  🖼️ 三視圖生成: {'✅ 通過' if format_test else '❌ 失敗'}")
        
        # 顯示生成的文件
        print(f"\\n📁 格式對比生成的文件:")
        if three_view_file.exists():
            print(f"  🖼️ 格式對比三視圖: {three_view_file}")
            print(f"  💡 請與參考圖像對比，確認格式是否一致")
        
        # 檢測結果詳情
        if result['detections']:
            print(f"\\n🎯 檢測結果詳情:")
            for i, det in enumerate(result['detections']):
                class_name = det['class_name']
                confidence = det['confidence']
                print(f"  {i+1}. {class_name}: {confidence:.3f}")
        
        print(f"\\n🎉 格式對比測試完成!")
        print(f"💡 新版本特點:")
        print(f"  - GT與pred使用相同的類別顏色")
        print(f"  - 標題包含P、R、F1、TP/FP/FN完整統計")
        print(f"  - 支持多行標題顯示")
        print(f"  - 保持與之前版本相同的佈局風格")
        
        return True
        
    except Exception as e:
        print(f"\\n💥 格式對比測試失敗: {e}")
        import traceback
        traceback.print_exc()
        
        print(f"\\n🛠️ 故障排除建議:")
        print(f"  1. 確認模型路徑和測試圖像路徑正確")
        print(f"  2. 檢查GT標註文件是否存在")
        print(f"  3. 確認CUDA環境配置正確")
        
        return False


def main():
    """主函數"""
    print("🚀 格式對比測試套件")
    print("驗證修改後的三視圖格式是否與之前版本匹配")
    print("=" * 70)
    
    # 執行格式對比測試
    success = test_format_comparison()
    
    if success:
        print("\\n✅ 格式對比測試通過!")
        print("\\n📋 格式對比功能說明:")
        print("  🎨 GT顏色一致性: GT標註現在使用與預測類別相同的顏色")
        print("  📊 完整統計顯示: 標題包含P、R、F1、TP/FP/FN等所有統計信息")
        print("  🖼️ 多行標題支持: 自動調整標題高度以顯示多行統計信息")
        print("  📐 佈局一致性: 保持與之前版本相同的水平佈局風格")
    else:
        print("\\n❌ 格式對比測試失敗，請檢查配置和環境")
    
    print(f"\\n💡 對比要點:")
    print(f"  1. 標題是否包含P、R、F1統計")
    print(f"  2. GT與pred顏色是否一致")
    print(f"  3. 佈局是否與參考圖像匹配")
    
    return success


if __name__ == "__main__":
    main()