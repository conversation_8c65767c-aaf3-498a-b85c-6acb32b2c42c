#!/usr/bin/env python3
"""
📂 GT標註載入器
載入和處理Ground Truth標註，支援多種格式
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
import cv2
import numpy as np

from ..config import UnifiedConfig


class GroundTruthLoader:
    """
    Ground Truth標註載入器
    
    支援格式：
    - LabelMe JSON
    - YOLO TXT
    - COCO JSON
    """
    
    def __init__(self, config: UnifiedConfig):
        """
        初始化GT載入器
        
        Args:
            config: 統一配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._gt_cache = {}  # GT緩存
    
    def load_gt_for_image(self, image_path: str) -> Optional[List[Dict[str, Any]]]:
        """
        為指定圖像載入GT標註
        
        Args:
            image_path: 圖像文件路徑
            
        Returns:
            List[Dict]: GT標註列表，None表示未找到
        """
        try:
            image_path = Path(image_path)
            
            # 檢查緩存
            cache_key = str(image_path)
            if cache_key in self._gt_cache:
                return self._gt_cache[cache_key]
            
            # 嘗試不同的GT文件格式
            gt_annotations = None
            
            # 1. 嘗試LabelMe JSON
            labelme_path = image_path.parent / f"{image_path.stem}.json"
            if labelme_path.exists():
                gt_annotations = self._load_labelme_json(labelme_path, image_path)
            
            # 2. 嘗試YOLO TXT
            if gt_annotations is None:
                yolo_path = image_path.parent / f"{image_path.stem}.txt"
                if yolo_path.exists():
                    gt_annotations = self._load_yolo_txt(yolo_path, image_path)
            
            # 3. 嘗試共享標註目錄
            if gt_annotations is None:
                gt_annotations = self._search_shared_annotations(image_path)
            
            # 緩存結果
            self._gt_cache[cache_key] = gt_annotations
            
            if gt_annotations:
                self.logger.debug(f"📂 載入GT標註: {image_path.name}, {len(gt_annotations)}個標註")
            
            return gt_annotations
            
        except Exception as e:
            self.logger.warning(f"⚠️ GT標註載入失敗 {image_path}: {str(e)}")
            return None
    
    def _load_labelme_json(self, json_path: Path, image_path: Path) -> Optional[List[Dict[str, Any]]]:
        """載入LabelMe JSON標註"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                labelme_data = json.load(f)
            
            annotations = []
            image_height, image_width = self._get_image_dimensions(image_path)
            
            for shape in labelme_data.get('shapes', []):
                try:
                    label = shape['label']
                    shape_type = shape['shape_type']
                    points = shape['points']
                    
                    # 找到對應的類別ID
                    class_id = self._find_class_id_by_name(label)
                    if class_id is None:
                        continue
                    
                    if shape_type == 'rectangle' and len(points) >= 2:
                        # 矩形標註
                        x1, y1 = points[0]
                        x2, y2 = points[1]
                        
                        # 確保座標順序正確
                        x1, x2 = min(x1, x2), max(x1, x2)
                        y1, y2 = min(y1, y2), max(y1, y2)
                        
                        bbox = [x1, y1, x2, y2]
                        area = (x2 - x1) * (y2 - y1)
                        
                        annotations.append({
                            'bbox': bbox,
                            'class_id': class_id,
                            'class_name': label,
                            'area': area,
                            'shape_type': 'rectangle'
                        })
                    
                    elif shape_type == 'polygon' and len(points) >= 3:
                        # 多邊形標註
                        polygon_points = np.array(points, dtype=np.int32)
                        
                        # 計算邊界框
                        x1, y1 = polygon_points.min(axis=0)
                        x2, y2 = polygon_points.max(axis=0)
                        bbox = [x1, y1, x2, y2]
                        
                        # 計算多邊形面積
                        area = cv2.contourArea(polygon_points)
                        
                        annotations.append({
                            'bbox': bbox,
                            'class_id': class_id,
                            'class_name': label,
                            'area': area,
                            'shape_type': 'polygon',
                            'polygon_points': polygon_points.tolist()
                        })
                        
                except Exception as e:
                    self.logger.warning(f"⚠️ 標註解析失敗: {str(e)}")
                    continue
            
            return annotations if annotations else None
            
        except Exception as e:
            self.logger.warning(f"⚠️ LabelMe JSON載入失敗: {str(e)}")
            return None
    
    def _load_yolo_txt(self, txt_path: Path, image_path: Path) -> Optional[List[Dict[str, Any]]]:
        """載入YOLO TXT標註"""
        try:
            image_height, image_width = self._get_image_dimensions(image_path)
            
            annotations = []
            with open(txt_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    parts = line.split()
                    if len(parts) < 5:
                        continue
                    
                    class_id = int(parts[0])
                    cx, cy, w, h = map(float, parts[1:5])
                    
                    # 轉換為絕對座標
                    x1 = (cx - w/2) * image_width
                    y1 = (cy - h/2) * image_height
                    x2 = (cx + w/2) * image_width
                    y2 = (cy + h/2) * image_height
                    
                    bbox = [x1, y1, x2, y2]
                    area = w * h * image_width * image_height
                    
                    # 獲取類別名稱
                    class_name = self._get_class_name(class_id)
                    
                    annotations.append({
                        'bbox': bbox,
                        'class_id': class_id,
                        'class_name': class_name,
                        'area': area,
                        'shape_type': 'rectangle'
                    })
            
            return annotations if annotations else None
            
        except Exception as e:
            self.logger.warning(f"⚠️ YOLO TXT載入失敗: {str(e)}")
            return None
    
    def _search_shared_annotations(self, image_path: Path) -> Optional[List[Dict[str, Any]]]:
        """在共享標註目錄中搜索"""
        # 實現共享標註目錄搜索邏輯
        # 這裡暫時返回None，可根據具體需求實現
        return None
    
    def _get_image_dimensions(self, image_path: Path) -> tuple[int, int]:
        """獲取圖像尺寸"""
        try:
            image = cv2.imread(str(image_path))
            if image is None:
                raise ValueError(f"無法載入圖像: {image_path}")
            return image.shape[:2]  # (height, width)
        except Exception as e:
            self.logger.warning(f"⚠️ 無法獲取圖像尺寸: {str(e)}")
            return (640, 640)  # 默認尺寸
    
    def _find_class_id_by_name(self, class_name: str) -> Optional[int]:
        """根據類別名稱查找類別ID"""
        for class_id, class_config in self.config.classes.items():
            if class_config.name == class_name or class_config.display_name == class_name:
                return class_id
        return None
    
    def _get_class_name(self, class_id: int) -> str:
        """根據類別ID獲取類別名稱"""
        class_config = self.config.get_class_config(class_id)
        return class_config.name if class_config else f"class_{class_id}"
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """獲取緩存統計信息"""
        return {
            'cached_files': len(self._gt_cache),
            'cache_hit_ratio': 0.0,  # 可以實現命中率統計
            'total_annotations': sum(
                len(annotations) for annotations in self._gt_cache.values()
                if annotations is not None
            )
        }
    
    def clear_cache(self):
        """清空GT緩存"""
        self._gt_cache.clear()
        self.logger.debug("📂 GT緩存已清空")
    
    def cleanup(self):
        """清理GT載入器資源"""
        self.logger.debug("🧹 清理GT載入器資源")
        self.clear_cache()