import cv2
import numpy as np
import albumentations as A
from pathlib import Path

# ---------- 設定 ----------
input_img_path = r"tttest\20250510140302018-79c30a19f8cbc64de533140bdbd3b632.jpg"  # ← 替換成你的圖片路徑
output_dir = Path("aug_shear_perspective_outputs")
output_dir.mkdir(exist_ok=True)

# ---------- 讀取圖像 ----------
img = cv2.imread(input_img_path)
assert img is not None, f"無法讀取圖片：{input_img_path}"
img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

# ---------- 測試 shear 多組參數 ----------
shear_values = [-50, -30, -15, 0, 15, 30, 50]
for shear in shear_values:
    aug = A.Affine(shear=shear, p=1.0)
    augmented = aug(image=img)["image"]
    save_path = output_dir / f"shear_{shear}.jpg"
    cv2.imwrite(str(save_path), cv2.cvtColor(augmented, cv2.COLOR_RGB2BGR))
    print(f"已儲存：{save_path}")

# ---------- 測試 perspective 多組參數 ----------
perspective_scales = [0.01, 0.03, 0.05, 0.1, 0.2 ,0.5]
for scale in perspective_scales:
    aug = A.Perspective(scale=(scale, scale), p=1.0)
    augmented = aug(image=img)["image"]
    save_path = output_dir / f"perspective_{scale:.2f}.jpg"
    cv2.imwrite(str(save_path), cv2.cvtColor(augmented, cv2.COLOR_RGB2BGR))
    print(f"已儲存：{save_path}")
