#!/usr/bin/env python3
"""
測試配置管理器修復
驗證 VisualizationConfig 是否包含所需的屬性
"""

import sys
from pathlib import Path

# 添加路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from models.inference.config_manager import UnifiedYOLOConfigManager, VisualizationConfig
    
    print("🔧 測試VisualizationConfig屬性:")
    
    # 創建配置實例
    viz_config = VisualizationConfig()
    
    # 檢查必要屬性
    required_attrs = [
        'enable_roi_preview',
        'force_single_image_roi_preview',
        'save_visualizations',
        'enable_three_view_output',
        'font_size',
        'font_thickness'
    ]
    
    print("✅ 屬性檢查結果:")
    for attr in required_attrs:
        if hasattr(viz_config, attr):
            value = getattr(viz_config, attr)
            print(f"   ✅ {attr} = {value}")
        else:
            print(f"   ❌ 缺少屬性: {attr}")
    
    # 測試配置管理器創建
    try:
        config_manager = UnifiedYOLOConfigManager(config_path=None)
        print(f"\n🎯 配置管理器創建成功!")
        print(f"   enable_roi_preview: {config_manager.visualization.enable_roi_preview}")
        print(f"   force_single_image_roi_preview: {config_manager.visualization.force_single_image_roi_preview}")
        
    except Exception as e:
        print(f"\n❌ 配置管理器創建失敗: {e}")
    
    print("\n✅ 測試完成 - 配置修復成功!")
    
except ImportError as e:
    print(f"❌ 導入錯誤: {e}")
except Exception as e:
    print(f"❌ 測試失敗: {e}")