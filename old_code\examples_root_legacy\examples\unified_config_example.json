{"model_config": {"detection_model_path": "models/yolo12_detection.pt", "segmentation_model_path": "models/yolo11_segmentation.pt", "secondary_model_path": null, "device": "auto", "enable_consensus": true, "consensus_threshold": 0.3, "img_size": 640, "global_conf": 0.25, "iou_threshold": 0.45, "max_det": 1000, "class_thresholds": {"0": 0.3, "1": 0.4, "2": 0.5}, "enable_class_filtering": true, "enable_detection_merge": true, "iou_merge_threshold": 0.3}, "sahi_config": {"enable_sahi": false, "slice_height": 512, "slice_width": 512, "overlap_height_ratio": 0.2, "overlap_width_ratio": 0.2, "auto_slice_resolution": true, "perform_standard_pred": true, "roi_ratio": [0.0, 0.0, 1.0, 1.0], "postprocess_type": "GREEDYNMM", "postprocess_match_threshold": 0.1, "postprocess_class_agnostic": false, "exclude_classes_by_name": [], "exclude_classes_by_id": [], "no_standard_prediction": false, "no_sliced_prediction": false, "export_pickle": false, "export_crop": false}, "processing_config": {"output_format": "both", "save_visualizations": true, "save_predictions": true, "save_statistics": true, "visualization_mode": "combined", "visualization_size": [1200, 900], "show_masks": true, "mask_alpha": 0.35, "target_classes": null, "skip_empty": true, "include_base64": true, "batch_size": 10, "smart_label_counting": true}, "annotation_config": {"labelme_dir": "", "auto_generate_classes": true, "auto_convert_annotations": true, "enable_gt_comparison": true, "gt_annotation_format": "auto", "class_names": {"0": "背景", "1": "裂縫", "2": "坑洞", "3": "龜裂", "4": "人孔蓋", "5": "補綻"}}}