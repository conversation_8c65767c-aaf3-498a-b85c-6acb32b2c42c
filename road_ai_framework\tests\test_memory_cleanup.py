#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
記憶體清理功能測試腳本
測試記憶體清理器的各項功能是否正常工作
"""

import sys
import time
import logging
from pathlib import Path

# 確保導入路徑正確
current_dir = Path(__file__).parent  # tests/
project_root = current_dir.parent    # road_ai_framework/
sys.path.insert(0, str(project_root))

def test_memory_cleaner():
    """測試記憶體清理器基本功能"""
    print("TEST: 開始測試記憶體清理器")
    
    try:
        # 導入記憶體清理模組
        from inference_system.performance import (
            MemoryCleaner, MemoryCleanupConfig, MemoryType, CleanupTrigger, create_memory_cleaner
        )
        print("[OK] 記憶體清理模組導入成功")
        
    except ImportError as e:
        print(f"[ERROR] 記憶體清理模組導入失敗: {e}")
        return False

    # 設置日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建測試配置
    test_config = MemoryCleanupConfig(
        enabled=True,
        trigger_mode=CleanupTrigger.MANUAL,
        cleanup_interval_seconds=10.0,
        cleanup_after_n_images=5,
        cpu_memory_threshold=80.0,
        gpu_memory_threshold=80.0,
        enable_torch_cleanup=True,
        enable_opencv_cleanup=True,
        enable_python_gc=True,
        enable_cuda_cleanup=True,
        enable_memory_monitoring=False,  # 關閉監控避免干擾測試
        log_memory_usage=True
    )
    
    print("📋 測試配置創建成功")
    
    # 創建記憶體清理器
    try:
        memory_cleaner = create_memory_cleaner(test_config)
        print("✅ 記憶體清理器創建成功")
    except Exception as e:
        print(f"❌ 記憶體清理器創建失敗: {e}")
        return False
    
    # 測試 1: 獲取記憶體使用情況
    print("\n🔍 測試 1: 獲取記憶體使用情況")
    try:
        memory_info = memory_cleaner.get_memory_usage()
        print(f"   CPU記憶體使用率: {memory_info['cpu_memory']['usage_percent']:.1f}%")
        print(f"   進程記憶體使用: {memory_info['process_memory']['rss_mb']:.1f} MB")
        
        if 'gpu_memory' in memory_info and memory_info['gpu_memory']:
            for gpu_id, gpu_info in memory_info['gpu_memory'].items():
                print(f"   {gpu_id}記憶體使用率: {gpu_info['usage_percent']:.1f}%")
        else:
            print("   無GPU記憶體信息")
            
        print("✅ 測試 1 通過")
    except Exception as e:
        print(f"❌ 測試 1 失敗: {e}")
        return False
    
    # 測試 2: 手動記憶體清理
    print("\n🧹 測試 2: 手動記憶體清理")
    try:
        cleanup_result = memory_cleaner.cleanup_memory(MemoryType.ALL, "manual_test")
        
        if cleanup_result.get("success"):
            effect = cleanup_result.get("effect", {})
            cpu_freed = effect.get("cpu_memory_freed_mb", 0)
            gpu_freed = effect.get("gpu_memory_freed_mb", 0)
            total_freed = effect.get("total_memory_freed_mb", 0)
            cleanup_time = cleanup_result.get("cleanup_time", 0)
            
            print(f"   CPU記憶體釋放: {cpu_freed:.1f} MB")
            print(f"   GPU記憶體釋放: {gpu_freed:.1f} MB") 
            print(f"   總計釋放: {total_freed:.1f} MB")
            print(f"   清理耗時: {cleanup_time:.3f} 秒")
            print(f"   執行動作: {len(cleanup_result.get('actions_performed', []))} 項")
            
            for action in cleanup_result.get('actions_performed', []):
                print(f"     - {action}")
                
            print("✅ 測試 2 通過")
        else:
            print(f"❌ 測試 2 失敗: {cleanup_result.get('error', '未知錯誤')}")
            return False
            
    except Exception as e:
        print(f"❌ 測試 2 失敗: {e}")
        return False
    
    # 測試 3: 圖像處理回調
    print("\n📸 測試 3: 圖像處理回調測試")
    try:
        # 模擬處理多張圖像
        for i in range(test_config.cleanup_after_n_images + 1):
            memory_cleaner.on_image_processed()
            if i == test_config.cleanup_after_n_images - 1:
                print(f"   已處理 {i+1} 張圖像")
        
        print("✅ 測試 3 通過")
    except Exception as e:
        print(f"❌ 測試 3 失敗: {e}")
        return False
    
    # 測試 4: 批次處理回調
    print("\n📦 測試 4: 批次處理回調測試")
    try:
        memory_cleaner.on_batch_start()
        print("   批次開始回調執行完成")
        
        memory_cleaner.on_batch_end()
        print("   批次結束回調執行完成")
        
        print("✅ 測試 4 通過")
    except Exception as e:
        print(f"❌ 測試 4 失敗: {e}")
        return False
    
    # 測試 5: 獲取統計信息
    print("\n📊 測試 5: 統計信息測試")
    try:
        stats = memory_cleaner.get_cleanup_stats()
        print(f"   總清理次數: {stats['total_cleanups']}")
        print(f"   總釋放記憶體: {stats['memory_freed_mb']:.1f} MB")
        print(f"   GPU釋放記憶體: {stats['gpu_memory_freed_mb']:.1f} MB")
        print(f"   手動清理次數: {stats['manual_cleanups']}")
        print(f"   自動清理次數: {stats['automatic_cleanups']}")
        
        print("✅ 測試 5 通過")
    except Exception as e:
        print(f"❌ 測試 5 失敗: {e}")
        return False
    
    # 清理資源
    try:
        memory_cleaner.cleanup()
        print("\n🧹 記憶體清理器資源清理完成")
    except Exception as e:
        print(f"⚠️ 資源清理警告: {e}")
    
    print("\n🎉 所有測試通過！記憶體清理功能正常運行")
    return True


def test_integration():
    """測試與主系統的集成"""
    print("\n🔗 測試與主系統集成")
    
    try:
        # 測試配置參數
        memory_cleanup_trigger = "hybrid"
        memory_cleanup_interval = 30.0
        memory_cleanup_after_images = 10
        memory_cpu_threshold = 85.0
        memory_gpu_threshold = 85.0
        
        # 導入必要模組
        from inference_system.performance import CleanupTrigger, MemoryCleanupConfig
        
        # 測試字符串到枚舉的轉換
        trigger_mapping = {
            "time_based": CleanupTrigger.TIME_BASED,
            "usage_based": CleanupTrigger.USAGE_BASED,
            "count_based": CleanupTrigger.COUNT_BASED,
            "hybrid": CleanupTrigger.HYBRID,
            "manual": CleanupTrigger.MANUAL
        }
        
        trigger_enum = trigger_mapping.get(memory_cleanup_trigger, CleanupTrigger.HYBRID)
        print(f"   觸發模式轉換: '{memory_cleanup_trigger}' -> {trigger_enum}")
        
        # 創建配置對象
        config = MemoryCleanupConfig(
            enabled=True,
            trigger_mode=trigger_enum,
            cleanup_interval_seconds=memory_cleanup_interval,
            cleanup_after_n_images=memory_cleanup_after_images,
            cpu_memory_threshold=memory_cpu_threshold,
            gpu_memory_threshold=memory_gpu_threshold
        )
        
        print(f"   配置對象創建成功: 觸發模式={config.trigger_mode.value}")
        print("✅ 集成測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 集成測試失敗: {e}")
        return False


if __name__ == "__main__":
    print("🚀 開始記憶體清理功能完整測試")
    print("=" * 60)
    
    # 基本功能測試
    basic_test_passed = test_memory_cleaner()
    
    # 集成測試
    integration_test_passed = test_integration()
    
    print("=" * 60)
    if basic_test_passed and integration_test_passed:
        print("🎊 所有測試通過！記憶體清理系統準備就緒")
        sys.exit(0)
    else:
        print("❌ 某些測試失敗，請檢查錯誤信息")
        sys.exit(1)