#!/usr/bin/env python3
"""
🧠 記憶體暫存清理管理器
定期清理GPU和CPU記憶體暫存，防止記憶體洩漏和OOM
支援PyTorch、TensorFlow、OpenCV等框架的記憶體管理
"""

import gc
import logging
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import threading

# 🛡️ 優雅處理psutil依賴 - 支援在沒有psutil的環境中運行
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None
    
# 🛡️ 優雅處理PyTorch依賴
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None
    
# 🛡️ 優雅處理OpenCV依賴
try:
    import cv2
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False
    cv2 = None


class MemoryType(Enum):
    """記憶體類型枚舉"""
    CPU = "cpu"
    GPU = "gpu"
    SHARED = "shared"
    ALL = "all"


class CleanupTrigger(Enum):
    """清理觸發條件"""
    TIME_BASED = "time_based"       # 基於時間間隔
    USAGE_BASED = "usage_based"     # 基於使用率
    COUNT_BASED = "count_based"     # 基於處理圖像數量
    MANUAL = "manual"               # 手動觸發
    HYBRID = "hybrid"               # 混合模式
    AGGRESSIVE = "aggressive"       # 🔥 激進清理模式


@dataclass
class MemoryCleanupConfig:
    """記憶體清理配置"""
    enabled: bool = True                                # 是否啟用記憶體清理
    
    # 🔥 激進模式設定
    aggressive_mode: bool = False                       # 啟用激進清理模式
    
    # 觸發設定
    trigger_mode: CleanupTrigger = CleanupTrigger.HYBRID  # 觸發模式
    
    # 時間觸發設定
    cleanup_interval_seconds: float = 30.0             # 清理間隔(秒) - 激進模式: 10秒
    cleanup_on_batch_start: bool = True                # 批次開始時清理
    cleanup_on_batch_end: bool = True                  # 批次結束時清理
    cleanup_after_n_images: int = 10                   # 每N張圖像後清理 - 激進模式: 3張
    
    # 🆕 新增清理時機
    cleanup_on_image_start: bool = False               # 每張圖像開始前清理 (激進模式)
    cleanup_on_image_end: bool = False                 # 每張圖像結束後清理 (激進模式)
    cleanup_on_model_load: bool = True                 # 模型載入後清理
    cleanup_on_inference_error: bool = True            # 推理錯誤時清理
    
    # 使用率觸發設定
    cpu_memory_threshold: float = 85.0                 # CPU記憶體使用率閾值(%) - 激進模式: 60%
    gpu_memory_threshold: float = 85.0                 # GPU記憶體使用率閾值(%) - 激進模式: 60%
    
    # 🚨 緊急清理設定
    emergency_threshold: float = 90.0                  # 緊急清理閾值(%)
    enable_emergency_cleanup: bool = True              # 啟用緊急清理
    emergency_force_gc: bool = True                    # 緊急情況強制垃圾回收
    emergency_clear_cache: bool = True                 # 緊急情況清空所有緩存
    
    # 清理範圍設定
    target_memory_types: List[MemoryType] = field(default_factory=lambda: [
        MemoryType.GPU, MemoryType.CPU
    ])
    
    # 清理策略設定
    enable_torch_cleanup: bool = True                   # PyTorch記憶體清理
    enable_opencv_cleanup: bool = True                  # OpenCV記憶體清理
    enable_python_gc: bool = True                       # Python垃圾回收
    enable_cuda_cleanup: bool = True                    # CUDA記憶體清理
    enable_shared_memory_cleanup: bool = True           # 共享記憶體清理
    enable_cache_cleanup: bool = True                   # 緩存清理
    
    # 🔧 進階清理選項
    enable_tensor_cleanup: bool = False                 # 張量清理 (激進模式)
    enable_gradient_cleanup: bool = False               # 梯度清理 (激進模式)  
    enable_model_cleanup: bool = False                  # 模型清理 (謹慎使用)
    force_sync_cleanup: bool = False                    # 強制同步清理 (激進模式)
    
    # 安全設定
    max_cleanup_time: float = 5.0                      # 最大清理時間(秒)
    aggressive_cleanup: bool = False                    # 激進清理模式 (向後兼容)
    preserve_model_cache: bool = True                   # 保留模型緩存
    
    # 📊 監控與診斷設定
    enable_memory_monitoring: bool = True              # 啟用記憶體監控
    log_memory_usage: bool = True                       # 記錄記憶體使用
    memory_warning_threshold: float = 90.0             # 記憶體警告閾值(%)
    enable_leak_detection: bool = False                # 記憶體洩漏檢測 (激進模式)
    enable_detailed_logging: bool = False              # 詳細日誌 (激進模式)


class MemoryCleaner:
    """
    記憶體暫存清理管理器
    
    功能特色:
    - 智能記憶體清理策略
    - GPU/CPU記憶體監控
    - 多框架支援
    - 自動和手動觸發
    - 詳細清理統計
    """
    
    def __init__(self, config: MemoryCleanupConfig):
        """
        初始化記憶體清理器
        
        Args:
            config: 記憶體清理配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 統計信息
        self.cleanup_stats = {
            'total_cleanups': 0,
            'total_time_saved': 0.0,
            'memory_freed_mb': 0.0,
            'gpu_memory_freed_mb': 0.0,
            'last_cleanup_time': 0.0,
            'images_processed_since_cleanup': 0,
            'automatic_cleanups': 0,
            'manual_cleanups': 0,
            'emergency_cleanups': 0,          # 🚨 緊急清理次數
            'tensor_cleanups': 0,             # 🔎 張量清理次數
            'leak_detections': 0,             # 🔍 洩漏檢測次數
            'failed_cleanups': 0              # ❗ 失敗清理次數
        }
        
        # 記憶體監控
        self.memory_history = []
        self.max_history_size = 200 if self.config.aggressive_mode else 100
        
        # 🔍 洩漏檢測
        self.leak_detection_baseline = None
        self.leak_detection_samples = []
        self.consecutive_increases = 0
        
        # 框架可用性檢測
        self.torch_available = TORCH_AVAILABLE and self._check_torch_availability()
        self.opencv_available = OPENCV_AVAILABLE and self._check_opencv_availability()
        self.cuda_available = self.torch_available and self._check_cuda_availability()
        
        # 背景監控線程
        self.monitoring_thread = None
        self.stop_monitoring = threading.Event()
        
        if self.config.enabled:
            mode_indicator = "🔥 激進模式" if self.config.aggressive_mode else "🛡️ 標準模式"
            self.logger.info(f"🧠 記憶體清理器初始化完成 - {mode_indicator}")
            self.logger.info(f"   觸發模式: {self.config.trigger_mode.value}")
            self.logger.info(f"   PyTorch: {'可用' if self.torch_available else '不可用'}")
            self.logger.info(f"   CUDA: {'可用' if self.cuda_available else '不可用'}")
            self.logger.info(f"   OpenCV: {'可用' if self.opencv_available else '不可用'}")
            
            if self.config.aggressive_mode:
                self.logger.info(f"   🔥 激進功能: 張量清理={self.config.enable_tensor_cleanup}, 洩漏檢測={self.config.enable_leak_detection}")
                self.logger.info(f"   🔥 清理間隔: {self.config.cleanup_interval_seconds}秒, 每{self.config.cleanup_after_n_images}張圖像")
            
            if self.config.enable_memory_monitoring:
                self._start_memory_monitoring()
    
    def _check_torch_availability(self) -> bool:
        """檢查PyTorch可用性"""
        if not TORCH_AVAILABLE:
            return False
        return True
    
    def _check_opencv_availability(self) -> bool:
        """檢查OpenCV可用性"""
        if not OPENCV_AVAILABLE:
            return False
        return True
    
    def _check_cuda_availability(self) -> bool:
        """檢查CUDA可用性"""
        if not TORCH_AVAILABLE:
            return False
        try:
            return torch.cuda.is_available()
        except:
            return False
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """
        獲取當前記憶體使用情況
        
        Returns:
            Dict: 記憶體使用統計
        """
        memory_info = {
            'timestamp': time.time(),
            'cpu_memory': {},
            'gpu_memory': {},
            'process_memory': {}
        }
        
        try:
            # CPU記憶體 - 僅在psutil可用時
            if PSUTIL_AVAILABLE:
                cpu_memory = psutil.virtual_memory()
                memory_info['cpu_memory'] = {
                    'total_mb': cpu_memory.total / 1024 / 1024,
                    'available_mb': cpu_memory.available / 1024 / 1024,
                    'used_mb': cpu_memory.used / 1024 / 1024,
                    'usage_percent': cpu_memory.percent
                }
                
                # 進程記憶體
                process = psutil.Process()
                process_memory = process.memory_info()
                memory_info['process_memory'] = {
                    'rss_mb': process_memory.rss / 1024 / 1024,
                    'vms_mb': process_memory.vms / 1024 / 1024,
                    'cpu_percent': process.cpu_percent()
                }
            else:
                # 降級處理：使用基礎記憶體信息
                memory_info['cpu_memory'] = {
                    'total_mb': 0,
                    'available_mb': 0,
                    'used_mb': 0,
                    'usage_percent': 0,
                    'note': 'psutil not available - basic monitoring only'
                }
                memory_info['process_memory'] = {
                    'rss_mb': 0,
                    'vms_mb': 0,
                    'cpu_percent': 0,
                    'note': 'psutil not available - process info unavailable'
                }
            
            # GPU記憶體 (如果可用)
            if self.cuda_available and TORCH_AVAILABLE:
                gpu_count = torch.cuda.device_count()
                gpu_info = {}
                
                for i in range(gpu_count):
                    try:
                        total_memory = torch.cuda.get_device_properties(i).total_memory
                        allocated_memory = torch.cuda.memory_allocated(i)
                        cached_memory = torch.cuda.memory_reserved(i)
                        
                        gpu_info[f'gpu_{i}'] = {
                            'total_mb': total_memory / 1024 / 1024,
                            'allocated_mb': allocated_memory / 1024 / 1024,
                            'cached_mb': cached_memory / 1024 / 1024,
                            'free_mb': (total_memory - allocated_memory) / 1024 / 1024,
                            'usage_percent': (allocated_memory / total_memory) * 100
                        }
                    except Exception as e:
                        self.logger.warning(f"無法獲取GPU {i} 記憶體信息: {e}")
                
                memory_info['gpu_memory'] = gpu_info
                
        except Exception as e:
            self.logger.error(f"獲取記憶體使用失敗: {e}")
        
        return memory_info
    
    def _start_memory_monitoring(self):
        """啟動背景記憶體監控"""
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            return
        
        def monitor_memory():
            while not self.stop_monitoring.is_set():
                try:
                    memory_info = self.get_memory_usage()
                    self.memory_history.append(memory_info)
                    
                    # 限制歷史記錄大小
                    if len(self.memory_history) > self.max_history_size:
                        self.memory_history = self.memory_history[-self.max_history_size:]
                    
                    # 檢查是否需要自動清理
                    if self.config.trigger_mode in [CleanupTrigger.USAGE_BASED, CleanupTrigger.HYBRID]:
                        self._check_usage_trigger(memory_info)
                    
                    # 檢查記憶體警告
                    cpu_usage = memory_info['cpu_memory'].get('usage_percent', 0)
                    if cpu_usage > self.config.memory_warning_threshold:
                        self.logger.warning(f"⚠️ CPU記憶體使用率過高: {cpu_usage:.1f}%")
                    
                    if 'gpu_memory' in memory_info:
                        for gpu_id, gpu_info in memory_info['gpu_memory'].items():
                            gpu_usage = gpu_info.get('usage_percent', 0)
                            if gpu_usage > self.config.memory_warning_threshold:
                                self.logger.warning(f"⚠️ {gpu_id}記憶體使用率過高: {gpu_usage:.1f}%")
                    
                except Exception as e:
                    self.logger.error(f"記憶體監控錯誤: {e}")
                
                time.sleep(1.0)  # 每秒監控一次
        
        self.monitoring_thread = threading.Thread(target=monitor_memory, daemon=True)
        self.monitoring_thread.start()
        self.logger.debug("🔍 記憶體監控線程已啟動")
    
    def _check_usage_trigger(self, memory_info: Dict[str, Any]):
        """檢查使用率觸發條件"""
        cpu_usage = memory_info['cpu_memory'].get('usage_percent', 0)
        
        # 🚨 緊急清理檢查 (最高優先級)
        if self.config.enable_emergency_cleanup and cpu_usage > self.config.emergency_threshold:
            self.logger.warning(f"🚨 緊急清理觸發: CPU使用率 {cpu_usage:.1f}% > {self.config.emergency_threshold:.1f}%")
            self._emergency_cleanup(memory_info)
            return
        
        # 標準使用率觸發檢查
        if cpu_usage > self.config.cpu_memory_threshold:
            trigger_type = "aggressive_trigger" if self.config.trigger_mode == CleanupTrigger.AGGRESSIVE else "usage_trigger"
            self.logger.info(f"🔄 CPU記憶體使用率觸發清理: {cpu_usage:.1f}%")
            self.cleanup_memory(MemoryType.CPU, trigger_type)
        
        # GPU記憶體觸發檢查
        if 'gpu_memory' in memory_info:
            for gpu_id, gpu_info in memory_info['gpu_memory'].items():
                gpu_usage = gpu_info.get('usage_percent', 0)
                
                # 🚨 GPU緊急清理檢查
                if self.config.enable_emergency_cleanup and gpu_usage > self.config.emergency_threshold:
                    self.logger.warning(f"🚨 緊急清理觸發: {gpu_id}使用率 {gpu_usage:.1f}% > {self.config.emergency_threshold:.1f}%")
                    self._emergency_cleanup(memory_info)
                    return
                    
                elif gpu_usage > self.config.gpu_memory_threshold:
                    trigger_type = "aggressive_trigger" if self.config.trigger_mode == CleanupTrigger.AGGRESSIVE else "usage_trigger"
                    self.logger.info(f"🔄 {gpu_id}記憶體使用率觸發清理: {gpu_usage:.1f}%")
                    self.cleanup_memory(MemoryType.GPU, trigger_type)
                    break
        
        # 🔍 洩漏檢測 (激進模式)
        if self.config.enable_leak_detection and self.config.aggressive_mode:
            self._detect_memory_leaks(memory_info)
    
    def cleanup_memory(self, memory_type: MemoryType = MemoryType.ALL, trigger: str = "manual") -> Dict[str, Any]:
        """
        執行記憶體清理
        
        Args:
            memory_type: 要清理的記憶體類型
            trigger: 觸發原因
            
        Returns:
            Dict: 清理結果統計
        """
        if not self.config.enabled:
            return {"enabled": False}
        
        cleanup_start_time = time.time()
        self.logger.debug(f"🧹 開始記憶體清理 (類型: {memory_type.value}, 觸發: {trigger})")
        
        # 獲取清理前記憶體狀態
        memory_before = self.get_memory_usage()
        
        cleanup_result = {
            "timestamp": cleanup_start_time,
            "trigger": trigger,
            "memory_type": memory_type.value,
            "success": True,
            "cleanup_time": 0.0,
            "memory_before": memory_before,
            "memory_after": {},
            "actions_performed": []
        }
        
        try:
            # 執行清理動作
            if memory_type in [MemoryType.CPU, MemoryType.ALL]:
                self._cleanup_cpu_memory(cleanup_result)
            
            if memory_type in [MemoryType.GPU, MemoryType.ALL]:
                self._cleanup_gpu_memory(cleanup_result)
            
            if memory_type in [MemoryType.SHARED, MemoryType.ALL]:
                self._cleanup_shared_memory(cleanup_result)
            
            # 🔥 激進模式額外清理
            if self.config.aggressive_mode or self.config.trigger_mode == CleanupTrigger.AGGRESSIVE:
                if self.config.enable_tensor_cleanup:
                    self._cleanup_tensors()
                if self.config.enable_cache_cleanup:
                    self._clear_all_caches()
                # 額外GC輪次 (激進模式)
                if self.config.enable_python_gc:
                    additional_collected = gc.collect()
                    if additional_collected > 0 and self.config.enable_detailed_logging:
                        self.logger.debug(f"🔥 激進GC額外回收: {additional_collected}個對象")
            
        except Exception as e:
            self.logger.error(f"❌ 記憶體清理失敗: {e}")
            cleanup_result["success"] = False
            cleanup_result["error"] = str(e)
        
        # 獲取清理後記憶體狀態
        cleanup_result["memory_after"] = self.get_memory_usage()
        cleanup_result["cleanup_time"] = time.time() - cleanup_start_time
        
        # 計算清理效果
        self._calculate_cleanup_effect(cleanup_result)
        
        # 更新統計
        self._update_cleanup_stats(cleanup_result, trigger)
        
        # 記錄清理結果
        if self.config.log_memory_usage:
            self._log_cleanup_result(cleanup_result)
        
        return cleanup_result
    
    def _cleanup_cpu_memory(self, result: Dict[str, Any]):
        """清理CPU記憶體"""
        actions = []
        
        try:
            # Python垃圾回收
            if self.config.enable_python_gc:
                collected = gc.collect()
                actions.append(f"Python GC: 回收 {collected} 個對象")
                self.logger.debug(f"🗑️ Python垃圾回收: {collected} 個對象")
            
            # OpenCV記憶體清理
            if self.config.enable_opencv_cleanup and self.opencv_available:
                import cv2
                # OpenCV沒有直接的記憶體清理API，但可以清除Mat緩存
                actions.append("OpenCV: 清理完成")
                self.logger.debug("🗑️ OpenCV記憶體清理完成")
            
            result["actions_performed"].extend(actions)
            
        except Exception as e:
            self.logger.error(f"CPU記憶體清理失敗: {e}")
            raise
    
    def _cleanup_gpu_memory(self, result: Dict[str, Any]):
        """清理GPU記憶體"""
        if not self.cuda_available:
            return
        
        actions = []
        
        try:
            import torch
            
            # PyTorch GPU記憶體清理
            if self.config.enable_torch_cleanup:
                # 清空PyTorch緩存
                if hasattr(torch.cuda, 'empty_cache'):
                    torch.cuda.empty_cache()
                    actions.append("PyTorch CUDA: 清空緩存")
                    self.logger.debug("🗑️ PyTorch CUDA緩存已清空")
                
                # 垃圾回收CUDA對象
                if hasattr(torch.cuda, 'ipc_collect'):
                    torch.cuda.ipc_collect()
                    actions.append("PyTorch CUDA: IPC清理")
                
                # 同步所有CUDA流
                if hasattr(torch.cuda, 'synchronize'):
                    torch.cuda.synchronize()
                    actions.append("PyTorch CUDA: 同步完成")
            
            # CUDA低階清理
            if self.config.enable_cuda_cleanup and self.config.aggressive_cleanup:
                try:
                    # 重置CUDA上下文 (激進模式)
                    for i in range(torch.cuda.device_count()):
                        with torch.cuda.device(i):
                            torch.cuda.empty_cache()
                            torch.cuda.synchronize()
                    actions.append("CUDA: 重置所有設備")
                except Exception as e:
                    self.logger.warning(f"CUDA重置失敗: {e}")
            
            result["actions_performed"].extend(actions)
            
        except Exception as e:
            self.logger.error(f"GPU記憶體清理失敗: {e}")
            raise
    
    def _cleanup_shared_memory(self, result: Dict[str, Any]):
        """清理共享記憶體"""
        actions = []
        
        try:
            # 強制垃圾回收
            gc.collect()
            actions.append("共享記憶體: 強制GC完成")
            
            result["actions_performed"].extend(actions)
            
        except Exception as e:
            self.logger.error(f"共享記憶體清理失敗: {e}")
            raise
    
    def _calculate_cleanup_effect(self, result: Dict[str, Any]):
        """計算清理效果"""
        try:
            memory_before = result["memory_before"]
            memory_after = result["memory_after"]
            
            # CPU記憶體變化
            cpu_before = memory_before['cpu_memory'].get('used_mb', 0)
            cpu_after = memory_after['cpu_memory'].get('used_mb', 0)
            cpu_freed = max(0, cpu_before - cpu_after)
            
            # GPU記憶體變化
            gpu_freed_total = 0
            if 'gpu_memory' in memory_before and 'gpu_memory' in memory_after:
                for gpu_id in memory_before['gpu_memory']:
                    if gpu_id in memory_after['gpu_memory']:
                        gpu_before = memory_before['gpu_memory'][gpu_id].get('allocated_mb', 0)
                        gpu_after = memory_after['gpu_memory'][gpu_id].get('allocated_mb', 0)
                        gpu_freed = max(0, gpu_before - gpu_after)
                        gpu_freed_total += gpu_freed
            
            result["effect"] = {
                "cpu_memory_freed_mb": cpu_freed,
                "gpu_memory_freed_mb": gpu_freed_total,
                "total_memory_freed_mb": cpu_freed + gpu_freed_total
            }
            
        except Exception as e:
            self.logger.warning(f"計算清理效果失敗: {e}")
            result["effect"] = {"cpu_memory_freed_mb": 0, "gpu_memory_freed_mb": 0, "total_memory_freed_mb": 0}
    
    def _update_cleanup_stats(self, result: Dict[str, Any], trigger: str):
        """更新清理統計"""
        self.cleanup_stats['total_cleanups'] += 1
        self.cleanup_stats['total_time_saved'] += result.get('cleanup_time', 0)
        self.cleanup_stats['last_cleanup_time'] = time.time()
        
        if 'effect' in result:
            self.cleanup_stats['memory_freed_mb'] += result['effect'].get('cpu_memory_freed_mb', 0)
            self.cleanup_stats['gpu_memory_freed_mb'] += result['effect'].get('gpu_memory_freed_mb', 0)
        
        if trigger == "manual":
            self.cleanup_stats['manual_cleanups'] += 1
        else:
            self.cleanup_stats['automatic_cleanups'] += 1
        
        # 重置圖像計數器
        self.cleanup_stats['images_processed_since_cleanup'] = 0
    
    def _log_cleanup_result(self, result: Dict[str, Any]):
        """記錄清理結果"""
        if result["success"]:
            effect = result.get("effect", {})
            cpu_freed = effect.get("cpu_memory_freed_mb", 0)
            gpu_freed = effect.get("gpu_memory_freed_mb", 0)
            
            self.logger.info(f"🧹 記憶體清理完成:")
            self.logger.info(f"   CPU記憶體釋放: {cpu_freed:.1f} MB")
            self.logger.info(f"   GPU記憶體釋放: {gpu_freed:.1f} MB")
            self.logger.info(f"   清理耗時: {result['cleanup_time']:.3f} 秒")
            self.logger.info(f"   執行動作: {len(result['actions_performed'])} 項")
        else:
            self.logger.error(f"❌ 記憶體清理失敗: {result.get('error', '未知錯誤')}")
    
    def on_image_processed(self):
        """處理單張圖像後的回調"""
        if not self.config.enabled:
            return
        
        self.cleanup_stats['images_processed_since_cleanup'] += 1
        
        # 基於圖像數量的觸發檢查
        trigger_modes = [CleanupTrigger.COUNT_BASED, CleanupTrigger.HYBRID, CleanupTrigger.AGGRESSIVE]
        if (self.config.trigger_mode in trigger_modes and
            self.cleanup_stats['images_processed_since_cleanup'] >= self.config.cleanup_after_n_images):
            
            trigger_type = "aggressive_count_trigger" if self.config.trigger_mode == CleanupTrigger.AGGRESSIVE else "count_trigger"
            if self.config.enable_detailed_logging:
                self.logger.debug(f"🔄 處理 {self.config.cleanup_after_n_images} 張圖像後觸發清理")
            self.cleanup_memory(MemoryType.ALL, trigger_type)
    
    def on_batch_start(self):
        """批次處理開始時的回調"""
        if self.config.enabled and self.config.cleanup_on_batch_start:
            self.logger.debug("🔄 批次開始時觸發清理")
            self.cleanup_memory(MemoryType.ALL, "batch_start")
    
    def on_batch_end(self):
        """批次處理結束時的回調"""
        if self.config.enabled and self.config.cleanup_on_batch_end:
            self.logger.debug("🔄 批次結束時觸發清理")
            self.cleanup_memory(MemoryType.ALL, "batch_end")
    
    def on_image_start(self):
        """🆕 圖像處理開始前的回調 (激進模式)"""
        if self.config.enabled and self.config.cleanup_on_image_start:
            if self.config.enable_detailed_logging:
                self.logger.debug("🔄 圖像處理開始前觸發清理")
            self.cleanup_memory(MemoryType.ALL, "image_start")
    
    def on_image_end(self):
        """🆕 圖像處理結束後的回調 (激進模式)"""
        if self.config.enabled and self.config.cleanup_on_image_end:
            if self.config.enable_detailed_logging:
                self.logger.debug("🔄 圖像處理結束後觸發清理")
            self.cleanup_memory(MemoryType.ALL, "image_end")
    
    def on_model_load(self):
        """📋 模型載入後的回調"""
        if self.config.enabled and self.config.cleanup_on_model_load:
            self.logger.debug("🔄 模型載入後觸發清理")
            self.cleanup_memory(MemoryType.ALL, "model_load")
    
    def on_inference_error(self, error: Exception = None):
        """⚠️ 推理錯誤時的回調"""
        if self.config.enabled and self.config.cleanup_on_inference_error:
            error_msg = f" (錯誤: {str(error)[:50]}...)" if error else ""
            self.logger.info(f"🔄 推理錯誤時觸發清理{error_msg}")
            # 推理錯誤時使用激進清理
            self.cleanup_memory(MemoryType.ALL, "inference_error")
    
    def get_cleanup_stats(self) -> Dict[str, Any]:
        """獲取清理統計"""
        stats = self.cleanup_stats.copy()
        stats['config'] = {
            'enabled': self.config.enabled,
            'aggressive_mode': self.config.aggressive_mode,
            'trigger_mode': self.config.trigger_mode.value,
            'cleanup_interval': self.config.cleanup_interval_seconds,
            'cpu_threshold': self.config.cpu_memory_threshold,
            'gpu_threshold': self.config.gpu_memory_threshold,
            'emergency_threshold': self.config.emergency_threshold,
            'tensor_cleanup': self.config.enable_tensor_cleanup,
            'leak_detection': self.config.enable_leak_detection
        }
        return stats
    
    def get_memory_history(self, last_n: int = 10) -> List[Dict[str, Any]]:
        """獲取記憶體使用歷史"""
        return self.memory_history[-last_n:] if self.memory_history else []
    
    def cleanup(self):
        """清理資源"""
        if self.config.enabled:
            # 停止監控線程
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.stop_monitoring.set()
                self.monitoring_thread.join(timeout=2.0)
            
            # 最終記憶體清理
            final_result = self.cleanup_memory(MemoryType.ALL, "shutdown")
            
            self.logger.info("🧹 記憶體清理器資源清理完成")
            self.logger.info(f"📊 總清理次數: {self.cleanup_stats['total_cleanups']}")
            self.logger.info(f"📊 總釋放記憶體: {self.cleanup_stats['memory_freed_mb']:.1f} MB")
            if self.config.aggressive_mode:
                self.logger.info(f"🚨 緊急清理: {self.cleanup_stats['emergency_cleanups']}次")
                self.logger.info(f"🔎 張量清理: {self.cleanup_stats['tensor_cleanups']}次")
                self.logger.info(f"🔍 洩漏檢測: {self.cleanup_stats['leak_detections']}次")
    
    # ========================================================================================
    # 🚨 緊急清理功能
    # ========================================================================================
    
    def _emergency_cleanup(self, memory_info: Dict[str, Any]):
        """🚨 緊急清理 - 當記憶體使用率過高時的激進清理"""
        self.logger.warning("🚨 開始緊急記憶體清理")
        emergency_start_time = time.time()
        
        try:
            # 1. 強制Python垃圾回收 (多次)
            if self.config.emergency_force_gc:
                for i in range(3):  # 多次GC確保徹底清理
                    collected = gc.collect()
                    if collected > 0:
                        self.logger.debug(f"🚨 緊急GC第{i+1}輪: 回收 {collected} 個對象")
            
            # 2. CUDA緩存清理
            if self.cuda_available and TORCH_AVAILABLE:
                import torch
                try:
                    # 清空所有GPU緩存
                    for i in range(torch.cuda.device_count()):
                        with torch.cuda.device(i):
                            torch.cuda.empty_cache()
                            if hasattr(torch.cuda, 'ipc_collect'):
                                torch.cuda.ipc_collect()
                            torch.cuda.synchronize()
                    
                    self.logger.debug("🚨 緊急CUDA緩存清理完成")
                except Exception as e:
                    self.logger.warning(f"🚨 CUDA緊急清理失敗: {e}")
            
            # 3. 清空所有緩存 (如果啟用)
            if self.config.emergency_clear_cache:
                self._clear_all_caches()
            
            # 4. 強制張量清理 (激進模式)
            if self.config.enable_tensor_cleanup:
                self._cleanup_tensors(emergency=True)
            
            # 5. 結束統計
            emergency_time = time.time() - emergency_start_time
            self.cleanup_stats['emergency_cleanups'] += 1
            
            # 獲取清理後記憶體狀態
            memory_after = self.get_memory_usage()
            cpu_after = memory_after['cpu_memory'].get('usage_percent', 0)
            
            self.logger.warning(f"🚨 緊急清理完成: 耗時{emergency_time:.3f}秒, CPU使用率: {cpu_after:.1f}%")
            
            # 如果清理後仍然過高，警告用戶
            if cpu_after > self.config.emergency_threshold:
                self.logger.error(f"⚠️ 緊急清理後記憶體仍過高: {cpu_after:.1f}% > {self.config.emergency_threshold:.1f}%")
                self.logger.error("⚠️ 建議檢查系統資源或程式碼是否存在記憶體洩漏")
                
        except Exception as e:
            self.logger.error(f"⚠️ 緊急清理失敗: {e}")
            self.cleanup_stats['failed_cleanups'] += 1
    
    def _clear_all_caches(self):
        """清空所有可能的緩存"""
        try:
            # Python內建緩存
            if hasattr(gc, 'get_objects'):
                # 清理弱引用緩存
                import weakref
                if hasattr(weakref, '_remove_dead_weakref'):
                    pass  # 內部清理機制
            
            # 清理sys模塊緩存
            import sys
            if hasattr(sys, 'intern'):
                pass  # 字符串內化緩存無法直接清除
            
            self.logger.debug("🧹 清空緩存完成")
            
        except Exception as e:
            self.logger.warning(f"清空緩存失敗: {e}")
    
    # ========================================================================================
    # 🔎 張量清理功能 (激進模式)
    # ========================================================================================
    
    def _cleanup_tensors(self, emergency: bool = False):
        """🔎 清理PyTorch張量和梯度 (激進模式)"""
        if not self.torch_available:
            return
        
        try:
            import torch
            cleanup_count = 0
            
            # 1. 清理未使用的張量
            if hasattr(torch, '_C') and hasattr(torch._C, '_cuda_clearCublasWorkspaces'):
                try:
                    torch._C._cuda_clearCublasWorkspaces()
                    cleanup_count += 1
                except:
                    pass
            
            # 2. 清理梯度 (如果啟用)
            if self.config.enable_gradient_cleanup:
                # 尋找所有requires_grad=True的張量並清理梯度
                import gc
                for obj in gc.get_objects():
                    try:
                        if torch.is_tensor(obj) and hasattr(obj, 'grad') and obj.grad is not None:
                            obj.grad = None
                            cleanup_count += 1
                    except:
                        continue
            
            # 3. 清理CUDA執行器緩存
            if self.cuda_available:
                try:
                    for device_id in range(torch.cuda.device_count()):
                        with torch.cuda.device(device_id):
                            if hasattr(torch.cuda, 'reset_accumulated_memory_stats'):
                                torch.cuda.reset_accumulated_memory_stats(device_id)
                            if hasattr(torch.cuda, 'reset_peak_memory_stats'):
                                torch.cuda.reset_peak_memory_stats(device_id)
                    cleanup_count += 1
                except:
                    pass
            
            # 4. 強制同步 (如果啟用)
            if self.config.force_sync_cleanup or emergency:
                if self.cuda_available:
                    torch.cuda.synchronize()
                cleanup_count += 1
            
            if cleanup_count > 0:
                self.cleanup_stats['tensor_cleanups'] += 1
                action_type = "緊急" if emergency else "例行"
                if self.config.enable_detailed_logging:
                    self.logger.debug(f"🔎 {action_type}張量清理完成: {cleanup_count}個操作")
            
        except Exception as e:
            self.logger.warning(f"🔎 張量清理失敗: {e}")
    
    # ========================================================================================
    # 🔍 洩漏檢測功能 (激進模式)
    # ========================================================================================
    
    def _detect_memory_leaks(self, memory_info: Dict[str, Any]):
        """🔍 檢測記憶體洩漏 (激進模式)"""
        try:
            current_usage = memory_info['cpu_memory'].get('usage_percent', 0)
            
            # 初始化基線
            if self.leak_detection_baseline is None:
                self.leak_detection_baseline = current_usage
                return
            
            # 記錄樣本
            self.leak_detection_samples.append({
                'timestamp': time.time(),
                'cpu_usage': current_usage,
                'gpu_usage': self._get_max_gpu_usage(memory_info)
            })
            
            # 保持樣本數量限制
            if len(self.leak_detection_samples) > 50:
                self.leak_detection_samples = self.leak_detection_samples[-50:]
            
            # 需要至少十個樣本才開始檢測
            if len(self.leak_detection_samples) < 10:
                return
            
            # 檢測記憶體是否持續上升
            recent_samples = self.leak_detection_samples[-10:]
            is_increasing = all(
                recent_samples[i]['cpu_usage'] <= recent_samples[i+1]['cpu_usage'] 
                for i in range(len(recent_samples)-1)
            )
            
            if is_increasing:
                self.consecutive_increases += 1
                
                # 連續5次上升視為可能洩漏
                if self.consecutive_increases >= 5:
                    avg_increase = (recent_samples[-1]['cpu_usage'] - recent_samples[0]['cpu_usage']) / len(recent_samples)
                    
                    if avg_increase > 0.5:  # 每次平均上升超過0.5%
                        self.logger.warning(
                            f"🔍 檢測到可能的記憶體洩漏: 連續{self.consecutive_increases}次上升, "
                            f"平均增率: {avg_increase:.2f}%/次"
                        )
                        
                        self.cleanup_stats['leak_detections'] += 1
                        
                        # 觸發激進清理
                        if self.consecutive_increases >= 8:  # 更嚴重的洩漏
                            self.logger.warning("🔍 嚴重洩漏檢測, 觸發激進清理")
                            self._emergency_cleanup(memory_info)
                        
                        # 重置計數器
                        self.consecutive_increases = 0
                        self.leak_detection_baseline = current_usage
            else:
                # 記憶體使用量下降或穩定，重置計數器
                self.consecutive_increases = 0
                
        except Exception as e:
            self.logger.warning(f"🔍 洩漏檢測失敗: {e}")
    
    def _get_max_gpu_usage(self, memory_info: Dict[str, Any]) -> float:
        """獲取GPU最大使用率"""
        max_usage = 0.0
        if 'gpu_memory' in memory_info:
            for gpu_info in memory_info['gpu_memory'].values():
                usage = gpu_info.get('usage_percent', 0)
                max_usage = max(max_usage, usage)
        return max_usage


def create_aggressive_config(
    cleanup_interval: float = 10.0,
    cleanup_after_images: int = 3,
    cpu_threshold: float = 60.0,
    gpu_threshold: float = 60.0,
    emergency_threshold: float = 90.0
) -> MemoryCleanupConfig:
    """
    🔥 創建激進模式配置
    
    Args:
        cleanup_interval: 清理間隔(秒)
        cleanup_after_images: 每N張圖像後清理
        cpu_threshold: CPU記憶體闾值(%)
        gpu_threshold: GPU記憶體闾值(%)
        emergency_threshold: 緊急清理闾值(%)
        
    Returns:
        MemoryCleanupConfig: 激進模式配置
    """
    return MemoryCleanupConfig(
        # 🔥 激進模式設定
        enabled=True,
        aggressive_mode=True,
        trigger_mode=CleanupTrigger.AGGRESSIVE,
        
        # 時間設定
        cleanup_interval_seconds=cleanup_interval,
        cleanup_after_n_images=cleanup_after_images,
        cleanup_on_batch_start=True,
        cleanup_on_batch_end=True,
        cleanup_on_image_start=True,  # 激進模式啟用
        cleanup_on_image_end=True,    # 激進模式啟用
        cleanup_on_model_load=True,
        cleanup_on_inference_error=True,
        
        # 闾值設定
        cpu_memory_threshold=cpu_threshold,
        gpu_memory_threshold=gpu_threshold,
        emergency_threshold=emergency_threshold,
        
        # 緊急清理
        enable_emergency_cleanup=True,
        emergency_force_gc=True,
        emergency_clear_cache=True,
        
        # 清理策略
        enable_torch_cleanup=True,
        enable_opencv_cleanup=True,
        enable_python_gc=True,
        enable_cuda_cleanup=True,
        enable_shared_memory_cleanup=True,
        enable_cache_cleanup=True,
        
        # 進階功能
        enable_tensor_cleanup=True,     # 激進模式啟用
        enable_gradient_cleanup=True,   # 激進模式啟用
        enable_model_cleanup=False,     # 謹慎使用
        force_sync_cleanup=True,        # 激進模式啟用
        
        # 監控設定
        enable_memory_monitoring=True,
        log_memory_usage=True,
        enable_leak_detection=True,     # 激進模式啟用
        enable_detailed_logging=True,   # 激進模式啟用
        memory_warning_threshold=85.0,
        
        # 安全設定
        max_cleanup_time=10.0,  # 激進模式允許更長清理時間
        aggressive_cleanup=True,
        preserve_model_cache=True
    )

def create_memory_cleaner(config: MemoryCleanupConfig) -> MemoryCleaner:
    """
    工廠函數：創建記憶體清理器
    
    Args:
        config: 記憶體清理配置
        
    Returns:
        MemoryCleaner: 記憶體清理器實例
        
    Raises:
        ImportError: 當必需的依賴庫不可用時
    """
    # 🛡️ 檢查依賴庫可用性
    missing_deps = []
    if not PSUTIL_AVAILABLE:
        missing_deps.append("psutil (系統監控)")
    
    # 警告：缺少可選依賴
    optional_missing = []
    if not TORCH_AVAILABLE:
        optional_missing.append("PyTorch (GPU監控)")
    if not OPENCV_AVAILABLE:
        optional_missing.append("OpenCV (圖像處理)")
    
    logger = logging.getLogger(__name__)
    
    if missing_deps:
        logger.warning(f"⚠️ 記憶體清理器缺少依賴庫: {', '.join(missing_deps)}")
        logger.warning("   -> 某些功能可能受限，建議安裝: pip install psutil")
    
    if optional_missing:
        logger.info(f"💡 可選依賴庫未安裝: {', '.join(optional_missing)}")
        logger.info("   -> 這不會影響基本功能，但會限制某些監控能力")
    
    # 激進模式提示
    if config.aggressive_mode or config.trigger_mode == CleanupTrigger.AGGRESSIVE:
        logger.info("🔥 激進記憶體清理模式已啟用")
        logger.info(f"   清理間隔: {config.cleanup_interval_seconds}秒")
        logger.info(f"   圖像觸發: 每{config.cleanup_after_n_images}張")
        logger.info(f"   闾值設定: CPU {config.cpu_memory_threshold}%, GPU {config.gpu_memory_threshold}%")
    
    return MemoryCleaner(config)