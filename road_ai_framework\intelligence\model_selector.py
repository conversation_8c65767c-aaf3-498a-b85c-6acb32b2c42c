# 🧠 智能模型選擇與動態切換系統
# Phase 4 核心功能 - 智能化模型管理

import time
import logging
import threading
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from enum import Enum
import numpy as np
import json
from datetime import datetime, timedelta

class ModelType(Enum):
    """模型類型枚舉"""
    DETECTION = "detection"
    SEGMENTATION = "segmentation" 
    CLASSIFICATION = "classification"
    VISION_MAMBA = "vision_mamba"
    CSP_IFORMER = "csp_iformer"

class ScenarioType(Enum):
    """應用場景枚舉"""
    REAL_TIME = "real_time"           # 實時處理
    BATCH_PROCESSING = "batch"        # 批量處理
    HIGH_ACCURACY = "high_accuracy"   # 高精度要求
    EDGE_COMPUTING = "edge"           # 邊緣計算
    MOBILE_DEVICE = "mobile"          # 移動設備

@dataclass
class ModelProfile:
    """模型檔案配置"""
    model_id: str
    model_type: ModelType
    model_path: str
    accuracy_score: float           # 準確率評分 (0-1)
    speed_score: float             # 速度評分 (fps)
    memory_usage: int              # 記憶體使用量 (MB)
    gpu_requirement: bool          # 是否需要GPU
    supported_scenarios: List[ScenarioType]
    confidence_threshold: float = 0.5
    batch_size: int = 1
    input_size: Tuple[int, int] = (640, 640)
    created_time: datetime = field(default_factory=datetime.now)
    last_used: datetime = field(default_factory=datetime.now)
    usage_count: int = 0
    performance_history: List[Dict] = field(default_factory=list)

@dataclass 
class InferenceRequest:
    """推理請求"""
    image_path: str
    scenario: ScenarioType
    priority: int = 1              # 優先級 1-10
    accuracy_requirement: float = 0.8  # 精度要求
    speed_requirement: float = 1.0     # 速度要求 (fps)
    memory_limit: int = 1000           # 記憶體限制 (MB)
    require_gpu: Optional[bool] = None
    custom_params: Dict[str, Any] = field(default_factory=dict)

class ModelSelector:
    """智能模型選擇器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self.models: Dict[str, ModelProfile] = {}
        self.current_system_load = 0.0
        self.available_memory = 8000  # MB
        self.gpu_available = True
        self._lock = threading.Lock()
        
        # 性能統計
        self.selection_stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'model_switches': 0,
            'avg_selection_time': 0.0
        }
        
        # 決策緩存
        self.decision_cache: Dict[str, Tuple[str, datetime]] = {}
        self.cache_ttl = timedelta(minutes=5)
        
        if config_path:
            self.load_model_profiles(config_path)

    def register_model(self, profile: ModelProfile) -> bool:
        """註冊新模型"""
        try:
            with self._lock:
                if profile.model_id in self.models:
                    self.logger.warning(f"模型 {profile.model_id} 已存在，將覆蓋")
                
                # 驗證模型文件存在
                if not Path(profile.model_path).exists():
                    self.logger.error(f"模型文件不存在: {profile.model_path}")
                    return False
                
                self.models[profile.model_id] = profile
                self.logger.info(f"成功註冊模型: {profile.model_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"註冊模型失敗: {e}")
            return False

    def select_optimal_model(self, request: InferenceRequest) -> Optional[ModelProfile]:
        """智能選擇最優模型"""
        start_time = time.time()
        
        try:
            with self._lock:
                self.selection_stats['total_requests'] += 1
                
                # 檢查緩存
                cache_key = self._generate_cache_key(request)
                if cache_key in self.decision_cache:
                    model_id, cached_time = self.decision_cache[cache_key]
                    if datetime.now() - cached_time < self.cache_ttl:
                        self.selection_stats['cache_hits'] += 1
                        return self.models.get(model_id)
                
                # 獲取候選模型
                candidates = self._get_candidate_models(request)
                if not candidates:
                    self.logger.warning("沒有找到合適的候選模型")
                    return None
                
                # 智能評分和選擇
                best_model = self._evaluate_and_select(candidates, request)
                
                # 更新緩存和統計
                if best_model:
                    self.decision_cache[cache_key] = (best_model.model_id, datetime.now())
                    best_model.last_used = datetime.now()
                    best_model.usage_count += 1
                
                selection_time = time.time() - start_time
                self._update_selection_stats(selection_time)
                
                return best_model
                
        except Exception as e:
            self.logger.error(f"模型選擇失敗: {e}")
            return None

    def _get_candidate_models(self, request: InferenceRequest) -> List[ModelProfile]:
        """獲取候選模型列表"""
        candidates = []
        
        for model in self.models.values():
            # 場景匹配
            if request.scenario not in model.supported_scenarios:
                continue
                
            # GPU要求檢查
            if model.gpu_requirement and not self.gpu_available:
                continue
                
            # 記憶體限制檢查
            if model.memory_usage > request.memory_limit:
                continue
                
            # 基本精度要求
            if model.accuracy_score < request.accuracy_requirement - 0.1:
                continue
                
            candidates.append(model)
        
        return candidates

    def _evaluate_and_select(self, candidates: List[ModelProfile], 
                           request: InferenceRequest) -> Optional[ModelProfile]:
        """評估候選模型並選擇最優"""
        if not candidates:
            return None
            
        scores = []
        
        for model in candidates:
            score = self._calculate_model_score(model, request)
            scores.append((score, model))
        
        # 按分數排序，選擇最高分
        scores.sort(key=lambda x: x[0], reverse=True)
        best_score, best_model = scores[0]
        
        self.logger.info(f"選擇模型: {best_model.model_id}, 評分: {best_score:.3f}")
        return best_model

    def _calculate_model_score(self, model: ModelProfile, 
                             request: InferenceRequest) -> float:
        """計算模型綜合評分"""
        # 基礎權重配置
        weights = {
            'accuracy': 0.4,
            'speed': 0.3,
            'memory': 0.15,
            'usage_history': 0.1,
            'scenario_match': 0.05
        }
        
        # 精度評分
        accuracy_score = min(model.accuracy_score / request.accuracy_requirement, 1.0)
        
        # 速度評分
        speed_score = min(model.speed_score / request.speed_requirement, 1.0)
        
        # 記憶體效率評分
        memory_score = max(0, 1 - (model.memory_usage / request.memory_limit))
        
        # 使用歷史評分
        usage_score = min(model.usage_count / 100, 1.0)  # 歸一化使用次數
        
        # 場景匹配度評分
        scenario_score = 1.0 if request.scenario in model.supported_scenarios else 0.0
        
        # 根據場景調整權重
        if request.scenario == ScenarioType.REAL_TIME:
            weights['speed'] = 0.5
            weights['accuracy'] = 0.3
        elif request.scenario == ScenarioType.HIGH_ACCURACY:
            weights['accuracy'] = 0.6
            weights['speed'] = 0.2
        elif request.scenario == ScenarioType.EDGE_COMPUTING:
            weights['memory'] = 0.4
            weights['speed'] = 0.3
        
        # 計算加權總分
        total_score = (
            accuracy_score * weights['accuracy'] +
            speed_score * weights['speed'] +
            memory_score * weights['memory'] +
            usage_score * weights['usage_history'] +
            scenario_score * weights['scenario_match']
        )
        
        return total_score

    def _generate_cache_key(self, request: InferenceRequest) -> str:
        """生成緩存鍵"""
        key_parts = [
            str(request.scenario.value),
            str(request.accuracy_requirement),
            str(request.speed_requirement),
            str(request.memory_limit),
            str(request.require_gpu)
        ]
        return '|'.join(key_parts)

    def _update_selection_stats(self, selection_time: float):
        """更新選擇統計"""
        current_avg = self.selection_stats['avg_selection_time']
        total_requests = self.selection_stats['total_requests']
        
        # 計算新的平均選擇時間
        new_avg = ((current_avg * (total_requests - 1)) + selection_time) / total_requests
        self.selection_stats['avg_selection_time'] = new_avg

    def update_model_performance(self, model_id: str, performance_data: Dict[str, Any]):
        """更新模型性能數據"""
        if model_id not in self.models:
            self.logger.warning(f"模型 {model_id} 不存在")
            return
            
        model = self.models[model_id]
        model.performance_history.append({
            'timestamp': datetime.now().isoformat(),
            'data': performance_data
        })
        
        # 保持歷史記錄在合理範圍內
        if len(model.performance_history) > 100:
            model.performance_history = model.performance_history[-50:]

    def get_model_recommendations(self, scenario: ScenarioType, 
                                limit: int = 3) -> List[ModelProfile]:
        """獲取模型推薦"""
        request = InferenceRequest(
            image_path="",
            scenario=scenario,
            accuracy_requirement=0.8,
            speed_requirement=1.0
        )
        
        candidates = self._get_candidate_models(request)
        scored_models = []
        
        for model in candidates:
            score = self._calculate_model_score(model, request)
            scored_models.append((score, model))
        
        scored_models.sort(key=lambda x: x[0], reverse=True)
        return [model for score, model in scored_models[:limit]]

    def get_statistics(self) -> Dict[str, Any]:
        """獲取選擇器統計信息"""
        return {
            'selection_stats': self.selection_stats.copy(),
            'total_models': len(self.models),
            'cache_size': len(self.decision_cache),
            'models_by_type': self._get_models_by_type(),
            'most_used_models': self._get_most_used_models(5)
        }

    def _get_models_by_type(self) -> Dict[str, int]:
        """按類型統計模型數量"""
        type_counts = {}
        for model in self.models.values():
            model_type = model.model_type.value
            type_counts[model_type] = type_counts.get(model_type, 0) + 1
        return type_counts

    def _get_most_used_models(self, limit: int) -> List[Dict[str, Any]]:
        """獲取最常用的模型"""
        models_by_usage = sorted(
            self.models.values(),
            key=lambda m: m.usage_count,
            reverse=True
        )
        
        return [
            {
                'model_id': model.model_id,
                'usage_count': model.usage_count,
                'last_used': model.last_used.isoformat(),
                'accuracy_score': model.accuracy_score
            }
            for model in models_by_usage[:limit]
        ]

    def save_model_profiles(self, output_path: str):
        """保存模型配置到文件"""
        try:
            profiles_data = {}
            for model_id, model in self.models.items():
                profiles_data[model_id] = {
                    'model_type': model.model_type.value,
                    'model_path': model.model_path,
                    'accuracy_score': model.accuracy_score,
                    'speed_score': model.speed_score,
                    'memory_usage': model.memory_usage,
                    'gpu_requirement': model.gpu_requirement,
                    'supported_scenarios': [s.value for s in model.supported_scenarios],
                    'confidence_threshold': model.confidence_threshold,
                    'batch_size': model.batch_size,
                    'input_size': model.input_size,
                    'usage_count': model.usage_count,
                    'created_time': model.created_time.isoformat(),
                    'last_used': model.last_used.isoformat()
                }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(profiles_data, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"模型配置已保存到: {output_path}")
            
        except Exception as e:
            self.logger.error(f"保存模型配置失敗: {e}")

    def load_model_profiles(self, config_path: str):
        """從文件加載模型配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                profiles_data = json.load(f)
            
            for model_id, data in profiles_data.items():
                profile = ModelProfile(
                    model_id=model_id,
                    model_type=ModelType(data['model_type']),
                    model_path=data['model_path'],
                    accuracy_score=data['accuracy_score'],
                    speed_score=data['speed_score'],
                    memory_usage=data['memory_usage'],
                    gpu_requirement=data['gpu_requirement'],
                    supported_scenarios=[ScenarioType(s) for s in data['supported_scenarios']],
                    confidence_threshold=data.get('confidence_threshold', 0.5),
                    batch_size=data.get('batch_size', 1),
                    input_size=tuple(data.get('input_size', [640, 640])),
                    usage_count=data.get('usage_count', 0),
                    created_time=datetime.fromisoformat(data.get('created_time', datetime.now().isoformat())),
                    last_used=datetime.fromisoformat(data.get('last_used', datetime.now().isoformat()))
                )
                self.models[model_id] = profile
            
            self.logger.info(f"成功加載 {len(self.models)} 個模型配置")
            
        except Exception as e:
            self.logger.error(f"加載模型配置失敗: {e}")


class IntelligentModelManager:
    """智能模型管理器 - 整合模型選擇和動態切換"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.selector = ModelSelector(config_path)
        self.current_model = None
        self.current_model_id = None
        self.switch_count = 0
        self.logger = logging.getLogger(__name__)
        
        # 預設模型配置
        self._register_default_models()

    def _register_default_models(self):
        """註冊預設模型配置"""
        # 建立相對於此檔案的基礎路徑
        # self.logger.info(f"當前檔案路徑: {__file__}") # for debug
        # road_ai_framework/intelligence/model_selector.py -> road_ai_framework/
        base_path = Path(__file__).parent.parent
        # self.logger.info(f"推斷基礎路徑: {base_path}") # for debug

        # YOLO11 分割模型
        yolo11_seg = ModelProfile(
            model_id="yolo11_seg_default",
            model_type=ModelType.SEGMENTATION,
            model_path=str(base_path / "models" / "yolo11_seg.pt"),
            accuracy_score=0.85,
            speed_score=45.0,
            memory_usage=1200,
            gpu_requirement=True,
            supported_scenarios=[
                ScenarioType.REAL_TIME,
                ScenarioType.BATCH_PROCESSING,
                ScenarioType.HIGH_ACCURACY
            ],
            confidence_threshold=0.3
        )
        
        # YOLO12 檢測模型
        yolo12_det = ModelProfile(
            model_id="yolo12_det_default",
            model_type=ModelType.DETECTION,
            model_path=str(base_path / "models" / "yolo12.pt"),
            accuracy_score=0.88,
            speed_score=60.0,
            memory_usage=1000,
            gpu_requirement=True,
            supported_scenarios=[
                ScenarioType.REAL_TIME,
                ScenarioType.BATCH_PROCESSING
            ],
            confidence_threshold=0.4
        )
        
        # 輕量級邊緣模型
        edge_model = ModelProfile(
            model_id="yolo_edge_lite",
            model_type=ModelType.DETECTION,
            model_path=str(base_path / "models" / "yolo_edge.pt"),
            accuracy_score=0.75,
            speed_score=120.0,
            memory_usage=400,
            gpu_requirement=False,
            supported_scenarios=[
                ScenarioType.EDGE_COMPUTING,
                ScenarioType.MOBILE_DEVICE,
                ScenarioType.REAL_TIME
            ],
            confidence_threshold=0.5
        )
        
        # 註冊模型
        for model in [yolo11_seg, yolo12_det, edge_model]:
            self.selector.register_model(model)

    def smart_inference(self, image_path: str, scenario: ScenarioType,
                       **kwargs) -> Optional[Dict[str, Any]]:
        """智能推理 - 自動選擇最優模型並執行推理"""
        try:
            # 創建推理請求
            request = InferenceRequest(
                image_path=image_path,
                scenario=scenario,
                **kwargs
            )
            
            # 選擇最優模型
            selected_model = self.selector.select_optimal_model(request)
            if not selected_model:
                self.logger.error("無法選擇合適的模型")
                return None
            
            # 檢查是否需要切換模型
            if self.current_model_id != selected_model.model_id:
                self._switch_model(selected_model)
            
            # 執行推理
            result = self._execute_inference(image_path, selected_model)
            
            # 更新性能數據
            if result and 'processing_time' in result:
                performance_data = {
                    'processing_time': result['processing_time'],
                    'accuracy': result.get('confidence', 0.0),
                    'memory_usage': result.get('memory_usage', 0)
                }
                self.selector.update_model_performance(
                    selected_model.model_id, performance_data
                )
            
            return result
            
        except Exception as e:
            self.logger.error(f"智能推理失敗: {e}")
            return None

    def _switch_model(self, new_model: ModelProfile):
        """切換到新模型"""
        self.logger.info(f"切換模型: {self.current_model_id} -> {new_model.model_id}")
        
        # 這裡應該實現實際的模型加載邏輯
        # 暫時用模擬實現
        self.current_model = new_model
        self.current_model_id = new_model.model_id
        self.switch_count += 1
        
        # 更新統計
        self.selector.selection_stats['model_switches'] += 1

    def _execute_inference(self, image_path: str, 
                         model: ModelProfile) -> Optional[Dict[str, Any]]:
        """執行實際推理（模擬實現）"""
        import time
        
        start_time = time.time()
        
        # 模擬推理過程
        time.sleep(0.1)  # 模擬推理時間
        
        processing_time = time.time() - start_time
        
        # 模擬推理結果
        result = {
            'model_id': model.model_id,
            'model_type': model.model_type.value,
            'processing_time': processing_time,
            'confidence': 0.85,
            'detections': [
                {'class': 'linear_crack', 'confidence': 0.85, 'bbox': [100, 100, 200, 200]},
                {'class': 'potholes', 'confidence': 0.72, 'bbox': [300, 150, 400, 250]}
            ],
            'memory_usage': model.memory_usage
        }
        
        return result

    def get_manager_stats(self) -> Dict[str, Any]:
        """獲取管理器統計信息"""
        selector_stats = self.selector.get_statistics()
        
        return {
            'current_model': self.current_model_id,
            'model_switches': self.switch_count,
            'selector_stats': selector_stats
        }


# 使用示例和測試
if __name__ == "__main__":
    # 配置日誌
    logging.basicConfig(level=logging.INFO)
    
    # 創建智能模型管理器
    manager = IntelligentModelManager()
    
    # 測試不同場景的智能推理
    test_scenarios = [
        (ScenarioType.REAL_TIME, {"accuracy_requirement": 0.7, "speed_requirement": 30.0}),
        (ScenarioType.HIGH_ACCURACY, {"accuracy_requirement": 0.9, "speed_requirement": 10.0}),
        (ScenarioType.EDGE_COMPUTING, {"memory_limit": 500})
    ]
    
    for scenario, params in test_scenarios:
        print(f"\n🧪 測試場景: {scenario.value}")
        result = manager.smart_inference(
            image_path="./test_image.jpg",
            scenario=scenario,
            **params
        )
        if result:
            print(f"✅ 使用模型: {result['model_id']}")
            print(f"   處理時間: {result['processing_time']:.3f}s")
            print(f"   檢測結果: {len(result['detections'])} 個")
        else:
            print("❌ 推理失敗")
    
    # 顯示統計信息
    print(f"\n📊 管理器統計:")
    stats = manager.get_manager_stats()
    print(f"   當前模型: {stats['current_model']}")
    print(f"   模型切換次數: {stats['model_switches']}")
    print(f"   總請求數: {stats['selector_stats']['selection_stats']['total_requests']}")
    print(f"   緩存命中率: {stats['selector_stats']['selection_stats']['cache_hits'] / max(1, stats['selector_stats']['selection_stats']['total_requests']) * 100:.1f}%")