#!/usr/bin/env python3
"""
🔍 測試導入問題診斷腳本
"""

import sys
from pathlib import Path

# 添加路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_basic_imports():
    """測試基本導入"""
    print("🔍 測試基本導入...")
    
    try:
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy 導入失敗: {e}")
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError as e:
        print(f"❌ OpenCV 導入失敗: {e}")
    
    try:
        from typing import Optional, Tuple
        print("✅ Typing imports: OK")
    except ImportError as e:
        print(f"❌ Typing 導入失敗: {e}")

def test_font_manager():
    """測試字體管理器導入"""
    print("\n🖋️ 測試字體管理器...")
    
    try:
        from inference_system.visualization.font_manager import FontManager
        print("✅ FontManager 導入成功")
        
        # 測試實例化
        font_manager = FontManager()
        print("✅ FontManager 實例化成功")
        
    except ImportError as e:
        print(f"❌ FontManager 導入失敗: {e}")
        import traceback
        traceback.print_exc()
    except Exception as e:
        print(f"❌ FontManager 實例化失敗: {e}")
        import traceback
        traceback.print_exc()

def test_inference_system():
    """測試推理系統導入"""
    print("\n🤖 測試推理系統...")
    
    try:
        from inference_system import create_inference_system, UnifiedConfig, ClassConfig
        print("✅ 推理系統核心組件導入成功")
    except ImportError as e:
        print(f"❌ 推理系統導入失敗: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函數"""
    print("🚀 開始導入測試...")
    print("=" * 50)
    
    test_basic_imports()
    test_font_manager()
    test_inference_system()
    
    print("\n" + "=" * 50)
    print("✅ 導入測試完成")

if __name__ == "__main__":
    main()