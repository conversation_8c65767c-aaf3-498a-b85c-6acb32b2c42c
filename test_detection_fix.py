#!/usr/bin/env python3
"""
測試Detection對象修復的小腳本
"""

import sys
from pathlib import Path

# 添加路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from road_ai_framework.inference_system.core.base_inference import Detection
    
    # 測試創建Detection對象
    det1 = Detection(
        bbox=[10.0, 20.0, 30.0, 40.0],
        confidence=0.8,
        class_id=1,
        class_name='test'
    )
    print("✅ Detection對象創建成功:", det1)
    
    # 測試列表操作
    det_list = [det1]
    copied_list = det_list[:]  # 使用切片複製
    print("✅ 列表切片複製成功:", len(copied_list))
    
    # 測試字典操作
    det_dict = {
        'bbox': [10.0, 20.0, 30.0, 40.0],
        'confidence': 0.8,
        'class_id': 1,
        'class_name': 'test'
    }
    copied_dict = det_dict.copy()
    print("✅ 字典copy()成功:", copied_dict['class_name'])
    
    print("🎉 所有測試通過！Detection對象相關的copy問題已修復。")
    
except Exception as e:
    print(f"❌ 測試失敗: {e}")
    import traceback
    traceback.print_exc()