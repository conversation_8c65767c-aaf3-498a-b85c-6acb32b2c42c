#!/usr/bin/env python3
"""
🧪 統合測試框架
測試各模組間的整合和端到端功能
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import numpy as np
import cv2

from ..main import UnifiedYOLOInference, create_inference_system
from ..config import UnifiedConfig, ClassConfig
from ..core.base_inference import Detection, TimingInfo


class TestIntegration:
    """統合測試類"""
    
    @pytest.fixture
    def temp_dir(self):
        """創建臨時測試目錄"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def sample_image(self, temp_dir):
        """創建測試圖像"""
        image_path = Path(temp_dir) / "test_image.jpg"
        
        # 創建640x640的測試圖像
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        cv2.imwrite(str(image_path), test_image)
        
        return str(image_path)
    
    @pytest.fixture
    def sample_config(self, temp_dir):
        """創建測試配置"""
        config = UnifiedConfig()
        config.model.model_path = "mock_model.pt"
        config.output_dir = str(Path(temp_dir) / "output")
        
        # 簡化類別配置
        config.classes = {
            2: ClassConfig(
                id=2,
                name="linear_crack",
                display_name="縱向裂縫",
                color=[0, 0, 255],
                confidence_threshold=0.2,
                sahi_confidence_threshold=0.08,
                enabled=True
            )
        }
        
        return config
    
    @pytest.fixture
    def mock_model_predictions(self):
        """模擬模型預測結果"""
        return [
            Detection(
                bbox=[100, 100, 200, 150],
                confidence=0.85,
                class_id=2,
                class_name="linear_crack",
                area=5000.0,
                mask=None
            ),
            Detection(
                bbox=[300, 200, 400, 250], 
                confidence=0.75,
                class_id=2,
                class_name="linear_crack",
                area=2500.0,
                mask=None
            )
        ]
    
    def test_create_inference_system(self, sample_config):
        """測試推理系統創建"""
        with patch('inference_system.core.model_adapter.YOLO') as mock_yolo:
            mock_yolo.return_value = Mock()
            
            system = create_inference_system(config=sample_config)
            assert isinstance(system, UnifiedYOLOInference)
            assert system.config == sample_config
    
    @patch('inference_system.core.model_adapter.YOLO')
    def test_single_image_inference(self, mock_yolo, sample_config, sample_image, mock_model_predictions):
        """測試單張圖像推理"""
        # 設置mock
        mock_model = Mock()
        mock_model.return_value = [Mock()]  # 模擬YOLO結果
        mock_yolo.return_value = mock_model
        
        with patch.object(UnifiedYOLOInference, '_unified_system') as mock_system:
            mock_system.process_single_image.return_value = {
                'success': True,
                'detections': mock_model_predictions,
                'timing_info': TimingInfo(total_time=1.5, model_time=1.0, post_time=0.5),
                'detection_statistics': {'total_detections': 2}
            }
            
            system = UnifiedYOLOInference(sample_config)
            result = system.process_single_image(sample_image)
            
            assert result['success'] is True
            assert len(result['detections']) == 2
            assert 'timing_info' in result
    
    @patch('inference_system.core.model_adapter.YOLO')
    def test_directory_processing(self, mock_yolo, sample_config, temp_dir, mock_model_predictions):
        """測試目錄批量處理"""
        # 創建多個測試圖像
        test_images = []
        for i in range(3):
            image_path = Path(temp_dir) / f"test_{i}.jpg"
            test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
            cv2.imwrite(str(image_path), test_image)
            test_images.append(str(image_path))
        
        # 設置mock
        mock_model = Mock()
        mock_model.return_value = [Mock()]
        mock_yolo.return_value = mock_model
        
        with patch.object(UnifiedYOLOInference, 'process_single_image') as mock_process:
            mock_process.return_value = {
                'success': True,
                'detections': mock_model_predictions,
                'timing_info': TimingInfo(total_time=1.5, model_time=1.0, post_time=0.5)
            }
            
            system = UnifiedYOLOInference(sample_config)
            summary = system.process_directory(temp_dir)
            
            assert 'total_images' in summary
            assert summary.get('total_images', 0) >= 3
    
    def test_config_validation(self, temp_dir):
        """測試配置驗證"""
        config = UnifiedConfig()
        config.model.model_path = "nonexistent_model.pt"
        config.output_dir = str(Path(temp_dir) / "output")
        
        # 測試無效模型路徑的處理
        with patch('inference_system.core.model_adapter.YOLO') as mock_yolo:
            mock_yolo.side_effect = Exception("模型載入失敗")
            
            with pytest.raises(Exception):
                UnifiedYOLOInference(config)
    
    def test_sahi_integration(self, sample_config, sample_image, mock_model_predictions):
        """測試SAHI切片推理整合"""
        # 啟用SAHI
        sample_config.sahi.enabled = True
        sample_config.sahi.slice_height = 320
        sample_config.sahi.slice_width = 320
        
        with patch('inference_system.core.model_adapter.YOLO') as mock_yolo:
            mock_model = Mock()
            mock_model.return_value = [Mock()]
            mock_yolo.return_value = mock_model
            
            with patch('inference_system.processing.slice_processor.get_sliced_prediction') as mock_sahi:
                mock_sahi.return_value = Mock(object_prediction_list=[])
                
                system = UnifiedYOLOInference(sample_config)
                result = system.process_single_image(sample_image)
                
                # 驗證SAHI被調用
                assert mock_sahi.called
    
    def test_intelligent_filtering(self, sample_config, mock_model_predictions):
        """測試智能過濾功能"""
        # 創建有衝突的檢測結果
        conflicting_detections = [
            Detection(
                bbox=[100, 100, 120, 200],  # 細長形狀 - linear_crack
                confidence=0.8,
                class_id=2,
                class_name="linear_crack",
                area=4000.0
            ),
            Detection(
                bbox=[105, 105, 115, 180],  # 重疊的龜裂
                confidence=0.7,
                class_id=3,
                class_name="Alligator_crack", 
                area=1500.0
            )
        ]
        
        sample_config.enable_intelligent_filtering = True
        
        with patch('inference_system.core.model_adapter.YOLO') as mock_yolo:
            mock_model = Mock()
            mock_yolo.return_value = mock_model
            
            # 測試過濾邏輯
            from ..processing.post_processor import PostProcessor
            processor = PostProcessor(sample_config)
            
            filtered = processor._intelligent_filtering(conflicting_detections)
            
            # 應該保留linear_crack，移除Alligator_crack
            assert len(filtered) == 1
            assert filtered[0].class_name == "linear_crack"
    
    def test_three_view_generation(self, sample_config, sample_image):
        """測試三視圖生成"""
        sample_config.visualization.enable_three_view = True
        
        with patch('inference_system.core.model_adapter.YOLO') as mock_yolo:
            mock_model = Mock()
            mock_yolo.return_value = mock_model
            
            with patch('inference_system.visualization.three_view_generator.ThreeViewGenerator') as mock_gen:
                mock_gen.return_value.generate_three_view.return_value = "mock_three_view.jpg"
                
                system = UnifiedYOLOInference(sample_config)
                # 這裡需要mock inference_engine的方法
                # 測試邏輯需要進一步完善
    
    def test_csv_statistics_output(self, sample_config, sample_image, mock_model_predictions):
        """測試CSV統計輸出"""
        with patch('inference_system.core.model_adapter.YOLO') as mock_yolo:
            mock_model = Mock()
            mock_yolo.return_value = mock_model
            
            with patch('inference_system.io.csv_manager.CSVManager') as mock_csv:
                mock_csv_instance = Mock()
                mock_csv.return_value = mock_csv_instance
                
                system = UnifiedYOLOInference(sample_config)
                result = system.process_single_image(sample_image)
                
                # 驗證CSV管理器被調用
                assert mock_csv.called
    
    def test_error_handling(self, sample_config):
        """測試錯誤處理"""
        with patch('inference_system.core.model_adapter.YOLO') as mock_yolo:
            mock_yolo.side_effect = Exception("模型載入失敗")
            
            with pytest.raises(Exception):
                UnifiedYOLOInference(sample_config)
    
    def test_cleanup_resources(self, sample_config):
        """測試資源清理"""
        with patch('inference_system.core.model_adapter.YOLO') as mock_yolo:
            mock_model = Mock()
            mock_yolo.return_value = mock_model
            
            system = UnifiedYOLOInference(sample_config)
            
            # 測試清理功能
            system.cleanup()
            # 應該沒有異常發生


class TestModuleInteractions:
    """測試模組間交互"""
    
    def test_config_propagation(self):
        """測試配置在模組間的傳遞"""
        config = UnifiedConfig()
        
        # 測試配置傳遞到各個模組
        with patch('inference_system.core.model_adapter.YOLO'):
            system = UnifiedYOLOInference(config)
            
            assert system.config is config
            assert system.model_adapter.config is config
            assert system.inference_engine.config is config
    
    def test_data_flow(self):
        """測試數據在模組間的流動"""
        # 創建測試檢測結果
        detections = [
            Detection(
                bbox=[100, 100, 200, 200],
                confidence=0.9,
                class_id=2,
                class_name="linear_crack",
                area=10000.0
            )
        ]
        
        # 測試數據從inference_engine -> post_processor -> csv_manager的流動
        # 這裡可以添加更詳細的數據流測試
        assert len(detections) == 1
        assert detections[0].class_name == "linear_crack"


if __name__ == "__main__":
    # 運行測試
    pytest.main([__file__, "-v"])