#!/usr/bin/env python3
"""
📊 指標計算工具
提供混淆矩陣、IoU等指標計算功能
"""

import numpy as np
from typing import List, Dict, Any, Tuple

from ..core.base_inference import Detection, ConfusionMetrics


def calculate_iou(bbox1: List[float], bbox2: List[float]) -> float:
    """
    計算兩個邊界框的IoU
    
    Args:
        bbox1: 第一個邊界框 [x1, y1, x2, y2]
        bbox2: 第二個邊界框 [x1, y1, x2, y2]
        
    Returns:
        float: IoU值 (0-1)
    """
    x1_max = max(bbox1[0], bbox2[0])
    y1_max = max(bbox1[1], bbox2[1])
    x2_min = min(bbox1[2], bbox2[2])
    y2_min = min(bbox1[3], bbox2[3])
    
    if x2_min <= x1_max or y2_min <= y1_max:
        return 0.0
    
    intersection = (x2_min - x1_max) * (y2_min - y1_max)
    area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
    area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0


def calculate_confusion_metrics(predictions: List[Detection], 
                              gt_annotations: List[Dict[str, Any]], 
                              iou_threshold: float = 0.5) -> ConfusionMetrics:
    """
    計算混淆矩陣指標
    
    Args:
        predictions: 預測結果列表
        gt_annotations: GT標註列表
        iou_threshold: IoU閾值
        
    Returns:
        ConfusionMetrics: 混淆矩陣指標
    """
    tp = 0  # True Positive
    fp = 0  # False Positive
    fn = 0  # False Negative
    
    matched_gt = set()
    
    # 計算TP和FP
    for pred in predictions:
        best_iou = 0.0
        best_gt_idx = -1
        
        for gt_idx, gt in enumerate(gt_annotations):
            if gt_idx in matched_gt:
                continue
            
            # 檢查類別匹配
            if pred.class_id != gt.get('class_id', -1):
                continue
            
            # 計算IoU
            gt_bbox = gt.get('bbox', [])
            if len(gt_bbox) >= 4:
                iou = calculate_iou(pred.bbox, gt_bbox)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = gt_idx
        
        # 判斷TP或FP
        if best_iou >= iou_threshold and best_gt_idx >= 0:
            tp += 1
            matched_gt.add(best_gt_idx)
        else:
            fp += 1
    
    # 計算FN
    fn = len(gt_annotations) - len(matched_gt)
    
    # 計算指標
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
    
    return ConfusionMetrics(
        true_positive=tp,
        false_positive=fp,
        false_negative=fn,
        precision=precision,
        recall=recall,
        f1_score=f1_score
    )


def calculate_bbox_area(bbox: List[float]) -> float:
    """計算邊界框面積"""
    return (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])


def calculate_bbox_center(bbox: List[float]) -> Tuple[float, float]:
    """計算邊界框中心點"""
    center_x = (bbox[0] + bbox[2]) / 2
    center_y = (bbox[1] + bbox[3]) / 2
    return center_x, center_y


def calculate_bbox_aspect_ratio(bbox: List[float]) -> float:
    """計算邊界框長寬比（較短邊/較長邊）"""
    width = bbox[2] - bbox[0]
    height = bbox[3] - bbox[1]
    return min(width, height) / max(width, height) if max(width, height) > 0 else 0.0


def calculate_distance_between_centers(bbox1: List[float], bbox2: List[float]) -> float:
    """計算兩個邊界框中心點的距離"""
    center1 = calculate_bbox_center(bbox1)
    center2 = calculate_bbox_center(bbox2)
    
    return np.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)


def filter_detections_by_confidence(detections: List[Detection], threshold: float) -> List[Detection]:
    """根據置信度過濾檢測結果"""
    return [det for det in detections if det.confidence >= threshold]


def filter_detections_by_area(detections: List[Detection], 
                             min_area: float = 0, 
                             max_area: float = float('inf')) -> List[Detection]:
    """根據面積過濾檢測結果"""
    return [
        det for det in detections 
        if det.area is not None and min_area <= det.area <= max_area
    ]


def group_detections_by_class(detections: List[Detection]) -> Dict[int, List[Detection]]:
    """按類別分組檢測結果"""
    groups = {}
    for detection in detections:
        class_id = detection.class_id
        if class_id not in groups:
            groups[class_id] = []
        groups[class_id].append(detection)
    return groups


def calculate_detection_statistics(detections: List[Detection]) -> Dict[str, Any]:
    """計算檢測結果統計信息"""
    if not detections:
        return {
            'total_detections': 0,
            'class_distribution': {},
            'confidence_stats': {},
            'area_stats': {}
        }
    
    # 類別分佈
    class_counts = {}
    confidences = []
    areas = []
    
    for detection in detections:
        # 類別統計
        class_name = detection.class_name
        class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        # 置信度統計
        confidences.append(detection.confidence)
        
        # 面積統計
        if detection.area is not None:
            areas.append(detection.area)
    
    # 計算統計指標
    confidence_stats = {
        'mean': np.mean(confidences),
        'median': np.median(confidences),
        'std': np.std(confidences),
        'min': np.min(confidences),
        'max': np.max(confidences)
    }
    
    area_stats = {}
    if areas:
        area_stats = {
            'mean': np.mean(areas),
            'median': np.median(areas),
            'std': np.std(areas),
            'min': np.min(areas),
            'max': np.max(areas)
        }
    
    return {
        'total_detections': len(detections),
        'class_distribution': class_counts,
        'confidence_stats': confidence_stats,
        'area_stats': area_stats
    }