#!/usr/bin/env python3
"""
🔌 插件管理器
支援動態載入和管理自定義插件
"""

import logging
import importlib
import inspect
from pathlib import Path
from typing import Dict, List, Any, Optional, Type, Callable
from abc import ABC, abstractmethod
import threading

from ..config import UnifiedConfig
from ..core.base_inference import Detection


class BasePlugin(ABC):
    """插件基類"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @property
    @abstractmethod
    def name(self) -> str:
        """插件名稱"""
        pass
    
    @property
    @abstractmethod
    def version(self) -> str:
        """插件版本"""
        pass
    
    @abstractmethod
    def initialize(self) -> bool:
        """
        初始化插件
        
        Returns:
            bool: 初始化是否成功
        """
        pass
    
    @abstractmethod
    def cleanup(self):
        """清理插件資源"""
        pass


class PreProcessorPlugin(BasePlugin):
    """前處理插件基類"""
    
    @abstractmethod
    def process(self, image_path: str, image_data: Any) -> Any:
        """
        處理圖像數據
        
        Args:
            image_path: 圖像路徑
            image_data: 圖像數據
            
        Returns:
            Any: 處理後的圖像數據
        """
        pass


class PostProcessorPlugin(BasePlugin):
    """後處理插件基類"""
    
    @abstractmethod
    def process(self, detections: List[Detection], image_path: str) -> List[Detection]:
        """
        處理檢測結果
        
        Args:
            detections: 檢測結果列表
            image_path: 圖像路徑
            
        Returns:
            List[Detection]: 處理後的檢測結果
        """
        pass


class ExportPlugin(BasePlugin):
    """導出插件基類"""
    
    @property
    @abstractmethod
    def export_format(self) -> str:
        """導出格式名稱"""
        pass
    
    @abstractmethod
    def export(self, 
               detections: List[Detection], 
               image_path: str, 
               output_path: str) -> str:
        """
        導出檢測結果
        
        Args:
            detections: 檢測結果
            image_path: 圖像路徑
            output_path: 輸出路徑
            
        Returns:
            str: 導出文件路徑
        """
        pass


class PluginManager:
    """
    插件管理器
    
    特色功能:
    - 動態插件載入
    - 插件依賴管理
    - 錯誤隔離
    - 性能監控
    """
    
    def __init__(self, config: UnifiedConfig):
        """
        初始化插件管理器
        
        Args:
            config: 統一配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 插件註冊表
        self.preprocessor_plugins: Dict[str, PreProcessorPlugin] = {}
        self.postprocessor_plugins: Dict[str, PostProcessorPlugin] = {}
        self.export_plugins: Dict[str, ExportPlugin] = {}
        
        # 插件搜索路徑
        self.plugin_paths: List[Path] = []
        
        # 執行統計
        self.execution_stats: Dict[str, Dict[str, Any]] = {}
        
        # 線程安全
        self._lock = threading.RLock()
        
        self.logger.info("🔌 插件管理器初始化完成")
    
    def add_plugin_path(self, path: str):
        """添加插件搜索路徑"""
        plugin_path = Path(path)
        if plugin_path.exists() and plugin_path not in self.plugin_paths:
            self.plugin_paths.append(plugin_path)
            self.logger.info(f"🔌 添加插件路徑: {plugin_path}")
    
    def discover_plugins(self) -> Dict[str, List[str]]:
        """
        自動發現插件
        
        Returns:
            Dict: 發現的插件列表
        """
        discovered = {
            'preprocessor': [],
            'postprocessor': [],
            'export': []
        }
        
        for plugin_path in self.plugin_paths:
            try:
                for py_file in plugin_path.glob("*.py"):
                    if py_file.name.startswith("_"):
                        continue
                    
                    module_name = py_file.stem
                    
                    # 動態導入模組
                    spec = importlib.util.spec_from_file_location(module_name, py_file)
                    if spec and spec.loader:
                        module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(module)
                        
                        # 查找插件類
                        for name, obj in inspect.getmembers(module, inspect.isclass):
                            if (obj != BasePlugin and 
                                issubclass(obj, BasePlugin) and 
                                not inspect.isabstract(obj)):
                                
                                plugin_type = self._determine_plugin_type(obj)
                                if plugin_type:
                                    discovered[plugin_type].append(f"{module_name}.{name}")
                                    
            except Exception as e:
                self.logger.warning(f"⚠️ 插件發現失敗 {plugin_path}: {str(e)}")
        
        if any(discovered.values()):
            self.logger.info(f"🔍 發現插件: {dict(discovered)}")
        
        return discovered
    
    def _determine_plugin_type(self, plugin_class: Type[BasePlugin]) -> Optional[str]:
        """確定插件類型"""
        if issubclass(plugin_class, PreProcessorPlugin):
            return 'preprocessor'
        elif issubclass(plugin_class, PostProcessorPlugin):
            return 'postprocessor'
        elif issubclass(plugin_class, ExportPlugin):
            return 'export'
        return None
    
    def load_plugin(self, 
                   plugin_class: Type[BasePlugin], 
                   plugin_config: Optional[Dict[str, Any]] = None) -> bool:
        """
        載入插件
        
        Args:
            plugin_class: 插件類
            plugin_config: 插件配置
            
        Returns:
            bool: 載入是否成功
        """
        try:
            with self._lock:
                # 創建插件實例
                config = plugin_config or {}
                plugin_instance = plugin_class(config)
                
                # 初始化插件
                if not plugin_instance.initialize():
                    self.logger.error(f"❌ 插件初始化失敗: {plugin_instance.name}")
                    return False
                
                # 註冊插件
                plugin_type = self._determine_plugin_type(plugin_class)
                if plugin_type == 'preprocessor':
                    self.preprocessor_plugins[plugin_instance.name] = plugin_instance
                elif plugin_type == 'postprocessor':
                    self.postprocessor_plugins[plugin_instance.name] = plugin_instance
                elif plugin_type == 'export':
                    self.export_plugins[plugin_instance.name] = plugin_instance
                else:
                    self.logger.error(f"❌ 未知插件類型: {plugin_class}")
                    return False
                
                # 初始化統計
                self.execution_stats[plugin_instance.name] = {
                    'execution_count': 0,
                    'total_time': 0.0,
                    'average_time': 0.0,
                    'error_count': 0
                }
                
                self.logger.info(f"✅ 插件載入成功: {plugin_instance.name} v{plugin_instance.version}")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ 插件載入失敗: {str(e)}")
            return False
    
    def unload_plugin(self, plugin_name: str) -> bool:
        """
        卸載插件
        
        Args:
            plugin_name: 插件名稱
            
        Returns:
            bool: 卸載是否成功
        """
        try:
            with self._lock:
                plugin = None
                
                # 查找並移除插件
                if plugin_name in self.preprocessor_plugins:
                    plugin = self.preprocessor_plugins.pop(plugin_name)
                elif plugin_name in self.postprocessor_plugins:
                    plugin = self.postprocessor_plugins.pop(plugin_name)
                elif plugin_name in self.export_plugins:
                    plugin = self.export_plugins.pop(plugin_name)
                
                if plugin:
                    # 清理插件資源
                    plugin.cleanup()
                    
                    # 清理統計
                    self.execution_stats.pop(plugin_name, None)
                    
                    self.logger.info(f"🗑️ 插件已卸載: {plugin_name}")
                    return True
                else:
                    self.logger.warning(f"⚠️ 插件未找到: {plugin_name}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"❌ 插件卸載失敗 {plugin_name}: {str(e)}")
            return False
    
    def execute_preprocessors(self, image_path: str, image_data: Any) -> Any:
        """
        執行前處理插件鏈
        
        Args:
            image_path: 圖像路徑
            image_data: 圖像數據
            
        Returns:
            Any: 處理後的圖像數據
        """
        processed_data = image_data
        
        for plugin_name, plugin in self.preprocessor_plugins.items():
            try:
                import time
                start_time = time.time()
                
                processed_data = plugin.process(image_path, processed_data)
                
                # 更新統計
                execution_time = time.time() - start_time
                self._update_plugin_stats(plugin_name, execution_time)
                
                self.logger.debug(f"🔌 前處理插件完成: {plugin_name} ({execution_time:.3f}s)")
                
            except Exception as e:
                self.logger.error(f"❌ 前處理插件錯誤 {plugin_name}: {str(e)}")
                self._update_plugin_stats(plugin_name, 0, error=True)
                # 繼續執行其他插件
        
        return processed_data
    
    def execute_postprocessors(self, 
                              detections: List[Detection], 
                              image_path: str) -> List[Detection]:
        """
        執行後處理插件鏈
        
        Args:
            detections: 檢測結果
            image_path: 圖像路徑
            
        Returns:
            List[Detection]: 處理後的檢測結果
        """
        processed_detections = detections
        
        for plugin_name, plugin in self.postprocessor_plugins.items():
            try:
                import time
                start_time = time.time()
                
                processed_detections = plugin.process(processed_detections, image_path)
                
                # 更新統計
                execution_time = time.time() - start_time
                self._update_plugin_stats(plugin_name, execution_time)
                
                self.logger.debug(f"🔌 後處理插件完成: {plugin_name} ({execution_time:.3f}s)")
                
            except Exception as e:
                self.logger.error(f"❌ 後處理插件錯誤 {plugin_name}: {str(e)}")
                self._update_plugin_stats(plugin_name, 0, error=True)
                # 繼續執行其他插件
        
        return processed_detections
    
    def execute_export(self, 
                      export_format: str,
                      detections: List[Detection], 
                      image_path: str, 
                      output_path: str) -> Optional[str]:
        """
        執行導出插件
        
        Args:
            export_format: 導出格式
            detections: 檢測結果
            image_path: 圖像路徑
            output_path: 輸出路徑
            
        Returns:
            Optional[str]: 導出文件路徑
        """
        for plugin_name, plugin in self.export_plugins.items():
            if plugin.export_format == export_format:
                try:
                    import time
                    start_time = time.time()
                    
                    result_path = plugin.export(detections, image_path, output_path)
                    
                    # 更新統計
                    execution_time = time.time() - start_time
                    self._update_plugin_stats(plugin_name, execution_time)
                    
                    self.logger.debug(f"🔌 導出插件完成: {plugin_name} ({execution_time:.3f}s)")
                    return result_path
                    
                except Exception as e:
                    self.logger.error(f"❌ 導出插件錯誤 {plugin_name}: {str(e)}")
                    self._update_plugin_stats(plugin_name, 0, error=True)
        
        self.logger.warning(f"⚠️ 未找到導出格式的插件: {export_format}")
        return None
    
    def _update_plugin_stats(self, plugin_name: str, execution_time: float, error: bool = False):
        """更新插件執行統計"""
        if plugin_name in self.execution_stats:
            stats = self.execution_stats[plugin_name]
            stats['execution_count'] += 1
            
            if error:
                stats['error_count'] += 1
            else:
                stats['total_time'] += execution_time
                stats['average_time'] = stats['total_time'] / max(1, stats['execution_count'] - stats['error_count'])
    
    def get_plugin_list(self) -> Dict[str, List[Dict[str, Any]]]:
        """獲取插件列表"""
        plugin_list = {
            'preprocessor': [],
            'postprocessor': [],
            'export': []
        }
        
        for name, plugin in self.preprocessor_plugins.items():
            plugin_list['preprocessor'].append({
                'name': name,
                'version': plugin.version,
                'stats': self.execution_stats.get(name, {})
            })
        
        for name, plugin in self.postprocessor_plugins.items():
            plugin_list['postprocessor'].append({
                'name': name,
                'version': plugin.version,
                'stats': self.execution_stats.get(name, {})
            })
        
        for name, plugin in self.export_plugins.items():
            plugin_list['export'].append({
                'name': name,
                'version': plugin.version,
                'export_format': plugin.export_format,
                'stats': self.execution_stats.get(name, {})
            })
        
        return plugin_list
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """獲取執行統計信息"""
        return {
            'total_plugins': (len(self.preprocessor_plugins) + 
                            len(self.postprocessor_plugins) + 
                            len(self.export_plugins)),
            'plugin_counts': {
                'preprocessor': len(self.preprocessor_plugins),
                'postprocessor': len(self.postprocessor_plugins),
                'export': len(self.export_plugins)
            },
            'execution_stats': dict(self.execution_stats)
        }
    
    def cleanup(self):
        """清理插件管理器"""
        self.logger.debug("🧹 清理插件管理器資源")
        
        with self._lock:
            # 清理所有插件
            all_plugins = (list(self.preprocessor_plugins.values()) + 
                          list(self.postprocessor_plugins.values()) + 
                          list(self.export_plugins.values()))
            
            for plugin in all_plugins:
                try:
                    plugin.cleanup()
                except Exception as e:
                    self.logger.warning(f"⚠️ 插件清理失敗 {plugin.name}: {str(e)}")
            
            # 清空註冊表
            self.preprocessor_plugins.clear()
            self.postprocessor_plugins.clear()
            self.export_plugins.clear()
            self.execution_stats.clear()