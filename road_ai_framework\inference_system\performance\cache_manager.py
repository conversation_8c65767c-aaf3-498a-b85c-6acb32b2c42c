#!/usr/bin/env python3
"""
💾 緩存管理器
提供智能緩存機制，提升重複推理的性能
"""

import pickle
import hashlib
import logging
import threading
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from datetime import datetime, timedelta
import json
import sqlite3
from dataclasses import asdict

from ..core.base_inference import Detection
from ..config import UnifiedConfig


class CacheManager:
    """
    智能緩存管理器
    
    特色功能:
    - 多級緩存策略（內存 + 磁盤）
    - 自動過期清理
    - 緩存命中率統計
    - 線程安全設計
    """
    
    def __init__(self, 
                 config: UnifiedConfig,
                 cache_dir: Optional[str] = None,
                 max_memory_size: int = 100,  # MB
                 max_disk_size: int = 1000,   # MB
                 ttl_hours: int = 24):
        """
        初始化緩存管理器
        
        Args:
            config: 統一配置
            cache_dir: 緩存目錄
            max_memory_size: 最大內存緩存大小(MB)
            max_disk_size: 最大磁盤緩存大小(MB)  
            ttl_hours: 緩存生存時間(小時)
        """
        self.config = config
        self.cache_dir = Path(cache_dir or config.output_dir) / "cache"
        self.max_memory_size = max_memory_size * 1024 * 1024  # 轉換為bytes
        self.max_disk_size = max_disk_size * 1024 * 1024
        self.ttl = timedelta(hours=ttl_hours)
        
        # 內存緩存
        self._memory_cache: Dict[str, Any] = {}
        self._memory_timestamps: Dict[str, datetime] = {}
        self._memory_size = 0
        
        # 統計信息
        self._hit_count = 0
        self._miss_count = 0
        
        # 線程鎖
        self._lock = threading.RLock()
        
        self.logger = logging.getLogger(__name__)
        
        # 初始化緩存目錄和數據庫
        self._initialize_cache()
        
        self.logger.info("💾 緩存管理器初始化完成")
        self.logger.info(f"   緩存目錄: {self.cache_dir}")
        self.logger.info(f"   內存限制: {max_memory_size}MB")
        self.logger.info(f"   磁盤限制: {max_disk_size}MB")
        self.logger.info(f"   TTL: {ttl_hours}小time")
    
    def _initialize_cache(self):
        """初始化緩存系統"""
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 創建SQLite數據庫用於索引管理
        self.db_path = self.cache_dir / "cache_index.db"
        self._init_database()
        
        # 清理過期緩存
        self._cleanup_expired_cache()
    
    def _init_database(self):
        """初始化緩存索引數據庫"""
        with sqlite3.connect(str(self.db_path)) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS cache_index (
                    cache_key TEXT PRIMARY KEY,
                    file_path TEXT NOT NULL,
                    created_at TIMESTAMP NOT NULL,
                    last_accessed TIMESTAMP NOT NULL,
                    size_bytes INTEGER NOT NULL,
                    hit_count INTEGER DEFAULT 0
                )
            """)
            conn.commit()
    
    def get_cache_key(self, 
                     image_path: str, 
                     model_hash: Optional[str] = None,
                     config_hash: Optional[str] = None) -> str:
        """
        生成緩存鍵
        
        Args:
            image_path: 圖像路徑
            model_hash: 模型哈希
            config_hash: 配置哈希
            
        Returns:
            str: 緩存鍵
        """
        # 獲取圖像文件的修改時間和大小
        image_path_obj = Path(image_path)
        if image_path_obj.exists():
            stat = image_path_obj.stat()
            image_info = f"{image_path}:{stat.st_mtime}:{stat.st_size}"
        else:
            image_info = str(image_path)
        
        # 組合所有信息
        cache_data = {
            'image_info': image_info,
            'model_hash': model_hash or "default",
            'config_hash': config_hash or self._get_config_hash(),
            'version': '3.0'  # 緩存版本
        }
        
        # 生成MD5哈希
        cache_str = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_str.encode()).hexdigest()
    
    def _get_config_hash(self) -> str:
        """獲取配置哈希"""
        # 提取影響推理結果的關鍵配置
        key_config = {
            'model_path': self.config.model.model_path,
            'sahi_enabled': self.config.sahi.enabled,
            'slice_height': self.config.sahi.slice_height,
            'slice_width': self.config.sahi.slice_width,
            'overlap_ratio': self.config.sahi.overlap_height_ratio,
            'postprocess_type': self.config.sahi.postprocess_type,
            'classes': {k: asdict(v) for k, v in self.config.classes.items()},
            'intelligent_filtering': self.config.enable_intelligent_filtering
        }
        
        config_str = json.dumps(key_config, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()[:8]
    
    def get(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        從緩存獲取數據
        
        Args:
            cache_key: 緩存鍵
            
        Returns:
            Optional[Dict]: 緩存的數據，None表示未命中
        """
        with self._lock:
            # 1. 首先檢查內存緩存
            if cache_key in self._memory_cache:
                timestamp = self._memory_timestamps[cache_key]
                if datetime.now() - timestamp < self.ttl:
                    self._hit_count += 1
                    self.logger.debug(f"💾 內存緩存命中: {cache_key[:8]}...")
                    return self._memory_cache[cache_key]
                else:
                    # 過期，清理內存緩存
                    self._remove_from_memory_cache(cache_key)
            
            # 2. 檢查磁盤緩存
            disk_data = self._get_from_disk_cache(cache_key)
            if disk_data is not None:
                # 載入到內存緩存
                self._add_to_memory_cache(cache_key, disk_data)
                self._hit_count += 1
                self.logger.debug(f"💿 磁盤緩存命中: {cache_key[:8]}...")
                return disk_data
            
            # 3. 緩存未命中
            self._miss_count += 1
            self.logger.debug(f"❌ 緩存未命中: {cache_key[:8]}...")
            return None
    
    def put(self, cache_key: str, data: Dict[str, Any]):
        """
        將數據放入緩存
        
        Args:
            cache_key: 緩存鍵
            data: 要緩存的數據
        """
        with self._lock:
            # 1. 添加到內存緩存
            self._add_to_memory_cache(cache_key, data)
            
            # 2. 異步保存到磁盤緩存
            threading.Thread(
                target=self._save_to_disk_cache,
                args=(cache_key, data),
                daemon=True
            ).start()
    
    def _add_to_memory_cache(self, cache_key: str, data: Dict[str, Any]):
        """添加到內存緩存"""
        # 估算數據大小
        data_size = len(pickle.dumps(data))
        
        # 檢查內存限制
        while self._memory_size + data_size > self.max_memory_size and self._memory_cache:
            # LRU策略：移除最舊的項目
            oldest_key = min(self._memory_timestamps.keys(), 
                           key=lambda k: self._memory_timestamps[k])
            self._remove_from_memory_cache(oldest_key)
        
        # 添加新數據
        self._memory_cache[cache_key] = data
        self._memory_timestamps[cache_key] = datetime.now()
        self._memory_size += data_size
    
    def _remove_from_memory_cache(self, cache_key: str):
        """從內存緩存移除"""
        if cache_key in self._memory_cache:
            data_size = len(pickle.dumps(self._memory_cache[cache_key]))
            del self._memory_cache[cache_key]
            del self._memory_timestamps[cache_key]
            self._memory_size -= data_size
    
    def _get_from_disk_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """從磁盤緩存獲取數據"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.execute(
                    "SELECT file_path, created_at FROM cache_index WHERE cache_key = ?",
                    (cache_key,)
                )
                row = cursor.fetchone()
                
                if row:
                    file_path, created_at_str = row
                    created_at = datetime.fromisoformat(created_at_str)
                    
                    # 檢查是否過期
                    if datetime.now() - created_at < self.ttl:
                        cache_file = Path(file_path)
                        if cache_file.exists():
                            with open(cache_file, 'rb') as f:
                                data = pickle.load(f)
                            
                            # 更新訪問時間和命中次數
                            conn.execute(
                                "UPDATE cache_index SET last_accessed = ?, hit_count = hit_count + 1 WHERE cache_key = ?",
                                (datetime.now().isoformat(), cache_key)
                            )
                            conn.commit()
                            
                            return data
                    else:
                        # 過期，清理
                        self._remove_from_disk_cache(cache_key)
            
            return None
            
        except Exception as e:
            self.logger.warning(f"⚠️ 磁盤緩存讀取失敗 {cache_key[:8]}: {str(e)}")
            return None
    
    def _save_to_disk_cache(self, cache_key: str, data: Dict[str, Any]):
        """保存到磁盤緩存"""
        try:
            cache_file = self.cache_dir / f"{cache_key}.pkl"
            
            # 保存數據
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f)
            
            # 更新索引
            file_size = cache_file.stat().st_size
            now = datetime.now().isoformat()
            
            with sqlite3.connect(str(self.db_path)) as conn:
                conn.execute(
                    "INSERT OR REPLACE INTO cache_index (cache_key, file_path, created_at, last_accessed, size_bytes) VALUES (?, ?, ?, ?, ?)",
                    (cache_key, str(cache_file), now, now, file_size)
                )
                conn.commit()
            
            self.logger.debug(f"💿 已保存到磁盤緩存: {cache_key[:8]}...")
            
            # 檢查磁盤空間限制
            self._cleanup_disk_cache_by_size()
            
        except Exception as e:
            self.logger.warning(f"⚠️ 磁盤緩存保存失敗 {cache_key[:8]}: {str(e)}")
    
    def _remove_from_disk_cache(self, cache_key: str):
        """從磁盤緩存移除"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.execute(
                    "SELECT file_path FROM cache_index WHERE cache_key = ?",
                    (cache_key,)
                )
                row = cursor.fetchone()
                
                if row:
                    file_path = Path(row[0])
                    if file_path.exists():
                        file_path.unlink()
                    
                    conn.execute("DELETE FROM cache_index WHERE cache_key = ?", (cache_key,))
                    conn.commit()
                    
        except Exception as e:
            self.logger.warning(f"⚠️ 磁盤緩存清理失敗 {cache_key[:8]}: {str(e)}")
    
    def _cleanup_expired_cache(self):
        """清理過期緩存"""
        try:
            cutoff_time = (datetime.now() - self.ttl).isoformat()
            
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.execute(
                    "SELECT cache_key, file_path FROM cache_index WHERE created_at < ?",
                    (cutoff_time,)
                )
                
                expired_keys = cursor.fetchall()
                
                for cache_key, file_path in expired_keys:
                    file_path_obj = Path(file_path)
                    if file_path_obj.exists():
                        file_path_obj.unlink()
                    
                    # 從內存緩存移除
                    if cache_key in self._memory_cache:
                        self._remove_from_memory_cache(cache_key)
                
                # 清理數據庫記錄  
                conn.execute("DELETE FROM cache_index WHERE created_at < ?", (cutoff_time,))
                conn.commit()
                
                if expired_keys:
                    self.logger.info(f"🧹 清理過期緩存: {len(expired_keys)}項")
                    
        except Exception as e:
            self.logger.warning(f"⚠️ 過期緩存清理失敗: {str(e)}")
    
    def _cleanup_disk_cache_by_size(self):
        """按大小清理磁盤緩存"""
        try:
            with sqlite3.connect(str(self.db_path)) as conn:
                cursor = conn.execute(
                    "SELECT SUM(size_bytes) FROM cache_index"
                )
                total_size = cursor.fetchone()[0] or 0
                
                if total_size > self.max_disk_size:
                    # LRU策略：按訪問時間清理
                    cursor = conn.execute(
                        "SELECT cache_key, file_path, size_bytes FROM cache_index ORDER BY last_accessed ASC"
                    )
                    
                    for cache_key, file_path, size_bytes in cursor:
                        self._remove_from_disk_cache(cache_key)
                        total_size -= size_bytes
                        
                        if total_size <= self.max_disk_size * 0.8:  # 保留20%空間
                            break
                            
        except Exception as e:
            self.logger.warning(f"⚠️ 磁盤緩存大小清理失敗: {str(e)}")
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """獲取緩存統計信息"""
        with self._lock:
            total_requests = self._hit_count + self._miss_count
            hit_rate = (self._hit_count / total_requests * 100) if total_requests > 0 else 0
            
            # 磁盤緩存統計
            disk_stats = {'count': 0, 'size_mb': 0}
            try:
                with sqlite3.connect(str(self.db_path)) as conn:
                    cursor = conn.execute("SELECT COUNT(*), SUM(size_bytes) FROM cache_index")
                    row = cursor.fetchone()
                    if row and row[0]:
                        disk_stats['count'] = row[0]
                        disk_stats['size_mb'] = (row[1] or 0) / (1024 * 1024)
            except Exception:
                pass
            
            return {
                'hit_count': self._hit_count,
                'miss_count': self._miss_count,
                'hit_rate_percent': hit_rate,
                'memory_cache': {
                    'count': len(self._memory_cache),
                    'size_mb': self._memory_size / (1024 * 1024)
                },
                'disk_cache': disk_stats
            }
    
    def clear_cache(self, older_than_hours: Optional[int] = None):
        """清空緩存"""
        with self._lock:
            if older_than_hours is None:
                # 清空所有緩存
                self._memory_cache.clear()
                self._memory_timestamps.clear()
                self._memory_size = 0
                
                # 清空磁盤緩存
                try:
                    with sqlite3.connect(str(self.db_path)) as conn:
                        cursor = conn.execute("SELECT file_path FROM cache_index")
                        for (file_path,) in cursor:
                            Path(file_path).unlink(missing_ok=True)
                        
                        conn.execute("DELETE FROM cache_index")
                        conn.commit()
                except Exception as e:
                    self.logger.warning(f"⚠️ 磁盤緩存清空失敗: {str(e)}")
                
                self.logger.info("🧹 已清空所有緩存")
            else:
                # 清空指定時間之前的緩存
                cutoff_time = datetime.now() - timedelta(hours=older_than_hours)
                
                # 清理內存緩存
                keys_to_remove = [
                    k for k, v in self._memory_timestamps.items()
                    if v < cutoff_time
                ]
                for key in keys_to_remove:
                    self._remove_from_memory_cache(key)
                
                # 清理磁盤緩存
                cutoff_time_str = cutoff_time.isoformat()
                try:
                    with sqlite3.connect(str(self.db_path)) as conn:
                        cursor = conn.execute(
                            "SELECT cache_key FROM cache_index WHERE created_at < ?",
                            (cutoff_time_str,)
                        )
                        old_keys = [row[0] for row in cursor.fetchall()]
                        
                        for cache_key in old_keys:
                            self._remove_from_disk_cache(cache_key)
                            
                        self.logger.info(f"🧹 清理{older_than_hours}小時前的緩存: {len(old_keys)}項")
                except Exception as e:
                    self.logger.warning(f"⚠️ 條件緩存清理失敗: {str(e)}")
    
    def cleanup(self):
        """清理緩存管理器資源"""
        self.logger.debug("🧹 清理緩存管理器資源")
        
        # 不需要特別清理，SQLite連接會自動關閉