import os

def delete_files_from_list(txt_file, target_folder):
    # 讀取 txt 檔案中的主檔名清單
    try:
        with open(txt_file, 'r', encoding='utf-8') as f:
            # 獲取主檔名（去除副檔名和換行符號），並過濾空行
            base_names = {os.path.splitext(line.strip())[0] for line in f if line.strip()}
    except FileNotFoundError:
        print(f"錯誤: 找不到文字檔 {txt_file}")
        return 0
    except Exception as e:
        print(f"讀取文字檔時發生錯誤: {e}")
        return 0

    deleted_count = 0

    # 檢查目標資料夾中的所有檔案
    for filename in os.listdir(target_folder):
        # 獲取當前檔案的主檔名
        current_base = os.path.splitext(filename)[0]
        
        # 檢查是否在刪除清單中
        if current_base in base_names:
            file_path = os.path.join(target_folder, filename)
            try:
                os.remove(file_path)
                print(f"已刪除: {file_path}")
                deleted_count += 1
            except Exception as e:
                print(f"刪除 {file_path} 失敗: {e}")

    print(f"\n完成! 共刪除了 {deleted_count} 個檔案")
    return deleted_count

def main():
    print("=== 檔案刪除工具 ===")
    print("說明: 根據文字檔中的主檔名刪除目標資料夾中所有匹配的檔案")
    
    # 輸入文字檔路徑
    txt_file = r"D:\image\road_crack\image_names.txt"
    
    # 輸入目標資料夾路徑
    target_folder = r'\\192.168.1.46\RD_Universe\專案\2025_3.道路破損辨識\1.專案執行\第2次_2338張_全資料\1_修正裂縫_原圖'

    # 檢查路徑是否存在
    if not os.path.isfile(txt_file):
        print(f"錯誤: {txt_file} 不是有效的檔案路徑")
        return

    if not os.path.isdir(target_folder):
        print(f"錯誤: {target_folder} 不是有效的資料夾路徑")
        return

    # 顯示預覽
    print("\n=== 將刪除以下主檔名的所有檔案 ===")
    with open(txt_file, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                print(f"- {os.path.splitext(line.strip())[0]}.*")

    # 確認操作
    confirm = input("\n確定要刪除這些檔案嗎? 此操作無法復原! (y/n): ").lower()
    if confirm != 'y':
        print("操作已取消")
        return

    deleted_count = delete_files_from_list(txt_file, target_folder)

    # 最終確認
    if deleted_count > 0:
        print("\n警告: 檔案已永久刪除，無法恢復!")
    else:
        print("\n沒有刪除任何檔案")

if __name__ == "__main__":
    main()