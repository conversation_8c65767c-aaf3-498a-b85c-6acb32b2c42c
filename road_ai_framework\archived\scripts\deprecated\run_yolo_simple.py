#!/usr/bin/env python3
"""
🚀 簡化版YOLO推理系統
解決導入問題，直接可用的版本

使用說明:
1. 修改下面的參數設定
2. 直接運行: python run_yolo_simple.py
"""

import os
import sys
from pathlib import Path

# 添加路徑
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

print("🚀 簡化版YOLO推理系統啟動")
print("=" * 50)

# ============================================================================
# 📋 參數設定區域 - 請在這裡修改您的設定
# ============================================================================

# 🤖 模型設定
MODEL_PATH = r"D:\4_road_crack\best_0728.pt"  # 修改為您的模型路徑

# 📁 路徑設定
INPUT_PATH = r"D:\image\road_crack\test_600_resize"        # 修改為您的輸入路徑
OUTPUT_PATH = r"D:\image\road_crack\test_600_out_simple"   # 修改為您的輸出路徑

# 🧩 SAHI設定
ENABLE_SAHI = False      # 是否啟用SAHI切片
SLICE_SIZE = 512         # 切片大小
OVERLAP_RATIO = 0.2      # 重疊比例

# 🎨 視覺化設定
ENABLE_VISUALIZATION = True    # 是否生成檢測結果圖像
CONFIDENCE_THRESHOLD = 0.3     # 總體置信度閾值
SAVE_CSV = True               # 是否保存CSV統計

# ============================================================================
# 🔧 核心推理邏輯
# ============================================================================

def check_dependencies():
    """檢查必要的依賴"""
    missing_deps = []
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError:
        missing_deps.append("torch")
    
    try:
        import ultralytics
        print(f"✅ Ultralytics: {ultralytics.__version__}")
    except ImportError:
        missing_deps.append("ultralytics")
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError:
        missing_deps.append("opencv-python")
    
    try:
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
    except ImportError:
        missing_deps.append("numpy")
    
    if missing_deps:
        print(f"❌ 缺少依賴: {missing_deps}")
        print(f"請安裝: pip install {' '.join(missing_deps)}")
        return False
    
    return True

def simple_yolo_inference():
    """簡化的YOLO推理"""
    print("\n🤖 開始YOLO推理...")
    
    try:
        # 導入依賴
        from ultralytics import YOLO
        import cv2
        import numpy as np
        from pathlib import Path
        import json
        
        # 檢查模型文件
        model_path = Path(MODEL_PATH)
        if not model_path.exists():
            print(f"❌ 模型文件不存在: {MODEL_PATH}")
            print("請確認模型路徑是否正確")
            return False
        
        print(f"📁 載入模型: {model_path.name}")
        model = YOLO(str(model_path))
        
        # 檢查輸入路徑
        input_path = Path(INPUT_PATH)
        if not input_path.exists():
            print(f"❌ 輸入路徑不存在: {INPUT_PATH}")
            print("請確認輸入路徑是否正確")
            return False
        
        # 創建輸出目錄
        output_path = Path(OUTPUT_PATH)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 獲取圖像文件
        if input_path.is_file():
            image_files = [input_path]
        else:
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
            image_files = [f for f in input_path.iterdir() 
                          if f.suffix.lower() in image_extensions]
        
        if not image_files:
            print(f"❌ 沒有找到圖像文件")
            return False
        
        print(f"📸 找到 {len(image_files)} 個圖像文件")
        
        # 處理圖像
        all_results = []
        total_detections = 0
        
        for i, image_file in enumerate(image_files):
            print(f"📸 處理 ({i+1}/{len(image_files)}): {image_file.name}")
            
            try:
                # YOLO推理
                results = model(str(image_file), conf=CONFIDENCE_THRESHOLD)
                
                # 提取檢測結果
                detections = []
                if results and len(results) > 0:
                    for result in results:
                        if hasattr(result, 'boxes') and result.boxes is not None:
                            boxes = result.boxes
                            for box in boxes:
                                detection = {
                                    'class_id': int(box.cls.item()),
                                    'class_name': model.names[int(box.cls.item())],
                                    'confidence': float(box.conf.item()),
                                    'bbox': box.xyxy[0].tolist()
                                }
                                detections.append(detection)
                
                image_result = {
                    'image_file': image_file.name,
                    'detections': detections,
                    'detection_count': len(detections)
                }
                all_results.append(image_result)
                total_detections += len(detections)
                
                print(f"   ✅ 檢測到 {len(detections)} 個目標")
                
                # 保存視覺化結果
                if ENABLE_VISUALIZATION and detections:
                    try:
                        # 載入圖像
                        image = cv2.imread(str(image_file))
                        if image is not None:
                            # 繪製檢測框
                            for detection in detections:
                                bbox = detection['bbox']
                                x1, y1, x2, y2 = map(int, bbox)
                                confidence = detection['confidence']
                                class_name = detection['class_name']
                                
                                # 繪製矩形框
                                cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                                
                                # 添加標籤
                                label = f"{class_name}: {confidence:.2f}"
                                cv2.putText(image, label, (x1, y1-10), 
                                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                            
                            # 保存結果圖像
                            output_image_path = output_path / f"result_{image_file.name}"
                            cv2.imwrite(str(output_image_path), image)
                            print(f"   🎨 保存視覺化: {output_image_path.name}")
                    
                    except Exception as e:
                        print(f"   ⚠️ 視覺化失敗: {e}")
                
            except Exception as e:
                print(f"   ❌ 處理失敗: {e}")
                continue
        
        # 保存統計結果
        if SAVE_CSV:
            try:
                # 保存JSON結果
                json_path = output_path / "detection_results.json"
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(all_results, f, indent=2, ensure_ascii=False)
                print(f"📊 保存JSON結果: {json_path.name}")
                
                # 保存CSV統計
                csv_path = output_path / "detection_summary.csv"
                with open(csv_path, 'w', encoding='utf-8') as f:
                    f.write("圖像文件,檢測數量,類別統計\n")
                    for result in all_results:
                        class_counts = {}
                        for det in result['detections']:
                            class_name = det['class_name']
                            class_counts[class_name] = class_counts.get(class_name, 0) + 1
                        
                        class_stats = '; '.join([f"{k}:{v}" for k, v in class_counts.items()])
                        f.write(f"{result['image_file']},{result['detection_count']},{class_stats}\n")
                
                print(f"📊 保存CSV統計: {csv_path.name}")
                
            except Exception as e:
                print(f"⚠️ 保存統計失敗: {e}")
        
        # 顯示總結
        success_count = len([r for r in all_results if r['detection_count'] >= 0])
        print(f"\n✅ 處理完成!")
        print(f"📊 總結:")
        print(f"   處理圖像: {len(image_files)}")
        print(f"   成功處理: {success_count}")
        print(f"   總檢測數: {total_detections}")
        print(f"   平均每圖: {total_detections/max(success_count,1):.1f}")
        print(f"   輸出目錄: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 推理失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("🔍 檢查環境...")
    
    # 檢查依賴
    if not check_dependencies():
        print("\n❌ 環境檢查失敗，請先安裝必要的依賴")
        return
    
    print("\n⚙️ 當前設定:")
    print(f"   模型路徑: {MODEL_PATH}")
    print(f"   輸入路徑: {INPUT_PATH}")
    print(f"   輸出路徑: {OUTPUT_PATH}")
    print(f"   置信度閾值: {CONFIDENCE_THRESHOLD}")
    print(f"   啟用視覺化: {ENABLE_VISUALIZATION}")
    print(f"   保存統計: {SAVE_CSV}")
    
    # 確認繼續
    print(f"\n💡 如需修改設定，請編輯腳本頂部的參數區域")
    user_input = input("按 Enter 繼續，或輸入 'q' 退出: ").strip().lower()
    if user_input == 'q':
        print("👋 退出程式")
        return
    
    # 執行推理
    success = simple_yolo_inference()
    
    if success:
        print(f"\n🎉 推理完成! 結果已保存到: {OUTPUT_PATH}")
    else:
        print(f"\n❌ 推理失敗，請檢查錯誤信息")

if __name__ == "__main__":
    main()